<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API调用日志</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">API调用日志</h2>
        </div>
        <div class="layui-card-body">
            <!-- 搜索表单 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">API:</label>
                        <div class="layui-input-inline">
                            <select name="api_id" id="apiSelect">
                                <option value="">全部API</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">用户:</label>
                        <div class="layui-input-inline">
                            <select name="user_id" id="userSelect">
                                <option value="">全部用户</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">时间范围:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="dateRange" id="dateRange" placeholder="请选择时间范围" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="searchBtn" lay-submit>
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- 数据表格 -->
            <table id="logTable" lay-filter="logTable"></table>
        </div>
    </div>
</div>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    {{#  if(d.status == 1){ }}
    <span class="layui-badge layui-bg-green">成功</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-red">失败</span>
    {{#  } }}
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
</script>

<!-- 日志详情弹窗 -->
<script type="text/html" id="logDetailTpl">
    <div style="padding: 20px;">
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">API名称:</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{{d.api_name}}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">用户名:</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{{d.username}}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">请求时间:</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{{d.created_at}}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">请求IP:</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{{d.ip}}</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">请求参数:</label>
                <div class="layui-input-block">
                    <pre class="layui-code" lay-title="请求参数" lay-skin="notepad">{{d.request_data}}</pre>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">响应结果:</label>
                <div class="layui-input-block">
                    <pre class="layui-code" lay-title="响应结果" lay-skin="notepad">{{d.response_data}}</pre>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">执行时间:</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{{d.execute_time}} ms</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">状态:</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {{#  if(d.status == 1){ }}
                        <span class="layui-badge layui-bg-green">成功</span>
                        {{#  } else { }}
                        <span class="layui-badge layui-bg-red">失败</span>
                        {{#  } }}
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">错误信息:</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{{d.error_msg || '无'}}</div>
                </div>
            </div>
        </div>
    </div>
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../../Easyweb/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script>
layui.use(['table', 'form', 'layer', 'laytpl', 'laydate', 'code'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var laydate = layui.laydate;
    var code = layui.code;
    var $ = layui.jquery;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#logTable',
        url: '../controllers/ApiController.php?action=getLogs',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'api_name', title: 'API名称', sort: true},
            {field: 'username', title: '用户名', sort: true},
            {field: 'ip', title: '请求IP', width: 150},
            {field: 'execute_time', title: '执行时间(ms)', width: 120, sort: true},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100, sort: true},
            {field: 'created_at', title: '请求时间', sort: true},
            {title: '操作', toolbar: '#tableBar', width: 100, fixed: 'right'}
        ]]
    });
    
    // 渲染日期范围选择器
    laydate.render({
        elem: '#dateRange',
        range: true,
        trigger: 'click',
        done: function(value, date, endDate){
            // 将日期范围分割为开始日期和结束日期
            if(value){
                var dates = value.split(' - ');
                $('input[name="start_date"]').val(dates[0]);
                $('input[name="end_date"]').val(dates[1]);
            } else {
                $('input[name="start_date"]').val('');
                $('input[name="end_date"]').val('');
            }
        }
    });
    
    // 加载API列表
    loadApis();
    
    // 加载用户列表
    loadUsers();
    
    // 表格工具条点击事件
    table.on('tool(logTable)', function(obj){
        var data = obj.data;
        var layEvent = obj.event;
        
        if(layEvent === 'detail'){ // 查看详情
            showLogDetail(data);
        }
    });
    
    // 搜索按钮点击事件
    form.on('submit(searchBtn)', function(data){
        // 获取日期范围
        var dateRange = $('#dateRange').val();
        if(dateRange){
            var dates = dateRange.split(' - ');
            data.field.start_date = dates[0];
            data.field.end_date = dates[1];
        }
        
        insTb.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 加载API列表
    function loadApis(){
        $.get('../controllers/ApiController.php?action=getApiList', function(res){
            if(res.code === 0){
                var options = '<option value="">全部API</option>';
                
                $.each(res.data, function(i, item){
                    options += '<option value="' + item.id + '">' + item.name + '</option>';
                });
                
                $('#apiSelect').html(options);
                form.render('select');
            }
        }, 'json');
    }
    
    // 加载用户列表
    function loadUsers(){
        $.get('../controllers/UserController.php?action=getUserList', function(res){
            if(res.code === 0){
                var options = '<option value="">全部用户</option>';
                
                $.each(res.data.list, function(i, item){
                    options += '<option value="' + item.id + '">' + item.username + '</option>';
                });
                
                $('#userSelect').html(options);
                form.render('select');
            }
        }, 'json');
    }
    
    // 显示日志详情
    function showLogDetail(data){
        layer.open({
            type: 1,
            title: '日志详情',
            area: ['700px', '600px'],
            content: laytpl($('#logDetailTpl').html()).render(data),
            success: function(){
                // 渲染代码高亮
                code({
                    elem: '.layui-code'
                });
            }
        });
    }
});
</script>
</body>
</html>