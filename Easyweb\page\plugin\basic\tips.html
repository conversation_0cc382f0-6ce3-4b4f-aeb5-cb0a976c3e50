<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>tips文字提示</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=315"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        .demoTipsOffset button {
            height: 100px;
            line-height: 100px;
        }
    </style>
</head>
<body>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">tips文字提示</div>
                <div class="layui-card-body text-center" style="padding: 30px 0px;">
                    <button lay-tips="Tips Left 提示" lay-direction="1" class="layui-btn layui-btn-primary">
                        &emsp;上&emsp;
                    </button>&emsp;
                    <button lay-tips="Tips Right 提示" lay-direction="2" class="layui-btn layui-btn-primary">
                        &emsp;右&emsp;
                    </button>
                    <br><br>
                    <button lay-tips="Tips Bottom 提示" lay-direction="3" class="layui-btn layui-btn-primary">
                        &emsp;下&emsp;
                    </button>&emsp;
                    <button lay-tips="Tips Top 提示" lay-direction="4" class="layui-btn layui-btn-primary">
                        &emsp;左&emsp;
                    </button>
                </div>
            </div>
        </div>
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">设置偏移实现居中</div>
                <div class="layui-card-body text-center demoTipsOffset" style="padding: 30px 0px;">
                    <button lay-tips="Hello Word!" lay-direction="2" lay-bg="rgba(0,0,0,.6)"
                            class="layui-btn layui-btn-primary">
                        &nbsp;未偏移&nbsp;
                    </button>&emsp;&emsp;
                    <button lay-tips="Hello Word!" lay-direction="4" lay-offset="35px"
                            class="layui-btn layui-btn-primary">
                        垂直居中
                    </button>&emsp;&emsp;
                </div>
            </div>
        </div>
    </div>
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">主题颜色</div>
                <div class="layui-card-body text-center" style="padding: 109px 0px;">
                    <button lay-tips="Hello Word!" lay-bg="#F56C6C" class="layui-btn layui-btn-primary">
                        红色
                    </button>
                    <button lay-tips="Hello Word!" lay-bg="#67C23A" class="layui-btn layui-btn-primary">
                        绿色
                    </button>
                    <button lay-tips="<span style='color:#333;'>Hello Word!</span>" lay-bg="#fff"
                            class="layui-btn layui-btn-primary">
                        白色
                    </button>
                    <button lay-tips="Hello Word!" lay-bg="rgba(70,76,91,.8)" class="layui-btn layui-btn-primary">
                        透明
                    </button>
                </div>
            </div>
        </div>
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">表单验证</div>
                <div class="layui-card-body">
                    <form class="layui-form model-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">姓&emsp;名：</label>
                            <div class="layui-input-block">
                                <input name="username" class="layui-input" type="text" placeholder="输入姓名"
                                       lay-verType="tips" lay-verify="required" required/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">邮&emsp;箱：</label>
                            <div class="layui-input-block">
                                <input name="email" class="layui-input" lay-bg="#FF9500" type="text" placeholder="输入邮箱"
                                       lay-verType="tips" lay-verify="required|email" required/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">性&emsp;别：</label>
                            <div class="layui-input-block">
                                <select name="sex" lay-verType="tips" lay-verify="required" required>
                                    <option value="">请选择性别</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item text-center">
                            <button class="layui-btn layui-btn-primary icon-btn" type="reset">
                                <i class="layui-icon">&#x1006;</i>重置
                            </button>
                            <button class="layui-btn icon-btn" lay-filter="demoTipsformSub" lay-submit>
                                <i class="layui-icon">&#xe605;</i>提交
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=315"></script>
<script>
    layui.use(['layer', 'admin', 'form'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var admin = layui.admin;
        var form = layui.form;

        form.on('submit(demoTipsformSub)', function (data) {
            layer.closeAll('tips');
            layer.msg('表单验证通过', {icon: 1});
            return false;
        });

    });
</script>
</body>
</html>