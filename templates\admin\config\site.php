<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>网站配置 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .layui-card-body {
            padding: 15px;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .config-group-title {
            border-left: 4px solid #5FB878;
            padding-left: 10px;
            font-size: 16px;
            margin: 15px 0;
        }
        .config-group-title:first-child {
            margin-top: 0;
        }
        .layui-upload-img {
            width: 120px;
            height: 120px;
            margin-top: 10px;
        }
        .layui-form-mid {
            color: #999;
        }
        .layui-form-text .layui-textarea {
            min-height: 100px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">网站配置</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="configForm">
                <!-- 基础配置 -->
                <div class="config-group-title">基础配置</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">网站名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="site_name" placeholder="请输入网站名称" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">网站标题</label>
                    <div class="layui-input-block">
                        <input type="text" name="site_title" placeholder="请输入网站标题" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">网站关键词</label>
                    <div class="layui-input-block">
                        <input type="text" name="site_keywords" placeholder="请输入网站关键词，多个关键词用英文逗号分隔" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">网站描述</label>
                    <div class="layui-input-block">
                        <textarea name="site_description" placeholder="请输入网站描述" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">网站LOGO</label>
                    <div class="layui-input-block">
                        <div class="layui-upload">
                            <button type="button" class="layui-btn" id="logoUpload">上传图片</button>
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" id="logoPreview">
                                <p id="logoText"></p>
                            </div>
                            <input type="hidden" name="site_logo" id="site_logo">
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">网站图标</label>
                    <div class="layui-input-block">
                        <div class="layui-upload">
                            <button type="button" class="layui-btn" id="faviconUpload">上传图标</button>
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" id="faviconPreview" style="width: 32px; height: 32px;">
                                <p id="faviconText"></p>
                            </div>
                            <input type="hidden" name="site_favicon" id="site_favicon">
                            <div class="layui-form-mid">建议上传ICO、PNG格式，大小为32x32像素的图片</div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备案信息</label>
                    <div class="layui-input-block">
                        <input type="text" name="site_icp" placeholder="请输入备案信息，例如：京ICP备12345678号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">版权信息</label>
                    <div class="layui-input-block">
                        <input type="text" name="site_copyright" placeholder="请输入版权信息，例如：© 2023 API系统 All Rights Reserved." autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <!-- 联系方式 -->
                <div class="config-group-title">联系方式</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">联系电话</label>
                    <div class="layui-input-block">
                        <input type="text" name="contact_phone" placeholder="请输入联系电话" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">联系邮箱</label>
                    <div class="layui-input-block">
                        <input type="text" name="contact_email" placeholder="请输入联系邮箱" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">联系地址</label>
                    <div class="layui-input-block">
                        <input type="text" name="contact_address" placeholder="请输入联系地址" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">QQ</label>
                    <div class="layui-input-block">
                        <input type="text" name="contact_qq" placeholder="请输入QQ号码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">微信</label>
                    <div class="layui-input-block">
                        <input type="text" name="contact_wechat" placeholder="请输入微信号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <!-- 邮件配置 -->
                <div class="config-group-title">邮件配置</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">SMTP服务器</label>
                    <div class="layui-input-block">
                        <input type="text" name="mail_host" placeholder="请输入SMTP服务器地址，例如：smtp.qq.com" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">SMTP端口</label>
                    <div class="layui-input-block">
                        <input type="text" name="mail_port" placeholder="请输入SMTP端口，例如：465" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">邮箱账号</label>
                    <div class="layui-input-block">
                        <input type="text" name="mail_username" placeholder="请输入邮箱账号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">邮箱密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="mail_password" placeholder="请输入邮箱密码或授权码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">发件人名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="mail_from_name" placeholder="请输入发件人名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">加密方式</label>
                    <div class="layui-input-block">
                        <select name="mail_encryption">
                            <option value="">无</option>
                            <option value="ssl">SSL</option>
                            <option value="tls">TLS</option>
                        </select>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">测试邮件</label>
                    <div class="layui-input-inline">
                        <input type="text" id="testEmail" placeholder="请输入测试邮箱" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-input-inline" style="width: auto;">
                        <button type="button" class="layui-btn layui-btn-normal" id="testMailBtn">发送测试邮件</button>
                    </div>
                </div>
                
                <!-- 安全配置 -->
                <div class="config-group-title">安全配置</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">API密钥长度</label>
                    <div class="layui-input-block">
                        <input type="number" name="api_key_length" placeholder="请输入API密钥长度，建议32位以上" value="32" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">API密钥前缀</label>
                    <div class="layui-input-block">
                        <input type="text" name="api_key_prefix" placeholder="请输入API密钥前缀，例如：API_" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">IP黑名单</label>
                    <div class="layui-input-block">
                        <textarea name="ip_blacklist" placeholder="请输入IP黑名单，每行一个IP地址" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">IP白名单</label>
                    <div class="layui-input-block">
                        <textarea name="ip_whitelist" placeholder="请输入IP白名单，每行一个IP地址" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">QPS限制</label>
                    <div class="layui-input-block">
                        <input type="number" name="qps_limit" placeholder="请输入每秒请求次数限制，0表示不限制" value="0" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <!-- 上传配置 -->
                <div class="config-group-title">上传配置</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">上传大小限制</label>
                    <div class="layui-input-block">
                        <input type="number" name="upload_max_size" placeholder="请输入上传大小限制，单位MB" value="10" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">允许的文件类型</label>
                    <div class="layui-input-block">
                        <input type="text" name="upload_allowed_types" placeholder="请输入允许上传的文件类型，多个用英文逗号分隔，例如：jpg,png,gif" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">存储方式</label>
                    <div class="layui-input-block">
                        <select name="upload_storage" lay-filter="uploadStorage">
                            <option value="local">本地存储</option>
                            <option value="oss">阿里云OSS</option>
                            <option value="cos">腾讯云COS</option>
                            <option value="qiniu">七牛云</option>
                        </select>
                    </div>
                </div>
                
                <!-- 本地存储配置 -->
                <div id="localConfig">
                    <div class="layui-form-item">
                        <label class="layui-form-label">上传目录</label>
                        <div class="layui-input-block">
                            <input type="text" name="upload_local_path" placeholder="请输入上传目录，相对于网站根目录" value="uploads" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">访问URL</label>
                        <div class="layui-input-block">
                            <input type="text" name="upload_local_url" placeholder="请输入访问URL，例如：http://example.com/uploads" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                
                <!-- 阿里云OSS配置 -->
                <div id="ossConfig" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">AccessKey ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="oss_access_key_id" placeholder="请输入阿里云AccessKey ID" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">AccessKey Secret</label>
                        <div class="layui-input-block">
                            <input type="password" name="oss_access_key_secret" placeholder="请输入阿里云AccessKey Secret" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">Endpoint</label>
                        <div class="layui-input-block">
                            <input type="text" name="oss_endpoint" placeholder="请输入阿里云OSS Endpoint" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">Bucket</label>
                        <div class="layui-input-block">
                            <input type="text" name="oss_bucket" placeholder="请输入阿里云OSS Bucket" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">目录前缀</label>
                        <div class="layui-input-block">
                            <input type="text" name="oss_prefix" placeholder="请输入目录前缀，例如：uploads/" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                
                <!-- 腾讯云COS配置 -->
                <div id="cosConfig" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">SecretId</label>
                        <div class="layui-input-block">
                            <input type="text" name="cos_secret_id" placeholder="请输入腾讯云SecretId" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">SecretKey</label>
                        <div class="layui-input-block">
                            <input type="password" name="cos_secret_key" placeholder="请输入腾讯云SecretKey" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">地域</label>
                        <div class="layui-input-block">
                            <input type="text" name="cos_region" placeholder="请输入腾讯云COS地域，例如：ap-guangzhou" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">Bucket</label>
                        <div class="layui-input-block">
                            <input type="text" name="cos_bucket" placeholder="请输入腾讯云COS Bucket" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">目录前缀</label>
                        <div class="layui-input-block">
                            <input type="text" name="cos_prefix" placeholder="请输入目录前缀，例如：uploads/" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                
                <!-- 七牛云配置 -->
                <div id="qiniuConfig" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">AccessKey</label>
                        <div class="layui-input-block">
                            <input type="text" name="qiniu_access_key" placeholder="请输入七牛云AccessKey" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">SecretKey</label>
                        <div class="layui-input-block">
                            <input type="password" name="qiniu_secret_key" placeholder="请输入七牛云SecretKey" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">Bucket</label>
                        <div class="layui-input-block">
                            <input type="text" name="qiniu_bucket" placeholder="请输入七牛云Bucket" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">域名</label>
                        <div class="layui-input-block">
                            <input type="text" name="qiniu_domain" placeholder="请输入七牛云域名，例如：http://example.com" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">目录前缀</label>
                        <div class="layui-input-block">
                            <input type="text" name="qiniu_prefix" placeholder="请输入目录前缀，例如：uploads/" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                
                <!-- 自定义样式 -->
                <div class="config-group-title">自定义样式</div>
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">自定义CSS</label>
                    <div class="layui-input-block">
                        <textarea name="custom_css" placeholder="请输入自定义CSS代码" class="layui-textarea" style="min-height: 150px;"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">自定义JS</label>
                    <div class="layui-input-block">
                        <textarea name="custom_js" placeholder="请输入自定义JavaScript代码" class="layui-textarea" style="min-height: 150px;"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">统计代码</label>
                    <div class="layui-input-block">
                        <textarea name="statistics_code" placeholder="请输入第三方统计代码" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="configSubmit">保存配置</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'upload', 'admin'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var upload = layui.upload;
    var admin = layui.admin;
    
    // 获取配置信息
    var loadIndex = layer.load(2);
    $.get('/admin/config/get', function(res) {
        layer.close(loadIndex);
        if (res.code === 0) {
            // 回显表单数据
            form.val('configForm', res.data);
            
            // 回显图片
            if (res.data.site_logo) {
                $('#logoPreview').attr('src', res.data.site_logo);
            }
            if (res.data.site_favicon) {
                $('#faviconPreview').attr('src', res.data.site_favicon);
            }
            
            // 显示对应的存储配置
            showStorageConfig(res.data.upload_storage);
        } else {
            layer.msg(res.msg, {icon: 2});
        }
    }, 'json');
    
    // 监听存储方式切换
    form.on('select(uploadStorage)', function(data) {
        showStorageConfig(data.value);
    });
    
    // 显示对应的存储配置
    function showStorageConfig(storage) {
        $('#localConfig, #ossConfig, #cosConfig, #qiniuConfig').hide();
        switch (storage) {
            case 'local':
                $('#localConfig').show();
                break;
            case 'oss':
                $('#ossConfig').show();
                break;
            case 'cos':
                $('#cosConfig').show();
                break;
            case 'qiniu':
                $('#qiniuConfig').show();
                break;
        }
    }
    
    // 上传LOGO
    upload.render({
        elem: '#logoUpload',
        url: '/admin/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        done: function(res) {
            if (res.code === 0) {
                $('#logoPreview').attr('src', res.data.url);
                $('#site_logo').val(res.data.url);
                $('#logoText').html('');
            } else {
                $('#logoText').html('上传失败：' + res.msg);
            }
        },
        error: function() {
            $('#logoText').html('上传失败，请重试');
        }
    });
    
    // 上传网站图标
    upload.render({
        elem: '#faviconUpload',
        url: '/admin/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        done: function(res) {
            if (res.code === 0) {
                $('#faviconPreview').attr('src', res.data.url);
                $('#site_favicon').val(res.data.url);
                $('#faviconText').html('');
            } else {
                $('#faviconText').html('上传失败：' + res.msg);
            }
        },
        error: function() {
            $('#faviconText').html('上传失败，请重试');
        }
    });
    
    // 发送测试邮件
    $('#testMailBtn').click(function() {
        var email = $('#testEmail').val();
        if (!email) {
            layer.msg('请输入测试邮箱', {icon: 2});
            return;
        }
        
        var loadIndex = layer.load(2);
        $.post('/admin/config/test_mail', {
            email: email,
            mail_host: $('input[name="mail_host"]').val(),
            mail_port: $('input[name="mail_port"]').val(),
            mail_username: $('input[name="mail_username"]').val(),
            mail_password: $('input[name="mail_password"]').val(),
            mail_from_name: $('input[name="mail_from_name"]').val(),
            mail_encryption: $('select[name="mail_encryption"]').val()
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    });
    
    // 表单提交
    form.on('submit(configSubmit)', function(data) {
        var loadIndex = layer.load(2);
        $.post('/admin/config/save', data.field, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
});
</script>
</body>
</html>