<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>IP黑白名单 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- IP黑名单 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">IP黑名单</div>
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <div class="layui-input-inline">
                                    <input id="blacklistKeyword" class="layui-input" type="text" placeholder="IP地址"/>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button id="blacklistSearchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                <button id="blacklistAddBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                            </div>
                        </div>
                    </div>

                    <table class="layui-table" id="blacklistTable" lay-filter="blacklistTable"></table>
                </div>
            </div>
        </div>
        
        <!-- IP白名单 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">IP白名单</div>
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <div class="layui-input-inline">
                                    <input id="whitelistKeyword" class="layui-input" type="text" placeholder="IP地址"/>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button id="whitelistSearchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                <button id="whitelistAddBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                            </div>
                        </div>
                    </div>

                    <table class="layui-table" id="whitelistTable" lay-filter="whitelistTable"></table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- IP控制策略 -->
    <div class="layui-card">
        <div class="layui-card-header">IP控制策略</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="ipPolicyForm">
                <div class="layui-form-item">
                    <label class="layui-form-label">IP控制模式</label>
                    <div class="layui-input-block">
                        <input type="radio" name="ip_control_mode" value="blacklist" title="黑名单模式（禁止黑名单IP访问）" checked>
                        <input type="radio" name="ip_control_mode" value="whitelist" title="白名单模式（仅允许白名单IP访问）">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">API接口控制</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="api_ip_control" lay-skin="switch" lay-text="开启|关闭">
                        <div class="layui-form-mid layui-word-aux">开启后，将对API接口应用IP控制策略</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">后台管理控制</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="admin_ip_control" lay-skin="switch" lay-text="开启|关闭">
                        <div class="layui-form-mid layui-word-aux">开启后，将对后台管理页面应用IP控制策略</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">异常IP处理</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="auto_block" lay-skin="switch" lay-text="开启|关闭">
                        <div class="layui-form-mid layui-word-aux">开启后，系统将自动检测并拦截异常IP（如：短时间内多次访问失败）</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">自动拦截阈值</label>
                    <div class="layui-input-inline">
                        <input type="number" name="block_threshold" placeholder="请输入阈值" value="10" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">单位时间内失败次数超过阈值将被自动拦截</div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">检测时间窗口</label>
                    <div class="layui-input-inline">
                        <input type="number" name="time_window" placeholder="请输入时间窗口" value="60" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">单位：秒，系统检测的时间范围</div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">自动解封时间</label>
                    <div class="layui-input-inline">
                        <input type="number" name="auto_unblock_time" placeholder="请输入自动解封时间" value="3600" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">单位：秒，0表示永久拦截</div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="ipPolicySubmit">保存策略</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 添加IP弹窗 -->
<script type="text/html" id="ipAddDialog">
    <form id="ipAddForm" lay-filter="ipAddForm" class="layui-form model-form">
        <input name="type" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label">IP地址</label>
            <div class="layui-input-block">
                <input name="ip" placeholder="请输入IP地址，支持IP段如：***********/24" type="text" class="layui-input" maxlength="100" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <input name="remark" placeholder="请输入备注信息" type="text" class="layui-input" maxlength="200"/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="ipAddSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;

    // 渲染黑名单表格
    var blacklistTable = table.render({
        elem: '#blacklistTable',
        url: '/admin/security/ip_blacklist',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'ip', title: 'IP地址'},
            {field: 'remark', title: '备注'},
            {field: 'create_time', title: '添加时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }},
            {title: '操作', toolbar: '#tableBar', width: 80}
        ]]
    });

    // 渲染白名单表格
    var whitelistTable = table.render({
        elem: '#whitelistTable',
        url: '/admin/security/ip_whitelist',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'ip', title: 'IP地址'},
            {field: 'remark', title: '备注'},
            {field: 'create_time', title: '添加时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }},
            {title: '操作', toolbar: '#tableBar', width: 80}
        ]]
    });

    // 黑名单搜索按钮点击事件
    $('#blacklistSearchBtn').click(function () {
        blacklistTable.reload({
            where: {
                keyword: $('#blacklistKeyword').val()
            },
            page: {curr: 1}
        });
    });

    // 白名单搜索按钮点击事件
    $('#whitelistSearchBtn').click(function () {
        whitelistTable.reload({
            where: {
                keyword: $('#whitelistKeyword').val()
            },
            page: {curr: 1}
        });
    });

    // 黑名单添加按钮点击事件
    $('#blacklistAddBtn').click(function () {
        showAddDialog('blacklist');
    });

    // 白名单添加按钮点击事件
    $('#whitelistAddBtn').click(function () {
        showAddDialog('whitelist');
    });

    // 工具条点击事件
    table.on('tool(blacklistTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该IP吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/security/ip_delete', {
                    id: data.id,
                    type: 'blacklist'
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        blacklistTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });

    // 工具条点击事件
    table.on('tool(whitelistTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该IP吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/security/ip_delete', {
                    id: data.id,
                    type: 'whitelist'
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        whitelistTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });

    // 显示添加弹窗
    function showAddDialog(type) {
        admin.open({
            type: 1,
            title: type === 'blacklist' ? '添加黑名单IP' : '添加白名单IP',
            content: $('#ipAddDialog').html(),
            success: function (layero, dIndex) {
                // 设置类型
                form.val('ipAddForm', {
                    type: type
                });
                
                // 表单提交事件
                form.on('submit(ipAddSubmit)', function (data) {
                    layer.load(2);
                    $.post('/admin/security/ip_add', data.field, function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            if (type === 'blacklist') {
                                blacklistTable.reload();
                            } else {
                                whitelistTable.reload();
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    return false;
                });
            }
        });
    }
    
    // 获取IP控制策略
    $.get('/admin/security/ip_policy', function(res) {
        if (res.code === 0) {
            form.val('ipPolicyForm', res.data);
            form.render();
        }
    }, 'json');
    
    // 保存IP控制策略
    form.on('submit(ipPolicySubmit)', function(data) {
        layer.load(2);
        $.post('/admin/security/ip_policy_save', data.field, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
});
</script>
</body>
</html>