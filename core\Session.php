<?php
/**
 * 会话管理类
 */
class Session
{
    private static $instance = null;
    
    /**
     * 构造函数
     */
    private function __construct()
    {
        if (session_status() == PHP_SESSION_NONE) {
            // 设置安全的cookie参数
            session_set_cookie_params([
                'lifetime' => 0,
                'path' => '/',
                'domain' => '',
                'secure' => isset($_SERVER['HTTPS']),
                'httponly' => true,
                'samesite' => 'Lax'
            ]);
            
            session_start();
        }
    }
    
    /**
     * 获取实例（单例模式）
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 设置会话值
     */
    public function set($key, $value)
    {
        $_SESSION[$key] = $value;
    }
    
    /**
     * 获取会话值
     */
    public function get($key, $default = null)
    {
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }
    
    /**
     * 检查会话键是否存在
     */
    public function has($key)
    {
        return isset($_SESSION[$key]);
    }
    
    /**
     * 删除会话值
     */
    public function delete($key)
    {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
            return true;
        }
        return false;
    }
    
    /**
     * 清空所有会话
     */
    public function clear()
    {
        session_unset();
        return session_destroy();
    }
    
    /**
     * 设置闪存消息（一次性消息）
     */
    public function setFlash($key, $value)
    {
        $_SESSION['_flash'][$key] = $value;
    }
    
    /**
     * 获取闪存消息
     */
    public function getFlash($key, $default = null)
    {
        if (isset($_SESSION['_flash'][$key])) {
            $value = $_SESSION['_flash'][$key];
            unset($_SESSION['_flash'][$key]);
            return $value;
        }
        return $default;
    }
    
    /**
     * 检查闪存消息是否存在
     */
    public function hasFlash($key)
    {
        return isset($_SESSION['_flash'][$key]);
    }
    
    /**
     * 获取所有闪存消息
     */
    public function getAllFlash()
    {
        $flash = isset($_SESSION['_flash']) ? $_SESSION['_flash'] : [];
        $_SESSION['_flash'] = [];
        return $flash;
    }
    
    /**
     * 重新生成会话ID
     */
    public function regenerate()
    {
        return session_regenerate_id(true);
    }
    
    /**
     * 获取会话ID
     */
    public function getId()
    {
        return session_id();
    }
    
    /**
     * 设置CSRF令牌
     */
    public function setCsrfToken()
    {
        if (!$this->has('csrf_token')) {
            $this->set('csrf_token', bin2hex(random_bytes(32)));
        }
        return $this->get('csrf_token');
    }
    
    /**
     * 获取CSRF令牌
     */
    public function getCsrfToken()
    {
        return $this->setCsrfToken();
    }
    
    /**
     * 验证CSRF令牌
     */
    public function validateCsrfToken($token)
    {
        return $token === $this->getCsrfToken();
    }
}
