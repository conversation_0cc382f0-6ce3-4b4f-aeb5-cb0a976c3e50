<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>表格扩展</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        #xTable2 + .layui-table-view .layui-table-click, #xTable2 + .layui-table-view .layui-table-hover, #xTable2 + .layui-table-view .layui-table tbody tr:hover {
            background: transparent;
        }
    </style>
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-tab layui-tab-brief" lay-filter="tableXTab">
            <ul class="layui-tab-title">
                <li class="layui-this">前端分页 | 搜索 | 排序</li>
                <li>合并单元格</li>
                <li>后端排序</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item text-right">
                            <div class="layui-inline pull-left">
                                <button class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                                <button class="layui-btn layui-btn-danger icon-btn"><i
                                        class="layui-icon">&#xe640;</i>删除
                                </button>
                            </div>
                            <div class="layui-inline">
                                <input tb-search="xTable1" class="layui-input icon-search" type="text"/>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-btn-group">
                                    <button tb-export="xTable1" class="layui-btn layui-btn-primary icon-btn">
                                        <i class="layui-icon">&#xe67d;</i>
                                    </button>
                                    <button tb-refresh="xTable1" class="layui-btn layui-btn-primary icon-btn">
                                        <i class="layui-icon">&#xe9aa;</i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <table id="xTable1" lay-filter="xTable1"></table>
                </div>
                <div class="layui-tab-item ">
                    <table id="xTable2" lay-filter="xTable2"></table>
                </div>
                <div class="layui-tab-item">
                    点击排序会自动传递“sort”和“order”字段给后端接口
                    <table id="xTable3" lay-filter="xTable3"></table>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-body text-danger">
            表格扩展tableX模块还封装了导出xlsx格式的excel数据、绑定行鼠标右键、post方式下载文件(支持传参数，可用于请求后端导出excel下载)等实用功能。
        </div>
    </div>
</div>

<!-- 表格状态列 -->
<script type="text/html" id="tableState">
    <input type="checkbox" lay-filter="ckState" value="{{d.userId}}" lay-skin="switch"
           lay-text="正常|锁定" {{d.state==0?'checked':''}}/>
    <!-- export-show用于前端搜索和导出数据使用 -->
    <div class="export-show">{{d.state==0?'正常':'锁定'}}</div>
</script>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'element', 'util', 'table', 'tableX'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var element = layui.element;
        var util = layui.util;
        var table = layui.table;
        var tableX = layui.tableX;

        // 前端分页
        tableX.renderFront({
            elem: '#xTable1',
            url: '../../../json/user-all.json',
            page: {groups: 6},
            cellMinWidth: 100,
            title: '用户表',
            cols: [[
                {type: 'checkbox'},
                {field: 'nickName', title: '用户名', sort: true},
                {field: 'sex', title: '性别', sort: true},
                {field: 'phone', title: '手机号', sort: true},
                {
                    templet: function (d) {
                        var str = '';
                        for (var i = 0; i < d.roles.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.roles[i].roleName + '</span>&nbsp;');
                            if (i != d.roles.length - 1) {
                                str += '<span class="layui-badge-rim layui-hide">，</span>';
                            }
                        }
                        return str;
                    }, title: '角色', sort: true
                },
                {field: 'state', templet: '#tableState', title: '状态', sort: true},
                {
                    field: 'createTime', templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, title: '创建时间', sort: true
                }
            ]]
        });

        // 合并单元格
        table.render({
            elem: '#xTable2',
            url: '../../../json/tablex-tb2.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'parentName', title: '模块名称', sort: true},
                {field: 'authorityName', title: '菜单名称', sort: true},
                {field: 'authority', title: '权限标识', sort: true},
                {
                    templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, title: '创建时间', sort: true
                }
            ]],
            done: function () {
                tableX.merges('xTable2', [1]);
            }
        });

        // 排序自动传递sort和order
        var insTb3 = tableX.render({
            elem: '#xTable3',
            url: '../../../json/user.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'nickName', title: '用户名', sort: true},
                {field: 'sex', title: '性别', sort: true},
                {field: 'phone', title: '手机号', sort: true},
                {
                    field: 'roleId', templet: function (d) {
                        var str = '';
                        for (var i = 0; i < d.roles.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.roles[i].roleName + '</span>&nbsp;');
                            if (i != d.roles.length - 1) {
                                str += '<span class="layui-badge-rim layui-hide">,</span>';
                            }
                        }
                        return str;
                    }, title: '角色', sort: true
                },
                {field: 'state', templet: '#tableState', title: '状态', sort: true},
                {
                    field: 'createTime', templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, title: '创建时间', sort: true
                }
            ]],
            done: function () {
                // 绑定鼠标右键
                tableX.bindCtxMenu('xTable3', [{
                    icon: 'layui-icon layui-icon-edit',
                    name: '修改用户',
                    click: function (d) {
                        layer.msg('点击了修改，userId：' + d.userId);
                    }
                }, {
                    icon: 'layui-icon layui-icon-unlink',
                    name: '冻结用户',
                    click: function (d) {
                        layer.msg('点击了冻结，userId：' + d.userId);
                    }
                }, {
                    icon: 'layui-icon layui-icon-close text-danger',
                    name: '<span class="text-danger">删除用户</span>',
                    click: function (d) {
                        layer.msg('点击了删除，userId：' + d.userId);
                    }
                }]);
            }
        });

        // 导出数据包含templet
        $('#btnExport3').click(function () {
            tableX.exportData({
                cols: insTb3.config.cols,
                data: table.cache.xTable3,
                fileName: '用户表'
            });
        });

        // 切换Tab重置表格尺寸
        element.on('tab(tableXTab)', function (data) {
            var tableIds = ['xTable1', 'xTable2', 'xTable3'];
            table.resize(tableIds[data.index]);
        });

        setTimeout(function () {
            table.resize('xTable1');
        }, 200);

    });
</script>
</body>
</html>