<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API分类管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">API分类管理</h2>
            <div class="layui-btn-group">
                <button class="layui-btn" id="btnAdd"><i class="layui-icon layui-icon-add-1"></i>添加分类</button>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 数据表格 -->
            <table id="categoryTable" lay-filter="categoryTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="statusSwitch" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status==1?'checked':''}} />
</script>

<!-- 添加/编辑分类弹窗 -->
<script type="text/html" id="categoryFormTpl">
    <form class="layui-form" lay-filter="categoryForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">分类名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入分类名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">分类描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入分类描述" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">图标</label>
            <div class="layui-input-block">
                <input type="text" name="icon" placeholder="请输入图标类名" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort_order" placeholder="请输入排序" class="layui-input" value="0">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-filter="categorySubmit" lay-submit>保存</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../../Easyweb/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script>
layui.use(['table', 'form', 'layer', 'laytpl'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var $ = layui.jquery;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#categoryTable',
        url: '../controllers/ApiController.php?action=getCategories',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'name', title: '分类名称', sort: true},
            {field: 'description', title: '分类描述'},
            {field: 'icon', title: '图标'},
            {field: 'sort_order', title: '排序', width: 100, sort: true},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100, sort: true},
            {field: 'created_at', title: '创建时间', sort: true},
            {title: '操作', toolbar: '#tableBar', width: 150, fixed: 'right'}
        ]]
    });
    
    // 表格工具条点击事件
    table.on('tool(categoryTable)', function(obj){
        var data = obj.data;
        var layEvent = obj.event;
        
        if(layEvent === 'edit'){ // 修改
            showEditForm(data);
        } else if(layEvent === 'del'){ // 删除
            layer.confirm('确定要删除该分类吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(i){
                layer.close(i);
                
                $.get('../controllers/ApiController.php?action=deleteCategory&id=' + data.id, function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 表单提交事件
    form.on('submit(categorySubmit)', function(data){
        var loadIndex = layer.load(2);
        
        $.ajax({
            url: '../controllers/ApiController.php?action=' + (data.field.id ? 'editCategory' : 'addCategory'),
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function(res){
                layer.close(loadIndex);
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                    layer.closeAll('page');
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(){
                layer.close(loadIndex);
                layer.msg('服务器错误', {icon: 2});
            }
        });
        
        return false;
    });
    
    // 添加按钮点击事件
    $('#btnAdd').click(function(){
        showEditForm();
    });
    
    // 状态开关点击事件
    form.on('switch(statusSwitch)', function(obj){
        var loadIndex = layer.load(2);
        
        $.get('../controllers/ApiController.php?action=changeCategoryStatus&id=' + obj.value + '&status=' + (obj.elem.checked ? 1 : 0), function(res){
            layer.close(loadIndex);
            if(res.code !== 0){
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });
    
    // 显示编辑表单
    function showEditForm(data){
        var title = data ? '编辑分类' : '添加分类';
        
        layer.open({
            type: 1,
            title: title,
            area: ['500px', '450px'],
            content: laytpl($('#categoryFormTpl').html()).render({}),
            success: function(layero, index){
                form.render();
                
                // 如果是编辑，加载数据
                if(data){
                    form.val('categoryForm', data);
                }
            }
        });
    }
});
</script>
</body>
</html>
