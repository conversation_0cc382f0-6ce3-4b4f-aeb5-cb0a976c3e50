/** 透明侧边栏导航 */
.layui-layout-admin .layui-side .layui-nav {
    background-color: transparent;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item > a:hover {
    background: rgba(255, 255, 255, .03);
}

/** logo部分样式 */
.layui-layout-admin .layui-header .layui-logo {
    background-color: #3C8DBC;
    color: #fff;
    box-shadow: none !important;
}

/** header样式 */
.layui-layout-admin .layui-header {
    background-color: #3C8DBC;
}

.layui-layout-admin .layui-header a {
    color: #fff;
}

.layui-layout-admin .layui-header a:hover {
    color: #fff;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-more {
    border-color: #eee transparent transparent;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-mored {
    border-color: transparent transparent #eee;
}

/** header线条 */
.layui-layout-admin .layui-header .layui-nav .layui-this:after, .layui-layout-admin .layui-header .layui-nav-bar {
    background-color: #fff;
}

/** 侧边栏样式 */
.layui-layout-admin .layui-side {
    background-color: #222D32;
}

/** 侧边栏文字颜色 */
.layui-side .layui-nav .layui-nav-item a {
    color: #b8c7ce;
}

.layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a, .layui-nav-tree .layui-this > a:hover {
    background-color: #3C8DBC;
}

.layui-nav-tree .layui-nav-bar, .layui-nav-tree > .layui-nav-item > a:before {
    background-color: #3C8DBC;
}

.layui-side .layui-nav-itemed > a, .layui-nav-tree .layui-nav-title a, .layui-nav-tree .layui-nav-title a:hover, .layui-side .layui-nav-item.layui-this > a {
    color: #fff !important;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-itemed > .layui-nav-child {
    background-color: rgba(0, 0, 0, .2) !important;
}

/** PC端折叠鼠标经过样式 */
.layui-layout-admin.admin-nav-mini .layui-side .layui-nav .admin-nav-hover > .layui-nav-child:before {
    background: #222D32 !important;
}

/** tab下划线 */
.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li.layui-this:after {
    background-color: #3C8DBC;
    top: 38px;
}

/** 主体标题 */
.layui-body-header-title {
    border-left-color: #3C8DBC;
}

/** 主题切换 */
.btnTheme:hover, .btnTheme.active {
    border-color: #3C8DBC;
}

/** admin风格弹窗样式 */
.layui-layer.layui-layer-admin .layui-layer-title {
    background-color: #3C8DBC;
    color: #ffffff;
}

/** 按钮颜色 */
.layui-layer.layui-layer-admin .layui-layer-setwin a {
    color: #ffffff;
}

/* 最小化按钮 */
.layui-layer.layui-layer-admin .layui-layer-setwin .layui-layer-min cite {
    background-color: #dddddd;
}

/** 弹窗按钮 */
.layui-layer.layui-layer-admin .layui-layer-btn .layui-layer-btn0 {
    border-color: #3C8DBC;
    background-color: #3C8DBC;
}

/* 圆形按钮 */
.btn-circle {
    background: #3C8DBC;
}

/** 主题颜色 */

/** 按钮 */
.layui-btn:not(.layui-btn-primary):not(.layui-btn-normal):not(.layui-btn-warm):not(.layui-btn-danger):not(.layui-btn-disabled) {
    background-color: #3C8DBC;
}

.layui-btn.layui-btn-primary:hover {
    border-color: #3C8DBC;
}

/** 开关 */
.layui-form-onswitch {
    border-color: #3C8DBC;
    background-color: #3C8DBC;
}

/** 分页插件 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #3C8DBC;
}

.layui-table-page .layui-laypage input:focus {
    border-color: #3C8DBC !important;
}

.layui-table-view select:focus {
    border-color: #3C8DBC !important;
}

.layui-table-page .layui-laypage a:hover {
    color: #3C8DBC;
}

/** 单选按钮 */
.layui-form-radio > i:hover, .layui-form-radioed > i {
    color: #3C8DBC;
}

/** 下拉条目选中 */
.layui-form-select dl dd.layui-this {
    background-color: #3C8DBC;
}

/** 选项卡 */
.layui-tab-brief > .layui-tab-title .layui-this {
    color: #3C8DBC;
}

.layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-color: #3C8DBC !important;
}

/** 面包屑导航 */
.layui-breadcrumb a:hover {
    color: #3C8DBC !important;
}

/** 日期选择器按钮 */
.laydate-footer-btns span:hover {
    color: #3C8DBC !important;
}

/** 时间轴 */
.layui-timeline-axis {
    color: #3C8DBC;
}

/** 复选框 */
.layui-form-checked[lay-skin=primary] i {
    border-color: #3C8DBC !important;
    background-color: #3C8DBC;
}

.layui-form-checkbox[lay-skin=primary] i:hover {
    border-color: #3C8DBC;
}

.layui-form-checkbox[lay-skin=primary]:hover i {
    border-color: #3C8DBC;
}

/** 加载动画颜色 */
.ball-loader > span, .signal-loader > span {
    background-color: #3C8DBC;
}
