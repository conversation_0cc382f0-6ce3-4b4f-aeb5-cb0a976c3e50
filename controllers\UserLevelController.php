<?php
/**
 * 用户会员等级控制器
 */
class UserLevelController extends BaseController
{
    /**
     * 会员等级列表页面
     */
    public function index()
    {
        // 检查用户登录状态
        $this->checkLogin();
        
        // 获取当前用户信息
        $userId = $this->getCurrentUserId();
        $user = $this->db->query("SELECT * FROM users WHERE id = ?", [$userId], true);
        
        // 获取用户当前等级
        $userLevel = $this->db->query(
            "SELECT ulr.*, ul.name, ul.description, ul.icon, ul.price, ul.discount_rate, ul.daily_request_limit, ul.features 
            FROM user_level_relations ulr 
            LEFT JOIN user_levels ul ON ulr.level_id = ul.id 
            WHERE ulr.user_id = ?", 
            [$userId], 
            true
        );
        
        // 获取所有可用的会员等级
        $levels = $this->db->query(
            "SELECT * FROM user_levels WHERE status = 1 ORDER BY sort_order ASC, id ASC"
        );
        
        // 处理特权功能
        foreach ($levels as &$level) {
            if (!empty($level['features'])) {
                $level['features'] = json_decode($level['features'], true);
            } else {
                $level['features'] = [];
            }
        }
        
        // 获取用户余额
        $balance = $this->db->query("SELECT balance FROM user_balances WHERE user_id = ?", [$userId], true);
        $userBalance = $balance ? $balance['balance'] : 0;
        
        // 渲染页面
        $this->render('user/level', [
            'user' => $user,
            'userLevel' => $userLevel,
            'levels' => $levels,
            'userBalance' => $userBalance
        ]);
    }
    
    /**
     * 会员等级详情
     */
    public function detail()
    {
        // 获取参数
        $id = intval($this->getParam('id', 0));
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        // 获取会员等级详情
        $level = $this->db->query("SELECT * FROM user_levels WHERE id = ? AND status = 1", [$id], true);
        if (empty($level)) {
            $this->error('会员等级不存在或已禁用');
        }
        
        // 处理特权功能
        if (!empty($level['features'])) {
            $level['features'] = json_decode($level['features'], true);
        } else {
            $level['features'] = [];
        }
        
        $this->success($level);
    }
    
    /**
     * 创建会员等级升级订单
     */
    public function createUpgradeOrder()
    {
        // 检查用户登录状态
        $this->checkLogin();
        
        // 获取参数
        $levelId = intval($this->getParam('level_id', 0));
        $duration = intval($this->getParam('duration', 1)); // 默认1个月
        $payType = $this->getParam('pay_type', 'balance'); // 支付方式：balance余额支付，alipay支付宝，wechat微信
        
        // 验证参数
        if ($levelId <= 0) {
            $this->error('请选择要升级的会员等级');
        }
        
        if ($duration <= 0 || $duration > 36) {
            $this->error('购买时长必须在1-36个月之间');
        }
        
        // 获取当前用户ID
        $userId = $this->getCurrentUserId();
        
        // 获取目标等级信息
        $targetLevel = $this->db->query("SELECT * FROM user_levels WHERE id = ? AND status = 1", [$levelId], true);
        if (empty($targetLevel)) {
            $this->error('会员等级不存在或已禁用');
        }
        
        // 获取用户当前等级
        $userLevel = $this->db->query(
            "SELECT ulr.*, ul.id as level_id, ul.name, ul.price 
            FROM user_level_relations ulr 
            LEFT JOIN user_levels ul ON ulr.level_id = ul.id 
            WHERE ulr.user_id = ?", 
            [$userId], 
            true
        );
        
        // 如果用户已经是该等级，且是永久会员，则不能再次购买
        if ($userLevel && $userLevel['level_id'] == $levelId && $userLevel['expire_time'] === null) {
            $this->error('您已经是该等级的永久会员，无需再次购买');
        }
        
        // 计算价格
        $price = $targetLevel['price'] * $duration;
        
        // 如果价格为0，则直接升级
        if ($price <= 0) {
            $this->upgradeUserLevel($userId, $userLevel ? $userLevel['level_id'] : 0, $levelId, $duration);
            $this->success('升级成功');
        }
        
        // 生成订单号
        $orderNo = 'UL' . date('YmdHis') . rand(1000, 9999);
        
        // 当前时间
        $now = time();
        
        // 开启事务
        $this->db->beginTransaction();
        
        try {
            // 创建订单
            $this->db->execute(
                "INSERT INTO orders (order_no, user_id, type, title, amount, pay_type, status, create_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $orderNo, 
                    $userId, 
                    'level_upgrade', 
                    '会员等级升级：' . $targetLevel['name'] . ' x ' . $duration . '个月', 
                    $price, 
                    $payType, 
                    0, // 0待支付
                    $now
                ]
            );
            
            $orderId = $this->db->lastInsertId();
            
            // 创建会员升级订单
            $this->db->execute(
                "INSERT INTO user_level_upgrades (order_id, user_id, from_level, to_level, duration, price, status, create_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $orderId,
                    $userId,
                    $userLevel ? $userLevel['level_id'] : 0,
                    $levelId,
                    $duration,
                    $price,
                    0, // 0待支付
                    $now
                ]
            );
            
            // 如果是余额支付，则检查余额并扣款
            if ($payType == 'balance') {
                // 获取用户余额
                $balance = $this->db->query("SELECT balance FROM user_balances WHERE user_id = ?", [$userId], true);
                $userBalance = $balance ? $balance['balance'] : 0;
                
                // 检查余额是否足够
                if ($userBalance < $price) {
                    throw new Exception('余额不足，请先充值或选择其他支付方式');
                }
                
                // 扣除余额
                $this->db->execute(
                    "UPDATE user_balances SET balance = balance - ?, update_time = ? WHERE user_id = ?",
                    [$price, $now, $userId]
                );
                
                // 添加余额变动记录
                $this->db->execute(
                    "INSERT INTO user_balance_logs (user_id, type, amount, before_balance, after_balance, remark, related_id, create_time) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                    [
                        $userId,
                        'consume',
                        -$price,
                        $userBalance,
                        $userBalance - $price,
                        '购买会员等级：' . $targetLevel['name'] . ' x ' . $duration . '个月',
                        $orderId,
                        $now
                    ]
                );
                
                // 更新订单状态
                $this->db->execute(
                    "UPDATE orders SET status = ?, pay_time = ?, update_time = ? WHERE id = ?",
                    [1, $now, $now, $orderId]
                );
                
                // 更新会员升级订单状态
                $this->db->execute(
                    "UPDATE user_level_upgrades SET status = ?, complete_time = ? WHERE order_id = ?",
                    [1, $now, $orderId]
                );
                
                // 升级用户等级
                $this->upgradeUserLevel($userId, $userLevel ? $userLevel['level_id'] : 0, $levelId, $duration);
                
                // 提交事务
                $this->db->commit();
                
                $this->success('支付成功，会员等级已升级');
            } else {
                // 其他支付方式，跳转到支付页面
                $this->db->commit();
                
                $this->success([
                    'order_id' => $orderId,
                    'order_no' => $orderNo,
                    'amount' => $price,
                    'pay_type' => $payType,
                    'pay_url' => '/pay/' . $payType . '?order_no=' . $orderNo
                ]);
            }
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 升级用户等级
     * 
     * @param int $userId 用户ID
     * @param int $fromLevel 原等级ID
     * @param int $toLevel 目标等级ID
     * @param int $duration 购买时长（月）
     */
    private function upgradeUserLevel($userId, $fromLevel, $toLevel, $duration)
    {
        // 获取用户当前等级关系
        $userLevel = $this->db->query(
            "SELECT * FROM user_level_relations WHERE user_id = ?", 
            [$userId], 
            true
        );
        
        // 计算过期时间
        $expireTime = null;
        if ($duration > 0) {
            if ($userLevel && $userLevel['expire_time'] > time()) {
                // 如果用户已有等级且未过期，则在原过期时间基础上增加
                $expireTime = $userLevel['expire_time'] + $duration * 30 * 86400;
            } else {
                // 否则从当前时间开始计算
                $expireTime = time() + $duration * 30 * 86400;
            }
        }
        
        // 当前时间
        $now = time();
        
        if ($userLevel) {
            // 更新用户等级
            $this->db->execute(
                "UPDATE user_level_relations SET level_id = ?, expire_time = ?, update_time = ? WHERE user_id = ?",
                [$toLevel, $expireTime, $now, $userId]
            );
        } else {
            // 添加用户等级
            $this->db->execute(
                "INSERT INTO user_level_relations (user_id, level_id, expire_time, create_time, update_time) 
                VALUES (?, ?, ?, ?, ?)",
                [$userId, $toLevel, $expireTime, $now, $now]
            );
        }
    }
    
    /**
     * 支付回调处理
     */
    public function payCallback()
    {
        // 获取参数
        $orderNo = $this->getParam('order_no', '');
        $payType = $this->getParam('pay_type', '');
        $tradeNo = $this->getParam('trade_no', '');
        
        if (empty($orderNo) || empty($payType) || empty($tradeNo)) {
            $this->error('参数错误');
        }
        
        // 获取订单信息
        $order = $this->db->query(
            "SELECT * FROM orders WHERE order_no = ? AND pay_type = ? AND status = 0", 
            [$orderNo, $payType], 
            true
        );
        
        if (empty($order)) {
            $this->error('订单不存在或已支付');
        }
        
        // 获取会员升级订单
        $upgradeOrder = $this->db->query(
            "SELECT * FROM user_level_upgrades WHERE order_id = ? AND status = 0", 
            [$order['id']], 
            true
        );
        
        if (empty($upgradeOrder)) {
            $this->error('会员升级订单不存在或已处理');
        }
        
        // 当前时间
        $now = time();
        
        // 开启事务
        $this->db->beginTransaction();
        
        try {
            // 更新订单状态
            $this->db->execute(
                "UPDATE orders SET status = ?, pay_time = ?, trade_no = ?, update_time = ? WHERE id = ?",
                [1, $now, $tradeNo, $now, $order['id']]
            );
            
            // 更新会员升级订单状态
            $this->db->execute(
                "UPDATE user_level_upgrades SET status = ?, complete_time = ? WHERE order_id = ?",
                [1, $now, $order['id']]
            );
            
            // 升级用户等级
            $this->upgradeUserLevel(
                $upgradeOrder['user_id'], 
                $upgradeOrder['from_level'], 
                $upgradeOrder['to_level'], 
                $upgradeOrder['duration']
            );
            
            // 提交事务
            $this->db->commit();
            
            $this->success('支付成功，会员等级已升级');
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->error('处理失败：' . $e->getMessage());
        }
    }
}