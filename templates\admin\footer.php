<!-- 页面底部内容 -->
<script>
    // 全局设置
    layui.use(['layer'], function() {
        var $ = layui.jquery;
        var layer = layui.layer;
        
        // 全局AJAX错误处理
        $(document).ajaxError(function(event, xhr, settings) {
            if (xhr.status === 401) {
                layer.msg('会话已过期，请重新登录', {icon: 2, time: 1500}, function() {
                    location.href = '/admin/login';
                });
            } else if (xhr.status === 403) {
                layer.msg('没有权限执行此操作', {icon: 2});
            } else if (xhr.status === 500) {
                layer.msg('服务器错误，请稍后再试', {icon: 2});
            }
        });
        
        // 退出登录事件处理
        $('body').on('click', '*[ew-event="logout"]', function() {
            var url = $(this).data('url') || '/admin/logout';
            layer.confirm('确定要退出登录吗？', {
                title: '提示',
                btn: ['确定', '取消']
            }, function(index) {
                layer.close(index);
                location.href = url;
            });
        });
    });
</script>