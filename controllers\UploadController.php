<?php
/**
 * 文件上传控制器
 */
class UploadController {
    private $db;
    private $config;
    
    public function __construct() {
        global $db, $config;
        $this->db = $db;
        $this->config = $config;
        
        // 检查用户登录状态
        checkLogin();
    }
    
    /**
     * 上传图片
     */
    public function uploadImage() {
        // 检查是否有文件上传
        if (!isset($_FILES['file']) || empty($_FILES['file']['name'])) {
            return json([
                'code' => 1,
                'msg' => '请选择要上传的图片'
            ]);
        }
        
        $file = $_FILES['file'];
        
        // 检查文件类型
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            return json([
                'code' => 1,
                'msg' => '只允许上传JPG、PNG、GIF、WEBP格式的图片'
            ]);
        }
        
        // 检查文件大小
        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            return json([
                'code' => 1,
                'msg' => '图片大小不能超过5MB'
            ]);
        }
        
        // 创建上传目录
        $uploadDir = ROOT_PATH . '/uploads/images/' . date('Ymd');
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // 生成文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = md5(uniqid(mt_rand(), true)) . '.' . $extension;
        $filepath = $uploadDir . '/' . $filename;
        
        // 移动上传的文件
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            return json([
                'code' => 1,
                'msg' => '文件上传失败'
            ]);
        }
        
        // 生成访问URL
        $fileUrl = '/uploads/images/' . date('Ymd') . '/' . $filename;
        
        // 记录上传信息到数据库
        $this->saveUploadRecord($fileUrl, $file['size'], $file['type']);
        
        return json([
            'code' => 0,
            'msg' => '上传成功',
            'data' => [
                'src' => $fileUrl,
                'title' => pathinfo($file['name'], PATHINFO_FILENAME)
            ]
        ]);
    }
    
    /**
     * 后台上传图片（富文本编辑器使用）
     */
    public function adminUploadImage() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        // 调用上传图片方法
        return $this->uploadImage();
    }
    
    /**
     * 保存上传记录
     */
    private function saveUploadRecord($fileUrl, $fileSize, $fileType) {
        $userId = $_SESSION['user_id'] ?? 0;
        $userType = isset($_SESSION['admin_id']) ? 'admin' : 'user';
        
        $stmt = $this->db->prepare("INSERT INTO uploads (user_id, user_type, file_url, file_size, file_type, upload_time) 
                                   VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$userId, $userType, $fileUrl, $fileSize, $fileType, time()]);
    }
}