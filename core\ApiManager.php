<?php
/**
 * API管理核心类
 */
class ApiManager {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = require_once 'config/app.php';
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest($apiPath, $params, $userId) {
        // 获取API配置
        $api = $this->db->fetchOne(
            "SELECT * FROM apis WHERE path = ? AND status = 1",
            [$apiPath]
        );
        
        if (!$api) {
            return $this->errorResponse('API不存在或已禁用');
        }
        
        // 检查用户权限
        if (!$this->checkPermission($userId, $api['id'])) {
            return $this->errorResponse('无权限访问此API');
        }
        
        // 检查QPS限制
        if (!$this->checkQpsLimit($userId, $api['id'])) {
            return $this->errorResponse('请求过于频繁，请稍后再试');
        }
        
        // 检查用户余额
        if (!$this->checkUserBalance($userId, $api['price'])) {
            return $this->errorResponse('余额不足');
        }
        
        // 记录请求日志
        $logId = $this->logRequest($userId, $api['id'], $params);
        
        try {
            // 执行API调用
            $result = $this->executeApi($api, $params);
            
            // 扣费
            $this->deductBalance($userId, $api['price']);
            
            // 更新日志
            $this->updateRequestLog($logId, true, $result);
            
            return $this->successResponse($result);
            
        } catch (Exception $e) {
            // 更新日志
            $this->updateRequestLog($logId, false, $e->getMessage());
            
            return $this->errorResponse('API调用失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 执行API调用
     */
    private function executeApi($api, $params) {
        if ($api['type'] == 'local') {
            // 本地接口
            return $this->executeLocalApi($api, $params);
        } else {
            // 远程接口
            return $this->executeRemoteApi($api, $params);
        }
    }
    
    /**
     * 执行本地API
     */
    private function executeLocalApi($api, $params) {
        $config = json_decode($api['config'], true);
        $className = $config['class'];
        $method = $config['method'];
        
        if (!class_exists($className)) {
            throw new Exception("类 {$className} 不存在");
        }
        
        $instance = new $className();
        
        if (!method_exists($instance, $method)) {
            throw new Exception("方法 {$method} 不存在");
        }
        
        return $instance->$method($params);
    }
    
    /**
     * 执行远程API
     */
    private function executeRemoteApi($api, $params) {
        $config = json_decode($api['config'], true);
        $url = $config['url'];
        $method = $config['method'] ?? 'GET';
        $headers = $config['headers'] ?? [];
        
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['api']['default_timeout'],
            CURLOPT_HTTPHEADER => $headers,
        ]);
        
        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            throw new Exception('CURL错误: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP错误: {$httpCode}");
        }
        
        return json_decode($response, true);
    }
    
    /**
     * 检查用户权限
     */
    private function checkPermission($userId, $apiId) {
        $permission = $this->db->fetchOne(
            "SELECT * FROM user_api_permissions WHERE user_id = ? AND api_id = ?",
            [$userId, $apiId]
        );
        
        return $permission !== false;
    }
    
    /**
     * 检查QPS限制
     */
    private function checkQpsLimit($userId, $apiId) {
        $now = time();
        $count = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM request_logs 
             WHERE user_id = ? AND api_id = ? AND created_at > ?",
            [$userId, $apiId, date('Y-m-d H:i:s', $now - 1)]
        );
        
        return $count['count'] < $this->config['api']['qps_limit'];
    }
    
    /**
     * 检查用户余额
     */
    private function checkUserBalance($userId, $price) {
        $user = $this->db->fetchOne(
            "SELECT balance FROM users WHERE id = ?",
            [$userId]
        );
        
        return $user['balance'] >= $price;
    }
    
    /**
     * 扣除余额
     */
    private function deductBalance($userId, $amount) {
        $this->db->update('users', [
            'balance' => new PDO_Expression('balance - ' . $amount)
        ], 'id = ?', [$userId]);
        
        // 记录余额变动
        $this->db->insert('balance_logs', [
            'user_id' => $userId,
            'type' => 'deduct',
            'amount' => $amount,
            'description' => 'API调用扣费',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 记录请求日志
     */
    private function logRequest($userId, $apiId, $params) {
        $this->db->insert('request_logs', [
            'user_id' => $userId,
            'api_id' => $apiId,
            'params' => json_encode($params),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * 更新请求日志
     */
    private function updateRequestLog($logId, $success, $response) {
        $this->db->update('request_logs', [
            'success' => $success ? 1 : 0,
            'response' => is_string($response) ? $response : json_encode($response),
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$logId]);
    }
    
    /**
     * 成功响应
     */
    private function successResponse($data) {
        return [
            'code' => 200,
            'message' => '成功',
            'data' => $data
        ];
    }
    
    /**
     * 错误响应
     */
    private function errorResponse($message, $code = 400) {
        return [
            'code' => $code,
            'message' => $message,
            'data' => null
        ];
    }
}