<?php
/**
 * API商业系统核心函数库
 */

/**
 * 获取系统配置
 * @param string $key 配置键名
 * @param mixed $default 默认值
 * @return mixed 配置值
 */
function get_config($key, $default = null) {
    global $pdo, $dbConfig;
    
    static $configs = null;
    
    if ($configs === null) {
        try {
            $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}configs");
            $configs = [];
            while ($row = $stmt->fetch()) {
                $configs[$row['key']] = $row['value'];
            }
        } catch (PDOException $e) {
            error_log('获取配置失败: ' . $e->getMessage());
            return $default;
        }
    }
    
    return isset($configs[$key]) ? $configs[$key] : $default;
}

/**
 * 更新系统配置
 * @param string $key 配置键名
 * @param mixed $value 配置值
 * @return boolean 是否成功
 */
function update_config($key, $value) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}configs SET value = :value, updated_at = NOW() WHERE `key` = :key");
        $stmt->execute([':value' => $value, ':key' => $key]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log('更新配置失败: ' . $e->getMessage());
        return false;
    }
}

/**
 * 生成API密钥
 * @return string API密钥
 */
function generate_api_key() {
    return bin2hex(random_bytes(32));
}

/**
 * 生成订单号
 * @param string $prefix 前缀
 * @return string 订单号
 */
function generate_order_no($prefix = '') {
    $date = date('YmdHis');
    $random = mt_rand(1000, 9999);
    return $prefix . $date . $random;
}

/**
 * 密码加密
 * @param string $password 原始密码
 * @return string 加密后的密码
 */
function password_encrypt($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * 密码验证
 * @param string $password 原始密码
 * @param string $hash 加密后的密码
 * @return boolean 是否匹配
 */
function password_verify_custom($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * 生成JWT令牌
 * @param array $payload 载荷数据
 * @param int $expire 过期时间（秒）
 * @return string JWT令牌
 */
function generate_jwt($payload, $expire = 7200) {
    global $appConfig;
    
    $header = [
        'alg' => 'HS256',
        'typ' => 'JWT'
    ];
    
    $payload['exp'] = time() + $expire;
    $payload['iat'] = time();
    
    $base64Header = base64_encode(json_encode($header));
    $base64Payload = base64_encode(json_encode($payload));
    
    $signature = hash_hmac('sha256', "$base64Header.$base64Payload", $appConfig['security']['jwt_secret'], true);
    $base64Signature = base64_encode($signature);
    
    return "$base64Header.$base64Payload.$base64Signature";
}

/**
 * 验证JWT令牌
 * @param string $jwt JWT令牌
 * @return array|boolean 成功返回载荷数据，失败返回false
 */
function verify_jwt($jwt) {
    global $appConfig;
    
    $parts = explode('.', $jwt);
    if (count($parts) != 3) {
        return false;
    }
    
    list($base64Header, $base64Payload, $base64Signature) = $parts;
    
    $signature = base64_decode($base64Signature);
    $expectedSignature = hash_hmac('sha256', "$base64Header.$base64Payload", $appConfig['security']['jwt_secret'], true);
    
    if (!hash_equals($signature, $expectedSignature)) {
        return false;
    }
    
    $payload = json_decode(base64_decode($base64Payload), true);
    
    if (isset($payload['exp']) && $payload['exp'] < time()) {
        return false;
    }
    
    return $payload;
}

/**
 * 获取当前用户信息
 * @return array|null 用户信息
 */
function get_current_user() {
    global $pdo, $dbConfig;
    
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}users WHERE id = :id");
        $stmt->execute([':id' => $_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log('获取用户信息失败: ' . $e->getMessage());
        return null;
    }
}

/**
 * 检查用户权限
 * @param string $permission 权限名称
 * @return boolean 是否有权限
 */
function check_permission($permission) {
    $user = get_current_user();
    if (!$user) {
        return false;
    }
    
    // 管理员拥有所有权限
    if ($user['role'] == 'admin') {
        return true;
    }
    
    // 根据不同权限进行判断
    switch ($permission) {
        case 'access_admin':
            return $user['role'] == 'admin';
        case 'manage_api':
            return in_array($user['role'], ['admin', 'merchant']);
        case 'create_api':
            return in_array($user['role'], ['admin', 'merchant']) && ($user['role'] == 'admin' || $user['merchant_level'] > 0);
        default:
            return false;
    }
}

/**
 * 记录API调用日志
 * @param int $api_id API ID
 * @param string $api_key API密钥
 * @param int $response_code 响应状态码
 * @param int $response_time 响应时间(ms)
 * @return boolean 是否成功
 */
function log_api_call($api_id, $api_key, $response_code, $response_time) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("SELECT user_id FROM {$dbConfig['prefix']}api_keys WHERE api_key = :api_key");
        $stmt->execute([':api_key' => $api_key]);
        $user_id = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("INSERT INTO {$dbConfig['prefix']}logs (api_id, user_id, api_key, ip, method, url, params, response_code, response_time, created_at) 
                              VALUES (:api_id, :user_id, :api_key, :ip, :method, :url, :params, :response_code, :response_time, NOW())");
        
        $params = [
            ':api_id' => $api_id,
            ':user_id' => $user_id,
            ':api_key' => $api_key,
            ':ip' => $_SERVER['REMOTE_ADDR'],
            ':method' => $_SERVER['REQUEST_METHOD'],
            ':url' => $_SERVER['REQUEST_URI'],
            ':params' => json_encode($_REQUEST),
            ':response_code' => $response_code,
            ':response_time' => $response_time
        ];
        
        return $stmt->execute($params);
    } catch (PDOException $e) {
        error_log('记录API调用日志失败: ' . $e->getMessage());
        return false;
    }
}

/**
 * 检查API密钥是否有效
 * @param string $api_key API密钥
 * @return boolean 是否有效
 */
function check_api_key($api_key) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}api_keys WHERE api_key = :api_key AND status = 1");
        $stmt->execute([':api_key' => $api_key]);
        $key = $stmt->fetch();
        
        if (!$key) {
            return false;
        }
        
        // 检查IP白名单
        if (!empty($key['ip_whitelist'])) {
            $whitelist = explode(',', $key['ip_whitelist']);
            if (!in_array($_SERVER['REMOTE_ADDR'], $whitelist)) {
                return false;
            }
        }
        
        // 检查IP黑名单
        if (!empty($key['ip_blacklist'])) {
            $blacklist = explode(',', $key['ip_blacklist']);
            if (in_array($_SERVER['REMOTE_ADDR'], $blacklist)) {
                return false;
            }
        }
        
        // 检查QPS限制
        if ($key['qps_limit'] > 0) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$dbConfig['prefix']}logs 
                                  WHERE api_key = :api_key AND created_at > DATE_SUB(NOW(), INTERVAL 1 SECOND)");
            $stmt->execute([':api_key' => $api_key]);
            $qps = $stmt->fetchColumn();
            
            if ($qps >= $key['qps_limit']) {
                return false;
            }
        }
        
        // 检查每日调用限制
        if ($key['daily_limit'] > 0) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$dbConfig['prefix']}logs 
                                  WHERE api_key = :api_key AND created_at > CURDATE()");
            $stmt->execute([':api_key' => $api_key]);
            $daily_calls = $stmt->fetchColumn();
            
            if ($daily_calls >= $key['daily_limit']) {
                return false;
            }
        }
        
        return true;
    } catch (PDOException $e) {
        error_log('检查API密钥失败: ' . $e->getMessage());
        return false;
    }
}

/**
 * 检查用户是否有权限调用API
 * @param int $user_id 用户ID
 * @param int $api_id API ID
 * @return boolean 是否有权限
 */
function check_api_permission($user_id, $api_id) {
    global $pdo, $dbConfig;
    
    try {
        // 获取API信息
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}apis WHERE id = :api_id");
        $stmt->execute([':api_id' => $api_id]);
        $api = $stmt->fetch();
        
        if (!$api || $api['status'] != 1) {
            return false;
        }
        
        // 如果是免费API，直接返回true
        if ($api['is_free'] == 1) {
            return true;
        }
        
        // 检查用户是否购买了该API
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}purchases 
                              WHERE user_id = :user_id AND api_id = :api_id AND status = 1 
                              AND (expire_at IS NULL OR expire_at > NOW())");
        $stmt->execute([':user_id' => $user_id, ':api_id' => $api_id]);
        $purchase = $stmt->fetch();
        
        if ($purchase) {
            return true;
        }
        
        // 检查用户VIP等级是否有免费调用次数
        $stmt = $pdo->prepare("SELECT v.daily_free_calls FROM {$dbConfig['prefix']}users u 
                              JOIN {$dbConfig['prefix']}vip_levels v ON u.vip_level = v.id 
                              WHERE u.id = :user_id AND u.vip_expire > NOW()");
        $stmt->execute([':user_id' => $user_id]);
        $vip = $stmt->fetch();
        
        if ($vip && $vip['daily_free_calls'] > 0) {
            // 检查今日已调用次数
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$dbConfig['prefix']}logs 
                                  WHERE user_id = :user_id AND api_id = :api_id AND created_at > CURDATE()");
            $stmt->execute([':user_id' => $user_id, ':api_id' => $api_id]);
            $daily_calls = $stmt->fetchColumn();
            
            if ($daily_calls < $vip['daily_free_calls']) {
                return true;
            }
        }
        
        return false;
    } catch (PDOException $e) {
        error_log('检查API权限失败: ' . $e->getMessage());
        return false;
    }
}

/**
 * 扣除API调用费用
 * @param int $user_id 用户ID
 * @param int $api_id API ID
 * @return boolean 是否成功
 */
function deduct_api_fee($user_id, $api_id) {
    global $pdo, $dbConfig;
    
    try {
        // 获取API信息
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}apis WHERE id = :api_id");
        $stmt->execute([':api_id' => $api_id]);
        $api = $stmt->fetch();
        
        if (!$api) {
            return false;
        }
        
        // 如果是免费API，直接返回true
        if ($api['is_free'] == 1) {
            return true;
        }
        
        // 检查用户是否购买了该API
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}purchases 
                              WHERE user_id = :user_id AND api_id = :api_id AND status = 1 
                              AND (expire_at IS NULL OR expire_at > NOW())");
        $stmt->execute([':user_id' => $user_id, ':api_id' => $api_id]);
        $purchase = $stmt->fetch();
        
        if ($purchase) {
            return true;
        }
        
        // 检查用户VIP等级是否有免费调用次数
        $stmt = $pdo->prepare("SELECT v.daily_free_calls FROM {$dbConfig['prefix']}users u 
                              JOIN {$dbConfig['prefix']}vip_levels v ON u.vip_level = v.id 
                              WHERE u.id = :user_id AND u.vip_expire > NOW()");
        $stmt->execute([':user_id' => $user_id]);
        $vip = $stmt->fetch();
        
        if ($vip && $vip['daily_free_calls'] > 0) {
            // 检查今日已调用次数
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$dbConfig['prefix']}logs 
                                  WHERE user_id = :user_id AND api_id = :api_id AND created_at > CURDATE()");
            $stmt->execute([':user_id' => $user_id, ':api_id' => $api_id]);
            $daily_calls = $stmt->fetchColumn();
            
            if ($daily_calls < $vip['daily_free_calls']) {
                return true;
            }
        }
        
        // 获取用户VIP折扣
        $stmt = $pdo->prepare("SELECT v.discount FROM {$dbConfig['prefix']}users u 
                              JOIN {$dbConfig['prefix']}vip_levels v ON u.vip_level = v.id 
                              WHERE u.id = :user_id AND u.vip_expire > NOW()");
        $stmt->execute([':user_id' => $user_id]);
        $discount = $stmt->fetchColumn();
        
        if (!$discount) {
            $discount = 100;
        }
        
        // 计算实际扣费金额
        $fee = $api['price'] * ($discount / 100);
        
        // 扣除用户余额
        $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}users SET balance = balance - :fee WHERE id = :user_id AND balance >= :fee");
        $stmt->execute([':user_id' => $user_id, ':fee' => $fee]);
        
        if ($stmt->rowCount() == 0) {
            return false;
        }
        
        // 更新API调用次数
        $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}apis SET call_count = call_count + 1 WHERE id = :api_id");
        $stmt->execute([':api_id' => $api_id]);
        
        // 如果API是由商家创建的，给商家分成
        if ($api['creator_id']) {
            // 获取商家分成比例
            $stmt = $pdo->prepare("SELECT m.commission_rate FROM {$dbConfig['prefix']}users u 
                                  JOIN {$dbConfig['prefix']}merchant_levels m ON u.merchant_level = m.id 
                                  WHERE u.id = :creator_id");
            $stmt->execute([':creator_id' => $api['creator_id']]);
            $commission_rate = $stmt->fetchColumn();
            
            if ($commission_rate) {
                $commission = $fee * ($commission_rate / 100);
                
                // 给商家增加余额
                $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}users SET balance = balance + :commission WHERE id = :creator_id");
                $stmt->execute([':creator_id' => $api['creator_id'], ':commission' => $commission]);
            }
        }
        
        return true;
    } catch (PDOException $e) {
        error_log('扣除API费用失败: ' . $e->getMessage());
        return false;
    }
}

/**
 * 发送邮件
 * @param string $to 收件人
 * @param string $subject 主题
 * @param string $body 内容
 * @return boolean 是否成功
 */
function send_email($to, $subject, $body) {
    global $appConfig;
    
    // 如果未配置邮件服务器，直接返回false
    if (empty($appConfig['mail']['smtp_host']) || empty($appConfig['mail']['smtp_username'])) {
        error_log('邮件服务器未配置');
        return false;
    }
    
    // 这里可以集成PHPMailer等邮件发送库
    // 简单实现，仅作示例
    $headers = "From: {$appConfig['mail']['from_name']} <{$appConfig['mail']['from_email']}>\r\n";
    $headers .= "Reply-To: {$appConfig['mail']['from_email']}\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $body, $headers);
}

/**
 * 生成随机字符串
 * @param int $length 长度
 * @return string 随机字符串
 */
function random_string($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $result = '';
    for ($i = 0; $i < $length; $i++) {
        $result .= $chars[mt_rand(0, strlen($chars) - 1)];
    }
    return $result;
}

/**
 * 获取分页数据
 * @param string $table 表名
 * @param array $where 条件
 * @param int $page 页码
 * @param int $limit 每页数量
 * @param string $order 排序
 * @return array 分页数据
 */
function get_pagination($table, $where = [], $page = 1, $limit = 10, $order = 'id DESC') {
    global $pdo, $dbConfig;
    
    try {
        $table = $dbConfig['prefix'] . $table;
        
        // 构建WHERE条件
        $whereStr = '';
        $params = [];
        if (!empty($where)) {
            $whereArr = [];
            foreach ($where as $key => $value) {
                $whereArr[] = "$key = :$key";
                $params[":$key"] = $value;
            }
            $whereStr = 'WHERE ' . implode(' AND ', $whereArr);
        }
        
        // 获取总记录数
        $countSql = "SELECT COUNT(*) FROM $table $whereStr";
        $stmt = $pdo->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        // 计算总页数
        $totalPages = ceil($total / $limit);
        
        // 确保页码有效
        $page = max(1, min($page, $totalPages));
        
        // 计算偏移量
        $offset = ($page - 1) * $limit;
        
        // 获取数据
        $dataSql = "SELECT * FROM $table $whereStr ORDER BY $order LIMIT :offset, :limit";
        $stmt = $pdo->prepare($dataSql);
        
        // 绑定参数
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        
        $stmt->execute();
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => $totalPages
        ];
    } catch (PDOException $e) {
        error_log('获取分页数据失败: ' . $e->getMessage());
        return [
            'data' => [],
            'total' => 0,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => 0
        ];
    }
}

/**
 * 上传文件
 * @param string $file 文件字段名
 * @param string $dir 保存目录
 * @param array $allowed_types 允许的文件类型
 * @param int $max_size 最大文件大小(字节)
 * @return array 上传结果
 */
function upload_file($file, $dir = 'uploads', $allowed_types = null, $max_size = null) {
    global $appConfig;
    
    // 如果未指定允许的文件类型，使用配置中的设置
    if ($allowed_types === null) {
        $allowed_types = $appConfig['upload']['allowed_types'];
    }
    
    // 如果未指定最大文件大小，使用配置中的设置
    if ($max_size === null) {
        $max_size = $appConfig['upload']['max_size'];
    }
    
    // 检查上传是否成功
    if (!isset($_FILES[$file]) || $_FILES[$file]['error'] != UPLOAD_ERR_OK) {
        return [
            'success' => false,
            'message' => '文件上传失败: ' . upload_error_message($_FILES[$file]['error'])
        ];
    }
    
    // 检查文件大小
    if ($_FILES[$file]['size'] > $max_size) {
        return [
            'success' => false,
            'message' => '文件大小超过限制'
        ];
    }
    
    // 检查文件类型
    $ext = strtolower(pathinfo($_FILES[$file]['name'], PATHINFO_EXTENSION));
    if (!in_array($ext, $allowed_types)) {
        return [
            'success' => false,
            'message' => '不支持的文件类型'
        ];
    }
    
    // 创建保存目录
    $upload_dir = rtrim($dir, '/') . '/' . date('Ymd');
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    // 生成唯一文件名
    $filename = uniqid() . '.' . $ext;
    $filepath = $upload_dir . '/' . $filename;
    
    // 移动上传的文件
    if (move_uploaded_file($_FILES[$file]['tmp_name'], $filepath)) {
        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'url' => '/' . $filepath
        ];
    } else {
        return [
            'success' => false,
            'message' => '文件保存失败'
        ];
    }
}

/**
 * 获取上传错误信息
 * @param int $error_code 错误代码
 * @return string 错误信息
 */
function upload_error_message($error_code) {
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
            return '上传的文件超过了php.ini中upload_max_filesize指令限制的值';
        case UPLOAD_ERR_FORM_SIZE:
            return '上传的文件超过了HTML表单中MAX_FILE_SIZE指令指定的值';
        case UPLOAD_ERR_PARTIAL:
            return '文件只有部分被上传';
        case UPLOAD_ERR_NO_FILE:
            return '没有文件被上传';
        case UPLOAD_ERR_NO_TMP_DIR:
            return '找不到临时文件夹';
        case UPLOAD_ERR_CANT_WRITE:
            return '文件写入失败';
        case UPLOAD_ERR_EXTENSION:
            return '文件上传因扩展而停止';
        default:
            return '未知上传错误';
    }
}

/**
 * 格式化日期时间
 * @param string $datetime 日期时间
 * @param string $format 格式
 * @return string 格式化后的日期时间
 */
function format_datetime($datetime, $format = 'Y-m-d H:i:s') {
    if (!$datetime) {
        return '';
    }
    
    return date($format, strtotime($datetime));
}

/**
 * 格式化金额
 * @param float $amount 金额
 * @param int $decimals 小数位数
 * @return string 格式化后的金额
 */
function format_money($amount, $decimals = 2) {
    return number_format($amount, $decimals, '.', ',');
}

/**
 * 获取客户端IP
 * @return string IP地址
 */
function get_client_ip() {
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    
    return $ip;
}

/**
 * 检查IP是否在白名单中
 * @param string $ip IP地址
 * @param string $whitelist IP白名单，逗号分隔
 * @return boolean 是否在白名单中
 */
function check_ip_whitelist($ip, $whitelist) {
    if (empty($whitelist)) {
        return true;
    }
    
    $whitelist = explode(',', $whitelist);
    return in_array($ip, $whitelist);
}

/**
 * 检查IP是否在黑名单中
 * @param string $ip IP地址
 * @param string $blacklist IP黑名单，逗号分隔
 * @return boolean 是否在黑名单中
 */
function check_ip_blacklist($ip, $blacklist) {
    if (empty($blacklist)) {
        return false;
    }
    
    $blacklist = explode(',', $blacklist);
    return in_array($ip, $blacklist);
}

/**
 * 记录系统日志
 * @param string $action 操作
 * @param string $message 消息
 * @param int $user_id 用户ID
 * @return boolean 是否成功
 */
function log_system($action, $message, $user_id = 0) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO {$dbConfig['prefix']}system_logs (user_id, action, message, ip, created_at) 
                              VALUES (:user_id, :action, :message, :ip, NOW())");
        
        $params = [
            ':user_id' => $user_id,
            ':action' => $action,
            ':message' => $message,
            ':ip' => get_client_ip()
        ];
        
        return $stmt->execute($params);
    } catch (PDOException $e) {
        error_log('记录系统日志失败: ' . $e->getMessage());
        return false;
    }
}

/**
 * 发送异常通知邮件
 * @param string $subject 主题
 * @param string $message 消息
 * @return boolean 是否成功
 */
function send_exception_email($subject, $message) {
    global $appConfig;
    
    $admin_email = get_config('admin_email', $appConfig['mail']['from_email']);
    if (empty($admin_email)) {
        return false;
    }
    
    $site_name = get_config('site_name', 'API商业系统');
    $subject = "[{$site_name}] {$subject}";
    
    return send_email($admin_email, $subject, $message);
}

/**
 * 获取API文档
 * @param int $api_id API ID
 * @return array API文档
 */
function get_api_doc($api_id) {
    global $pdo, $dbConfig;
    
    try {
        // 获取API基本信息
        $stmt = $pdo->prepare("SELECT a.*, c.name as category_name FROM {$dbConfig['prefix']}apis a 
                              LEFT JOIN {$dbConfig['prefix']}categories c ON a.category_id = c.id 
                              WHERE a.id = :api_id");
        $stmt->execute([':api_id' => $api_id]);
        $api = $stmt->fetch();
        
        if (!$api) {
            return null;
        }
        
        // 获取API参数
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}parameters WHERE api_id = :api_id ORDER BY sort_order");
        $stmt->execute([':api_id' => $api_id]);
        $parameters = $stmt->fetchAll();
        
        // 获取API响应示例
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}responses WHERE api_id = :api_id");
        $stmt->execute([':api_id' => $api_id]);
        $responses = $stmt->fetchAll();
        
        return [
            'api' => $api,
            'parameters' => $parameters,
            'responses' => $responses
        ];
    } catch (PDOException $e) {
        error_log('获取API文档失败: ' . $e->getMessage());
        return null;
    }
}

/**
 * 生成API文档HTML
 * @param array $doc API文档
 * @return string HTML
 */
function generate_api_doc_html($doc) {
    if (!$doc) {
        return '<div class="alert alert-danger">API文档不存在</div>';
    }
    
    $api = $doc['api'];
    $parameters = $doc['parameters'];
    $responses = $doc['responses'];
    
    $html = <<<HTML
<div class="api-doc">
    <h2>{$api['name']}</h2>
    <div class="api-desc">{$api['description']}</div>
    
    <div class="api-info">
        <div class="api-info-item">
            <span class="api-info-label">分类：</span>
            <span class="api-info-value">{$api['category_name']}</span>
        </div>
        <div class="api-info-item">
            <span class="api-info-label">请求方法：</span>
            <span class="api-info-value">{$api['method']}</span>
        </div>
        <div class="api-info-item">
            <span class="api-info-label">接口地址：</span>
            <span class="api-info-value">{$api['url']}</span>
        </div>
        <div class="api-info-item">
            <span class="api-info-label">价格：</span>
            <span class="api-info-value">
HTML;
    
    if ($api['is_free'] == 1) {
        $html .= '免费';
    } else {
        $html .= format_money($api['price']) . ' 元/次';
    }
    
    $html .= <<<HTML
            </span>
        </div>
    </div>
    
    <h3>请求参数</h3>
HTML;
    
    if (empty($parameters)) {
        $html .= '<div class="alert alert-info">无需参数</div>';
    } else {
        $html .= <<<HTML
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>是否必填</th>
                <th>默认值</th>
                <th>说明</th>
            </tr>
        </thead>
        <tbody>
HTML;
        
        foreach ($parameters as $param) {
            $required = $param['required'] ? '<span class="badge badge-danger">是</span>' : '<span class="badge badge-secondary">否</span>';
            $default = $param['default_value'] ?: '-';
            
            $html .= <<<HTML
            <tr>
                <td>{$param['name']}</td>
                <td>{$param['type']}</td>
                <td>{$required}</td>
                <td>{$default}</td>
                <td>{$param['description']}</td>
            </tr>
HTML;
        }
        
        $html .= <<<HTML
        </tbody>
    </table>
HTML;
    }
    
    $html .= '<h3>响应示例</h3>';
    
    if (empty($responses)) {
        $html .= '<div class="alert alert-info">暂无响应示例</div>';
    } else {
        foreach ($responses as $response) {
            $html .= <<<HTML
    <div class="api-response">
        <div class="api-response-title">{$response['name']}</div>
        <pre class="api-response-content">{$response['content']}</pre>
    </div>
HTML;
        }
    }
    
    $html .= <<<HTML
    <div class="api-debug">
        <h3>在线调试</h3>
        <form class="api-debug-form" action="/api/debug" method="post">
            <input type="hidden" name="api_id" value="{$api['id']}">
            <div class="form-group">
                <label>API密钥</label>
                <input type="text" name="api_key" class="form-control" placeholder="请输入您的API密钥">
            </div>
HTML;
    
    foreach ($parameters as $param) {
        $required = $param['required'] ? 'required' : '';
        $default = $param['default_value'] ?: '';
        
        $html .= <<<HTML
            <div class="form-group">
                <label>{$param['name']}</label>
                <input type="text" name="param_{$param['name']}" class="form-control" placeholder="{$param['description']}" value="{$default}" {$required}>
            </div>
HTML;
    }
    
    $html .= <<<HTML
            <button type="submit" class="btn btn-primary">发送请求</button>
        </form>
        <div class="api-debug-result"></div>
    </div>
</div>
HTML;
    
    return $html;
}

/**
 * 检查用户是否已登录
 * @return boolean 是否已登录
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * 重定向
 * @param string $url 目标URL
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * 获取当前页面URL
 * @return string 当前页面URL
 */
function current_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * 获取基础URL
 * @return string 基础URL
 */
function base_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'];
}

/**
 * 获取分页HTML
 * @param int $page 当前页码
 * @param int $total_pages 总页数
 * @param string $url 链接模板，使用{page}作为页码占位符
 * @return string 分页HTML
 */
function pagination_html($page, $total_pages, $url = '?page={page}') {
    if ($total_pages <= 1) {
        return '';
    }
    
    $html = '<ul class="pagination">';
    
    // 上一页
    if ($page > 1) {
        $prev_url = str_replace('{page}', $page - 1, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $prev_url . '">上一页</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><a class="page-link" href="javascript:void(0);">上一页</a></li>';
    }
    
    // 页码
    $start = max(1, $page - 2);
    $end = min($total_pages, $page + 2);
    
    if ($start > 1) {
        $first_url = str_replace('{page}', 1, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $first_url . '">1</a></li>';
        if ($start > 2) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="javascript:void(0);">...</a></li>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        $page_url = str_replace('{page}', $i, $url);
        if ($i == $page) {
            $html .= '<li class="page-item active"><a class="page-link" href="javascript:void(0);">' . $i . '</a></li>';
        } else {
            $html .= '<li class="page-item"><a class="page-link" href="' . $page_url . '">' . $i . '</a></li>';
        }
    }
    
    if ($end < $total_pages) {
        if ($end < $total_pages - 1) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="javascript:void(0);">...</a></li>';
        }
        $last_url = str_replace('{page}', $total_pages, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $last_url . '">' . $total_pages . '</a></li>';
    }
    
    // 下一页
    if ($page < $total_pages) {
        $next_url = str_replace('{page}', $page + 1, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $next_url . '">下一页</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><a class="page-link" href="javascript:void(0);">下一页</a></li>';
    }
    
    $html .= '</ul>';
    
    return $html;
}

/**
 * 获取导航菜单
 * @param int $parent_id 父级ID
 * @return array 导航菜单
 */
function get_navigation($parent_id = 0) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}navigations WHERE parent_id = :parent_id AND status = 1 ORDER BY sort_order");
        $stmt->execute([':parent_id' => $parent_id]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取导航菜单失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 生成导航菜单HTML
 * @param array $navs 导航菜单
 * @param string $current_url 当前URL
 * @return string HTML
 */
function navigation_html($navs, $current_url = '') {
    if (empty($navs)) {
        return '';
    }
    
    if (empty($current_url)) {
        $current_url = current_url();
    }
    
    $html = '<ul class="nav-menu">';
    
    foreach ($navs as $nav) {
        $active = ($current_url == $nav['url']) ? 'active' : '';
        $icon = $nav['icon'] ? '<i class="' . $nav['icon'] . '"></i> ' : '';
        
        $html .= '<li class="nav-item ' . $active . '">';
        $html .= '<a href="' . $nav['url'] . '" target="' . $nav['target'] . '">' . $icon . $nav['name'] . '</a>';
        
        // 获取子菜单
        $children = get_navigation($nav['id']);
        if (!empty($children)) {
            $html .= '<ul class="nav-submenu">';
            foreach ($children as $child) {
                $child_active = ($current_url == $child['url']) ? 'active' : '';
                $child_icon = $child['icon'] ? '<i class="' . $child['icon'] . '"></i> ' : '';
                
                $html .= '<li class="nav-subitem ' . $child_active . '">';
                $html .= '<a href="' . $child['url'] . '" target="' . $child['target'] . '">' . $child_icon . $child['name'] . '</a>';
                $html .= '</li>';
            }
            $html .= '</ul>';
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    
    return $html;
}

/**
 * 获取面包屑导航
 * @param array $items 面包屑项目
 * @return string HTML
 */
function breadcrumb_html($items) {
    if (empty($items)) {
        return '';
    }
    
    $html = '<ol class="breadcrumb">';
    
    foreach ($items as $key => $item) {
        if (is_array($item)) {
            $name = $item['name'];
            $url = $item['url'];
        } else {
            $name = $item;
            $url = '';
        }
        
        if ($url && $key < count($items) - 1) {
            $html .= '<li class="breadcrumb-item"><a href="' . $url . '">' . $name . '</a></li>';
        } else {
            $html .= '<li class="breadcrumb-item active">' . $name . '</li>';
        }
    }
    
    $html .= '</ol>';
    
    return $html;
}

/**
 * 获取API分类
 * @return array 分类列表
 */
function get_api_categories() {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}categories WHERE status = 1 ORDER BY sort_order");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取API分类失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 获取VIP等级
 * @return array VIP等级列表
 */
function get_vip_levels() {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}vip_levels WHERE status = 1 ORDER BY sort_order");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取VIP等级失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 获取商家等级
 * @return array 商家等级列表
 */
function get_merchant_levels() {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}merchant_levels WHERE status = 1 ORDER BY sort_order");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取商家等级失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 获取轮播图
 * @return array 轮播图列表
 */
function get_banners() {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}banners WHERE status = 1 ORDER BY sort_order");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取轮播图失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 获取文章分类
 * @return array 文章分类列表
 */
function get_article_categories() {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}article_categories WHERE status = 1 ORDER BY sort_order");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取文章分类失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 获取最新文章
 * @param int $limit 数量
 * @return array 文章列表
 */
function get_latest_articles($limit = 5) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}articles WHERE status = 1 ORDER BY created_at DESC LIMIT :limit");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取最新文章失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 获取热门API
 * @param int $limit 数量
 * @return array API列表
 */
function get_hot_apis($limit = 5) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}apis WHERE status = 1 ORDER BY call_count DESC LIMIT :limit");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取热门API失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 获取最新API
 * @param int $limit 数量
 * @return array API列表
 */
function get_latest_apis($limit = 5) {
    global $pdo, $dbConfig;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}apis WHERE status = 1 ORDER BY created_at DESC LIMIT :limit");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log('获取最新API失败: ' . $e->getMessage());
        return [];
    }
}

/**
 * 检查是否是AJAX请求
 * @return boolean 是否是AJAX请求
 */
function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}

/**
 * 输出JSON响应
 * @param array $data 数据
 * @param int $code 状态码
 * @param string $message 消息
 */
function json_response($data = [], $code = 0, $message = 'success') {
    header('Content-Type: application/json');
    echo json_encode([
        'code' => $code,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

/**
 * 获取系统统计数据
 * @return array 统计数据
 */
function get_system_stats() {
    global $pdo, $dbConfig;
    
    try {
        $stats = [];
        
        // 用户总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM {$dbConfig['prefix']}users");
        $stats['user_count'] = $stmt->fetchColumn();
        
        // API总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM {$dbConfig['prefix']}apis");
        $stats['api_count'] = $stmt->fetchColumn();
        
        // 调用总次数
        $stmt = $pdo->query("SELECT COUNT(*) FROM {$dbConfig['prefix']}logs");
        $stats['call_count'] = $stmt->fetchColumn();
        
        // 今日调用次数
        $stmt = $pdo->query("SELECT COUNT(*) FROM {$dbConfig['prefix']}logs WHERE created_at > CURDATE()");
        $stats['today_call_count'] = $stmt->fetchColumn();
        
        // 商家总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM {$dbConfig['prefix']}users WHERE role = 'merchant'");
        $stats['merchant_count'] = $stmt->fetchColumn();
        
        return $stats;
    } catch (PDOException $e) {
        error_log('获取系统统计数据失败: ' . $e->getMessage());
        return [
            'user_count' => 0,
            'api_count' => 0,
            'call_count' => 0,
            'today_call_count' => 0,
            'merchant_count' => 0
        ];
    }
}
