<?php
/**
 * 用户模型
 * API管理系统 - 用户数据模型
 */

require_once __DIR__ . '/../core/Model.php';

class User extends Model {
    protected $table = 'users';
    protected $fillable = [
        'username', 'password', 'email', 'phone', 'nickname', 
        'avatar', 'level', 'balance', 'status'
    ];
    protected $hidden = ['password', 'secret_key'];
    
    /**
     * 创建用户
     */
    public function create($data) {
        if (isset($data['password'])) {
            $data['password'] = System::hashPassword($data['password']);
        }
        
        // 生成API密钥
        $data['api_key'] = $this->generateApiKey();
        $data['secret_key'] = System::generateRandomString(64);
        
        return parent::create($data);
    }
    
    /**
     * 更新用户
     */
    public function update($id, $data) {
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = System::hashPassword($data['password']);
        } else {
            unset($data['password']);
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * 根据用户名查找用户
     */
    public function findByUsername($username) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE username = :username";
        $result = $this->db->fetchOne($sql, ['username' => $username]);
        return $result ?: null;
    }
    
    /**
     * 根据API Key查找用户
     */
    public function findByApiKey($apiKey) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE api_key = :api_key AND status = 1";
        $result = $this->db->fetchOne($sql, ['api_key' => $apiKey]);
        return $result ? $this->hideFields($result) : null;
    }
    
    /**
     * 用户登录
     */
    public function login($username, $password) {
        $user = $this->findByUsername($username);
        if (!$user) {
            return false;
        }
        
        if (!System::verifyPassword($password, $user['password'])) {
            return false;
        }
        
        if ($user['status'] != 1) {
            throw new Exception('账户已被禁用');
        }
        
        // 更新登录信息
        $this->update($user['id'], [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => System::getClientIp()
        ]);
        
        return $this->hideFields($user);
    }
    
    /**
     * 获取用户列表（带等级信息）
     */
    public function getListWithLevel($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['username'])) {
            $whereClause[] = "u.username LIKE :username";
            $params['username'] = '%' . $conditions['username'] . '%';
        }
        
        if (!empty($conditions['level'])) {
            $whereClause[] = "u.level = :level";
            $params['level'] = $conditions['level'];
        }
        
        if (!empty($conditions['status'])) {
            $whereClause[] = "u.status = :status";
            $params['status'] = $conditions['status'];
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->getTableName()} u {$whereStr}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT u.*, l.name as level_name, l.daily_limit, l.monthly_limit
                FROM {$this->getTableName()} u 
                LEFT JOIN {$this->db->getPrefix()}user_levels l ON u.level = l.level 
                {$whereStr}
                ORDER BY u.id DESC 
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        // 隐藏敏感字段
        $data = array_map([$this, 'hideFields'], $data);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 生成唯一的API Key
     */
    private function generateApiKey() {
        do {
            $apiKey = 'ak_' . System::generateRandomString(32);
        } while ($this->findByApiKey($apiKey));
        
        return $apiKey;
    }
    
    /**
     * 重新生成API密钥
     */
    public function regenerateApiKey($userId) {
        $newApiKey = $this->generateApiKey();
        $newSecretKey = System::generateRandomString(64);
        
        $result = $this->update($userId, [
            'api_key' => $newApiKey,
            'secret_key' => $newSecretKey
        ]);
        
        if ($result) {
            return [
                'api_key' => $newApiKey,
                'secret_key' => $newSecretKey
            ];
        }
        
        return false;
    }
    
    /**
     * 充值余额
     */
    public function recharge($userId, $amount, $description = '') {
        $user = $this->find($userId);
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        $newBalance = $user['balance'] + $amount;
        
        $this->beginTransaction();
        try {
            // 更新余额
            $this->update($userId, ['balance' => $newBalance]);
            
            // 记录充值日志
            $this->recordBalanceLog($userId, $amount, $newBalance, 'recharge', $description);
            
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * 扣除余额
     */
    public function deductBalance($userId, $amount, $description = '') {
        $user = $this->find($userId);
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        if ($user['balance'] < $amount) {
            throw new Exception('余额不足');
        }
        
        $newBalance = $user['balance'] - $amount;
        
        $this->beginTransaction();
        try {
            // 更新余额
            $this->update($userId, ['balance' => $newBalance]);
            
            // 记录扣费日志
            $this->recordBalanceLog($userId, -$amount, $newBalance, 'deduct', $description);
            
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * 记录余额变动日志
     */
    private function recordBalanceLog($userId, $amount, $balance, $type, $description) {
        $sql = "INSERT INTO {$this->db->getPrefix()}balance_logs 
                (user_id, amount, balance, type, description, created_at) 
                VALUES (:user_id, :amount, :balance, :type, :description, :created_at)";
        
        $this->db->query($sql, [
            'user_id' => $userId,
            'amount' => $amount,
            'balance' => $balance,
            'type' => $type,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 获取用户统计信息
     */
    public function getUserStats() {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_new,
                    SUM(balance) as total_balance
                FROM {$this->getTableName()}";
        
        return $this->db->fetchOne($sql);
    }
    
    /**
     * 检查用户名是否存在
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->getTableName()} WHERE username = :username";
        $params = ['username' => $username];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
}