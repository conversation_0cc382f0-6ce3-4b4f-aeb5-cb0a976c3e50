<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>数据表格</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318" media="all"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /** 数据表格中的select尺寸调整 */
        .layui-table-view .layui-table-cell .layui-select-title .layui-input {
            height: 28px;
            line-height: 28px;
        }

        .layui-table-view [lay-size="lg"] .layui-table-cell .layui-select-title .layui-input {
            height: 40px;
            line-height: 40px;
        }

        .layui-table-view [lay-size="lg"] .layui-table-cell .layui-select-title .layui-input {
            height: 40px;
            line-height: 40px;
        }

        .layui-table-view [lay-size="sm"] .layui-table-cell .layui-select-title .layui-input {
            height: 20px;
            line-height: 20px;
        }

        .layui-table-view [lay-size="sm"] .layui-table-cell .layui-btn-xs {
            height: 18px;
            line-height: 18px;
        }
    </style>
</head>
<body onscroll="layui.admin.hideFixedEl();">
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar table-tool-mini">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">搜索:</label>
                        <div class="layui-input-inline">
                            <input name="keyword" class="layui-input" type="text" placeholder="输入关键字"/>
                        </div>
                    </div>
                    <div class="layui-inline" style="padding-right: 110px;">
                        <button class="layui-btn icon-btn" lay-filter="tbBasicTbSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button id="tbBasicExportBtn" class="layui-btn icon-btn" type="button">
                            <i class="layui-icon">&#xe67d;</i>导出
                        </button>
                        <!-- 下拉按钮 -->
                        <div class="dropdown-menu dropdown-hover">
                            <button class="layui-btn icon-btn" type="button">
                                &nbsp;更多 <i class="layui-icon layui-icon-drop"></i></button>
                            <ul class="dropdown-menu-nav">
                                <li><a><i class="layui-icon layui-icon-edit"></i>修改用户</a></li>
                                <li><a><i class="layui-icon layui-icon-delete"></i>删除用户</a></li>
                                <li><a><i class="layui-icon layui-icon-password"></i>锁定用户</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="tbBasicTable" lay-filter="tbBasicTable"></table>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-body">
            表格中的select下拉框、dropdown下拉按钮等都不需要自己对表格做任何处理就可以完美的显示，
            因为easyweb提供了fixed定位方式的select和dropdown。
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tbBasicTbBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    <a class="layui-btn layui-btn-xs" data-dropdown="#userTbDrop{{d.LAY_INDEX}}" no-shade="true">
        更多<i class="layui-icon layui-icon-drop" style="font-size: 12px;margin-right: 0;"></i></a>
    <!-- 下拉菜单 -->
    <ul class="dropdown-menu-nav dropdown-bottom-right layui-hide" id="userTbDrop{{d.LAY_INDEX}}">
        <div class="dropdown-anchor"></div>
        <li><a lay-event="lock"><i class="layui-icon layui-icon-password"></i>锁定用户</a></li>
        <li><a lay-event="reset"><i class="layui-icon layui-icon-key"></i>重置密码</a></li>
        <li class="have-more show-left">
            <a><i class="layui-icon layui-icon-upload-drag"></i>上传资料&nbsp;&nbsp;</a>
            <ul class="dropdown-menu-nav-child">
                <li><a><i class="layui-icon layui-icon-camera"></i>上传头像</a></li>
                <li><a><i class="layui-icon layui-icon-picture-fine"></i>上传照片</a></li>
            </ul>
        </li>
    </ul>
</script>
<!-- 表格性别选择列 -->
<script type="text/html" id="tbBasicTbSex">
    <div class="ew-select-fixed">
        <select lay-filter="tbBasicTbSexSel">
            <option value="">请选择</option>
            <option value="男" {{d.sex=='男'?'selected':''}}>男</option>
            <option value="女" {{d.sex=='女'?'selected':''}}>女</option>
        </select>
    </div>
</script>
<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'dropdown'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var dropdown = layui.dropdown;

        /* 渲染表格 */
        var insTb = table.render({
            elem: '#tbBasicTable',
            url: '../../../json/user.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'checkbox'},
                {type: 'numbers'},
                {field: 'username', title: '账号', align: 'center', sort: true},
                {field: 'nickName', title: '用户名', align: 'center', sort: true},
                {field: 'phone', title: '手机号', align: 'center', sort: true},
                {field: 'sex', title: '性别', templet: '#tbBasicTbSex', sort: true},
                {
                    field: 'createTime', title: '创建时间', templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, align: 'center', sort: true
                },
                {
                    field: 'state', title: '状态', templet: function (d) {
                        var strs = [
                            '<span class="text-success">正常</span>',
                            '<span class="text-danger">锁定</span>'
                        ];
                        return strs[d.state];
                    }, align: 'center', sort: true
                },
                {title: '操作', toolbar: '#tbBasicTbBar', align: 'center', minWidth: 200}
            ]]
        });

        /* 表格搜索 */
        form.on('submit(tbBasicTbSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格工具条点击事件 */
        table.on('tool(tbBasicTable)', function (obj) {
            var data = obj.data; // 获得当前行数据
            if (obj.event === 'edit') { // 修改
                layer.msg('点击了修改');
            } else if (obj.event === 'del') { // 删除
                layer.msg('点击了删除');
            } else if (obj.event === 'view') { // 查看
                layer.msg('点击了查看');
            } else if (obj.event === 'reset') { // 重置密码
                layer.msg('点击了重置密码');
            } else if (obj.event === 'lock') { // 锁定
                layer.msg('点击了锁定');
            }
            dropdown.hideAll();
        });

        // 导出excel
        $('#tbBasicExportBtn').click(function () {
            var checkRows = table.checkStatus('tbBasicTable');
            if (checkRows.data.length === 0) {
                layer.msg('请选择要导出的数据', {icon: 2});
            } else {
                table.exportFile(insTb.config.id, checkRows.data, 'xls');
            }
        });

    });
</script>
</body>
</html>