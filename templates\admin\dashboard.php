<?php include __DIR__ . '/header.php'; ?>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    用户统计
                    <span class="layui-badge layui-bg-blue layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font"><?php echo $stats['users']['total']; ?></p>
                    <p>
                        今日新增
                        <span class="layuiadmin-span-color"><?php echo $stats['users']['today']; ?> <i class="layui-inline layui-icon layui-icon-user"></i></span>
                    </p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    订单统计
                    <span class="layui-badge layui-bg-cyan layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font"><?php echo $stats['orders']['total']; ?></p>
                    <p>
                        今日订单
                        <span class="layuiadmin-span-color"><?php echo $stats['orders']['today']; ?> <i class="layui-inline layui-icon layui-icon-rmb"></i></span>
                    </p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    API调用
                    <span class="layui-badge layui-bg-green layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font"><?php echo $stats['api_calls']['total']; ?></p>
                    <p>
                        今日调用
                        <span class="layuiadmin-span-color"><?php echo $stats['api_calls']['today']; ?> <i class="layui-inline layui-icon layui-icon-chart"></i></span>
                    </p>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    商家统计
                    <span class="layui-badge layui-bg-orange layuiadmin-badge">总</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font"><?php echo $stats['merchants']['total']; ?></p>
                    <p>
                        待审核
                        <span class="layuiadmin-span-color"><?php echo $stats['merchants']['pending']; ?> <i class="layui-inline layui-icon layui-icon-app"></i></span>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="layui-col-sm12">
            <div class="layui-card">
                <div class="layui-card-header">收入统计</div>
                <div class="layui-card-body">
                    <div class="layui-row">
                        <div class="layui-col-sm8">
                            <div class="layui-card-body" style="min-height: 300px;" id="echarts-income"></div>
                        </div>
                        <div class="layui-col-sm4">
                            <div class="layui-card">
                                <div class="layui-card-header">收入概览</div>
                                <div class="layui-card-body">
                                    <table class="layui-table layuiadmin-page-table" lay-skin="line">
                                        <tbody>
                                            <tr>
                                                <td>总收入</td>
                                                <td>
                                                    <span style="color: #FF5722;">￥<?php echo number_format($stats['orders']['amount_total'], 2); ?></span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>今日收入</td>
                                                <td>
                                                    <span style="color: #FF5722;">￥<?php echo number_format($stats['orders']['amount_today'], 2); ?></span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>待处理工单</td>
                                                <td>
                                                    <span style="color: #01AAED;"><?php echo $stats['tickets']['pending']; ?></span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>API总数</td>
                                                <td>
                                                    <span style="color: #01AAED;"><?php echo $stats['apis']['total']; ?></span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-col-sm12">
            <div class="layui-card">
                <div class="layui-card-header">系统信息</div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <colgroup>
                            <col width="150">
                            <col>
                        </colgroup>
                        <tbody>
                            <tr>
                                <td>服务器操作系统</td>
                                <td><?php echo PHP_OS; ?></td>
                            </tr>
                            <tr>
                                <td>PHP版本</td>
                                <td><?php echo PHP_VERSION; ?></td>
                            </tr>
                            <tr>
                                <td>MySQL版本</td>
                                <td><?php 
                                    try {
                                        echo $this->db->query("SELECT VERSION() as version", [], true)['version'] ?? '未知';
                                    } catch (Exception $e) {
                                        echo '未知';
                                    }
                                ?></td>
                            </tr>
                            <tr>
                                <td>服务器时间</td>
                                <td><?php echo date('Y-m-d H:i:s'); ?></td>
                            </tr>
                            <tr>
                                <td>上传限制</td>
                                <td><?php echo ini_get('upload_max_filesize'); ?></td>
                            </tr>
                            <tr>
                                <td>执行时间限制</td>
                                <td><?php echo ini_get('max_execution_time'); ?>秒</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['admin', 'echarts'], function () {
        var $ = layui.jquery;
        var echarts = layui.echarts;
        
        // 收入统计图表
        var echartsIncome = echarts.init(document.getElementById('echarts-income'));
        
        // 模拟数据
        var days = [];
        var data = [];
        
        // 获取最近7天的日期
        var now = new Date();
        for (var i = 6; i >= 0; i--) {
            var date = new Date();
            date.setDate(now.getDate() - i);
            days.push(date.getMonth() + 1 + '/' + date.getDate());
            // 模拟数据
            data.push(Math.floor(Math.random() * 1000));
        }
        
        // 指定图表的配置项和数据
        var option = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['收入金额']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: days
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                name: '收入金额',
                type: 'line',
                stack: '总量',
                areaStyle: {},
                data: data,
                itemStyle: {
                    normal: {
                        color: '#FF5722'
                    }
                }
            }]
        };
        
        // 使用刚指定的配置项和数据显示图表。
        echartsIncome.setOption(option);
        
        // 窗口大小改变时，重置图表大小
        window.onresize = function () {
            echartsIncome.resize();
        };
    });
</script>

<?php include __DIR__ . '/footer.php'; ?>