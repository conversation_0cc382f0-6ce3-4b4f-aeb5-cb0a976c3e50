<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>管理员登录 - <?php echo $site_config['site_name']; ?></title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/css/login.css"/>
    <style>
        body {
            background-image: url(/Easyweb/assets/images/bg-login.jpg);
            background-repeat: no-repeat;
            background-size: cover;
            min-height: 100vh;
        }
        .login-wrapper {
            max-width: 420px;
            padding: 20px;
            margin: 0 auto;
            position: relative;
            box-sizing: border-box;
            z-index: 2;
        }
        .login-wrapper .layui-form {
            padding: 25px 30px;
            background-color: #fff;
            box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.19);
            box-sizing: border-box;
            border-radius: 4px;
        }
        .login-wrapper .layui-form-item {
            margin-bottom: 25px;
        }
        .login-wrapper .layui-form-item:last-child {
            margin-bottom: 0;
        }
        .login-wrapper .layui-input {
            height: 46px;
            line-height: 46px;
            border-radius: 2px !important;
        }
        .login-wrapper .layui-input:focus {
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
        }
        .login-wrapper .login-captcha {
            width: 100%;
            cursor: pointer;
        }
        .login-wrapper .login-other > * {
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
            font-size: 14px;
        }
        .login-wrapper .login-other a {
            color: #009688;
        }
        .login-wrapper .login-other .layui-icon {
            position: relative;
            top: 2px;
            font-size: 26px;
        }
        .login-wrapper .login-other .layui-icon-login-qq {
            color: #3492ED;
        }
        .login-wrapper .login-other .layui-icon-login-wechat {
            color: #4DAF29;
        }
        .login-wrapper .login-other .layui-icon-login-weibo {
            color: #CF1900;
        }
        .login-copyright {
            color: #fff;
            padding-bottom: 20px;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        @media screen and (min-height: 550px) {
            .login-wrapper {
                margin: 0 auto;
                position: absolute;
                width: 100%;
                top: 50%;
                left: 0;
                margin-top: -230px;
            }
            .login-copyright {
                position: absolute;
                bottom: 0;
                width: 100%;
            }
        }
        .layui-btn {
            background-color: #5FB878;
            font-weight: bold;
            font-size: 16px;
            height: 46px;
            line-height: 46px;
            border-radius: 2px !important;
        }
    </style>
</head>
<body>
<div class="login-wrapper">
    <div class="login-header">
        <img src="<?php echo $site_config['site_logo'] ?: '/Easyweb/assets/images/logo.png'; ?>" alt="logo">
        <h2><?php echo $site_config['site_name']; ?> - 管理后台</h2>
    </div>
    <form class="layui-form">
        <div class="layui-form-item">
            <input type="text" name="username" placeholder="用户名" autocomplete="off" class="layui-input" lay-verify="required">
        </div>
        <div class="layui-form-item">
            <input type="password" name="password" placeholder="密码" autocomplete="off" class="layui-input" lay-verify="required">
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-xs7">
                    <input type="text" name="captcha" placeholder="验证码" autocomplete="off" class="layui-input" lay-verify="required">
                </div>
                <div class="layui-col-xs5" style="padding-left: 10px;">
                    <img class="login-captcha" src="/admin/captcha" alt="验证码">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="loginSubmit">登 录</button>
        </div>
    </form>
</div>
<div class="login-copyright">
    <?php echo $site_config['site_copyright']; ?>
</div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script>
    layui.use(['layer', 'form'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        
        // 表单提交
        form.on('submit(loginSubmit)', function (data) {
            $.post('/admin/login', data.field, function (res) {
                if (res.code === 0) {
                    layer.msg('登录成功', {icon: 1, time: 1500}, function () {
                        location.href = '/admin';
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                    // 刷新验证码
                    $('.login-captcha').attr('src', '/admin/captcha?t=' + new Date().getTime());
                }
            }, 'json');
            return false;
        });
        
        // 点击刷新验证码
        $('.login-captcha').click(function () {
            this.src = '/admin/captcha?t=' + new Date().getTime();
        });
    });
</script>
</body>
</html>