<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API参数管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .layui-card-body {
            padding: 15px;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-table-tool-temp {
            padding-right: 0;
        }
        .layui-table-cell {
            height: auto;
            line-height: 28px;
            padding: 6px 15px;
            position: relative;
            box-sizing: border-box;
        }
        .param-required {
            color: #FF5722;
        }
        .param-optional {
            color: #01AAED;
        }
        .param-sort {
            width: 60px;
            height: 30px;
            text-align: center;
        }
        .api-info {
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f8f8;
            border-radius: 2px;
        }
        .api-info-item {
            margin-bottom: 10px;
        }
        .api-info-item:last-child {
            margin-bottom: 0;
        }
        .api-info-label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
        .api-info-value {
            display: inline-block;
        }
        .api-method {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            color: #fff;
            font-size: 12px;
        }
        .api-method.get {
            background-color: #5FB878;
        }
        .api-method.post {
            background-color: #1E9FFF;
        }
        .api-method.put {
            background-color: #FFB800;
        }
        .api-method.delete {
            background-color: #FF5722;
        }
        .api-method.patch {
            background-color: #01AAED;
        }
        .api-method.options {
            background-color: #2F4056;
        }
        .api-method.head {
            background-color: #393D49;
        }
        .api-price {
            font-weight: bold;
            color: #FF5722;
        }
        .api-price.free {
            color: #5FB878;
        }
        .param-type-badge {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            color: #fff;
            font-size: 12px;
            background-color: #1E9FFF;
        }
        .param-type-badge.string {
            background-color: #5FB878;
        }
        .param-type-badge.number {
            background-color: #1E9FFF;
        }
        .param-type-badge.boolean {
            background-color: #FFB800;
        }
        .param-type-badge.array {
            background-color: #FF5722;
        }
        .param-type-badge.object {
            background-color: #01AAED;
        }
        .param-type-badge.file {
            background-color: #2F4056;
        }
        .param-type-badge.enum {
            background-color: #393D49;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">API参数管理</div>
        <div class="layui-card-body">
            <!-- API信息 -->
            <div class="api-info">
                <div class="api-info-item">
                    <span class="api-info-label">接口名称：</span>
                    <span class="api-info-value"><?php echo $api['name']; ?></span>
                </div>
                <div class="api-info-item">
                    <span class="api-info-label">请求方式：</span>
                    <span class="api-info-value">
                        <span class="api-method <?php echo strtolower($api['method']); ?>"><?php echo $api['method']; ?></span>
                    </span>
                </div>
                <div class="api-info-item">
                    <span class="api-info-label">接口地址：</span>
                    <span class="api-info-value"><?php echo $api['endpoint']; ?></span>
                </div>
                <div class="api-info-item">
                    <span class="api-info-label">价格：</span>
                    <span class="api-info-value">
                        <?php if ($api['is_free']): ?>
                        <span class="api-price free">免费</span>
                        <?php else: ?>
                        <span class="api-price">￥<?php echo $api['price']; ?>/<?php echo $api['price_unit']; ?></span>
                        <?php endif; ?>
                    </span>
                </div>
                <div class="api-info-item">
                    <span class="api-info-label">所属分类：</span>
                    <span class="api-info-value"><?php echo $api['category_name']; ?></span>
                </div>
            </div>
            
            <div class="layui-tab layui-tab-brief" lay-filter="paramTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">请求参数</li>
                    <li>返回参数</li>
                    <li>错误码</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 请求参数 -->
                    <div class="layui-tab-item layui-show">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input type="text" name="keyword" placeholder="参数名称" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <select name="required">
                                            <option value="">全部</option>
                                            <option value="1">必填</option>
                                            <option value="0">选填</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button class="layui-btn icon-btn" lay-filter="searchRequestBtn" lay-submit>
                                        <i class="layui-icon layui-icon-search"></i>搜索
                                    </button>
                                    <button class="layui-btn icon-btn" id="addRequestBtn">
                                        <i class="layui-icon layui-icon-add-1"></i>添加
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <table id="requestParamTable" lay-filter="requestParamTable"></table>
                    </div>
                    
                    <!-- 返回参数 -->
                    <div class="layui-tab-item">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input type="text" name="keyword" placeholder="参数名称" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button class="layui-btn icon-btn" lay-filter="searchResponseBtn" lay-submit>
                                        <i class="layui-icon layui-icon-search"></i>搜索
                                    </button>
                                    <button class="layui-btn icon-btn" id="addResponseBtn">
                                        <i class="layui-icon layui-icon-add-1"></i>添加
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <table id="responseParamTable" lay-filter="responseParamTable"></table>
                    </div>
                    
                    <!-- 错误码 -->
                    <div class="layui-tab-item">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input type="text" name="keyword" placeholder="错误码/错误信息" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button class="layui-btn icon-btn" lay-filter="searchErrorBtn" lay-submit>
                                        <i class="layui-icon layui-icon-search"></i>搜索
                                    </button>
                                    <button class="layui-btn icon-btn" id="addErrorBtn">
                                        <i class="layui-icon layui-icon-add-1"></i>添加
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <table id="errorCodeTable" lay-filter="errorCodeTable"></table>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item" style="margin-top: 15px;">
                <div class="layui-input-block" style="margin-left: 0;">
                    <button type="button" class="layui-btn layui-btn-primary" id="backBtn">返回</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 表格必填列 -->
<script type="text/html" id="requiredTpl">
    {{# if(d.required == 1){ }}
    <span class="param-required">必填</span>
    {{# } else { }}
    <span class="param-optional">选填</span>
    {{# } }}
</script>

<!-- 表格类型列 -->
<script type="text/html" id="typeTpl">
    <span class="param-type-badge {{d.type}}">{{d.type}}</span>
</script>

<!-- 添加/编辑请求参数弹窗 -->
<script type="text/html" id="requestParamForm">
    <form class="layui-form" lay-filter="requestParamForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <input type="hidden" name="api_id" value="<?php echo $api['id']; ?>">
        <input type="hidden" name="param_type" value="request">
        
        <div class="layui-form-item">
            <label class="layui-form-label">参数名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入参数名称" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">参数类型</label>
            <div class="layui-input-block">
                <select name="type" lay-verify="required" lay-filter="paramType">
                    <option value="string">字符串(string)</option>
                    <option value="number">数字(number)</option>
                    <option value="integer">整数(integer)</option>
                    <option value="boolean">布尔值(boolean)</option>
                    <option value="array">数组(array)</option>
                    <option value="object">对象(object)</option>
                    <option value="file">文件(file)</option>
                    <option value="enum">枚举(enum)</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item" id="enumValuesItem" style="display: none;">
            <label class="layui-form-label">枚举值</label>
            <div class="layui-input-block">
                <textarea name="enum_values" placeholder="请输入枚举值，多个值用英文逗号分隔" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">是否必填</label>
            <div class="layui-input-block">
                <input type="radio" name="required" value="1" title="必填" checked>
                <input type="radio" name="required" value="0" title="选填">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">默认值</label>
            <div class="layui-input-block">
                <input type="text" name="default_value" placeholder="请输入默认值" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">示例值</label>
            <div class="layui-input-block">
                <input type="text" name="example" placeholder="请输入示例值" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">参数描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入参数描述" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort" value="100" placeholder="数字越小越靠前" lay-verify="required|number" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="requestParamSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- 添加/编辑返回参数弹窗 -->
<script type="text/html" id="responseParamForm">
    <form class="layui-form" lay-filter="responseParamForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <input type="hidden" name="api_id" value="<?php echo $api['id']; ?>">
        <input type="hidden" name="param_type" value="response">
        
        <div class="layui-form-item">
            <label class="layui-form-label">参数名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入参数名称" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">参数类型</label>
            <div class="layui-input-block">
                <select name="type" lay-verify="required">
                    <option value="string">字符串(string)</option>
                    <option value="number">数字(number)</option>
                    <option value="integer">整数(integer)</option>
                    <option value="boolean">布尔值(boolean)</option>
                    <option value="array">数组(array)</option>
                    <option value="object">对象(object)</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">示例值</label>
            <div class="layui-input-block">
                <input type="text" name="example" placeholder="请输入示例值" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">参数描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入参数描述" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort" value="100" placeholder="数字越小越靠前" lay-verify="required|number" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="responseParamSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- 添加/编辑错误码弹窗 -->
<script type="text/html" id="errorCodeForm">
    <form class="layui-form" lay-filter="errorCodeForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <input type="hidden" name="api_id" value="<?php echo $api['id']; ?>">
        
        <div class="layui-form-item">
            <label class="layui-form-label">错误码</label>
            <div class="layui-input-block">
                <input type="text" name="code" placeholder="请输入错误码" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">错误信息</label>
            <div class="layui-input-block">
                <input type="text" name="message" placeholder="请输入错误信息" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">错误描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入错误描述" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">解决方案</label>
            <div class="layui-input-block">
                <textarea name="solution" placeholder="请输入解决方案" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort" value="100" placeholder="数字越小越靠前" lay-verify="required|number" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="errorCodeSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'element'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var element = layui.element;
    
    // API ID
    var apiId = <?php echo $api['id']; ?>;
    
    // 渲染请求参数表格
    var requestParamTable = table.render({
        elem: '#requestParamTable',
        url: '/admin/api/params/list?api_id=' + apiId + '&param_type=request',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'name', title: '参数名称', sort: true},
            {field: 'type', title: '参数类型', templet: '#typeTpl'},
            {field: 'required', title: '是否必填', templet: '#requiredTpl', width: 100},
            {field: 'default_value', title: '默认值'},
            {field: 'example', title: '示例值'},
            {field: 'description', title: '参数描述'},
            {field: 'sort', title: '排序', sort: true, width: 100, templet: function(d) {
                return '<input type="number" class="param-sort" data-id="' + d.id + '" data-type="request" value="' + d.sort + '">';
            }},
            {title: '操作', toolbar: '#tableBar', width: 120, align: 'center'}
        ]]
    });
    
    // 渲染返回参数表格
    var responseParamTable = table.render({
        elem: '#responseParamTable',
        url: '/admin/api/params/list?api_id=' + apiId + '&param_type=response',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'name', title: '参数名称', sort: true},
            {field: 'type', title: '参数类型', templet: '#typeTpl'},
            {field: 'example', title: '示例值'},
            {field: 'description', title: '参数描述'},
            {field: 'sort', title: '排序', sort: true, width: 100, templet: function(d) {
                return '<input type="number" class="param-sort" data-id="' + d.id + '" data-type="response" value="' + d.sort + '">';
            }},
            {title: '操作', toolbar: '#tableBar', width: 120, align: 'center'}
        ]]
    });
    
    // 渲染错误码表格
    var errorCodeTable = table.render({
        elem: '#errorCodeTable',
        url: '/admin/api/error_codes/list?api_id=' + apiId,
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'code', title: '错误码', sort: true},
            {field: 'message', title: '错误信息'},
            {field: 'description', title: '错误描述'},
            {field: 'solution', title: '解决方案'},
            {field: 'sort', title: '排序', sort: true, width: 100, templet: function(d) {
                return '<input type="number" class="param-sort" data-id="' + d.id + '" data-type="error" value="' + d.sort + '">';
            }},
            {title: '操作', toolbar: '#tableBar', width: 120, align: 'center'}
        ]]
    });
    
    // 搜索请求参数
    form.on('submit(searchRequestBtn)', function(data) {
        requestParamTable.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 搜索返回参数
    form.on('submit(searchResponseBtn)', function(data) {
        responseParamTable.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 搜索错误码
    form.on('submit(searchErrorBtn)', function(data) {
        errorCodeTable.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 添加请求参数按钮点击事件
    $('#addRequestBtn').click(function() {
        showRequestParamForm();
    });
    
    // 添加返回参数按钮点击事件
    $('#addResponseBtn').click(function() {
        showResponseParamForm();
    });
    
    // 添加错误码按钮点击事件
    $('#addErrorBtn').click(function() {
        showErrorCodeForm();
    });
    
    // 返回按钮点击事件
    $('#backBtn').click(function() {
        location.href = '/admin/api/list';
    });
    
    // 监听请求参数表格工具条
    table.on('tool(requestParamTable)', function(obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showRequestParamForm(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该参数吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(index) {
                layer.close(index);
                var loadIndex = layer.load(2);
                
                $.post('/admin/api/params/delete', {
                    id: data.id
                }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                tableObj.reload();
            }
        }, 'json');
    });
    
    // 监听参数类型选择
    form.on('select(paramType)', function(data) {
        if (data.value === 'enum') {
            $('#enumValuesItem').show();
        } else {
            $('#enumValuesItem').hide();
        }
    });
    
    // 显示请求参数表单弹窗
    function showRequestParamForm(data) {
        admin.open({
            type: 1,
            title: (data ? '修改' : '添加') + '请求参数',
            content: $('#requestParamForm').html(),
            area: ['550px', '650px'],
            success: function(layero, dIndex) {
                // 回显表单数据
                if (data) {
                    form.val('requestParamForm', data);
                    
                    // 处理枚举值显示
                    if (data.type === 'enum') {
                        $('#enumValuesItem').show();
                    }
                }
                
                // 表单提交事件
                form.on('submit(requestParamSubmit)', function(data) {
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/api/params/' + (data.field.id ? 'update' : 'add'), data.field, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            requestParamTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
                
                // 禁止弹窗出现滚动条
                $(layero).children('.layui-layer-content').css('overflow', 'visible');
            }
        });
    }
    
    // 显示返回参数表单弹窗
    function showResponseParamForm(data) {
        admin.open({
            type: 1,
            title: (data ? '修改' : '添加') + '返回参数',
            content: $('#responseParamForm').html(),
            area: ['550px', '500px'],
            success: function(layero, dIndex) {
                // 回显表单数据
                if (data) {
                    form.val('responseParamForm', data);
                }
                
                // 表单提交事件
                form.on('submit(responseParamSubmit)', function(data) {
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/api/params/' + (data.field.id ? 'update' : 'add'), data.field, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            responseParamTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
                
                // 禁止弹窗出现滚动条
                $(layero).children('.layui-layer-content').css('overflow', 'visible');
            }
        });
    }
    
    // 显示错误码表单弹窗
    function showErrorCodeForm(data) {
        admin.open({
            type: 1,
            title: (data ? '修改' : '添加') + '错误码',
            content: $('#errorCodeForm').html(),
            area: ['550px', '550px'],
            success: function(layero, dIndex) {
                // 回显表单数据
                if (data) {
                    form.val('errorCodeForm', data);
                }
                
                // 表单提交事件
                form.on('submit(errorCodeSubmit)', function(data) {
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/api/error_codes/' + (data.field.id ? 'update' : 'add'), data.field, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            errorCodeTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
                
                // 禁止弹窗出现滚动条
                $(layero).children('.layui-layer-content').css('overflow', 'visible');
            }
        });
    }
});
</script>
</body>
</html>
                        requestParamTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 监听返回参数表格工具条
    table.on('tool(responseParamTable)', function(obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showResponseParamForm(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该参数吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(index) {
                layer.close(index);
                var loadIndex = layer.load(2);
                
                $.post('/admin/api/params/delete', {
                    id: data.id
                }, function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        responseParamTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 监听错误码表格工具条
    table.on('tool(errorCodeTable)', function(obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showErrorCodeForm(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该错误码吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(index) {
                layer.close(index);
                var loadIndex = layer.load(2);
                
                $.post('/admin/api/error_codes/delete', {
                    id: data.id
                }, function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        errorCodeTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 监听排序修改
    $(document).on('change', '.param-sort', function() {
        var id = $(this).data('id');
        var type = $(this).data('type');
        var sort = $(this).val();
        var url = '';
        var tableObj = null;
        
        if (type === 'request' || type === 'response') {
            url = '/admin/api/params/sort';
            tableObj = type === 'request' ? requestParamTable : responseParamTable;
        } else if (type === 'error') {
            url = '/admin/api/error_codes/sort';
            tableObj = errorCodeTable;
        }
        
        var loadIndex = layer.load(2);
        $.post(url, {
            id: id,
            sort: sort
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});