-- 用户等级表
CREATE TABLE IF NOT EXISTS `user_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `description` varchar(255) DEFAULT NULL COMMENT '等级描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '等级图标',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '等级价格',
  `discount_rate` decimal(3,2) NOT NULL DEFAULT 1.00 COMMENT '折扣率',
  `daily_request_limit` int(11) NOT NULL DEFAULT 0 COMMENT '每日请求次数限制，0表示无限制',
  `features` text DEFAULT NULL COMMENT '等级特权，JSON格式',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级表';

-- 用户等级关联表
CREATE TABLE IF NOT EXISTS `user_level_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `level_id` int(11) NOT NULL COMMENT '等级ID',
  `expire_time` int(11) DEFAULT NULL COMMENT '过期时间，NULL表示永久',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `level_id` (`level_id`),
  KEY `expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级关联表';

-- 用户等级升级订单表
CREATE TABLE IF NOT EXISTS `user_level_upgrades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `from_level` int(11) NOT NULL COMMENT '原等级',
  `to_level` int(11) NOT NULL COMMENT '目标等级',
  `duration` int(11) NOT NULL COMMENT '购买时长（月）',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待支付，1已完成',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `complete_time` int(11) DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级升级订单表';

-- 用户API请求记录表
CREATE TABLE IF NOT EXISTS `user_api_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `merchant_api_id` int(11) DEFAULT NULL COMMENT '商家API ID',
  `request_time` int(11) NOT NULL COMMENT '请求时间',
  `request_ip` varchar(50) NOT NULL COMMENT '请求IP',
  `request_params` text DEFAULT NULL COMMENT '请求参数',
  `response_code` int(11) NOT NULL COMMENT '响应状态码',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间',
  `response_data` text DEFAULT NULL COMMENT '响应数据',
  `cost` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '消费金额',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `api_id` (`api_id`),
  KEY `merchant_api_id` (`merchant_api_id`),
  KEY `request_time` (`request_time`),
  KEY `request_ip` (`request_ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户API请求记录表';

-- 用户API密钥表
CREATE TABLE IF NOT EXISTS `user_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `name` varchar(100) DEFAULT NULL COMMENT '密钥名称',
  `ip_whitelist` text DEFAULT NULL COMMENT 'IP白名单，多个IP用逗号分隔',
  `domain_whitelist` text DEFAULT NULL COMMENT '域名白名单，多个域名用逗号分隔',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_key` (`api_key`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户API密钥表';

-- 用户余额表
CREATE TABLE IF NOT EXISTS `user_balances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `balance` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额表';

-- 用户余额变动记录表
CREATE TABLE IF NOT EXISTS `user_balance_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '类型：recharge充值，consume消费，refund退款，admin管理员操作',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `before_balance` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `after_balance` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `related_id` int(11) DEFAULT NULL COMMENT '关联ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额变动记录表';