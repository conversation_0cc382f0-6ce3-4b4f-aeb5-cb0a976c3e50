<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>商家等级管理 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
</head>
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-user"></i> 商家等级管理
            <button class="layui-btn layui-btn-sm" id="btnAdd" style="position: absolute; right: 15px; top: 10px;">
                <i class="layui-icon">&#xe654;</i> 添加等级
            </button>
        </div>
        <div class="layui-card-body">
            <table id="tableLevel" lay-filter="tableLevel"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
</script>

<!-- 状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="ckStatus" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status==1?'checked':''}}/>
</script>

<!-- 添加/编辑弹窗 -->
<script type="text/html" id="modelLevel">
    <form id="modelLevelForm" lay-filter="modelLevelForm" class="layui-form model-form">
        <input name="id" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label">等级名称</label>
            <div class="layui-input-block">
                <input name="name" placeholder="请输入等级名称" type="text" class="layui-input" maxlength="20"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">等级价格</label>
            <div class="layui-input-block">
                <input name="price" placeholder="请输入等级价格" type="number" class="layui-input" min="0" step="0.01"
                       lay-verType="tips" lay-verify="required|number" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">折扣率</label>
            <div class="layui-input-block">
                <input name="discount_rate" placeholder="请输入折扣率，如0.9表示9折" type="number" class="layui-input" min="0" max="1" step="0.01"
                       lay-verType="tips" lay-verify="required|number" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">API数量上限</label>
            <div class="layui-input-block">
                <input name="max_api_count" placeholder="请输入API数量上限，0表示无限制" type="number" class="layui-input" min="0" step="1"
                       lay-verType="tips" lay-verify="required|number" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">等级描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入等级描述" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">等级特权</label>
            <div class="layui-input-block">
                <div class="layui-form-item" style="margin-bottom: 5px;">
                    <input type="checkbox" name="features[custom_price]" title="自定义API价格" lay-skin="primary">
                </div>
                <div class="layui-form-item" style="margin-bottom: 5px;">
                    <input type="checkbox" name="features[api_doc]" title="自定义API文档" lay-skin="primary">
                </div>
                <div class="layui-form-item" style="margin-bottom: 5px;">
                    <input type="checkbox" name="features[custom_domain]" title="自定义域名" lay-skin="primary">
                </div>
                <div class="layui-form-item" style="margin-bottom: 5px;">
                    <input type="checkbox" name="features[priority_support]" title="优先技术支持" lay-skin="primary">
                </div>
                <div class="layui-form-item" style="margin-bottom: 5px;">
                    <input type="checkbox" name="features[api_analytics]" title="API调用分析" lay-skin="primary">
                </div>
                <div class="layui-form-item" style="margin-bottom: 5px;">
                    <input type="checkbox" name="features[custom_style]" title="自定义样式" lay-skin="primary">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="modelSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script type="text/javascript" src="/Easyweb/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="/Easyweb/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;

        // 渲染表格
        var insTb = table.render({
            elem: '#tableLevel',
            url: '/admin/merchant/level_list',
            page: true,
            toolbar: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'id', title: 'ID', sort: true},
                {field: 'name', title: '等级名称'},
                {field: 'price', title: '价格', templet: function(d) {
                    return '¥' + d.price.toFixed(2);
                }},
                {field: 'discount_rate', title: '折扣率', templet: function(d) {
                    return (d.discount_rate * 100).toFixed(0) + '%';
                }},
                {field: 'max_api_count', title: 'API数量上限', templet: function(d) {
                    return d.max_api_count > 0 ? d.max_api_count : '无限制';
                }},
                {field: 'description', title: '描述'},
                {field: 'status', title: '状态', templet: '#statusTpl'},
                {field: 'create_time', title: '创建时间', templet: function(d) {
                    return util.toDateString(d.create_time * 1000);
                }},
                {title: '操作', toolbar: '#tableBar', align: 'center', width: 120}
            ]]
        });

        // 添加
        $('#btnAdd').click(function () {
            showEditModel();
        });

        // 表格工具条点击事件
        table.on('tool(tableLevel)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            if (layEvent === 'edit') { // 修改
                showEditModel(data);
            } else if (layEvent === 'del') { // 删除
                doDel(data.id, data.name);
            }
        });

        // 表单提交事件
        form.on('submit(modelSubmit)', function (data) {
            var loadIndex = layer.load(2);
            
            // 处理特权数据
            var features = [];
            for (var key in data.field) {
                if (key.indexOf('features[') === 0) {
                    var featureName = key.substring(9, key.length - 1);
                    features.push(featureName);
                    delete data.field[key];
                }
            }
            data.field.features = JSON.stringify(features);
            
            $.post('/admin/merchant/level_save', data.field, function (res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.closeAll('page');
                    layer.msg(res.msg, {icon: 1});
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
            return false;
        });

        // 显示表单弹窗
        function showEditModel(mLevel) {
            admin.open({
                type: 1,
                title: (mLevel ? '修改' : '添加') + '商家等级',
                content: $('#modelLevel').html(),
                success: function (layero, dIndex) {
                    form.render();
                    
                    // 回显数据
                    if (mLevel) {
                        form.val('modelLevelForm', mLevel);
                        
                        // 回显特权
                        try {
                            var features = JSON.parse(mLevel.features);
                            for (var i = 0; i < features.length; i++) {
                                $('input[name="features[' + features[i] + ']"]').prop('checked', true);
                            }
                            form.render('checkbox');
                        } catch (e) {}
                    }
                }
            });
        }

        // 删除
        function doDel(id, name) {
            layer.confirm('确定要删除"' + name + '"吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                var loadIndex = layer.load(2);
                $.post('/admin/merchant/level_del', {
                    id: id
                }, function (res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }

        // 修改状态
        form.on('switch(ckStatus)', function (obj) {
            var loadIndex = layer.load(2);
            $.post('/admin/merchant/level_status', {
                id: obj.value,
                status: obj.elem.checked ? 1 : 0
            }, function (res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                    $(obj.elem).prop('checked', !obj.elem.checked);
                    form.render('checkbox');
                }
            }, 'json');
        });
    });
</script>
</body>
</html>