<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>商家管理 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
</head>
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-user"></i> 商家管理
            <div class="layui-btn-group" style="position: absolute; right: 15px; top: 10px;">
                <button class="layui-btn layui-btn-sm" id="btnApply">
                    <i class="layui-icon">&#xe605;</i> 入驻申请
                </button>
                <button class="layui-btn layui-btn-sm" id="btnMerchant">
                    <i class="layui-icon">&#xe612;</i> 商家列表
                </button>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 商家入驻申请表格 -->
            <div id="applyTableView">
                <div class="layui-form toolbar">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto">申请状态:</label>
                            <div class="layui-input-inline">
                                <select id="applyStatus">
                                    <option value="">所有状态</option>
                                    <option value="0">待审核</option>
                                    <option value="1">已通过</option>
                                    <option value="2">已拒绝</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto">商家名称:</label>
                            <div class="layui-input-inline">
                                <input id="applyName" class="layui-input" placeholder="输入商家名称"/>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn icon-btn" id="applySearchBtn">
                                <i class="layui-icon">&#xe615;</i>搜索
                            </button>
                        </div>
                    </div>
                </div>
                <table id="applyTable" lay-filter="applyTable"></table>
            </div>
            
            <!-- 商家列表表格 -->
            <div id="merchantTableView" style="display: none;">
                <div class="layui-form toolbar">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto">商家状态:</label>
                            <div class="layui-input-inline">
                                <select id="merchantStatus">
                                    <option value="">所有状态</option>
                                    <option value="1">正常</option>
                                    <option value="0">已禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label w-auto">商家名称:</label>
                            <div class="layui-input-inline">
                                <input id="merchantName" class="layui-input" placeholder="输入商家名称"/>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn icon-btn" id="merchantSearchBtn">
                                <i class="layui-icon">&#xe615;</i>搜索
                            </button>
                        </div>
                    </div>
                </div>
                <table id="merchantTable" lay-filter="merchantTable"></table>
            </div>
        </div>
    </div>
</div>

<!-- 申请表格操作列 -->
<script type="text/html" id="applyTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="pass">通过</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="reject">拒绝</a>
</script>

<!-- 商家表格操作列 -->
<script type="text/html" id="merchantTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable">{{d.status==1?'禁用':'启用'}}</a>
</script>

<!-- 申请状态 -->
<script type="text/html" id="applyStatusTpl">
    {{#  if(d.status === 0){ }}
    <span class="layui-badge layui-bg-orange">待审核</span>
    {{#  } else if(d.status === 1){ }}
    <span class="layui-badge layui-bg-green">已通过</span>
    {{#  } else if(d.status === 2){ }}
    <span class="layui-badge">已拒绝</span>
    {{#  } }}
</script>

<!-- 商家状态 -->
<script type="text/html" id="merchantStatusTpl">
    <input type="checkbox" lay-filter="ckStatus" value="{{d.id}}" lay-skin="switch" lay-text="正常|禁用" {{d.status==1?'checked':''}}/>
</script>

<!-- 查看申请弹窗 -->
<script type="text/html" id="viewApplyDialog">
    <div style="padding: 20px;">
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">商家名称</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.name}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系人</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.contact}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系电话</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.phone}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系邮箱</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.email}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系地址</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.address}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">营业执照</label>
                <div class="layui-input-block">
                    <img src="{{d.business_license}}" style="max-width: 100%; max-height: 300px;">
                </div>
            </div>
            {{# if(d.id_card_front){ }}
            <div class="layui-form-item">
                <label class="layui-form-label">身份证正面</label>
                <div class="layui-input-block">
                    <img src="{{d.id_card_front}}" style="max-width: 100%; max-height: 300px;">
                </div>
            </div>
            {{# } }}
            {{# if(d.id_card_back){ }}
            <div class="layui-form-item">
                <label class="layui-form-label">身份证背面</label>
                <div class="layui-input-block">
                    <img src="{{d.id_card_back}}" style="max-width: 100%; max-height: 300px;">
                </div>
            </div>
            {{# } }}
            <div class="layui-form-item">
                <label class="layui-form-label">申请描述</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" readonly>{{d.description}}</textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">申请时间</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{layui.util.toDateString(d.create_time * 1000)}}" readonly>
                </div>
            </div>
            {{# if(d.status !== 0){ }}
            <div class="layui-form-item">
                <label class="layui-form-label">审核时间</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{layui.util.toDateString(d.audit_time * 1000)}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">审核备注</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" readonly>{{d.audit_remark}}</textarea>
                </div>
            </div>
            {{# } }}
        </div>
    </div>
</script>

<!-- 审核弹窗 -->
<script type="text/html" id="auditDialog">
    <form id="auditForm" lay-filter="auditForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id}}"/>
        <input name="status" type="hidden" value="{{d.status}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">商家名称</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.name}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系人</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.contact}}" readonly>
            </div>
        </div>
        {{# if(d.status === 1){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">商家等级</label>
            <div class="layui-input-block">
                <select name="level" lay-verify="required">
                    <option value="">请选择商家等级</option>
                </select>
            </div>
        </div>
        {{# } }}
        <div class="layui-form-item">
            <label class="layui-form-label">审核备注</label>
            <div class="layui-input-block">
                <textarea name="audit_remark" placeholder="请输入审核备注" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="auditSubmit" lay-submit>确定</button>
        </div>
    </form>
</script>

<!-- 编辑商家弹窗 -->
<script type="text/html" id="editMerchantDialog">
    <form id="editMerchantForm" lay-filter="editMerchantForm" class="layui-form model-form">
        <input name="id" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label">商家名称</label>
            <div class="layui-input-block">
                <input name="name" placeholder="请输入商家名称" type="text" class="layui-input" maxlength="50"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系人</label>
            <div class="layui-input-block">
                <input name="contact" placeholder="请输入联系人" type="text" class="layui-input" maxlength="20"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系电话</label>
            <div class="layui-input-block">
                <input name="phone" placeholder="请输入联系电话" type="text" class="layui-input" maxlength="20"
                       lay-verType="tips" lay-verify="required|phone" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系邮箱</label>
            <div class="layui-input-block">
                <input name="email" placeholder="请输入联系邮箱" type="text" class="layui-input" maxlength="50"
                       lay-verType="tips" lay-verify="required|email" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系地址</label>
            <div class="layui-input-block">
                <input name="address" placeholder="请输入联系地址" type="text" class="layui-input" maxlength="100"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">商家等级</label>
            <div class="layui-input-block">
                <select name="level" lay-verify="required">
                    <option value="">请选择商家等级</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="正常" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="editMerchantSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script type="text/javascript" src="/Easyweb/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="/Easyweb/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'admin', 'laytpl'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;
        var laytpl = layui.laytpl;
        
        // 渲染申请表格
        var applyTable = table.render({
            elem: '#applyTable',
            url: '/admin/merchant/apply_list',
            page: true,
            toolbar: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'id', title: 'ID', sort: true},
                {field: 'name', title: '商家名称'},
                {field: 'contact', title: '联系人'},
                {field: 'phone', title: '联系电话'},
                {field: 'email', title: '联系邮箱'},
                {field: 'status', title: '状态', templet: '#applyStatusTpl'},
                {field: 'create_time', title: '申请时间', templet: function(d) {
                    return util.toDateString(d.create_time * 1000);
                }},
                {title: '操作', toolbar: '#applyTableBar', align: 'center', width: 180}
            ]]
        });
        
        // 渲染商家表格
        var merchantTable = table.render({
            elem: '#merchantTable',
            url: '/admin/merchant/list',
            page: true,
            toolbar: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'id', title: 'ID', sort: true},
                {field: 'name', title: '商家名称'},
                {field: 'contact', title: '联系人'},
                {field: 'phone', title: '联系电话'},
                {field: 'email', title: '联系邮箱'},
                {field: 'level_name', title: '商家等级'},
                {field: 'api_count', title: 'API数量'},
                {field: 'status', title: '状态', templet: '#merchantStatusTpl'},
                {field: 'create_time', title: '创建时间', templet: function(d) {
                    return util.toDateString(d.create_time * 1000);
                }},
                {title: '操作', toolbar: '#merchantTableBar', align: 'center', width: 120}
            ]]
        });
        
        // 切换视图
        $('#btnApply').click(function() {
            $('#applyTableView').show();
            $('#merchantTableView').hide();
            $(this).addClass('layui-btn-normal').removeClass('layui-btn-primary');
            $('#btnMerchant').addClass('layui-btn-primary').removeClass('layui-btn-normal');
        });
        
        $('#btnMerchant').click(function() {
            $('#applyTableView').hide();
            $('#merchantTableView').show();
            $(this).addClass('layui-btn-normal').removeClass('layui-btn-primary');
            $('#btnApply').addClass('layui-btn-primary').removeClass('layui-btn-normal');
        });
        
        // 搜索申请
        $('#applySearchBtn').click(function() {
            applyTable.reload({
                where: {
                    status: $('#applyStatus').val(),
                    name: $('#applyName').val()
                },
                page: {curr: 1}
            });
        });
        
        // 搜索商家
        $('#merchantSearchBtn').click(function() {
            merchantTable.reload({
                where: {
                    status: $('#merchantStatus').val(),
                    name: $('#merchantName').val()
                },
                page: {curr: 1}
            });
        });
        
        // 申请表格工具条点击事件
        table.on('tool(applyTable)', function(obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'view') { // 查看
                viewApply(data);
            } else if (layEvent === 'pass') { // 通过
                auditApply(data, 1);
            } else if (layEvent === 'reject') { // 拒绝
                auditApply(data, 2);
            }
        });
        
        // 商家表格工具条点击事件
        table.on('tool(merchantTable)', function(obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'edit') { // 编辑
                editMerchant(data);
            } else if (layEvent === 'disable') { // 禁用/启用
                var newStatus = data.status === 1 ? 0 : 1;
                var statusText = newStatus === 1 ? '启用' : '禁用';
                
                layer.confirm('确定要' + statusText + '该商家吗？', {
                    skin: 'layui-layer-admin',
                    shade: .1
                }, function(i) {
                    layer.close(i);
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/merchant/status', {
                        id: data.id,
                        status: newStatus
                    }, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            obj.update({
                                status: newStatus
                            });
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                });
            }
        });
        
        // 查看申请
        function viewApply(data) {
            laytpl($('#viewApplyDialog').html()).render(data, function(html) {
                admin.open({
                    type: 1,
                    title: '查看申请',
                    area: ['600px', '80%'],
                    content: html
                });
            });
        }
        
        // 审核申请
        function auditApply(data, status) {
            if (data.status !== 0) {
                layer.msg('该申请已审核', {icon: 2});
                return;
            }
            
            data.status = status;
            
            laytpl($('#auditDialog').html()).render(data, function(html) {
                admin.open({
                    type: 1,
                    title: status === 1 ? '通过申请' : '拒绝申请',
                    area: ['500px', '400px'],
                    content: html,
                    success: function() {
                        // 如果是通过申请，加载商家等级
                        if (status === 1) {
                            // 获取商家等级列表
                            $.get('/admin/merchant/level_list', {page: 1, limit: 100}, function(res) {
                                if (res.code === 0) {
                                    var levels = res.data;
                                    var options = '<option value="">请选择商家等级</option>';
                                    
                                    for (var i = 0; i < levels.length; i++) {
                                        if (levels[i].status === 1) {
                                            options += '<option value="' + levels[i].id + '">' + levels[i].name + '</option>';
                                        }
                                    }
                                    
                                    $('select[name="level"]').html(options);
                                    form.render('select');
                                }
                            }, 'json');
                        }
                    }
                });
            });
        }
        
        // 审核表单提交
        form.on('submit(auditSubmit)', function(data) {
            var loadIndex = layer.load(2);
            
            $.post('/admin/merchant/audit', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.closeAll('page');
                    layer.msg(res.msg, {icon: 1});
                    applyTable.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
            
            return false;
        });
        
        // 编辑商家
        function editMerchant(data) {
            admin.open({
                type: 1,
                title: '编辑商家',
                area: ['500px', '500px'],
                content: $('#editMerchantDialog').html(),
                success: function() {
                    // 获取商家等级列表
                    $.get('/admin/merchant/level_list', {page: 1, limit: 100}, function(res) {
                        if (res.code === 0) {
                            var levels = res.data;
                            var options = '<option value="">请选择商家等级</option>';
                            
                            for (var i = 0; i < levels.length; i++) {
                                if (levels[i].status === 1) {
                                    options += '<option value="' + levels[i].id + '">' + levels[i].name + '</option>';
                                }
                            }
                            
                            $('select[name="level"]').html(options);
                            
                            // 回显数据
                            form.val('editMerchantForm', data);
                            form.render();
                        }
                    }, 'json');
                }
            });
        }
        
        // 编辑商家表单提交
        form.on('submit(editMerchantSubmit)', function(data) {
            var loadIndex = layer.load(2);
            
            $.post('/admin/merchant/update', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.closeAll('page');
                    layer.msg(res.msg, {icon: 1});
                    merchantTable.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
            
            return false;
        });
        
        // 修改状态
        form.on('switch(ckStatus)', function(obj) {
            var loadIndex = layer.load(2);
            
            $.post('/admin/merchant/status', {
                id: obj.value,
                status: obj.elem.checked ? 1 : 0
            }, function(res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                    $(obj.elem).prop('checked', !obj.elem.checked);
                    form.render('checkbox');
                }
            }, 'json');
        });
    });
</script>
</body>
</html>