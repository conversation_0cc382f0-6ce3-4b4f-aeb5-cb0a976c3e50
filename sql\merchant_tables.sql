-- 商家表
CREATE TABLE IF NOT EXISTS `merchants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '关联用户ID',
  `name` varchar(100) NOT NULL COMMENT '商家名称',
  `contact` varchar(50) NOT NULL COMMENT '联系人',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `email` varchar(100) NOT NULL COMMENT '联系邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '联系地址',
  `business_license` varchar(255) NOT NULL COMMENT '营业执照',
  `id_card_front` varchar(255) DEFAULT NULL COMMENT '身份证正面',
  `id_card_back` varchar(255) DEFAULT NULL COMMENT '身份证背面',
  `description` text DEFAULT NULL COMMENT '商家介绍',
  `logo` varchar(255) DEFAULT NULL COMMENT '商家logo',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '商家等级',
  `balance` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '商家余额',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `level` (`level`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家表';

-- 商家等级表
CREATE TABLE IF NOT EXISTS `merchant_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `description` varchar(255) DEFAULT NULL COMMENT '等级描述',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '等级价格',
  `discount_rate` decimal(3,2) NOT NULL DEFAULT 1.00 COMMENT '折扣率',
  `max_api_count` int(11) NOT NULL DEFAULT 0 COMMENT 'API数量上限，0表示无限制',
  `features` text DEFAULT NULL COMMENT '等级特权，JSON格式',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家等级表';

-- 商家入驻申请表
CREATE TABLE IF NOT EXISTS `merchant_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '关联用户ID',
  `name` varchar(100) NOT NULL COMMENT '商家名称',
  `contact` varchar(50) NOT NULL COMMENT '联系人',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `email` varchar(100) NOT NULL COMMENT '联系邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '联系地址',
  `business_license` varchar(255) NOT NULL COMMENT '营业执照',
  `id_card_front` varchar(255) DEFAULT NULL COMMENT '身份证正面',
  `id_card_back` varchar(255) DEFAULT NULL COMMENT '身份证背面',
  `description` text DEFAULT NULL COMMENT '申请描述',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待审核，1已通过，2已拒绝',
  `create_time` int(11) NOT NULL COMMENT '申请时间',
  `audit_time` int(11) DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家入驻申请表';

-- 商家API表
CREATE TABLE IF NOT EXISTS `merchant_apis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL COMMENT '商家ID',
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `description` text DEFAULT NULL COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `merchant_api` (`merchant_id`,`api_id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `api_id` (`api_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家API表';

-- 商家收入表
CREATE TABLE IF NOT EXISTS `merchant_incomes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL COMMENT '商家ID',
  `merchant_api_id` int(11) NOT NULL COMMENT '商家API ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `merchant_api_id` (`merchant_api_id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家收入表';

-- 商家提现表
CREATE TABLE IF NOT EXISTS `merchant_withdraws` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL COMMENT '商家ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `account_name` varchar(50) NOT NULL COMMENT '收款人',
  `account_number` varchar(50) NOT NULL COMMENT '收款账号',
  `bank_name` varchar(100) NOT NULL COMMENT '开户银行',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待审核，1已通过，2已拒绝',
  `create_time` int(11) NOT NULL COMMENT '申请时间',
  `audit_time` int(11) DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  PRIMARY KEY (`id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家提现表';

-- 商家升级订单表
CREATE TABLE IF NOT EXISTS `merchant_upgrades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `merchant_id` int(11) NOT NULL COMMENT '商家ID',
  `from_level` int(11) NOT NULL COMMENT '原等级',
  `to_level` int(11) NOT NULL COMMENT '目标等级',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待支付，1已完成',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `complete_time` int(11) DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家升级订单表';