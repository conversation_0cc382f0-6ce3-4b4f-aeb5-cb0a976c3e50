<?php
/**
 * API接口模型
 * API管理系统 - API接口数据模型
 */

require_once __DIR__ . '/../core/Model.php';

class ApiInterface extends Model {
    protected $table = 'interfaces';
    protected $fillable = [
        'name', 'path', 'method', 'description', 'category_id', 'merchant_id',
        'price', 'request_params', 'response_example', 'request_headers',
        'timeout', 'retry_count', 'rate_limit', 'status', 'is_public'
    ];
    
    /**
     * 创建API接口
     */
    public function create($data) {
        // 验证路径唯一性
        if ($this->pathExists($data['path'])) {
            throw new Exception('API路径已存在');
        }
        
        // 处理JSON字段
        if (isset($data['request_params']) && is_array($data['request_params'])) {
            $data['request_params'] = json_encode($data['request_params'], JSON_UNESCAPED_UNICODE);
        }
        
        if (isset($data['request_headers']) && is_array($data['request_headers'])) {
            $data['request_headers'] = json_encode($data['request_headers'], JSON_UNESCAPED_UNICODE);
        }
        
        return parent::create($data);
    }
    
    /**
     * 更新API接口
     */
    public function update($id, $data) {
        // 验证路径唯一性
        if (isset($data['path']) && $this->pathExists($data['path'], $id)) {
            throw new Exception('API路径已存在');
        }
        
        // 处理JSON字段
        if (isset($data['request_params']) && is_array($data['request_params'])) {
            $data['request_params'] = json_encode($data['request_params'], JSON_UNESCAPED_UNICODE);
        }
        
        if (isset($data['request_headers']) && is_array($data['request_headers'])) {
            $data['request_headers'] = json_encode($data['request_headers'], JSON_UNESCAPED_UNICODE);
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * 获取API列表（带分类和商家信息）
     */
    public function getListWithDetails($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['name'])) {
            $whereClause[] = "i.name LIKE :name";
            $params['name'] = '%' . $conditions['name'] . '%';
        }
        
        if (!empty($conditions['category_id'])) {
            $whereClause[] = "i.category_id = :category_id";
            $params['category_id'] = $conditions['category_id'];
        }
        
        if (!empty($conditions['merchant_id'])) {
            $whereClause[] = "i.merchant_id = :merchant_id";
            $params['merchant_id'] = $conditions['merchant_id'];
        }
        
        if (!empty($conditions['status'])) {
            $whereClause[] = "i.status = :status";
            $params['status'] = $conditions['status'];
        }
        
        if (!empty($conditions['method'])) {
            $whereClause[] = "i.method = :method";
            $params['method'] = $conditions['method'];
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->getTableName()} i {$whereStr}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT i.*, 
                       c.name as category_name,
                       m.company_name as merchant_name
                FROM {$this->getTableName()} i 
                LEFT JOIN {$this->db->getPrefix()}categories c ON i.category_id = c.id
                LEFT JOIN {$this->db->getPrefix()}merchants m ON i.merchant_id = m.id
                {$whereStr}
                ORDER BY i.id DESC 
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        // 解析JSON字段
        foreach ($data as &$item) {
            $item['request_params'] = json_decode($item['request_params'], true) ?: [];
            $item['request_headers'] = json_decode($item['request_headers'], true) ?: [];
        }
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 根据路径查找API
     */
    public function findByPath($path) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE path = :path AND status = 1";
        $result = $this->db->fetchOne($sql, ['path' => $path]);
        
        if ($result) {
            $result['request_params'] = json_decode($result['request_params'], true) ?: [];
            $result['request_headers'] = json_decode($result['request_headers'], true) ?: [];
        }
        
        return $result;
    }
    
    /**
     * 检查路径是否存在
     */
    public function pathExists($path, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->getTableName()} WHERE path = :path";
        $params = ['path' => $path];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * 增加调用次数
     */
    public function incrementCallCount($id, $success = true) {
        $sql = "UPDATE {$this->getTableName()} SET 
                call_count = call_count + 1,
                " . ($success ? "success_count = success_count + 1" : "error_count = error_count + 1") . "
                WHERE id = :id";
        
        $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * 获取API统计信息
     */
    public function getApiStats() {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
                    SUM(call_count) as total_calls,
                    SUM(success_count) as total_success,
                    SUM(error_count) as total_errors,
                    AVG(price) as avg_price
                FROM {$this->getTableName()}";
        
        return $this->db->fetchOne($sql);
    }
    
    /**
     * 获取热门API
     */
    public function getHotApis($limit = 10) {
        $sql = "SELECT id, name, path, call_count, success_count, error_count
                FROM {$this->getTableName()} 
                WHERE status = 1 
                ORDER BY call_count DESC 
                LIMIT {$limit}";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * 获取API调用趋势
     */
    public function getCallTrend($apiId = null, $days = 7) {
        $whereClause = $apiId ? "WHERE api_id = :api_id" : "";
        $params = $apiId ? ['api_id' => $apiId] : [];
        
        $sql = "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as calls,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as error_calls
                FROM {$this->db->getPrefix()}call_logs 
                {$whereClause}
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 批量更新状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids)) {
            return false;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->getTableName()} SET status = ?, updated_at = ? WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status, date('Y-m-d H:i:s')], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * 复制API
     */
    public function copyApi($id, $newName = null, $newPath = null) {
        $api = $this->find($id);
        if (!$api) {
            throw new Exception('原API不存在');
        }
        
        // 生成新的名称和路径
        if (!$newName) {
            $newName = $api['name'] . '（副本）';
        }
        
        if (!$newPath) {
            $newPath = $api['path'] . '_copy';
            $counter = 1;
            while ($this->pathExists($newPath)) {
                $newPath = $api['path'] . '_copy_' . $counter;
                $counter++;
            }
        }
        
        $newApiData = $api;
        unset($newApiData['id'], $newApiData['created_at'], $newApiData['updated_at']);
        $newApiData['name'] = $newName;
        $newApiData['path'] = $newPath;
        $newApiData['status'] = 0; // 默认禁用
        $newApiData['call_count'] = 0;
        $newApiData['success_count'] = 0;
        $newApiData['error_count'] = 0;
        
        return $this->create($newApiData);
    }
    
    /**
     * 导出API配置
     */
    public function exportApi($id) {
        $api = $this->find($id);
        if (!$api) {
            throw new Exception('API不存在');
        }
        
        // 解析JSON字段
        $api['request_params'] = json_decode($api['request_params'], true) ?: [];
        $api['request_headers'] = json_decode($api['request_headers'], true) ?: [];
        
        return [
            'name' => $api['name'],
            'path' => $api['path'],
            'method' => $api['method'],
            'description' => $api['description'],
            'price' => $api['price'],
            'request_params' => $api['request_params'],
            'response_example' => $api['response_example'],
            'request_headers' => $api['request_headers'],
            'timeout' => $api['timeout'],
            'retry_count' => $api['retry_count'],
            'rate_limit' => $api['rate_limit'],
            'export_time' => date('Y-m-d H:i:s'),
            'version' => '1.0'
        ];
    }
    
    /**
     * 导入API配置
     */
    public function importApi($config, $categoryId = null, $merchantId = null) {
        if (!isset($config['name']) || !isset($config['path'])) {
            throw new Exception('配置格式错误');
        }
        
        // 确保路径唯一
        $path = $config['path'];
        $counter = 1;
        while ($this->pathExists($path)) {
            $path = $config['path'] . '_import_' . $counter;
            $counter++;
        }
        
        $apiData = [
            'name' => $config['name'],
            'path' => $path,
            'method' => $config['method'] ?? 'GET',
            'description' => $config['description'] ?? '',
            'category_id' => $categoryId,
            'merchant_id' => $merchantId,
            'price' => $config['price'] ?? 0,
            'request_params' => $config['request_params'] ?? [],
            'response_example' => $config['response_example'] ?? '',
            'request_headers' => $config['request_headers'] ?? [],
            'timeout' => $config['timeout'] ?? 30,
            'retry_count' => $config['retry_count'] ?? 0,
            'rate_limit' => $config['rate_limit'] ?? 60,
            'status' => 0, // 默认禁用
            'is_public' => 1
        ];
        
        return $this->create($apiData);
    }
    
    /**
     * 获取API详情（带统计信息）
     */
    public function getDetailWithStats($id) {
        $api = $this->find($id);
        if (!$api) {
            return null;
        }
        
        // 解析JSON字段
        $api['request_params'] = json_decode($api['request_params'], true) ?: [];
        $api['request_headers'] = json_decode($api['request_headers'], true) ?: [];
        
        // 获取统计信息
        $sql = "SELECT 
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as error_calls,
                    AVG(response_time) as avg_response_time,
                    SUM(cost) as total_revenue
                FROM {$this->db->getPrefix()}call_logs 
                WHERE api_id = :api_id";
        
        $stats = $this->db->fetchOne($sql, ['api_id' => $id]);
        $api['stats'] = $stats;
        
        // 计算成功率
        if ($stats['total_calls'] > 0) {
            $api['success_rate'] = round(($stats['success_calls'] / $stats['total_calls']) * 100, 2);
        } else {
            $api['success_rate'] = 0;
        }
        
        return $api;
    }
    
    /**
     * 验证API参数
     */
    public function validateApiParams($apiId, $params) {
        $api = $this->find($apiId);
        if (!$api) {
            throw new Exception('API不存在');
        }
        
        $requestParams = json_decode($api['request_params'], true) ?: [];
        $errors = [];
        
        foreach ($requestParams as $param) {
            $name = $param['name'];
            $required = $param['required'] ?? false;
            $type = $param['type'] ?? 'string';
            
            if ($required && !isset($params[$name])) {
                $errors[] = "缺少必需参数: {$name}";
                continue;
            }
            
            if (isset($params[$name])) {
                $value = $params[$name];
                
                // 类型验证
                switch ($type) {
                    case 'integer':
                        if (!is_numeric($value)) {
                            $errors[] = "参数 {$name} 必须是整数";
                        }
                        break;
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[] = "参数 {$name} 必须是有效的邮箱地址";
                        }
                        break;
                    case 'url':
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            $errors[] = "参数 {$name} 必须是有效的URL";
                        }
                        break;
                }
                
                // 长度验证
                if (isset($param['max_length']) && strlen($value) > $param['max_length']) {
                    $errors[] = "参数 {$name} 长度不能超过 {$param['max_length']} 个字符";
                }
                
                if (isset($param['min_length']) && strlen($value) < $param['min_length']) {
                    $errors[] = "参数 {$name} 长度不能少于 {$param['min_length']} 个字符";
                }
            }
        }
        
        return $errors;
    }
}