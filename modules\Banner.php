<?php
/**
 * 轮播图管理模型
 * API管理系统 - 轮播图/广告管理
 */

require_once __DIR__ . '/../core/Model.php';

class Banner extends Model {
    protected $table = 'banners';
    protected $fillable = [
        'title', 'image_url', 'link_url', 'description', 'position', 
        'sort_order', 'status', 'start_time', 'end_time', 'click_count'
    ];
    
    // 轮播图位置常量
    const POSITION_HOME_SLIDER = 'home_slider';      // 首页轮播
    const POSITION_HOME_AD = 'home_ad';              // 首页广告
    const POSITION_SIDEBAR = 'sidebar';              // 侧边栏
    const POSITION_FOOTER = 'footer';                // 页脚
    const POSITION_API_LIST = 'api_list';            // API列表页
    
    /**
     * 获取指定位置的轮播图
     */
    public function getBannersByPosition($position, $limit = null) {
        $currentTime = date('Y-m-d H:i:s');
        
        $sql = "SELECT * FROM {$this->getTableName()} 
                WHERE position = :position 
                AND status = 1 
                AND (start_time IS NULL OR start_time <= :current_time)
                AND (end_time IS NULL OR end_time >= :current_time)
                ORDER BY sort_order ASC, id DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql, [
            'position' => $position,
            'current_time' => $currentTime
        ]);
    }
    
    /**
     * 获取轮播图列表（带筛选）
     */
    public function getBannerList($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        $whereClause = [];
        $params = [];
        
        // 构建查询条件
        if (!empty($conditions['title'])) {
            $whereClause[] = "title LIKE :title";
            $params['title'] = '%' . $conditions['title'] . '%';
        }
        
        if (!empty($conditions['position'])) {
            $whereClause[] = "position = :position";
            $params['position'] = $conditions['position'];
        }
        
        if (isset($conditions['status']) && $conditions['status'] !== '') {
            $whereClause[] = "status = :status";
            $params['status'] = $conditions['status'];
        }
        
        if (!empty($conditions['start_date'])) {
            $whereClause[] = "created_at >= :start_date";
            $params['start_date'] = $conditions['start_date'];
        }
        
        if (!empty($conditions['end_date'])) {
            $whereClause[] = "created_at <= :end_date";
            $params['end_date'] = $conditions['end_date'];
        }
        
        $whereSQL = empty($whereClause) ? '' : 'WHERE ' . implode(' AND ', $whereClause);
        
        // 查询总数
        $countSQL = "SELECT COUNT(*) as total FROM {$this->getTableName()} {$whereSQL}";
        $totalResult = $this->db->fetchOne($countSQL, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSQL = "SELECT * FROM {$this->getTableName()} {$whereSQL} 
                    ORDER BY sort_order ASC, id DESC 
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSQL, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 创建轮播图
     */
    public function createBanner($data) {
        // 验证必填字段
        $required = ['title', 'image_url', 'position'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception("字段 {$field} 不能为空");
            }
        }
        
        // 验证位置
        $validPositions = [
            self::POSITION_HOME_SLIDER,
            self::POSITION_HOME_AD,
            self::POSITION_SIDEBAR,
            self::POSITION_FOOTER,
            self::POSITION_API_LIST
        ];
        
        if (!in_array($data['position'], $validPositions)) {
            throw new Exception('无效的轮播图位置');
        }
        
        // 设置默认值
        $data['sort_order'] = $data['sort_order'] ?? 0;
        $data['status'] = $data['status'] ?? 1;
        $data['click_count'] = 0;
        
        // 验证时间格式
        if (!empty($data['start_time']) && !strtotime($data['start_time'])) {
            throw new Exception('开始时间格式不正确');
        }
        
        if (!empty($data['end_time']) && !strtotime($data['end_time'])) {
            throw new Exception('结束时间格式不正确');
        }
        
        if (!empty($data['start_time']) && !empty($data['end_time'])) {
            if (strtotime($data['start_time']) >= strtotime($data['end_time'])) {
                throw new Exception('开始时间必须早于结束时间');
            }
        }
        
        return $this->create($data);
    }
    
    /**
     * 更新轮播图
     */
    public function updateBanner($id, $data) {
        $banner = $this->find($id);
        if (!$banner) {
            throw new Exception('轮播图不存在');
        }
        
        // 验证位置
        if (!empty($data['position'])) {
            $validPositions = [
                self::POSITION_HOME_SLIDER,
                self::POSITION_HOME_AD,
                self::POSITION_SIDEBAR,
                self::POSITION_FOOTER,
                self::POSITION_API_LIST
            ];
            
            if (!in_array($data['position'], $validPositions)) {
                throw new Exception('无效的轮播图位置');
            }
        }
        
        // 验证时间格式
        if (!empty($data['start_time']) && !strtotime($data['start_time'])) {
            throw new Exception('开始时间格式不正确');
        }
        
        if (!empty($data['end_time']) && !strtotime($data['end_time'])) {
            throw new Exception('结束时间格式不正确');
        }
        
        if (!empty($data['start_time']) && !empty($data['end_time'])) {
            if (strtotime($data['start_time']) >= strtotime($data['end_time'])) {
                throw new Exception('开始时间必须早于结束时间');
            }
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * 批量更新状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids) || !is_array($ids)) {
            throw new Exception('ID列表不能为空');
        }
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "UPDATE {$this->getTableName()} SET status = ? WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * 批量删除
     */
    public function batchDelete($ids) {
        if (empty($ids) || !is_array($ids)) {
            throw new Exception('ID列表不能为空');
        }
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "DELETE FROM {$this->getTableName()} WHERE id IN ({$placeholders})";
        
        $stmt = $this->db->query($sql, $ids);
        
        return $stmt->rowCount();
    }
    
    /**
     * 更新排序
     */
    public function updateSort($id, $sortOrder) {
        return $this->update($id, ['sort_order' => $sortOrder]);
    }
    
    /**
     * 记录点击
     */
    public function recordClick($id) {
        $sql = "UPDATE {$this->getTableName()} SET click_count = click_count + 1 WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * 获取轮播图统计
     */
    public function getBannerStats() {
        $sql = "SELECT 
                    position,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_count,
                    SUM(click_count) as total_clicks
                FROM {$this->getTableName()} 
                GROUP BY position";
        
        $stats = $this->db->fetchAll($sql);
        
        $result = [];
        foreach ($stats as $stat) {
            $result[$stat['position']] = [
                'total' => $stat['total_count'],
                'active' => $stat['active_count'],
                'clicks' => $stat['total_clicks']
            ];
        }
        
        return $result;
    }
    
    /**
     * 获取热门轮播图
     */
    public function getPopularBanners($limit = 10) {
        $sql = "SELECT * FROM {$this->getTableName()} 
                WHERE status = 1 
                ORDER BY click_count DESC 
                LIMIT {$limit}";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * 清理过期轮播图
     */
    public function cleanExpiredBanners() {
        $currentTime = date('Y-m-d H:i:s');
        
        $sql = "UPDATE {$this->getTableName()} 
                SET status = 0 
                WHERE end_time IS NOT NULL 
                AND end_time < :current_time 
                AND status = 1";
        
        $stmt = $this->db->query($sql, ['current_time' => $currentTime]);
        
        return $stmt->rowCount();
    }
}