
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>步骤条</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
    </style>
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">基本用法</div>
        <div class="layui-card-body">
            <div class="layui-tab layui-steps">
                <ul class="layui-tab-title">
                    <li>
                        <i class="layui-icon layui-icon-ok">1</i>
                        <span class="layui-steps-title">已完成</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                    <li class="layui-this">
                        <i class="layui-icon layui-icon-ok">2</i>
                        <span class="layui-steps-title">进行中</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">3</i>
                        <span class="layui-steps-title">待进行</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">4</i>
                        <span class="layui-steps-title">待进行</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item">内容1</div>
                    <div class="layui-tab-item layui-show">内容2</div>
                    <div class="layui-tab-item">内容3</div>
                    <div class="layui-tab-item">内容4</div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">迷你版</div>
        <div class="layui-card-body">
            <div class="layui-tab layui-steps layui-steps-small">
                <ul class="layui-tab-title">
                    <li>
                        <i class="layui-icon layui-icon-ok">1</i>
                        <span class="layui-steps-title">已完成</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">2</i>
                        <span class="layui-steps-title">已完成</span>
                    </li>
                    <li class="layui-this">
                        <i class="layui-icon layui-icon-ok">3</i>
                        <span class="layui-steps-title">进行中</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">4</i>
                        <span class="layui-steps-title">待进行</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">5</i>
                        <span class="layui-steps-title">待进行</span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item">内容1</div>
                    <div class="layui-tab-item">内容2</div>
                    <div class="layui-tab-item layui-show">内容3</div>
                    <div class="layui-tab-item">内容4</div>
                    <div class="layui-tab-item">内容5</div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">自定义图标</div>
        <div class="layui-card-body">
            <div class="layui-tab layui-steps layui-steps-small">
                <ul class="layui-tab-title">
                    <li class="layui-this">
                        <i class="layui-icon layui-icon-username"></i>
                        <span class="layui-steps-title">账号注册</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-camera"></i>
                        <span class="layui-steps-title">上传头像</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-email"></i>
                        <span class="layui-steps-title">验证邮箱</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-gift"></i>
                        <span class="layui-steps-title">领取礼包</span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">内容1</div>
                    <div class="layui-tab-item">内容2</div>
                    <div class="layui-tab-item">内容3</div>
                    <div class="layui-tab-item">内容4</div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">垂直风格</div>
        <div class="layui-card-body">
            <div class="layui-tab layui-steps layui-steps-vertical">
                <ul class="layui-tab-title">
                    <li>
                        <i class="layui-icon layui-icon-ok">1</i>
                        <span class="layui-steps-title">已完成</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                    <li class="layui-this">
                        <i class="layui-icon layui-icon-ok">2</i>
                        <span class="layui-steps-title">进行中</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">3</i>
                        <span class="layui-steps-title">待进行</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">4</i>
                        <span class="layui-steps-title">待进行</span>
                        <span class="layui-steps-content">这里是该步骤的描述信息</span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item">内容1</div>
                    <div class="layui-tab-item layui-show">内容2</div>
                    <div class="layui-tab-item">内容3</div>
                    <div class="layui-tab-item">内容4</div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">简洁风格</div>
        <div class="layui-card-body">
            <div class="layui-tab layui-steps layui-steps-simple">
                <ul class="layui-tab-title">
                    <li>1.填写注册手机号</li>
                    <li class="layui-this">2.获取短信验证码</li>
                    <li>3.修改登录密码</li>
                    <li>4.完成密码修改</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item">内容1</div>
                    <div class="layui-tab-item layui-show">内容2</div>
                    <div class="layui-tab-item">内容3</div>
                    <div class="layui-tab-item">内容4</div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">找回密码</div>
        <div class="layui-card-body">
            <div class="layui-tab layui-steps layui-steps-readonly" lay-filter="stepsDemoForget"
                 style="max-width: 600px;">
                <ul class="layui-tab-title">
                    <li class="layui-this">
                        <i class="layui-icon layui-icon-ok">1</i>
                        <span class="layui-steps-title">第一步</span>
                        <span class="layui-steps-content">填写注册手机号</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">2</i>
                        <span class="layui-steps-title">第二步</span>
                        <span class="layui-steps-content">获取短信验证码</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">3</i>
                        <span class="layui-steps-title">第三步</span>
                        <span class="layui-steps-content">修改登录密码</span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <form class="layui-form" style="padding: 35px 150px 10px 60px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">手机号:</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" placeholder="请输入手机号"/>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-fluid layui-btn-radius"
                                            lay-filter="stepDemoFormSubmit1" lay-submit>下一步
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="layui-tab-item">
                        <form class="layui-form" style="padding: 35px 150px 10px 60px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">验证码:</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" placeholder="请输入验证码"/>
                                    <button style="position: absolute;right: 0;top: 0;" type="button"
                                            id="demoStepsFormBtn1" class="layui-btn layui-btn-primary">获取验证码
                                    </button>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button data-steps="prev" type="button"
                                            class="layui-btn layui-btn-primary layui-btn-radius">上一步
                                    </button>
                                    <button class="layui-btn layui-btn-radius" lay-filter="stepDemoFormSubmit2"
                                            lay-submit>下一步
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="layui-tab-item">
                        <form class="layui-form" style="padding: 35px 150px 10px 60px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">新密码:</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" placeholder="请输入新密码"/>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">确认密码:</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" placeholder="请再次输入新密码"/>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button data-steps="next" type="button"
                                            class="layui-btn layui-btn-primary layui-btn-radius">取消修改
                                    </button>
                                    <button class="layui-btn layui-btn-radius" lay-filter="stepDemoFormSubmit3"
                                            lay-submit>修改密码
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'steps', 'form', 'admin', 'formX'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var steps = layui.steps;
        var form = layui.form;
        var admin = layui.admin;
        var formX = layui.formX;

        // 填写手机号
        form.on('submit(stepDemoFormSubmit1)', function (data) {
            admin.btnLoading('[lay-filter="stepDemoFormSubmit1"]');
            setTimeout(function () {
                admin.btnLoading('[lay-filter="stepDemoFormSubmit1"]', false);
                steps.next('stepsDemoForget');
                formX.startTimer('#demoStepsFormBtn1', 60, function (t) {
                    return '已发送(' + t + 's)';
                });
            }, 600);
            return false;
        });

        // 获取验证码
        form.on('submit(stepDemoFormSubmit2)', function (data) {
            admin.btnLoading('[lay-filter="stepDemoFormSubmit2"]');
            setTimeout(function () {
                admin.btnLoading('[lay-filter="stepDemoFormSubmit2"]', false);
                steps.next('stepsDemoForget');
            }, 600);
            return false;
        });

        // 修改登录密码
        form.on('submit(stepDemoFormSubmit3)', function (data) {
            admin.btnLoading('[lay-filter="stepDemoFormSubmit3"]');
            setTimeout(function () {
                layer.msg('密码修改成功', {icon: 1});
                admin.btnLoading('[lay-filter="stepDemoFormSubmit3"]', false);
                steps.next('stepsDemoForget');
            }, 600);
            return false;
        });

    });
</script>
</body>
</html>