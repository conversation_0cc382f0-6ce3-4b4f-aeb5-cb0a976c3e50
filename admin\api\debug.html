<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API在线调试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
    <style>
        .api-debug-container {
            display: flex;
            height: calc(100vh - 120px);
        }
        .api-list-container {
            width: 300px;
            border-right: 1px solid #eee;
            overflow-y: auto;
            padding: 10px;
        }
        .api-debug-panel {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
        }
        .api-item {
            padding: 10px;
            margin-bottom: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        .api-item:hover {
            background-color: #f2f2f2;
        }
        .api-item.active {
            background-color: #1E9FFF;
            color: #fff;
        }
        .api-category {
            margin-bottom: 10px;
        }
        .api-category-title {
            font-weight: bold;
            padding: 5px 0;
            cursor: pointer;
        }
        .api-category-list {
            padding-left: 15px;
        }
        .response-container {
            margin-top: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            background-color: #f8f8f8;
            padding: 10px;
        }
        .response-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .response-body {
            max-height: 400px;
            overflow-y: auto;
            background-color: #272822;
            color: #f8f8f2;
            padding: 10px;
            border-radius: 2px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .param-table .required {
            color: #FF5722;
        }
        .param-table .layui-form-item {
            margin-bottom: 5px;
        }
        .api-info {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .api-info h3 {
            margin-bottom: 10px;
        }
        .api-info p {
            color: #666;
            margin-bottom: 5px;
        }
        .api-url {
            background-color: #f8f8f8;
            padding: 5px 10px;
            border-radius: 2px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .api-method {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            color: #fff;
            font-size: 12px;
            margin-right: 5px;
        }
        .api-method.get {
            background-color: #61affe;
        }
        .api-method.post {
            background-color: #49cc90;
        }
        .api-method.put {
            background-color: #fca130;
        }
        .api-method.delete {
            background-color: #f93e3e;
        }
        .search-box {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">API在线调试</h2>
        </div>
        <div class="layui-card-body">
            <div class="api-debug-container">
                <!-- API列表 -->
                <div class="api-list-container">
                    <div class="search-box">
                        <input type="text" id="api-search" placeholder="搜索API" class="layui-input">
                    </div>
                    <div id="api-tree"></div>
                </div>
                
                <!-- 调试面板 -->
                <div class="api-debug-panel">
                    <div id="api-debug-content">
                        <div class="layui-text" style="text-align: center; padding: 100px 0;">
                            <i class="layui-icon layui-icon-face-smile" style="font-size: 48px; color: #1E9FFF;"></i>
                            <p style="margin-top: 20px; font-size: 16px;">请从左侧选择一个API进行调试</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API调试模板 -->
<script type="text/html" id="api-debug-tpl">
    <div class="api-info">
        <h3>{{ d.name }}</h3>
        <p>{{ d.description }}</p>
        <p>
            <span class="api-method {{ d.method.toLowerCase() }}">{{ d.method }}</span>
            <span class="api-url">{{ d.url }}</span>
        </p>
    </div>
    
    <div class="layui-tab" lay-filter="api-debug-tab">
        <ul class="layui-tab-title">
            <li class="layui-this">参数设置</li>
            <li>请求头</li>
            <li>认证信息</li>
        </ul>
        <div class="layui-tab-content">
            <!-- 参数设置 -->
            <div class="layui-tab-item layui-show">
                <form class="layui-form param-form" lay-filter="param-form">
                    {{# if(d.params && d.params.length > 0){ }}
                    <table class="layui-table param-table" lay-skin="line">
                        <colgroup>
                            <col width="150">
                            <col width="150">
                            <col>
                            <col width="80">
                        </colgroup>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>描述</th>
                                <th>必填</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{# layui.each(d.params, function(index, item){ }}
                            <tr>
                                <td>{{ item.name }}</td>
                                <td>{{ item.type }}</td>
                                <td>{{ item.description }}</td>
                                <td>{{# if(item.required){ }}<span class="required">是</span>{{# } else { }}否{{# } }}</td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <div class="layui-form-item">
                                        <div class="layui-input-block" style="margin-left: 0;">
                                            {{# if(item.type === 'string' || item.type === 'number'){ }}
                                            <input type="text" name="{{ item.name }}" placeholder="请输入{{ item.name }}" class="layui-input" {{# if(item.required){ }}lay-verify="required"{{# } }}>
                                            {{# } else if(item.type === 'boolean'){ }}
                                            <select name="{{ item.name }}" {{# if(item.required){ }}lay-verify="required"{{# } }}>
                                                <option value="">请选择</option>
                                                <option value="true">true</option>
                                                <option value="false">false</option>
                                            </select>
                                            {{# } else if(item.type === 'array'){ }}
                                            <textarea name="{{ item.name }}" placeholder="请输入JSON数组，例如: [1, 2, 3]" class="layui-textarea" {{# if(item.required){ }}lay-verify="required"{{# } }}></textarea>
                                            {{# } else if(item.type === 'object'){ }}
                                            <textarea name="{{ item.name }}" placeholder="请输入JSON对象，例如: {&quot;key&quot;: &quot;value&quot;}" class="layui-textarea" {{# if(item.required){ }}lay-verify="required"{{# } }}></textarea>
                                            {{# } else if(item.type === 'file'){ }}
                                            <input type="file" name="{{ item.name }}" {{# if(item.required){ }}lay-verify="required"{{# } }}>
                                            {{# } }}
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {{# }); }}
                        </tbody>
                    </table>
                    {{# } else { }}
                    <div class="layui-text" style="padding: 20px 0; text-align: center; color: #999;">
                        该API没有需要设置的参数
                    </div>
                    {{# } }}
                </form>
            </div>
            
            <!-- 请求头 -->
            <div class="layui-tab-item">
                <form class="layui-form header-form" lay-filter="header-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">Content-Type</label>
                        <div class="layui-input-block">
                            <select name="content_type">
                                <option value="application/json">application/json</option>
                                <option value="application/x-www-form-urlencoded">application/x-www-form-urlencoded</option>
                                <option value="multipart/form-data">multipart/form-data</option>
                                <option value="text/plain">text/plain</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">Accept</label>
                        <div class="layui-input-block">
                            <input type="text" name="accept" value="application/json" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">自定义头</label>
                        <div class="layui-input-block">
                            <textarea name="custom_headers" placeholder="请输入自定义请求头，格式为JSON对象，例如: {&quot;X-Custom-Header&quot;: &quot;value&quot;}" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- 认证信息 -->
            <div class="layui-tab-item">
                <form class="layui-form auth-form" lay-filter="auth-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">认证类型</label>
                        <div class="layui-input-block">
                            <select name="auth_type" lay-filter="auth-type">
                                <option value="none">无认证</option>
                                <option value="api_key">API Key</option>
                                <option value="bearer">Bearer Token</option>
                                <option value="basic">Basic Auth</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- API Key认证 -->
                    <div id="auth-api-key" style="display: none;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">API Key</label>
                            <div class="layui-input-block">
                                <input type="text" name="api_key" placeholder="请输入API Key" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">参数名</label>
                            <div class="layui-input-block">
                                <input type="text" name="api_key_name" value="api_key" placeholder="API Key参数名" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">添加位置</label>
                            <div class="layui-input-block">
                                <select name="api_key_in">
                                    <option value="query">URL参数</option>
                                    <option value="header">请求头</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Bearer Token认证 -->
                    <div id="auth-bearer" style="display: none;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">Token</label>
                            <div class="layui-input-block">
                                <input type="text" name="bearer_token" placeholder="请输入Bearer Token" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Basic Auth认证 -->
                    <div id="auth-basic" style="display: none;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">用户名</label>
                            <div class="layui-input-block">
                                <input type="text" name="basic_username" placeholder="请输入用户名" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">密码</label>
                            <div class="layui-input-block">
                                <input type="password" name="basic_password" placeholder="请输入密码" class="layui-input">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 0; text-align: center;">
            <button class="layui-btn" id="send-request-btn">发送请求</button>
            <button class="layui-btn layui-btn-primary" id="reset-btn">重置</button>
        </div>
    </div>
    
    <!-- 响应结果 -->
    <div class="response-container" style="display: none;">
        <div class="response-header">
            <div>
                <span>状态码: </span>
                <span id="response-status" style="font-weight: bold;"></span>
            </div>
            <div>
                <span>耗时: </span>
                <span id="response-time"></span>
            </div>
        </div>
        <div class="layui-tab" lay-filter="response-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">响应体</li>
                <li>响应头</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="response-body" id="response-body"></div>
                </div>
                <div class="layui-tab-item">
                    <div class="response-body" id="response-headers"></div>
                </div>
            </div>
        </div>
    </div>
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../../Easyweb/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script>
layui.use(['element', 'form', 'layer', 'laytpl', 'util'], function(){
    var element = layui.element;
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var util = layui.util;
    var $ = layui.jquery;
    
    // 加载API列表
    loadApiList();
    
    // 认证类型切换事件
    form.on('select(auth-type)', function(data){
        var value = data.value;
        $('#auth-api-key, #auth-bearer, #auth-basic').hide();
        
        if(value === 'api_key') {
            $('#auth-api-key').show();
        } else if(value === 'bearer') {
            $('#auth-bearer').show();
        } else if(value === 'basic') {
            $('#auth-basic').show();
        }
    });
    
    // 发送请求按钮点击事件
    $(document).on('click', '#send-request-btn', function(){
        var apiData = $('#api-debug-content').data('api');
        if(!apiData) return;
        
        // 收集参数
        var paramData = {};
        var paramForm = $('.param-form').serializeArray();
        $.each(paramForm, function(i, field){
            if(field.value) {
                // 尝试解析JSON
                if(field.value.startsWith('[') || field.value.startsWith('{')) {
                    try {
                        paramData[field.name] = JSON.parse(field.value);
                    } catch(e) {
                        paramData[field.name] = field.value;
                    }
                } else {
                    paramData[field.name] = field.value;
                }
            }
        });
        
        // 收集请求头
        var headerForm = $('.header-form').serializeArray();
        var headers = {};
        $.each(headerForm, function(i, field){
            if(field.name === 'content_type') {
                headers['Content-Type'] = field.value;
            } else if(field.name === 'accept') {
                headers['Accept'] = field.value;
            } else if(field.name === 'custom_headers' && field.value) {
                try {
                    var customHeaders = JSON.parse(field.value);
                    $.extend(headers, customHeaders);
                } catch(e) {
                    layer.msg('自定义请求头格式错误，请检查JSON格式');
                    return false;
                }
            }
        });
        
        // 收集认证信息
        var authForm = $('.auth-form').serializeArray();
        var authType = '';
        var authData = {};
        
        $.each(authForm, function(i, field){
            if(field.name === 'auth_type') {
                authType = field.value;
            } else {
                authData[field.name] = field.value;
            }
        });
        
        // 添加认证信息到请求头
        if(authType === 'api_key' && authData.api_key) {
            if(authData.api_key_in === 'header') {
                headers[authData.api_key_name || 'api_key'] = authData.api_key;
            } else {
                // 添加到URL参数
                if(apiData.url.indexOf('?') > -1) {
                    apiData.url += '&';
                } else {
                    apiData.url += '?';
                }
                apiData.url += (authData.api_key_name || 'api_key') + '=' + encodeURIComponent(authData.api_key);
            }
        } else if(authType === 'bearer' && authData.bearer_token) {
            headers['Authorization'] = 'Bearer ' + authData.bearer_token;
        } else if(authType === 'basic' && authData.basic_username) {
            var base64 = btoa(authData.basic_username + ':' + (authData.basic_password || ''));
            headers['Authorization'] = 'Basic ' + base64;
        }
        
        // 显示加载层
        var loadIndex = layer.load(2);
        
        // 记录开始时间
        var startTime = new Date().getTime();
        
        // 发送请求
        $.ajax({
            url: apiData.url,
            type: apiData.method,
            data: apiData.method === 'GET' ? paramData : JSON.stringify(paramData),
            headers: headers,
            dataType: 'json',
            success: function(res, textStatus, xhr){
                showResponse(xhr, res, new Date().getTime() - startTime);
            },
            error: function(xhr){
                try {
                    var res = JSON.parse(xhr.responseText);
                    showResponse(xhr, res, new Date().getTime() - startTime);
                } catch(e) {
                    showResponse(xhr, xhr.responseText, new Date().getTime() - startTime);
                }
            },
            complete: function(){
                layer.close(loadIndex);
            }
        });
    });
    
    // 重置按钮点击事件
    $(document).on('click', '#reset-btn', function(){
        $('.param-form, .header-form, .auth-form')[0].reset();
        form.render();
        $('.response-container').hide();
    });
    
    // API搜索
    $('#api-search').on('input', function(){
        var keyword = $(this).val().toLowerCase();
        if(!keyword) {
            $('.api-item').show();
            $('.api-category').show();
            return;
        }
        
        $('.api-item').each(function(){
            var text = $(this).text().toLowerCase();
            if(text.indexOf(keyword) > -1) {
                $(this).show();
                $(this).parents('.api-category').show();
            } else {
                $(this).hide();
            }
        });
        
        // 隐藏空分类
        $('.api-category').each(function(){
            var visibleItems = $(this).find('.api-item:visible').length;
            if(visibleItems === 0) {
                $(this).hide();
            }
        });
    });
    
    // 加载API列表
    function loadApiList() {
        $.ajax({
            url: '../controllers/ApiController.php?action=getApiList',
            type: 'GET',
            dataType: 'json',
            success: function(res){
                if(res.code === 0) {
                    renderApiTree(res.data);
                } else {
                    layer.msg(res.msg || '加载API列表失败');
                }
            },
            error: function(){
                layer.msg('服务器错误');
            }
        });
    }
    
    // 渲染API树
    function renderApiTree(data) {
        var html = '';
        
        // 按分类分组
        var categories = {};
        $.each(data, function(i, item){
            if(!categories[item.category_id]) {
                categories[item.category_id] = {
                    id: item.category_id,
                    name: item.category_name || '未分类',
                    apis: []
                };
            }
            categories[item.category_id].apis.push(item);
        });
        
        // 生成HTML
        $.each(categories, function(id, category){
            html += '<div class="api-category">';
            html += '<div class="api-category-title"><i class="layui-icon layui-icon-triangle-d"></i> ' + category.name + '</div>';
            html += '<div class="api-category-list">';
            
            $.each(category.apis, function(i, api){
                html += '<div class="api-item" data-id="' + api.id + '">' + api.name + '</div>';
            });
            
            html += '</div>';
            html += '</div>';
        });
        
        $('#api-tree').html(html);
        
        // 绑定API点击事件
        $('.api-item').click(function(){
            $('.api-item').removeClass('active');
            $(this).addClass('active');
            
            var apiId = $(this).data('id');
            loadApiDetail(apiId);
        });
        
        // 分类展开/折叠
        $('.api-category-title').click(function(){
            $(this).find('i').toggleClass('layui-icon-triangle-d layui-icon-triangle-r');
            $(this).next('.api-category-list').slideToggle();
        });
    }
    
    // 加载API详情
    function loadApiDetail(apiId) {
        $.ajax({
            url: '../controllers/ApiController.php?action=getApiDetail',
            type: 'GET',
            data: {id: apiId},
            dataType: 'json',
            success: function(res){
                if(res.code === 0) {
                    renderApiDebug(res.data);
                } else {
                    layer.msg(res.msg || '加载API详情失败');
                }
            },
            error: function(){
                layer.msg('服务器错误');
            }
        });
    }
    
    // 渲染API调试面板
    function renderApiDebug(data) {
        laytpl($('#api-debug-tpl').html()).render(data, function(html){
            $('#api-debug-content').html(html).data('api', data);
            form.render();
            element.render('tab');
            
            // 隐藏响应结果
            $('.response-container').hide();
        });
    }
    
    // 显示响应结果
    function showResponse(xhr, data, time) {
        var status = xhr.status;
        var statusText = xhr.statusText;
        var headers = xhr.getAllResponseHeaders();
        
        $('#response-status').text(status + ' ' + statusText);
        $('#response-time').text(time + 'ms');
        
        // 格式化响应体
        var responseBody = '';
        if(typeof data === 'object') {
            responseBody = JSON.stringify(data, null, 2);
        } else {
            responseBody = data;
        }
        
        $('#response-body').text(responseBody);
        
        // 格式化响应头
        var formattedHeaders = '';
        var headerLines = headers.split('\r\n');
        $.each(headerLines, function(i, line){
            if(line) {
                formattedHeaders += line + '\n';
            }
        });
        
        $('#response-headers').text(formattedHeaders);
        
        // 显示响应结果
        $('.response-container').show();
        
        // 设置响应状态颜色
        if(status >= 200 && status < 300) {
            $('#response-status').css('color', '#67C23A');
        } else if(status >= 300 && status < 400) {
            $('#response-status').css('color', '#E6A23C');
        } else {
            $('#response-status').css('color', '#F56C6C');
        }
    }
});
</script>
</body>
</html>