<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API商业系统 - 后台管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../Easyweb/assets/module/admin.css">
    <link rel="stylesheet" href="../Easyweb/assets/module/icon/iconfont.css">
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!-- 头部 -->
    <div class="layui-header">
        <div class="layui-logo">
            <img src="../Easyweb/assets/images/logo.png" alt="logo">
            <cite>API商业系统</cite>
        </div>
        <ul class="layui-nav layui-layout-left">
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;" data-refresh="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
            </li>
        </ul>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="message" title="消息">
                    <i class="layui-icon layui-icon-notice"></i>
                    <span class="layui-badge-dot"></span>
                </a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="note" title="便签"><i class="layui-icon layui-icon-note"></i></a>
            </li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect>
                <a ew-event="fullScreen" title="全屏"><i class="layui-icon layui-icon-screen-full"></i></a>
            </li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect>
                <a ew-event="lockScreen" title="锁屏"><i class="layui-icon layui-icon-password"></i></a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a>
                    <img src="../Easyweb/assets/images/head.jpg" class="layui-nav-img">
                    <cite>管理员</cite>
                </a>
                <dl class="layui-nav-child">
                    <dd lay-unselect><a ew-href="admin/user-info.html">个人中心</a></dd>
                    <dd lay-unselect><a ew-event="psw">修改密码</a></dd>
                    <hr>
                    <dd lay-unselect><a ew-event="logout" data-url="admin/login.html">退出</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="theme" title="主题"><i class="layui-icon layui-icon-more-vertical"></i></a>
            </li>
        </ul>
    </div>
    
    <!-- 侧边栏 -->
    <div class="layui-side">
        <div class="layui-side-scroll">
            <ul class="layui-nav layui-nav-tree" lay-filter="admin-side-nav" lay-shrink="all">
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-home"></i>&emsp;<cite>控制台</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="dashboard/console.html">控制台</a></dd>
                        <dd><a href="javascript:;" data-url="dashboard/statistics.html">数据统计</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-set"></i>&emsp;<cite>系统管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="system/config.html">系统配置</a></dd>
                        <dd><a href="javascript:;" data-url="system/log.html">系统日志</a></dd>
                        <dd><a href="javascript:;" data-url="system/backup.html">数据备份</a></dd>
                        <dd><a href="javascript:;" data-url="system/template.html">模板管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-user"></i>&emsp;<cite>用户管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="user/list.html">用户列表</a></dd>
                        <dd><a href="javascript:;" data-url="user/vip.html">会员等级</a></dd>
                        <dd><a href="javascript:;" data-url="user/balance.html">余额管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-template"></i>&emsp;<cite>API管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="api/list.html">API列表</a></dd>
                        <dd><a href="javascript:;" data-url="api/category.html">分类管理</a></dd>
                        <dd><a href="javascript:;" data-url="api/logs.html">调用日志</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-rmb"></i>&emsp;<cite>支付管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="payment/order.html">订单管理</a></dd>
                        <dd><a href="javascript:;" data-url="payment/config.html">支付配置</a></dd>
                        <dd><a href="javascript:;" data-url="payment/statistics.html">收入统计</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-cart"></i>&emsp;<cite>商家管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="merchant/list.html">商家列表</a></dd>
                        <dd><a href="javascript:;" data-url="merchant/level.html">等级管理</a></dd>
                        <dd><a href="javascript:;" data-url="merchant/settlement.html">结算管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-release"></i>&emsp;<cite>内容管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="article/list.html">文章管理</a></dd>
                        <dd><a href="javascript:;" data-url="banner/list.html">轮播图管理</a></dd>
                        <dd><a href="javascript:;" data-url="navigation/list.html">导航管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-dialogue"></i>&emsp;<cite>工单管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="ticket/list.html">工单列表</a></dd>
                        <dd><a href="javascript:;" data-url="ticket/statistics.html">工单统计</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-auz"></i>&emsp;<cite>安全管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="security/ip.html">IP黑白名单</a></dd>
                        <dd><a href="javascript:;" data-url="security/qps.html">QPS限制</a></dd>
                        <dd><a href="javascript:;" data-url="security/monitor.html">异常监控</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- 主体部分 -->
    <div class="layui-body"></div>
    
    <!-- 底部 -->
    <div class="layui-footer">
        Copyright © 2023 API商业系统 All rights reserved.
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- js部分 -->
<script src="../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'element', 'admin'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var element = layui.element;
    var admin = layui.admin;
    
    // 默认加载主页
    $('.layui-body').load('dashboard/console.html');
    
    // 侧边栏点击事件
    $('[data-url]').click(function() {
        var url = $(this).data('url');
        $('.layui-body').load(url);
    });
    
    // 退出登录
    $('#logout').click(function() {
        layer.confirm('确定要退出登录吗？', function(index) {
            layer.close(index);
            // 发送退出请求
            $.ajax({
                url: 'controllers/AuthController.php?action=logout',
                type: 'POST',
                dataType: 'json',
                success: function(res) {
                    if (res.code === 200) {
                        location.href = 'login.html';
                    } else {
                        layer.msg(res.msg || '退出失败');
                    }
                },
                error: function() {
                    layer.msg('服务器错误');
                }
            });
        });
    });
    
    // 获取管理员信息
    $.ajax({
        url: 'controllers/AuthController.php?action=getInfo',
        type: 'GET',
        dataType: 'json',
        success: function(res) {
            if (res.code === 200 && res.data) {
                $('#admin-name').text(res.data.username);
                if (res.data.avatar) {
                    $('.layui-nav-img').attr('src', res.data.avatar);
                }
            }
        }
    });
});
</script>
    <div class="layui-footer">
        <span>Copyright © 2024 API商业系统 All Rights Reserved</span>
        <span class="pull-right">Version 1.0.0</span>
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- js部分 -->
<script src="../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../Easyweb/assets/js/common.js"></script>
<script>
layui.use(['index'], function () {
    var $ = layui.jquery;
    var index = layui.index;
    
    // 默认加载页面
    index.loadHome({
        menuPath: 'dashboard/console.html',
        menuName: '<i class="layui-icon layui-icon-home"></i>'
    });
    
    // 获取用户信息
    $.ajax({
        url: 'controllers/UserController.php?action=getUserInfo',
        type: 'GET',
        dataType: 'json',
        success: function(res) {
            if (res.code === 200) {
                $('#admin-name').text(res.data.username);
                if (res.data.avatar) {
                    $('.layui-nav-img').attr('src', res.data.avatar);
                }
            }
        }
    });
    
    // 退出登录
    $('#logout').click(function() {
        layer.confirm('确定要退出登录吗？', function(index) {
            $.ajax({
                url: 'controllers/AuthController.php?action=logout',
                type: 'POST',
                dataType: 'json',
                success: function(res) {
                    if (res.code === 200) {
                        location.href = 'login.html';
                    } else {
                        layer.msg(res.msg);
                    }
                }
            });
            layer.close(index);
        });
    });
});
</script>
</body>
</html>
