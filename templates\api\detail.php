<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title><?php echo $api['name']; ?> - <?php echo $site_name; ?></title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/style.css"/>
    <style>
        .api-detail-container {
            padding: 20px 0;
        }
        .api-detail-card {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            background-color: #fff;
            margin-bottom: 20px;
        }
        .api-detail-card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .api-detail-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        .api-detail-card-body {
            padding: 20px;
        }
        .api-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        .api-info-item {
            margin-bottom: 10px;
        }
        .api-info-item:last-child {
            margin-bottom: 0;
        }
        .api-info-label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
        .api-info-value {
            display: inline-block;
        }
        .api-method {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            color: #fff;
            font-size: 12px;
        }
        .api-method.get {
            background-color: #5FB878;
        }
        .api-method.post {
            background-color: #1E9FFF;
        }
        .api-method.put {
            background-color: #FFB800;
        }
        .api-method.delete {
            background-color: #FF5722;
        }
        .api-method.patch {
            background-color: #01AAED;
        }
        .api-method.options {
            background-color: #2F4056;
        }
        .api-method.head {
            background-color: #393D49;
        }
        .api-price {
            font-weight: bold;
            color: #FF5722;
        }
        .api-price.free {
            color: #5FB878;
        }
        .api-actions {
            display: flex;
            gap: 10px;
        }
        .api-action-btn {
            padding: 6px 15px;
            border-radius: 2px;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
            border: none;
            outline: none;
        }
        .api-action-btn.test {
            background-color: #1E9FFF;
        }
        .api-action-btn.test:hover {
            background-color: #0d8aee;
        }
        .api-action-btn.subscribe {
            background-color: #5FB878;
        }
        .api-action-btn.subscribe:hover {
            background-color: #4ea766;
        }
        .api-action-btn.favorite {
            background-color: #FFB800;
        }
        .api-action-btn.favorite:hover {
            background-color: #eaa700;
        }
        .api-action-btn.favorited {
            background-color: #FF5722;
        }
        .api-action-btn.favorited:hover {
            background-color: #ee4811;
        }
        .api-action-btn i {
            margin-right: 5px;
        }
        .api-description {
            line-height: 1.8;
            color: #333;
        }
        .api-merchant {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        .api-merchant-logo {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            margin-right: 15px;
        }
        .api-merchant-info {
            flex: 1;
        }
        .api-merchant-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .api-merchant-desc {
            color: #666;
            font-size: 14px;
        }
        .api-merchant-level {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            color: #fff;
            font-size: 12px;
            margin-left: 10px;
        }
        .api-merchant-level.level1 {
            background-color: #01AAED;
        }
        .api-merchant-level.level2 {
            background-color: #1E9FFF;
        }
        .api-merchant-level.level3 {
            background-color: #5FB878;
        }
        .api-merchant-level.level4 {
            background-color: #FFB800;
        }
        .api-merchant-level.level5 {
            background-color: #FF5722;
        }
        .api-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        .api-stat-item {
            text-align: center;
        }
        .api-stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1E9FFF;
        }
        .api-stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .api-sidebar-card {
            margin-bottom: 20px;
        }
        .api-sidebar-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-related-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-related-item:last-child {
            border-bottom: none;
        }
        .api-related-name {
            flex: 1;
            font-size: 14px;
        }
        .api-related-method {
            margin-right: 10px;
        }
        .api-related-price {
            font-size: 14px;
            color: #FF5722;
        }
        .api-related-price.free {
            color: #5FB878;
        }
        .api-subscribe-form {
            margin-top: 20px;
        }
        .api-subscribe-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .api-subscribe-option {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        .api-subscribe-option:hover {
            border-color: #1E9FFF;
        }
        .api-subscribe-option.selected {
            border-color: #1E9FFF;
            background-color: #f0faff;
        }
        .api-subscribe-radio {
            margin-right: 10px;
        }
        .api-subscribe-info {
            flex: 1;
        }
        .api-subscribe-name {
            font-weight: bold;
        }
        .api-subscribe-desc {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        .api-subscribe-price {
            font-weight: bold;
            color: #FF5722;
        }
        .api-subscribe-btn {
            width: 100%;
            padding: 10px 0;
            background-color: #1E9FFF;
            color: #fff;
            border: none;
            border-radius: 2px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 15px;
        }
        .api-subscribe-btn:hover {
            background-color: #0d8aee;
        }
        .api-subscribe-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .api-code-block {
            background-color: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
        }
        .api-code-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
        }
        .api-code-comment {
            color: #75715e;
        }
        .api-code-string {
            color: #a6e22e;
        }
        .api-code-keyword {
            color: #f92672;
        }
        .api-code-function {
            color: #66d9ef;
        }
        .api-code-variable {
            color: #fd971f;
        }
    </style>
</head>
<body>
<div class="page-container">
    <!-- 头部 -->
    <?php include 'templates/common/header.php'; ?>
    
    <!-- 主体内容 -->
    <div class="layui-container api-detail-container">
        <div class="layui-row layui-col-space20">
            <div class="layui-col-md8">
                <!-- API详情卡片 -->
                <div class="api-detail-card">
                    <div class="api-detail-card-header">
                        <h1 class="api-detail-card-title"><?php echo $api['name']; ?></h1>
                        <div class="api-actions">
                            <button class="api-action-btn test" onclick="location.href='/api/test?id=<?php echo $api['id']; ?>'">
                                <i class="layui-icon layui-icon-console"></i>在线测试
                            </button>
                            <?php if (!$is_subscribed): ?>
                            <button class="api-action-btn subscribe" id="subscribeBtn">
                                <i class="layui-icon layui-icon-add-1"></i>订阅API
                            </button>
                            <?php endif; ?>
                            <button class="api-action-btn <?php echo $is_favorited ? 'favorited' : 'favorite'; ?>" id="favoriteBtn">
                                <i class="layui-icon layui-icon-star<?php echo $is_favorited ? '-fill' : ''; ?>"></i><?php echo $is_favorited ? '已收藏' : '收藏'; ?>
                            </button>
                        </div>
                    </div>
                    <div class="api-detail-card-body">
                        <!-- API基本信息 -->
                        <div class="api-info">
                            <div class="api-info-item">
                                <span class="api-info-label">请求方式：</span>
                                <span class="api-info-value">
                                    <span class="api-method <?php echo strtolower($api['method']); ?>"><?php echo $api['method']; ?></span>
                                </span>
                            </div>
                            <div class="api-info-item">
                                <span class="api-info-label">接口地址：</span>
                                <span class="api-info-value"><?php echo $api['endpoint']; ?></span>
                            </div>
                            <div class="api-info-item">
                                <span class="api-info-label">价格：</span>
                                <span class="api-info-value">
                                    <?php if ($api['is_free']): ?>
                                    <span class="api-price free">免费</span>
                                    <?php else: ?>
                                    <span class="api-price">￥<?php echo $api['price']; ?>/<?php echo $api['price_unit']; ?></span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <div class="api-info-item">
                                <span class="api-info-label">所属分类：</span>
                                <span class="api-info-value"><?php echo $api['category_name']; ?></span>
                            </div>
                            <div class="api-info-item">
                                <span class="api-info-label">更新时间：</span>
                                <span class="api-info-value"><?php echo date('Y-m-d H:i:s', $api['update_time']); ?></span>
                            </div>
                        </div>
                        
                        <!-- API描述 -->
                        <div class="api-description">
                            <?php echo nl2br($api['description']); ?>
                        </div>
                        
                        <!-- API商家信息 -->
                        <div class="api-merchant">
                            <img src="<?php echo $merchant['logo'] ?: '/assets/images/default-logo.png'; ?>" class="api-merchant-logo" alt="<?php echo $merchant['name']; ?>">
                            <div class="api-merchant-info">
                                <div class="api-merchant-name">
                                    <?php echo $merchant['name']; ?>
                                    <span class="api-merchant-level level<?php echo $merchant['level']; ?>">
                                        <?php 
                                        $levels = ['', '一级商家', '二级商家', '三级商家', '四级商家', '五级商家'];
                                        echo $levels[$merchant['level']]; 
                                        ?>
                                    </span>
                                </div>
                                <div class="api-merchant-desc"><?php echo $merchant['description']; ?></div>
                            </div>
                        </div>
                        
                        <!-- API统计信息 -->
                        <div class="api-stats">
                            <div class="api-stat-item">
                                <div class="api-stat-value"><?php echo $api['call_count']; ?></div>
                                <div class="api-stat-label">调用次数</div>
                            </div>
                            <div class="api-stat-item">
                                <div class="api-stat-value"><?php echo $api['subscribe_count']; ?></div>
                                <div class="api-stat-label">订阅人数</div>
                            </div>
                            <div class="api-stat-item">
                                <div class="api-stat-value"><?php echo $api['favorite_count']; ?></div>
                                <div class="api-stat-label">收藏人数</div>
                            </div>
                            <div class="api-stat-item">
                                <div class="api-stat-value"><?php echo number_format($api['success_rate'], 1); ?>%</div>
                                <div class="api-stat-label">成功率</div>
                            </div>
                        </div>
                        
                        <!-- API文档 -->
                        <div class="layui-tab layui-tab-brief" lay-filter="docTab" style="margin-top: 20px;">
                            <ul class="layui-tab-title">
                                <li class="layui-this">接口说明</li>
                                <li>请求参数</li>
                                <li>返回参数</li>
                                <li>错误码</li>
                                <li>示例代码</li>
                            </ul>
                            <div class="layui-tab-content">
                                <!-- 接口说明 -->
                                <div class="layui-tab-item layui-show">
                                    <?php echo $api['description'] ? nl2br($api['description']) : '暂无接口说明'; ?>
                                </div>
                                
                                <!-- 请求参数 -->
                                <div class="layui-tab-item">
                                    <?php if (count($request_params) > 0): ?>
                                    <table class="layui-table" lay-skin="line">
                                        <thead>
                                            <tr>
                                                <th>参数名</th>
                                                <th>类型</th>
                                                <th>必填</th>
                                                <th>默认值</th>
                                                <th>说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($request_params as $param): ?>
                                            <tr>
                                                <td><?php echo $param['name']; ?></td>
                                                <td><?php echo $param['type']; ?></td>
                                                <td><?php echo $param['required'] ? '是' : '否'; ?></td>
                                                <td><?php echo $param['default_value'] ?: '-'; ?></td>
                                                <td><?php echo $param['description']; ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php else: ?>
                                    <div class="layui-none">暂无请求参数</div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- 返回参数 -->
                                <div class="layui-tab-item">
                                    <?php if (count($response_params) > 0): ?>
                                    <table class="layui-table" lay-skin="line">
                                        <thead>
                                            <tr>
                                                <th>参数名</th>
                                                <th>类型</th>
                                                <th>说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($response_params as $param): ?>
                                            <tr>
                                                <td><?php echo $param['name']; ?></td>
                                                <td><?php echo $param['type']; ?></td>
                                                <td><?php echo $param['description']; ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php else: ?>
                                    <div class="layui-none">暂无返回参数</div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- 错误码 -->
                                <div class="layui-tab-item">
                                    <?php if (count($error_codes) > 0): ?>
                                    <table class="layui-table" lay-skin="line">
                                        <thead>
                                            <tr>
                                                <th>错误码</th>
                                                <th>错误信息</th>
                                                <th>说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($error_codes as $error): ?>
                                            <tr>
                                                <td><?php echo $error['code']; ?></td>
                                                <td><?php echo $error['message']; ?></td>
                                                <td><?php echo $error['description']; ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <?php else: ?>
                                    <div class="layui-none">暂无错误码</div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- 示例代码 -->
                                <div class="layui-tab-item">
                                    <div class="layui-tab layui-tab-card" lay-filter="codeTab">
                                        <ul class="layui-tab-title">
                                            <li class="layui-this">PHP</li>
                                            <li>JavaScript</li>
                                            <li>Python</li>
                                            <li>Java</li>
                                            <li>Go</li>
                                        </ul>
                                        <div class="layui-tab-content">
                                            <!-- PHP示例 -->
                                            <div class="layui-tab-item layui-show">
                                                <div class="api-code-block">
                                                    <div class="api-code-title">PHP示例代码</div>
<pre><?php
$method = strtolower($api['method']);
$endpoint = $api['endpoint'];
$params = [];
foreach ($request_params as $param) {
    if ($param['required']) {
        $params[$param['name']] = $param['example'] ?: ($param['default_value'] ?: '');
    }
}
$paramsStr = var_export($params, true);

echo "<?php\n";
echo "// 设置API密钥\n";
echo "\$apiKey = 'YOUR_API_KEY';\n\n";

if ($method == 'get') {
    echo "// 构建请求URL\n";
    echo "\$url = '{$endpoint}';\n";
    echo "\$params = {$paramsStr};\n";
    echo "\$params['key'] = \$apiKey;\n";
    echo "\$url .= '?' . http_build_query(\$params);\n\n";
    
    echo "// 发送GET请求\n";
    echo "\$response = file_get_contents(\$url);\n\n";
} else {
    echo "// 设置请求参数\n";
    echo "\$params = {$paramsStr};\n";
    echo "\$params['key'] = \$apiKey;\n\n";
    
    echo "// 发送{$api['method']}请求\n";
    echo "\$ch = curl_init('{$endpoint}');\n";
    echo "curl_setopt(\$ch, CURLOPT_RETURNTRANSFER, true);\n";
    echo "curl_setopt(\$ch, CURLOPT_CUSTOMREQUEST, '{$api['method']}');\n";
    echo "curl_setopt(\$ch, CURLOPT_POSTFIELDS, http_build_query(\$params));\n";
    echo "curl_setopt(\$ch, CURLOPT_HTTPHEADER, [\n";
    echo "    'Content-Type: application/x-www-form-urlencoded',\n";
    echo "]);\n\n";
    echo "\$response = curl_exec(\$ch);\n";
    echo "curl_close(\$ch);\n\n";
}

echo "// 解析响应\n";
echo "\$result = json_decode(\$response, true);\n\n";

echo "// 处理结果\n";
echo "if (isset(\$result['code']) && \$result['code'] === 0) {\n";
echo "    // 请求成功，处理数据\n";
echo "    \$data = \$result['data'];\n";
echo "    print_r(\$data);\n";
echo "} else {\n";
echo "    // 请求失败，处理错误\n";
echo "    echo '错误: ' . (\$result['msg'] ?? '未知错误');\n";
echo "}\n";
?></pre>
                                                </div>
                                            </div>
                                            
                                            <!-- JavaScript示例 -->
                                            <div class="layui-tab-item">
                                                <div class="api-code-block">
                                                    <div class="api-code-title">JavaScript示例代码</div>
<pre><?php
$method = strtolower($api['method']);
$endpoint = $api['endpoint'];
$params = [];
foreach ($request_params as $param) {
    if ($param['required']) {
        $params[$param['name']] = $param['example'] ?: ($param['default_value'] ?: '');
    }
}
$paramsJson = json_encode($params, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
$paramsJson = str_replace('    ', '  ', $paramsJson);

echo "// 设置API密钥\n";
echo "const apiKey = 'YOUR_API_KEY';\n\n";

if ($method == 'get') {
    echo "// 构建请求参数\n";
    echo "const params = {$paramsJson};\n";
    echo "params.key = apiKey;\n\n";
    
    echo "// 构建URL\n";
    echo "const url = `{$endpoint}?\${new URLSearchParams(params)}`;\n\n";
    
    echo "// 发送GET请求\n";
    echo "fetch(url)\n";
    echo "  .then(response => response.json())\n";
    echo "  .then(result => {\n";
    echo "    if (result.code === 0) {\n";
    echo "      // 请求成功，处理数据\n";
    echo "      const data = result.data;\n";
    echo "      console.log(data);\n";
    echo "    } else {\n";
    echo "      // 请求失败，处理错误\n";
    echo "      console.error('错误:', result.msg || '未知错误');\n";
    echo "    }\n";
    echo "  })\n";
    echo "  .catch(error => {\n";
    echo "    console.error('请求异常:', error);\n";
    echo "  });\n";
} else {
    echo "// 设置请求参数\n";
    echo "const params = {$paramsJson};\n";
    echo "params.key = apiKey;\n\n";
    
    echo "// 发送{$api['method']}请求\n";
    echo "fetch('{$endpoint}', {\n";
    echo "  method: '{$api['method']}',\n";
    echo "  headers: {\n";
    echo "    'Content-Type': 'application/x-www-form-urlencoded',\n";
    echo "  },\n";
    echo "  body: new URLSearchParams(params)\n";
    echo "})\n";
    echo "  .then(response => response.json())\n";
    echo "  .then(result => {\n";
    echo "    if (result.code === 0) {\n";
    echo "      // 请求成功，处理数据\n";
    echo "      const data = result.data;\n";
    echo "      console.log(data);\n";
    echo "    } else {\n";
    echo "      // 请求失败，处理错误\n";
    echo "      console.error('错误:', result.msg || '未知错误');\n";
    echo "    }\n";
    echo "  })\n";
    echo "  .catch(error => {\n";
    echo "    console.error('请求异常:', error);\n";
    echo "  });\n";
}
?></pre>
                                                </div>
                                            </div>
                                            
                                            <!-- Python示例 -->
                                            <div class="layui-tab-item">
                                                <div class="api-code-block">
                                                    <div class="api-code-title">Python示例代码</div>
<pre><?php
$method = strtolower($api['method']);
$endpoint = $api['endpoint'];
$params = [];
foreach ($request_params as $param) {
    if ($param['required']) {
        $params[$param['name']] = $param['example'] ?: ($param['default_value'] ?: '');
    }
}

echo "import requests\n";
echo "import json\n\n";

echo "# 设置API密钥\n";
echo "api_key = 'YOUR_API_KEY'\n\n";

echo "# 设置请求参数\n";
echo "params = {\n";
foreach ($params as $key => $value) {
    if (is_numeric($value)) {
        echo "    '{$key}': {$value},\n";
    } else {
        echo "    '{$key}': '{$value}',\n";
    }
}
echo "    'key': api_key\n";
echo "}\n\n";

echo "# 发送{$api['method']}请求\n";
echo "response = requests.{$method}('{$endpoint}', ";
if ($method == 'get') {
    echo "params=params)\n\n";
} else {
    echo "data=params)\n\n";
}

echo "# 解析响应\n";
echo "result = response.json()\n\n";

echo "# 处理结果\n";
echo "if result.get('code') == 0:\n";
echo "    # 请求成功，处理数据\n";
echo "    data = result.get('data')\n";
echo "    print(data)\n";
echo "else:\n";
echo "    # 请求失败，处理错误\n";
echo "    print('错误:', result.get('msg', '未知错误'))\n";
?></pre>
                                                </div>
                                            </div>
                                            
                                            <!-- Java示例 -->
                                            <div class="layui-tab-item">
                                                <div class="api-code-block">
                                                    <div class="api-code-title">Java示例代码</div>
<pre><?php
$method = strtolower($api['method']);
$endpoint = $api['endpoint'];
$params = [];
foreach ($request_params as $param) {
    if ($param['required']) {
        $params[$param['name']] = $param['example'] ?: ($param['default_value'] ?: '');
    }
}

echo "import java.io.BufferedReader;\n";
echo "import java.io.DataOutputStream;\n";
echo "import java.io.InputStreamReader;\n";
echo "import java.net.HttpURLConnection;\n";
echo "import java.net.URL;\n";
echo "import java.net.URLEncoder;\n";
echo "import java.nio.charset.StandardCharsets;\n";
echo "import java.util.HashMap;\n";
echo "import java.util.Map;\n";
echo "import org.json.JSONObject;\n\n";

echo "public class ApiExample {\n";
echo "    public static void main(String[] args) {\n";
echo "        try {\n";
echo "            // 设置API密钥\n";
echo "            String apiKey = \"YOUR_API_KEY\";\n\n";

echo "            // 设置请求参数\n";
echo "            Map<String, String> params = new HashMap<>();\n";
foreach ($params as $key => $value) {
    if (is_numeric($value)) {
        echo "            params.put(\"{$key}\", \"{$value}\");\n";
    } else {
        echo "            params.put(\"{$key}\", \"{$value}\");\n";
    }
}
echo "            params.put(\"key\", apiKey);\n\n";

if ($method == 'get') {
    echo "            // 构建请求URL\n";
    echo "            StringBuilder urlBuilder = new StringBuilder(\"{$endpoint}?\");\n";
    echo "            for (Map.Entry<String, String> entry : params.entrySet()) {\n";
    echo "                urlBuilder.append(URLEncoder.encode(entry.getKey(), \"UTF-8\"));\n";
    echo "                urlBuilder.append(\"=\");\n";
    echo "                urlBuilder.append(URLEncoder.encode(entry.getValue(), \"UTF-8\"));\n";
    echo "                urlBuilder.append(\"&\");\n";
    echo "            }\n";
    echo "            String urlStr = urlBuilder.toString();\n";
    echo "            urlStr = urlStr.substring(0, urlStr.length() - 1); // 移除最后的 &\n\n";
    
    echo "            // 创建连接\n";
    echo "            URL url = new URL(urlStr);\n";
    echo "            HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n";
    echo "            conn.setRequestMethod(\"GET\");\n";
    echo "            conn.setRequestProperty(\"Accept\", \"application/json\");\n\n";
} else {
    echo "            // 构建请求参数字符串\n";
    echo "            StringBuilder postData = new StringBuilder();\n";
    echo "            for (Map.Entry<String, String> entry : params.entrySet()) {\n";
    echo "                if (postData.length() != 0) postData.append('&');\n";
    echo "                postData.append(URLEncoder.encode(entry.getKey(), \"UTF-8\"));\n";
    echo "                postData.append('=');\n";
    echo "                postData.append(URLEncoder.encode(entry.getValue(), \"UTF-8\"));\n";
    echo "            }\n";
    echo "            byte[] postDataBytes = postData.toString().getBytes(StandardCharsets.UTF_8);\n\n";
    
    echo "            // 创建连接\n";
    echo "            URL url = new URL(\"{$endpoint}\");\n";
    echo "            HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n";
    echo "            conn.setRequestMethod(\"{$api['method']}\");\n";
    echo "            conn.setRequestProperty(\"Content-Type\", \"application/x-www-form-urlencoded\");\n";
    echo "            conn.setRequestProperty(\"Content-Length\", String.valueOf(postDataBytes.length));\n";
    echo "            conn.setDoOutput(true);\n";
    echo "            conn.getOutputStream().write(postDataBytes);\n\n";
}

echo "            // 获取响应\n";
echo "            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), \"UTF-8\"));\n";
echo "            String inputLine;\n";
echo "            StringBuilder response = new StringBuilder();\n";
echo "            while ((inputLine = in.readLine()) != null) {\n";
echo "                response.append(inputLine);\n";
echo "            }\n";
echo "            in.close();\n\n";

echo "            // 解析响应\n";
echo "            JSONObject result = new JSONObject(response.toString());\n\n";

echo "            // 处理结果\n";
echo "            if (result.getInt(\"code\") == 0) {\n";
echo "                // 请求成功，处理数据\n";
echo "                JSONObject data = result.getJSONObject(\"data\");\n";
echo "                System.out.println(data.toString());\n";
echo "            } else {\n";
echo "                // 请求失败，处理错误\n";
echo "                String errorMsg = result.has(\"msg\") ? result.getString(\"msg\") : \"未知错误\";\n";
echo "                System.out.println(\"错误: \" + errorMsg);\n";
echo "            }\n";
echo "        } catch (Exception e) {\n";
echo "            e.printStackTrace();\n";
echo "        }\n";
echo "    }\n";
echo "}\n";
?></pre>
                                                </div>
                                            </div>
                                            
                                            <!-- Go示例 -->
                                            <div class="layui-tab-item">
                                                <div class="api-code-block">
                                                    <div class="api-code-title">Go示例代码</div>
<pre><?php
$method = strtolower($api['method']);
$endpoint = $api['endpoint'];
$params = [];
foreach ($request_params as $param) {
    if ($param['required']) {
        $params[$param['name']] = $param['example'] ?: ($param['default_value'] ?: '');
    }
}

echo "package main\n\n";
echo "import (\n";
echo "    \"encoding/json\"\n";
echo "    \"fmt\"\n";
echo "    \"io/ioutil\"\n";
echo "    \"net/http\"\n";
echo "    \"net/url\"\n";
echo "    \"strings\"\n";
echo ")\n\n";

echo "func main() {\n";
echo "    // 设置API密钥\n";
echo "    apiKey := \"YOUR_API_KEY\"\n\n";

echo "    // 设置请求参数\n";
echo "    params := url.Values{}\n";
foreach ($params as $key => $value) {
    if (is_numeric($value)) {
        echo "    params.Add(\"{$key}\", \"{$value}\")\n";
    } else {
        echo "    params.Add(\"{$key}\", \"{$value}\")\n";
    }
}
echo "    params.Add(\"key\", apiKey)\n\n";

if ($method == 'get') {
    echo "    // 构建请求URL\n";
    echo "    reqURL := \"{$endpoint}?\" + params.Encode()\n\n";
    
    echo "    // 发送GET请求\n";
    echo "    resp, err := http.Get(reqURL)\n";
    echo "    if err != nil {\n";
    echo "        fmt.Println(\"请求错误:\", err)\n";
    echo "        return\n";
    echo "    }\n";
    echo "    defer resp.Body.Close()\n\n";
} else {
    echo "    // 发送{$api['method']}请求\n";
    echo "    client := &http.Client{}\n";
    echo "    req, err := http.NewRequest(\"{$api['method']}\", \"{$endpoint}\", strings.NewReader(params.Encode()))\n";
    echo "    if err != nil {\n";
    echo "        fmt.Println(\"创建请求错误:\", err)\n";
    echo "        return\n";
    echo "    }\n\n";
    
    echo "    req.Header.Add(\"Content-Type\", \"application/x-www-form-urlencoded\")\n";
    echo "    resp, err := client.Do(req)\n";
    echo "    if err != nil {\n";
    echo "        fmt.Println(\"请求错误:\", err)\n";
    echo "        return\n";
    echo "    }\n";
    echo "    defer resp.Body.Close()\n\n";
}

echo "    // 读取响应\n";
echo "    body, err := ioutil.ReadAll(resp.Body)\n";
echo "    if err != nil {\n";
echo "        fmt.Println(\"读取响应错误:\", err)\n";
echo "        return\n";
echo "    }\n\n";

echo "    // 解析响应\n";
echo "    var result map[string]interface{}\n";
echo "    err = json.Unmarshal(body, &result)\n";
echo "    if err != nil {\n";
echo "        fmt.Println(\"解析响应错误:\", err)\n";
echo "        return\n";
echo "    }\n\n";

echo "    // 处理结果\n";
echo "    if code, ok := result[\"code\"].(float64); ok && code == 0 {\n";
echo "        // 请求成功，处理数据\n";
echo "        data := result[\"data\"]\n";
echo "        fmt.Println(data)\n";
echo "    } else {\n";
echo "        // 请求失败，处理错误\n";
echo "        msg, _ := result[\"msg\"].(string)\n";
echo "        if msg == \"\" {\n";
echo "            msg = \"未知错误\"\n";
echo "        }\n";
echo "        fmt.Println(\"错误:\", msg)\n";
echo "    }\n";
echo "}\n";
?></pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md4">
                <!-- 订阅API卡片 -->
                <?php if (!$is_subscribed && !$api['is_free']): ?>
                <div class="api-detail-card api-sidebar-card">
                    <div class="api-detail-card-header">
                        <h2 class="api-detail-card-title">订阅API</h2>
                    </div>
                    <div class="api-detail-card-body">
                        <div class="api-subscribe-form">
                            <div class="api-subscribe-title">选择套餐</div>
                            
                            <?php foreach ($packages as $package): ?>
                            <div class="api-subscribe-option" data-id="<?php echo $package['id']; ?>">
                                <input type="radio" name="package" class="api-subscribe-radio" value="<?php echo $package['id']; ?>">
                                <div class="api-subscribe-info">
                                    <div class="api-subscribe-name"><?php echo $package['name']; ?></div>
                                    <div class="api-subscribe-desc"><?php echo $package['description']; ?></div>
                                </div>
                                <div class="api-subscribe-price">￥<?php echo $package['price']; ?></div>
                            </div>
                            <?php endforeach; ?>
                            
                            <button class="api-subscribe-btn" id="confirmSubscribeBtn" disabled>立即订阅</button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- 相关API卡片 -->
                <div class="api-detail-card api-sidebar-card">
                    <div class="api-detail-card-header">
                        <h2 class="api-detail-card-title">相关API</h2>
                    </div>
                    <div class="api-detail-card-body">
                        <?php if (count($related_apis) > 0): ?>
                        <?php foreach ($related_apis as $related): ?>
                        <div class="api-related-item">
                            <span class="api-related-method api-method <?php echo strtolower($related['method']); ?>"><?php echo $related['method']; ?></span>
                            <a href="/api/detail?id=<?php echo $related['id']; ?>" class="api-related-name"><?php echo $related['name']; ?></a>
                            <span class="api-related-price <?php echo $related['is_free'] ? 'free' : ''; ?>">
                                <?php echo $related['is_free'] ? '免费' : '￥'.$related['price']; ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <div class="layui-none">暂无相关API</div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- 热门API卡片 -->
                <div class="api-detail-card api-sidebar-card">
                    <div class="api-detail-card-header">
                        <h2 class="api-detail-card-title">热门API</h2>
                    </div>
                    <div class="api-detail-card-body">
                        <?php if (count($hot_apis) > 0): ?>
                        <?php foreach ($hot_apis as $hot): ?>
                        <div class="api-related-item">
                            <span class="api-related-method api-method <?php echo strtolower($hot['method']); ?>"><?php echo $hot['method']; ?></span>
                            <a href="/api/detail?id=<?php echo $hot['id']; ?>" class="api-related-name"><?php echo $hot['name']; ?></a>
                            <span class="api-related-price <?php echo $hot['is_free'] ? 'free' : ''; ?>">
                                <?php echo $hot['is_free'] ? '免费' : '￥'.$hot['price']; ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <div class="layui-none">暂无热门API</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <?php include 'templates/common/footer.php'; ?>
</div>

<!-- 订阅确认弹窗 -->
<script type="text/html" id="subscribeConfirmTpl">
    <div style="padding: 20px;">
        <div style="font-size: 16px; margin-bottom: 15px;">确认订阅</div>
        <div style="margin-bottom: 15px;">
            <div>API名称：<span style="font-weight: bold;"><?php echo $api['name']; ?></span></div>
            <div>套餐名称：<span style="font-weight: bold;" id="packageName"></span></div>
            <div>套餐价格：<span style="font-weight: bold; color: #FF5722;" id="packagePrice"></span></div>
        </div>
        <div style="color: #999; font-size: 14px;">
            点击确认订阅后，系统将跳转至支付页面完成支付。
        </div>
    </div>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/assets/js/common.js"></script>
<script>
layui.use(['layer', 'element', 'util'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var element = layui.element;
    var util = layui.util;
    
    // API ID
    var apiId = <?php echo $api['id']; ?>;
    
    // 收藏API
    $('#favoriteBtn').click(function() {
        var isFavorited = $(this).hasClass('favorited');
        var loadIndex = layer.load(2);
        
        $.post('/api/favorite', {
            api_id: apiId,
            action: isFavorited ? 'cancel' : 'add'
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                if (isFavorited) {
                    $('#favoriteBtn').removeClass('favorited').addClass('favorite').html('<i class="layui-icon layui-icon-star"></i>收藏');
                } else {
                    $('#favoriteBtn').removeClass('favorite').addClass('favorited').html('<i class="layui-icon layui-icon-star-fill"></i>已收藏');
                }
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    });
    
    // 订阅选项点击事件
    $('.api-subscribe-option').click(function() {
        $('.api-subscribe-option').removeClass('selected');
        $(this).addClass('selected');
        $(this).find('input[type="radio"]').prop('checked', true);
        $('#confirmSubscribeBtn').prop('disabled', false);
    });
    
    // 确认订阅按钮点击事件
    $('#confirmSubscribeBtn').click(function() {
        var packageId = $('input[name="package"]:checked').val();
        if (!packageId) {
            layer.msg('请选择套餐', {icon: 2});
            return;
        }
        
        // 获取套餐信息
        var packageName = $('.api-subscribe-option.selected .api-subscribe-name').text();
        var packagePrice = $('.api-subscribe-option.selected .api-subscribe-price').text();
        
        // 显示确认弹窗
        layer.open({
            type: 1,
            title: '订阅确认',
            area: ['400px', 'auto'],
            content: $('#subscribeConfirmTpl').html(),
            success: function(layero, index) {
                $('#packageName').text(packageName);
                $('#packagePrice').text(packagePrice);
            },
            btn: ['确认订阅', '取消'],
            yes: function(index) {
                layer.close(index);
                
                // 跳转到支付页面
                location.href = '/pay?type=api&api_id=' + apiId + '&package_id=' + packageId;
            }
        });
    });
});
</script>
</body>
</html>
