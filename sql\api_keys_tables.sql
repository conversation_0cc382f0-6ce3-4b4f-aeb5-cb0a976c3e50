-- API密钥表
CREATE TABLE IF NOT EXISTS `api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `key_name` varchar(100) NOT NULL COMMENT '密钥名称',
  `key_value` varchar(64) NOT NULL COMMENT '密钥值',
  `ip_whitelist` varchar(500) DEFAULT NULL COMMENT 'IP白名单，多个IP用逗号分隔',
  `request_limit` int(11) NOT NULL DEFAULT '0' COMMENT '请求限制次数，0表示不限制',
  `request_count` int(11) NOT NULL DEFAULT '0' COMMENT '已请求次数',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间，0表示永不过期',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `last_used_time` int(11) NOT NULL DEFAULT '0' COMMENT '最后使用时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_key_value` (`key_value`),
  KEY `idx_user` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API密钥表';

-- API请求日志表
CREATE TABLE IF NOT EXISTS `api_request_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_key_id` int(11) NOT NULL COMMENT 'API密钥ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `request_uri` varchar(255) NOT NULL COMMENT '请求URI',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_params` text COMMENT '请求参数',
  `client_ip` varchar(50) NOT NULL COMMENT '客户端IP',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `request_time` int(11) NOT NULL COMMENT '请求时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_key` (`api_key_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_time` (`request_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API请求日志表';

-- 插入测试数据
INSERT INTO `api_keys` (`user_id`, `key_name`, `key_value`, `ip_whitelist`, `request_limit`, `request_count`, `expire_time`, `status`, `create_time`, `last_used_time`) VALUES
(1, '测试密钥', 'test_api_key_12345678', '', 1000, 0, 0, 1, UNIX_TIMESTAMP(), 0);