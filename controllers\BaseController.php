<?php
/**
 * 基础控制器
 * 提供共享的控制器功能
 */
class BaseController
{
    /**
     * 数据库连接
     * @var PDO
     */
    protected $db;
    
    /**
     * 站点配置
     * @var array
     */
    protected $siteConfig;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化数据库连接
        $this->initDb();
        
        // 加载站点配置
        $this->loadSiteConfig();
        
        // 启动会话
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * 初始化数据库连接
     */
    protected function initDb()
    {
        try {
            $config = require_once __DIR__ . '/../config/database.php';
            $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
            $this->db = new Database($dsn, $config['username'], $config['password']);
            
            // 检查必要的表是否存在，如果不存在则创建
            $this->checkAndCreateTables();
        } catch (Exception $e) {
            // 记录错误日志
            error_log('数据库连接失败：' . $e->getMessage());
            
            // 如果是AJAX请求，返回JSON错误
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                $this->error('数据库连接失败，请检查配置');
            } else {
                // 显示友好的错误页面
                $this->errorResponse('数据库连接失败，请检查配置');
            }
        }
    }
    
    /**
     * 检查并创建必要的表
     */
    protected function checkAndCreateTables()
    {
        // 检查users表
        if (!$this->db->tableExists('users')) {
            $this->db->execute("
                CREATE TABLE `users` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `username` varchar(50) NOT NULL,
                    `password` varchar(255) NOT NULL,
                    `email` varchar(100) NOT NULL,
                    `phone` varchar(20) DEFAULT NULL,
                    `status` tinyint(1) NOT NULL DEFAULT '1',
                    `create_time` int(11) NOT NULL,
                    `update_time` int(11) DEFAULT NULL,
                    `last_login_time` int(11) DEFAULT NULL,
                    `last_login_ip` varchar(50) DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `username` (`username`),
                    UNIQUE KEY `email` (`email`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
        }
        
        // 检查admins表
        if (!$this->db->tableExists('admins')) {
            $this->db->execute("
                CREATE TABLE `admins` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `username` varchar(50) NOT NULL,
                    `password` varchar(255) NOT NULL,
                    `role` varchar(20) NOT NULL DEFAULT 'admin',
                    `status` tinyint(1) NOT NULL DEFAULT '1',
                    `create_time` int(11) NOT NULL,
                    `update_time` int(11) DEFAULT NULL,
                    `last_login_time` int(11) DEFAULT NULL,
                    `last_login_ip` varchar(50) DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `username` (`username`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
            
            // 创建默认管理员账号
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            $time = time();
            $this->db->execute(
                "INSERT INTO `admins` (`username`, `password`, `role`, `status`, `create_time`) VALUES (?, ?, ?, ?, ?)",
                ['admin', $password, 'super_admin', 1, $time]
            );
        }
        
        // 检查site_config表
        if (!$this->db->tableExists('site_config')) {
            $this->db->execute("
                CREATE TABLE `site_config` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `key` varchar(50) NOT NULL,
                    `value` text,
                    `create_time` int(11) NOT NULL,
                    `update_time` int(11) DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `key` (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
            
            // 插入默认配置
            $time = time();
            $configs = [
                ['site_name', 'API商业系统', $time],
                ['site_keywords', 'API,接口,开发', $time],
                ['site_description', 'API商业系统是一个多功能的API接口管理和销售平台', $time],
                ['site_logo', '/assets/images/logo.png', $time],
                ['site_icp', '', $time],
                ['site_copyright', '© ' . date('Y') . ' API商业系统', $time],
                ['admin_email', '<EMAIL>', $time]
            ];
            
            foreach ($configs as $config) {
                $this->db->execute(
                    "INSERT INTO `site_config` (`key`, `value`, `create_time`) VALUES (?, ?, ?)",
                    $config
                );
            }
        }
    }
    
    /**
     * 加载站点配置
     */
    protected function loadSiteConfig()
    {
        try {
            $configs = $this->db->query("SELECT * FROM site_config");
            $this->siteConfig = [];
            
            foreach ($configs as $config) {
                $this->siteConfig[$config['key']] = $config['value'];
            }
        } catch (Exception $e) {
            // 如果数据库未初始化，使用默认配置
            $this->siteConfig = [
                'site_name' => 'API商业系统',
                'site_keywords' => 'API,接口,开发',
                'site_description' => 'API商业系统是一个多功能的API接口管理和销售平台',
                'site_logo' => '/assets/images/logo.png',
                'site_icp' => '',
                'site_copyright' => '© ' . date('Y') . ' API商业系统',
                'admin_email' => '<EMAIL>'
            ];
        }
    }
    
    /**
     * 渲染页面
     * 
     * @param string $template 模板路径
     * @param array $data 模板数据
     */
    protected function render($template, $data = [])
    {
        // 将站点配置添加到模板数据
        $data['site_config'] = $this->siteConfig;
        
        // 将当前用户信息添加到模板数据
        if ($this->isLoggedIn()) {
            $data['current_user'] = $this->getCurrentUser();
        }
        
        // 提取变量
        extract($data);
        
        // 包含模板文件
        $templateFile = __DIR__ . '/../templates/' . $template . '.php';
        
        if (file_exists($templateFile)) {
            include $templateFile;
        } else {
            $this->errorResponse('模板文件不存在：' . $template);
        }
    }
    
    /**
     * 检查用户是否已登录
     * 
     * @return bool
     */
    protected function isLoggedIn()
    {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * 检查管理员是否已登录
     * 
     * @return bool
     */
    protected function isAdminLoggedIn()
    {
        return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
    }
    
    /**
     * 检查商家是否已登录
     * 
     * @return bool
     */
    protected function isMerchantLoggedIn()
    {
        return isset($_SESSION['merchant_id']) && !empty($_SESSION['merchant_id']);
    }
    
    /**
     * 获取当前登录用户ID
     * 
     * @return int|null
     */
    protected function getCurrentUserId()
    {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * 获取当前登录管理员ID
     * 
     * @return int|null
     */
    protected function getCurrentAdminId()
    {
        return $_SESSION['admin_id'] ?? null;
    }
    
    /**
     * 获取当前登录商家ID
     * 
     * @return int|null
     */
    protected function getCurrentMerchantId()
    {
        return $_SESSION['merchant_id'] ?? null;
    }
    
    /**
     * 检查管理员登录状态
     */
    protected function checkAdminLogin()
    {
        if (!isset($_SESSION['admin_id'])) {
            // 未登录，重定向到登录页面
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                // AJAX请求返回JSON
                header('Content-Type: application/json');
                echo json_encode(['code' => 401, 'msg' => '请先登录']);
                exit;
            } else {
                // 普通请求重定向到登录页面
                header('Location: /admin/login');
                exit;
            }
        }
        
        // 检查管理员状态
        $admin = $this->db->query(
            "SELECT * FROM admins WHERE id = ? AND status = 1", 
            [$_SESSION['admin_id']], 
            true
        );
        
        if (!$admin) {
            // 管理员不存在或已禁用，清除会话并重定向到登录页面
            unset($_SESSION['admin_id']);
            unset($_SESSION['admin_username']);
            unset($_SESSION['admin_role']);
            
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                // AJAX请求返回JSON
                header('Content-Type: application/json');
                echo json_encode(['code' => 401, 'msg' => '账号已被禁用或不存在']);
                exit;
            } else {
                // 普通请求重定向到登录页面
                header('Location: /admin/login');
                exit;
            }
        }
    }
        return null;
    }
    
    /**
     * 获取当前登录管理员信息
     * 
     * @return array|null
     */
    protected function getCurrentAdmin()
    {
        $adminId = $this->getCurrentAdminId();
        if ($adminId) {
            return $this->db->query("SELECT * FROM admins WHERE id = ?", [$adminId], true);
        }
        return null;
    }
    
    /**
     * 获取当前登录商家信息
     * 
     * @return array|null
     */
    protected function getCurrentMerchant()
    {
        $merchantId = $this->getCurrentMerchantId();
        if ($merchantId) {
            return $this->db->query("SELECT * FROM merchants WHERE id = ?", [$merchantId], true);
        }
        return null;
    }
    
    /**
     * 检查用户登录状态，未登录则跳转到登录页面
     */
    protected function checkLogin()
    {
        if (!$this->isLoggedIn()) {
            $this->redirect('/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
            exit;
        }
    }
    
    /**
     * 检查管理员登录状态，未登录则跳转到管理员登录页面
     */
    protected function checkAdminLogin()
    {
        if (!$this->isAdminLoggedIn()) {
            $this->redirect('/admin/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
            exit;
        }
    }
    
    /**
     * 检查商家登录状态，未登录则跳转到商家登录页面
     */
    protected function checkMerchantLogin()
    {
        if (!$this->isMerchantLoggedIn()) {
            $this->redirect('/merchant/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
            exit;
        }
    }
    
    /**
     * 检查管理员权限
     * 
     * @param string $permission 权限标识
     */
    protected function checkAdminPermission($permission)
    {
        // 先检查管理员登录状态
        $this->checkAdminLogin();
        
        // 获取当前管理员信息
        $admin = $this->getCurrentAdmin();
        
        // 超级管理员拥有所有权限
        if ($admin['role'] === 'super_admin') {
            return true;
        }
        
        // 检查权限
        $hasPermission = $this->db->query(
            "SELECT COUNT(*) as count FROM admin_permissions 
            WHERE admin_id = ? AND permission = ? AND status = 1",
            [$admin['id'], $permission],
            true
        );
        
        if ($hasPermission['count'] == 0) {
            $this->errorResponse('您没有权限执行此操作');
        }
        
        return true;
    }
    
    /**
     * 重定向
     * 
     * @param string $url 目标URL
     */
    protected function redirect($url)
    {
        header('Location: ' . $url);
        exit;
    }
    
    /**
     * 获取请求参数
     * 
     * @param string $name 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getParam($name, $default = null)
    {
        if (isset($_GET[$name])) {
            return $_GET[$name];
        }
        
        if (isset($_POST[$name])) {
            return $_POST[$name];
        }
        
        // 检查JSON请求
        $json = $this->getJsonInput();
        if ($json && isset($json[$name])) {
            return $json[$name];
        }
        
        return $default;
    }
    
    /**
     * 获取JSON请求数据
     * 
     * @return array|null
     */
    protected function getJsonInput()
    {
        $input = file_get_contents('php://input');
        if (!empty($input)) {
            $json = json_decode($input, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $json;
            }
        }
        return null;
    }
    
    /**
     * 返回成功响应
     * 
     * @param mixed $data 响应数据
     * @param string $msg 响应消息
     */
    protected function success($data = null, $msg = '操作成功')
    {
        $this->jsonResponse(0, $msg, $data);
    }
    
    /**
     * 返回错误响应
     * 
     * @param string $msg 错误消息
     * @param int $code 错误代码
     * @param mixed $data 响应数据
     */
    protected function error($msg = '操作失败', $code = 1, $data = null)
    {
        $this->jsonResponse($code, $msg, $data);
    }
    
    /**
     * 返回JSON响应
     * 
     * @param int $code 状态码
     * @param string $msg 消息
     * @param mixed $data 数据
     */
    protected function jsonResponse($code, $msg, $data = null)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 返回错误响应并退出
     * 
     * @param string $message 错误消息
     * @param int $code HTTP状态码
     */
    protected function errorResponse($message, $code = 500)
    {
        http_response_code($code);
        
        // 检查是否是AJAX请求
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->error($message, $code);
        } else {
            // 普通请求，显示错误页面
            echo '<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>服务器错误</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
                    h1 { color: #d9534f; }
                    .error-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin-bottom: 20px; }
                    .error-message { font-weight: bold; }
                    .back-link { display: inline-block; margin-top: 20px; color: #007bff; text-decoration: none; }
                    .back-link:hover { text-decoration: underline; }
                </style>
            </head>
            <body>
                <h1>服务器错误</h1>
                <div class="error-box">
                    <p class="error-message">' . htmlspecialchars($message) . '</p>
                </div>
                <p>请稍后再试或联系网站管理员。</p>
                <a href="javascript:history.back()" class="back-link">返回上一页</a>
            </body>
            </html>';
        }
        exit;
    }
}