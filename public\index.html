<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API管理系统 - 专业的API接口管理平台</title>
    <meta name="keywords" content="API,接口,管理,平台">
    <meta name="description" content="专业的API接口管理平台，提供API发布、管理、调用等服务">
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../Easyweb/assets/module/admin.css">
    <link rel="icon" href="../Easyweb/assets/images/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        /* 头部导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            height: 70px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            text-decoration: none;
            color: white;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background: rgba(255,255,255,0.2);
        }
        
        .nav-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 8px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .btn-outline {
            color: white;
            border-color: white;
        }
        
        .btn-outline:hover {
            background: white;
            color: #667eea;
        }
        
        .btn-primary {
            background: white;
            color: #667eea;
        }
        
        .btn-primary:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
        }
        
        /* 主要内容 */
        .main-content {
            margin-top: 70px;
        }
        
        /* 英雄区域 */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-large {
            padding: 15px 35px;
            font-size: 1.1rem;
            border-radius: 30px;
        }
        
        .btn-white {
            background: white;
            color: #667eea;
        }
        
        .btn-white:hover {
            background: #f8f9fa;
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        /* 特性区域 */
        .features {
            padding: 100px 0;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .section-title h2 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .section-title p {
            font-size: 1.2rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }
        
        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 30px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 50px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2rem;
            color: white;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.8;
        }
        
        /* 统计区域 */
        .stats {
            padding: 80px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item h3 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .stat-item p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        /* API展示区域 */
        .api-showcase {
            padding: 100px 0;
            background: white;
        }
        
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }
        
        .api-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            transition: all 0.3s;
        }
        
        .api-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.1);
        }
        
        .api-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .api-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }
        
        .api-method {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }
        
        .method-get { background: #28a745; }
        .method-post { background: #007bff; }
        .method-put { background: #ffc107; color: #333; }
        .method-delete { background: #dc3545; }
        
        .api-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .api-price {
            font-size: 1.1rem;
            font-weight: 600;
            color: #667eea;
        }
        
        /* 页脚 */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 60px 0 30px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .footer-section h3 {
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .footer-section ul {
            list-style: none;
        }
        
        .footer-section ul li {
            margin-bottom: 10px;
        }
        
        .footer-section ul li a {
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-section ul li a:hover {
            color: white;
        }
        
        .footer-bottom {
            border-top: 1px solid #34495e;
            padding-top: 30px;
            text-align: center;
            color: #bdc3c7;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .api-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="nav-container">
            <a href="#" class="logo">
                <i class="layui-icon layui-icon-api"></i>
                API管理系统
            </a>
            
            <nav>
                <ul class="nav-menu">
                    <li><a href="#home" class="active">首页</a></li>
                    <li><a href="#features">功能特性</a></li>
                    <li><a href="#apis">API市场</a></li>
                    <li><a href="#pricing">价格</a></li>
                    <li><a href="#docs">文档</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </nav>
            
            <div class="nav-actions">
                <a href="login.html" class="btn btn-outline">登录</a>
                <a href="register.html" class="btn btn-primary">注册</a>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
        <!-- 英雄区域 -->
        <section id="home" class="hero">
            <div class="hero-container">
                <h1>专业的API管理平台</h1>
                <p>为开发者和企业提供完整的API生命周期管理解决方案，从发布到调用，从监控到变现，一站式服务</p>
                <div class="hero-actions">
                    <a href="register.html" class="btn btn-white btn-large">免费开始</a>
                    <a href="#features" class="btn btn-outline btn-large">了解更多</a>
                </div>
            </div>
        </section>

        <!-- 特性区域 -->
        <section id="features" class="features">
            <div class="container">
                <div class="section-title">
                    <h2>强大的功能特性</h2>
                    <p>为您提供全方位的API管理解决方案</p>
                </div>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-api"></i>
                        </div>
                        <h3>API管理中心</h3>
                        <p>完整的API生命周期管理，支持API发布、版本控制、文档生成、在线调试等功能</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-shop"></i>
                        </div>
                        <h3>商家管理系统</h3>
                        <p>支持商家入驻、多等级管理、收入结算，打造完整的API生态系统</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-user"></i>
                        </div>
                        <h3>用户权限体系</h3>
                        <p>灵活的用户权限管理，支持多级会员、角色权限、API访问控制</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-rmb"></i>
                        </div>
                        <h3>支付与变现</h3>
                        <p>集成支付宝、微信支付，支持多种计费模式，帮助API提供者轻松变现</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-chart"></i>
                        </div>
                        <h3>数据统计分析</h3>
                        <p>详细的调用统计、收入分析、用户行为分析，助力业务决策</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-set"></i>
                        </div>
                        <h3>系统配置管理</h3>
                        <p>灵活的系统配置，支持轮播图管理、邮件配置、安全设置等</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 统计区域 -->
        <section class="stats">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-item">
                        <h3 id="apiCount">1,234</h3>
                        <p>API接口数量</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="userCount">5,678</h3>
                        <p>注册用户</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="callCount">1.2M</h3>
                        <p>API调用次数</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="merchantCount">456</h3>
                        <p>合作商家</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- API展示区域 -->
        <section id="apis" class="api-showcase">
            <div class="container">
                <div class="section-title">
                    <h2>热门API接口</h2>
                    <p>精选优质API接口，满足各种业务需求</p>
                </div>
                
                <div class="api-grid" id="apiGrid">
                    <!-- API卡片将通过JavaScript动态加载 -->
                </div>
                
                <div style="text-align: center; margin-top: 40px;">
                    <a href="api-market.html" class="btn btn-primary btn-large">查看更多API</a>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>产品</h3>
                    <ul>
                        <li><a href="#features">功能特性</a></li>
                        <li><a href="#apis">API市场</a></li>
                        <li><a href="#pricing">价格方案</a></li>
                        <li><a href="#docs">开发文档</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>服务</h3>
                    <ul>
                        <li><a href="#support">技术支持</a></li>
                        <li><a href="#community">开发者社区</a></li>
                        <li><a href="#blog">技术博客</a></li>
                        <li><a href="#status">服务状态</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>公司</h3>
                    <ul>
                        <li><a href="#about">关于我们</a></li>
                        <li><a href="#careers">招聘信息</a></li>
                        <li><a href="#press">媒体报道</a></li>
                        <li><a href="#contact">联系我们</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>法律</h3>
                    <ul>
                        <li><a href="#privacy">隐私政策</a></li>
                        <li><a href="#terms">服务条款</a></li>
                        <li><a href="#security">安全说明</a></li>
                        <li><a href="#compliance">合规认证</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 API管理系统. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script src="../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPopularApis();
            animateStats();
            initSmoothScroll();
        });
        
        // 加载热门API
        function loadPopularApis() {
            // 模拟API数据
            const apis = [
                {
                    name: '天气查询API',
                    method: 'GET',
                    description: '获取全国各城市的实时天气信息，支持7天天气预报',
                    price: '¥0.01/次'
                },
                {
                    name: '身份证查询API',
                    method: 'POST',
                    description: '验证身份证号码的真实性，返回归属地等信息',
                    price: '¥0.05/次'
                },
                {
                    name: '手机归属地API',
                    method: 'GET',
                    description: '查询手机号码的归属地、运营商等详细信息',
                    price: '¥0.02/次'
                },
                {
                    name: '快递查询API',
                    method: 'GET',
                    description: '支持主流快递公司的物流信息查询',
                    price: '¥0.03/次'
                },
                {
                    name: '汇率查询API',
                    method: 'GET',
                    description: '实时汇率查询，支持全球主要货币',
                    price: '¥0.01/次'
                },
                {
                    name: '短信发送API',
                    method: 'POST',
                    description: '高到达率的短信发送服务，支持验证码、通知等',
                    price: '¥0.08/条'
                }
            ];
            
            const apiGrid = document.getElementById('apiGrid');
            apiGrid.innerHTML = '';
            
            apis.forEach(api => {
                const apiCard = document.createElement('div');
                apiCard.className = 'api-card';
                apiCard.innerHTML = `
                    <div class="api-header">
                        <div class="api-name">${api.name}</div>
                        <div class="api-method method-${api.method.toLowerCase()}">${api.method}</div>
                    </div>
                    <div class="api-description">${api.description}</div>
                    <div class="api-price">${api.price}</div>
                `;
                apiGrid.appendChild(apiCard);
            });
        }
        
        // 统计数字动画
        function animateStats() {
            const stats = [
                { id: 'apiCount', target: 1234 },
                { id: 'userCount', target: 5678 },
                { id: 'callCount', target: 1200000, suffix: 'M', divisor: 1000000 },
                { id: 'merchantCount', target: 456 }
            ];
            
            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                const target = stat.target;
                const divisor = stat.divisor || 1;
                const suffix = stat.suffix || '';
                let current = 0;
                const increment = target / 100;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    
                    let displayValue = Math.floor(current / divisor);
                    if (divisor > 1) {
                        displayValue = (current / divisor).toFixed(1);
                    }
                    
                    element.textContent = displayValue.toLocaleString() + suffix;
                }, 20);
            });
        }
        
        // 平滑滚动
        function initSmoothScroll() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        const offsetTop = targetElement.offsetTop - 70; // 考虑固定头部高度
                        
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }
        
        // 导航菜单激活状态
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-menu a');
            
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.clientHeight;
                
                if (window.pageYOffset >= sectionTop && window.pageYOffset < sectionTop + sectionHeight) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>