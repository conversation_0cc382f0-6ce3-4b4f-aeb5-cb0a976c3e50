<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>富文本编辑器</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=315"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div style="margin-bottom: 10px;">
                <button id="btnEdtGetData" class="layui-btn layui-btn-sm" type="button">获取内容</button>
                <button id="btnEdtSetData" class="layui-btn layui-btn-sm" type="button">设置内容</button>
                <button id="btnEdtAddData" class="layui-btn layui-btn-sm" type="button">插入内容</button>
                <a href="http://whvse.gitee.io/html-test/ckeditor/samples/toolbarconfigurator"
                   class="layui-btn layui-btn-primary layui-btn-sm" target="_blank">配置工具栏</a>
            </div>
            <textarea id="demoCkEditor"></textarea>
        </div>
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=315"></script>
<script>
    layui.use(['layer', 'CKEDITOR'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var CKEDITOR = layui.CKEDITOR;

        // 渲染富文本编辑器
        CKEDITOR.replace('demoCkEditor', {height: 450});
        var insEdt = CKEDITOR.instances.demoCkEditor;

        // 获取内容
        $('#btnEdtGetData').click(function () {
            var content = insEdt.getData();  // 获取到内容
            layer.prompt({
                shade: .1,
                offset: '35px',
                title: '源码',
                skin: 'layui-layer-admin layui-layer-prompt',
                formType: 2,
                value: content,
                btn: []
            });
        });

        // 设置内容
        $('#btnEdtSetData').click(function () {
            insEdt.setData('<h1><span style="color: red;">Hello Word!</span></h1>');
        });

        // 插入内容
        $('#btnEdtAddData').click(function () {
            insEdt.insertHtml('<h1><span style="color: #00CC00;">EasyWeb~</span></h1>');
        });

    });
</script>
</body>
</html>