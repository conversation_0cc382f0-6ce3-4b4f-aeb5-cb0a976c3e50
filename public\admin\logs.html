<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统日志 - API管理系统</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
    <link rel="icon" href="../../Easyweb/assets/images/favicon.ico">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            line-height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 220px;
            background: #2f3349;
            overflow-y: auto;
            z-index: 999;
        }
        
        .admin-main {
            margin-left: 220px;
            margin-top: 60px;
            padding: 20px;
            min-height: calc(100vh - 60px);
            background: #f5f5f5;
        }
        
        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-menu li {
            border-bottom: 1px solid #3a3f5c;
        }
        
        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: #b8c5d6;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        
        .nav-menu i {
            margin-right: 10px;
            width: 16px;
        }
        
        .user-info {
            float: right;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            vertical-align: middle;
            margin-right: 10px;
        }
        
        .level-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .level-debug { background: #8c8c8c; }
        .level-info { background: #1890ff; }
        .level-warning { background: #fa8c16; }
        .level-error { background: #f5222d; }
        .level-critical { background: #722ed1; }
        
        .type-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .type-system { background: #52c41a; }
        .type-api { background: #1890ff; }
        .type-user { background: #fa8c16; }
        .type-admin { background: #722ed1; }
        .type-payment { background: #eb2f96; }
        .type-security { background: #f5222d; }
        .type-database { background: #13c2c2; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-item {
            text-align: center;
        }
        
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .log-detail {
            max-width: 500px;
            word-break: break-all;
            white-space: pre-wrap;
        }
        
        .context-data {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-tabs {
            margin-bottom: 20px;
        }
        
        .log-tabs .layui-tab-title li {
            padding: 0 20px;
        }
        
        .chart-container {
            height: 300px;
            margin: 20px 0;
        }
        
        .export-btn {
            margin-left: 10px;
        }
        
        .cleanup-section {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="admin-header">
        <div style="float: left;">
            <h2 style="margin: 0; font-size: 18px;">
                <i class="layui-icon layui-icon-file"></i>
                系统日志
            </h2>
        </div>
        <div class="user-info">
            <img src="../../Easyweb/assets/images/head.jpg" alt="头像" class="user-avatar">
            <span id="adminName">管理员</span>
            <a href="javascript:;" onclick="logout()" style="color: white; margin-left: 15px;">
                <i class="layui-icon layui-icon-logout"></i> 退出
            </a>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="admin-sidebar">
        <ul class="nav-menu">
            <li>
                <a href="dashboard.html">
                    <i class="layui-icon layui-icon-home"></i>
                    仪表板
                </a>
            </li>
            <li>
                <a href="api-list.html">
                    <i class="layui-icon layui-icon-api"></i>
                    API管理
                </a>
            </li>
            <li>
                <a href="user-list.html">
                    <i class="layui-icon layui-icon-user"></i>
                    用户管理
                </a>
            </li>
            <li>
                <a href="merchant-list.html">
                    <i class="layui-icon layui-icon-shop"></i>
                    商家管理
                </a>
            </li>
            <li>
                <a href="order-list.html">
                    <i class="layui-icon layui-icon-dollar"></i>
                    订单管理
                </a>
            </li>
            <li>
                <a href="finance.html">
                    <i class="layui-icon layui-icon-rmb"></i>
                    财务管理
                </a>
            </li>
            <li>
                <a href="admin-list.html">
                    <i class="layui-icon layui-icon-username"></i>
                    管理员
                </a>
            </li>
            <li>
                <a href="role-list.html">
                    <i class="layui-icon layui-icon-group"></i>
                    角色权限
                </a>
            </li>
            <li>
                <a href="system-config.html">
                    <i class="layui-icon layui-icon-set"></i>
                    系统配置
                </a>
            </li>
            <li>
                <a href="logs.html" class="active">
                    <i class="layui-icon layui-icon-file"></i>
                    系统日志
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="admin-main">
        <!-- 统计卡片 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="todayLogs">0</div>
                        <div class="stats-label">今日日志</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="errorLogs">0</div>
                        <div class="stats-label">错误日志</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="warningLogs">0</div>
                        <div class="stats-label">警告日志</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="totalLogs">0</div>
                        <div class="stats-label">总日志数</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-card">
            <div class="layui-tab layui-tab-brief log-tabs" lay-filter="logTabs">
                <ul class="layui-tab-title">
                    <li class="layui-this">系统日志</li>
                    <li>安全日志</li>
                    <li>日志统计</li>
                    <li>日志管理</li>
                </ul>
                
                <div class="layui-tab-content">
                    <!-- 系统日志 -->
                    <div class="layui-tab-item layui-show">
                        <!-- 搜索表单 -->
                        <form class="layui-form search-form" lay-filter="searchForm">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md2">
                                    <select name="level" lay-search>
                                        <option value="">日志级别</option>
                                        <option value="debug">调试</option>
                                        <option value="info">信息</option>
                                        <option value="warning">警告</option>
                                        <option value="error">错误</option>
                                        <option value="critical">严重</option>
                                    </select>
                                </div>
                                <div class="layui-col-md2">
                                    <select name="type" lay-search>
                                        <option value="">日志类型</option>
                                        <option value="system">系统</option>
                                        <option value="api">API</option>
                                        <option value="user">用户</option>
                                        <option value="admin">管理员</option>
                                        <option value="payment">支付</option>
                                        <option value="security">安全</option>
                                        <option value="database">数据库</option>
                                    </select>
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="start_date" placeholder="开始日期" class="layui-input" id="startDate">
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="end_date" placeholder="结束日期" class="layui-input" id="endDate">
                                </div>
                                <div class="layui-col-md3">
                                    <input type="text" name="keyword" placeholder="搜索关键词" class="layui-input">
                                </div>
                                <div class="layui-col-md1">
                                    <button type="submit" class="layui-btn layui-btn-primary">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-warm export-btn" onclick="exportLogs()">
                                        <i class="layui-icon layui-icon-export"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- 日志列表 -->
                        <table class="layui-table" lay-filter="logTable">
                            <thead>
                                <tr>
                                    <th lay-data="{field:'id', width:80, sort:true}">ID</th>
                                    <th lay-data="{field:'level', width:80}">级别</th>
                                    <th lay-data="{field:'type', width:80}">类型</th>
                                    <th lay-data="{field:'message', width:300}">消息</th>
                                    <th lay-data="{field:'username', width:100}">用户</th>
                                    <th lay-data="{field:'ip_address', width:120}">IP地址</th>
                                    <th lay-data="{field:'created_at', width:160, sort:true}">创建时间</th>
                                    <th lay-data="{field:'operate', width:120, toolbar:'#operateBar'}">操作</th>
                                </tr>
                            </thead>
                            <tbody id="logTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>

                        <!-- 分页 -->
                        <div id="logPagination"></div>
                    </div>
                    
                    <!-- 安全日志 -->
                    <div class="layui-tab-item">
                        <!-- 安全日志搜索表单 -->
                        <form class="layui-form search-form" lay-filter="securitySearchForm">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md2">
                                    <select name="type" lay-search>
                                        <option value="">事件类型</option>
                                        <option value="login_success">登录成功</option>
                                        <option value="login_failed">登录失败</option>
                                        <option value="login_blocked">登录封禁</option>
                                        <option value="api_abuse">API滥用</option>
                                        <option value="suspicious_activity">可疑活动</option>
                                        <option value="permission_denied">权限拒绝</option>
                                        <option value="sql_injection">SQL注入</option>
                                        <option value="xss_attempt">XSS攻击</option>
                                    </select>
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="ip_address" placeholder="IP地址" class="layui-input">
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="start_date" placeholder="开始日期" class="layui-input" id="securityStartDate">
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="end_date" placeholder="结束日期" class="layui-input" id="securityEndDate">
                                </div>
                                <div class="layui-col-md3">
                                    <input type="text" name="user_id" placeholder="用户ID" class="layui-input">
                                </div>
                                <div class="layui-col-md1">
                                    <button type="submit" class="layui-btn layui-btn-primary">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- 安全日志列表 -->
                        <table class="layui-table" lay-filter="securityLogTable">
                            <thead>
                                <tr>
                                    <th lay-data="{field:'id', width:80, sort:true}">ID</th>
                                    <th lay-data="{field:'type', width:120}">事件类型</th>
                                    <th lay-data="{field:'description', width:300}">描述</th>
                                    <th lay-data="{field:'username', width:100}">用户</th>
                                    <th lay-data="{field:'ip_address', width:120}">IP地址</th>
                                    <th lay-data="{field:'created_at', width:160, sort:true}">发生时间</th>
                                    <th lay-data="{field:'operate', width:120, toolbar:'#securityOperateBar'}">操作</th>
                                </tr>
                            </thead>
                            <tbody id="securityLogTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>

                        <!-- 分页 -->
                        <div id="securityLogPagination"></div>
                    </div>
                    
                    <!-- 日志统计 -->
                    <div class="layui-tab-item">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md6">
                                <div class="content-card">
                                    <h3>日志级别分布</h3>
                                    <div class="chart-container" id="levelChart"></div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="content-card">
                                    <h3>日志类型分布</h3>
                                    <div class="chart-container" id="typeChart"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="content-card">
                            <h3>日志趋势图</h3>
                            <div class="chart-container" id="trendChart"></div>
                        </div>
                    </div>
                    
                    <!-- 日志管理 -->
                    <div class="layui-tab-item">
                        <div class="content-card">
                            <h3>日志文件管理</h3>
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>类型</th>
                                        <th>日期</th>
                                        <th>大小</th>
                                        <th>修改时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="logFilesTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="content-card cleanup-section">
                            <h3>日志清理</h3>
                            <div class="layui-form">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">保留天数</label>
                                    <div class="layui-input-inline">
                                        <input type="number" name="cleanup_days" value="30" class="layui-input" id="cleanupDays">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">清理指定天数之前的日志</div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button type="button" class="layui-btn layui-btn-danger" onclick="cleanupLogs()">
                                            <i class="layui-icon layui-icon-delete"></i> 清理日志
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-primary" onclick="refreshLogFiles()">
                                            <i class="layui-icon layui-icon-refresh"></i> 刷新列表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="operateBar">
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
    </script>

    <script type="text/html" id="securityOperateBar">
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="block">封禁IP</a>
    </script>

    <script src="../../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'laydate', 'layer', 'element'], function(){
            var table = layui.table;
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            var element = layui.element;
            
            // 初始化日期选择器
            laydate.render({
                elem: '#startDate',
                type: 'datetime'
            });
            
            laydate.render({
                elem: '#endDate',
                type: 'datetime'
            });
            
            laydate.render({
                elem: '#securityStartDate',
                type: 'datetime'
            });
            
            laydate.render({
                elem: '#securityEndDate',
                type: 'datetime'
            });
            
            // 加载统计数据
            loadStatistics();
            
            // 加载系统日志
            loadSystemLogs();
            
            // 加载安全日志
            loadSecurityLogs();
            
            // 加载日志文件列表
            loadLogFiles();
            
            // 搜索表单提交
            form.on('submit(searchForm)', function(data){
                loadSystemLogs(1, data.field);
                return false;
            });
            
            form.on('submit(securitySearchForm)', function(data){
                loadSecurityLogs(1, data.field);
                return false;
            });
            
            // 表格工具栏事件
            table.on('tool(logTable)', function(obj){
                var data = obj.data;
                
                if(obj.event === 'detail'){
                    showLogDetail(data);
                }
            });
            
            table.on('tool(securityLogTable)', function(obj){
                var data = obj.data;
                
                if(obj.event === 'detail'){
                    showSecurityLogDetail(data);
                } else if(obj.event === 'block'){
                    blockIP(data.ip_address);
                }
            });
        });
        
        // 加载统计数据
        function loadStatistics() {
            // 模拟数据
            document.getElementById('todayLogs').textContent = '1,234';
            document.getElementById('errorLogs').textContent = '56';
            document.getElementById('warningLogs').textContent = '123';
            document.getElementById('totalLogs').textContent = '12,345';
        }
        
        // 加载系统日志
        function loadSystemLogs(page = 1, conditions = {}) {
            // 模拟数据
            const logs = [
                {
                    id: 1,
                    level: 'info',
                    type: 'system',
                    message: '系统启动成功',
                    username: 'admin',
                    ip_address: '*************',
                    created_at: '2024-01-15 10:30:00'
                },
                {
                    id: 2,
                    level: 'warning',
                    type: 'api',
                    message: 'API调用频率过高',
                    username: 'user001',
                    ip_address: '*************',
                    created_at: '2024-01-15 10:25:00'
                },
                {
                    id: 3,
                    level: 'error',
                    type: 'database',
                    message: '数据库连接失败',
                    username: null,
                    ip_address: '*************',
                    created_at: '2024-01-15 10:20:00'
                }
            ];
            
            renderLogTable(logs);
        }
        
        // 渲染日志表格
        function renderLogTable(logs) {
            const tbody = document.getElementById('logTableBody');
            tbody.innerHTML = '';
            
            logs.forEach(log => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${log.id}</td>
                    <td><span class="level-badge level-${log.level}">${getLevelText(log.level)}</span></td>
                    <td><span class="type-badge type-${log.type}">${getTypeText(log.type)}</span></td>
                    <td class="log-detail">${log.message}</td>
                    <td>${log.username || '-'}</td>
                    <td>${log.ip_address}</td>
                    <td>${log.created_at}</td>
                    <td>
                        <button class="layui-btn layui-btn-xs" onclick="showLogDetail(${JSON.stringify(log).replace(/"/g, '&quot;')})">详情</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 加载安全日志
        function loadSecurityLogs(page = 1, conditions = {}) {
            // 模拟数据
            const securityLogs = [
                {
                    id: 1,
                    type: 'login_success',
                    description: '用户 admin 登录成功',
                    username: 'admin',
                    ip_address: '*************',
                    created_at: '2024-01-15 10:30:00'
                },
                {
                    id: 2,
                    type: 'login_failed',
                    description: '用户 test 登录失败，密码错误',
                    username: 'test',
                    ip_address: '*************',
                    created_at: '2024-01-15 10:25:00'
                },
                {
                    id: 3,
                    type: 'api_abuse',
                    description: 'API调用频率超限',
                    username: 'user001',
                    ip_address: '*************',
                    created_at: '2024-01-15 10:20:00'
                }
            ];
            
            renderSecurityLogTable(securityLogs);
        }
        
        // 渲染安全日志表格
        function renderSecurityLogTable(logs) {
            const tbody = document.getElementById('securityLogTableBody');
            tbody.innerHTML = '';
            
            logs.forEach(log => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${log.id}</td>
                    <td>${getSecurityTypeText(log.type)}</td>
                    <td class="log-detail">${log.description}</td>
                    <td>${log.username || '-'}</td>
                    <td>${log.ip_address}</td>
                    <td>${log.created_at}</td>
                    <td>
                        <button class="layui-btn layui-btn-xs" onclick="showSecurityLogDetail(${JSON.stringify(log).replace(/"/g, '&quot;')})">详情</button>
                        <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="blockIP('${log.ip_address}')">封禁IP</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 加载日志文件列表
        function loadLogFiles() {
            // 模拟数据
            const logFiles = [
                {
                    filename: 'system_2024-01-15.log',
                    type: 'system',
                    date: '2024-01-15',
                    size: '2.5MB',
                    modified: '2024-01-15 10:30:00'
                },
                {
                    filename: 'api_2024-01-15.log',
                    type: 'api',
                    date: '2024-01-15',
                    size: '1.8MB',
                    modified: '2024-01-15 10:25:00'
                },
                {
                    filename: 'security_2024-01-15.log',
                    type: 'security',
                    date: '2024-01-15',
                    size: '0.5MB',
                    modified: '2024-01-15 10:20:00'
                }
            ];
            
            renderLogFilesTable(logFiles);
        }
        
        // 渲染日志文件表格
        function renderLogFilesTable(files) {
            const tbody = document.getElementById('logFilesTableBody');
            tbody.innerHTML = '';
            
            files.forEach(file => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${file.filename}</td>
                    <td><span class="type-badge type-${file.type}">${getTypeText(file.type)}</span></td>
                    <td>${file.date}</td>
                    <td>${file.size}</td>
                    <td>${file.modified}</td>
                    <td>
                        <button class="layui-btn layui-btn-xs" onclick="downloadLogFile('${file.filename}')">下载</button>
                        <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteLogFile('${file.filename}')">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 显示日志详情
        function showLogDetail(log) {
            const content = `
                <div style="max-height: 400px; overflow-y: auto;">
                    <table class="layui-table">
                        <tr><td width="80">ID</td><td>${log.id}</td></tr>
                        <tr><td>级别</td><td><span class="level-badge level-${log.level}">${getLevelText(log.level)}</span></td></tr>
                        <tr><td>类型</td><td><span class="type-badge type-${log.type}">${getTypeText(log.type)}</span></td></tr>
                        <tr><td>消息</td><td>${log.message}</td></tr>
                        <tr><td>用户</td><td>${log.username || '-'}</td></tr>
                        <tr><td>IP地址</td><td>${log.ip_address}</td></tr>
                        <tr><td>用户代理</td><td>${log.user_agent || '-'}</td></tr>
                        <tr><td>请求URI</td><td>${log.request_uri || '-'}</td></tr>
                        <tr><td>创建时间</td><td>${log.created_at}</td></tr>
                    </table>
                    ${log.context ? `<div><strong>上下文数据：</strong><div class="context-data">${JSON.stringify(JSON.parse(log.context || '{}'), null, 2)}</div></div>` : ''}
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '日志详情',
                content: content,
                area: ['600px', '500px'],
                shadeClose: true
            });
        }
        
        // 显示安全日志详情
        function showSecurityLogDetail(log) {
            const content = `
                <div style="max-height: 400px; overflow-y: auto;">
                    <table class="layui-table">
                        <tr><td width="80">ID</td><td>${log.id}</td></tr>
                        <tr><td>事件类型</td><td>${getSecurityTypeText(log.type)}</td></tr>
                        <tr><td>描述</td><td>${log.description}</td></tr>
                        <tr><td>用户</td><td>${log.username || '-'}</td></tr>
                        <tr><td>IP地址</td><td>${log.ip_address}</td></tr>
                        <tr><td>用户代理</td><td>${log.user_agent || '-'}</td></tr>
                        <tr><td>发生时间</td><td>${log.created_at}</td></tr>
                    </table>
                    ${log.request_data ? `<div><strong>请求数据：</strong><div class="context-data">${JSON.stringify(JSON.parse(log.request_data || '{}'), null, 2)}</div></div>` : ''}
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '安全日志详情',
                content: content,
                area: ['600px', '500px'],
                shadeClose: true
            });
        }
        
        // 封禁IP
        function blockIP(ip) {
            layer.prompt({
                title: '封禁IP地址: ' + ip,
                formType: 2
            }, function(reason, index){
                layer.close(index);
                
                // 这里应该发送AJAX请求到后端
                layer.msg('IP地址已封禁', {icon: 1});
                
                // 刷新安全日志列表
                loadSecurityLogs();
            });
        }
        
        // 导出日志
        function exportLogs() {
            layer.open({
                type: 1,
                title: '导出日志',
                content: `
                    <div style="padding: 20px;">
                        <form class="layui-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">导出格式</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="format" value="csv" title="CSV" checked>
                                    <input type="radio" name="format" value="json" title="JSON">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">日期范围</label>
                                <div class="layui-input-inline">
                                    <input type="text" id="exportStartDate" placeholder="开始日期" class="layui-input">
                                </div>
                                <div class="layui-form-mid">-</div>
                                <div class="layui-input-inline">
                                    <input type="text" id="exportEndDate" placeholder="结束日期" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn" onclick="doExportLogs()">开始导出</button>
                                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                                </div>
                            </div>
                        </form>
                    </div>
                `,
                area: ['400px', '300px'],
                success: function(){
                    // 初始化日期选择器
                    laydate.render({
                        elem: '#exportStartDate',
                        type: 'date'
                    });
                    
                    laydate.render({
                        elem: '#exportEndDate',
                        type: 'date'
                    });
                }
            });
        }
        
        // 执行导出
        function doExportLogs() {
            const format = document.querySelector('input[name="format"]:checked').value;
            const startDate = document.getElementById('exportStartDate').value;
            const endDate = document.getElementById('exportEndDate').value;
            
            // 这里应该发送AJAX请求到后端进行导出
            layer.msg('导出任务已提交，请稍后下载', {icon: 1});
            layer.closeAll();
        }
        
        // 清理日志
        function cleanupLogs() {
            const days = document.getElementById('cleanupDays').value;
            
            layer.confirm(`确定要清理 ${days} 天前的日志吗？此操作不可恢复！`, {
                icon: 3,
                title: '确认清理'
            }, function(index){
                layer.close(index);
                
                // 这里应该发送AJAX请求到后端
                layer.msg('日志清理完成', {icon: 1});
                
                // 刷新统计数据和文件列表
                loadStatistics();
                loadLogFiles();
            });
        }
        
        // 刷新日志文件列表
        function refreshLogFiles() {
            loadLogFiles();
            layer.msg('列表已刷新', {icon: 1});
        }
        
        // 下载日志文件
        function downloadLogFile(filename) {
            // 这里应该创建下载链接
            window.open(`../logs/download.php?file=${filename}`);
        }
        
        // 删除日志文件
        function deleteLogFile(filename) {
            layer.confirm(`确定要删除日志文件 ${filename} 吗？`, {
                icon: 3,
                title: '确认删除'
            }, function(index){
                layer.close(index);
                
                // 这里应该发送AJAX请求到后端
                layer.msg('文件已删除', {icon: 1});
                
                // 刷新文件列表
                loadLogFiles();
            });
        }
        
        // 获取级别文本
        function getLevelText(level) {
            const levelMap = {
                'debug': '调试',
                'info': '信息',
                'warning': '警告',
                'error': '错误',
                'critical': '严重'
            };
            return levelMap[level] || level;
        }
        
        // 获取类型文本
        function getTypeText(type) {
            const typeMap = {
                'system': '系统',
                'api': 'API',
                'user': '用户',
                'admin': '管理员',
                'payment': '支付',
                'security': '安全',
                'database': '数据库'
            };
            return typeMap[type] || type;
        }
        
        // 获取安全事件类型文本
        function getSecurityTypeText(type) {
            const typeMap = {
                'login_success': '登录成功',
                'login_failed': '登录失败',
                'login_blocked': '登录封禁',
                'api_abuse': 'API滥用',
                'suspicious_activity': '可疑活动',
                'permission_denied': '权限拒绝',
                'sql_injection': 'SQL注入',
                'xss_attempt': 'XSS攻击',
                'csrf_attempt': 'CSRF攻击'
            };
            return typeMap[type] || type;
        }
        
        // 退出登录
        function logout() {
            layer.confirm('确定要退出登录吗？', {
                icon: 3,
                title: '确认退出'
            }, function(index){
                layer.close(index);
                window.location.href = '../login.html';
            });
        }
    </script>
</body>
</html>
