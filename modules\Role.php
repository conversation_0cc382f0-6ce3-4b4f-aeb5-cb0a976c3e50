<?php
/**
 * 角色模型
 * API管理系统 - 角色权限管理
 */

require_once __DIR__ . '/../core/Model.php';
require_once __DIR__ . '/Permission.php';

class Role extends Model {
    protected $table = 'roles';
    protected $fillable = ['name', 'description', 'permissions', 'status'];
    
    /**
     * 创建角色
     */
    public function create($data) {
        // 验证权限格式
        if (isset($data['permissions'])) {
            if (is_array($data['permissions'])) {
                if (!Permission::validatePermissions($data['permissions'])) {
                    throw new Exception('权限配置格式错误');
                }
                $data['permissions'] = json_encode($data['permissions']);
            }
        }
        
        return parent::create($data);
    }
    
    /**
     * 更新角色
     */
    public function update($id, $data) {
        // 验证权限格式
        if (isset($data['permissions'])) {
            if (is_array($data['permissions'])) {
                if (!Permission::validatePermissions($data['permissions'])) {
                    throw new Exception('权限配置格式错误');
                }
                $data['permissions'] = json_encode($data['permissions']);
            }
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * 获取角色列表（带权限解析）
     */
    public function getListWithPermissions($page = 1, $perPage = 20, $conditions = []) {
        $result = $this->paginate($page, $perPage, $conditions, 'id DESC');
        
        // 解析权限
        foreach ($result['data'] as &$role) {
            $permissions = json_decode($role['permissions'], true);
            $role['permissions_array'] = $permissions;
            $role['permissions_text'] = $this->formatPermissions($permissions);
        }
        
        return $result;
    }
    
    /**
     * 获取角色详情（带权限解析）
     */
    public function getDetailWithPermissions($id) {
        $role = $this->find($id);
        if (!$role) {
            return null;
        }
        
        $permissions = json_decode($role['permissions'], true);
        $role['permissions_array'] = $permissions;
        $role['permissions_text'] = $this->formatPermissions($permissions);
        
        return $role;
    }
    
    /**
     * 格式化权限显示
     */
    private function formatPermissions($permissions) {
        if (empty($permissions)) {
            return '无权限';
        }
        
        if (in_array('*', $permissions)) {
            return '超级管理员（所有权限）';
        }
        
        $allPermissions = Permission::getAllPermissions();
        $permissionNames = [];
        
        foreach ($permissions as $permission) {
            if (isset($allPermissions[$permission])) {
                $permissionNames[] = $allPermissions[$permission];
            }
        }
        
        return implode('、', $permissionNames);
    }
    
    /**
     * 获取所有启用的角色
     */
    public function getActiveRoles() {
        return $this->findAll(['status' => 1], 'id ASC');
    }
    
    /**
     * 检查角色名称是否存在
     */
    public function nameExists($name, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->getTableName()} WHERE name = :name";
        $params = ['name' => $name];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * 删除角色（检查是否有管理员使用）
     */
    public function delete($id) {
        // 检查是否有管理员使用此角色
        $sql = "SELECT COUNT(*) as count FROM {$this->db->getPrefix()}admins WHERE role_id = :role_id";
        $result = $this->db->fetchOne($sql, ['role_id' => $id]);
        
        if ($result['count'] > 0) {
            throw new Exception('该角色正在被使用，无法删除');
        }
        
        return parent::delete($id);
    }
    
    /**
     * 获取角色统计信息
     */
    public function getRoleStats() {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
                    (SELECT COUNT(*) FROM {$this->db->getPrefix()}admins WHERE role_id IN (SELECT id FROM {$this->getTableName()} WHERE status = 1)) as admin_count
                FROM {$this->getTableName()}";
        
        return $this->db->fetchOne($sql);
    }
    
    /**
     * 复制角色
     */
    public function copyRole($id, $newName) {
        $role = $this->find($id);
        if (!$role) {
            throw new Exception('原角色不存在');
        }
        
        if ($this->nameExists($newName)) {
            throw new Exception('角色名称已存在');
        }
        
        $newRoleData = [
            'name' => $newName,
            'description' => $role['description'] . '（复制）',
            'permissions' => $role['permissions'],
            'status' => 0 // 默认禁用
        ];
        
        return $this->create($newRoleData);
    }
    
    /**
     * 批量更新角色状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids)) {
            return false;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->getTableName()} SET status = ?, updated_at = ? WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status, date('Y-m-d H:i:s')], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * 获取权限树结构
     */
    public function getPermissionTree() {
        $groups = Permission::getPermissionGroups();
        $tree = [];
        
        foreach ($groups as $groupKey => $group) {
            $treeNode = [
                'id' => $groupKey,
                'title' => $group['name'],
                'spread' => true,
                'children' => []
            ];
            
            foreach ($group['permissions'] as $permKey => $permName) {
                $treeNode['children'][] = [
                    'id' => $permKey,
                    'title' => $permName,
                    'spread' => false
                ];
            }
            
            $tree[] = $treeNode;
        }
        
        return $tree;
    }
    
    /**
     * 验证角色权限配置
     */
    public function validateRolePermissions($permissions) {
        if (!is_array($permissions)) {
            return false;
        }
        
        // 检查是否包含超级管理员权限
        if (in_array('*', $permissions)) {
            return count($permissions) === 1; // 超级管理员权限不能与其他权限混合
        }
        
        return Permission::validatePermissions($permissions);
    }
    
    /**
     * 获取角色权限差异
     */
    public function getPermissionDiff($roleId1, $roleId2) {
        $role1 = $this->find($roleId1);
        $role2 = $this->find($roleId2);
        
        if (!$role1 || !$role2) {
            throw new Exception('角色不存在');
        }
        
        $permissions1 = json_decode($role1['permissions'], true);
        $permissions2 = json_decode($role2['permissions'], true);
        
        return [
            'only_in_role1' => array_diff($permissions1, $permissions2),
            'only_in_role2' => array_diff($permissions2, $permissions1),
            'common' => array_intersect($permissions1, $permissions2)
        ];
    }
    
    /**
     * 导出角色配置
     */
    public function exportRole($id) {
        $role = $this->getDetailWithPermissions($id);
        if (!$role) {
            throw new Exception('角色不存在');
        }
        
        return [
            'name' => $role['name'],
            'description' => $role['description'],
            'permissions' => $role['permissions_array'],
            'export_time' => date('Y-m-d H:i:s'),
            'version' => '1.0'
        ];
    }
    
    /**
     * 导入角色配置
     */
    public function importRole($config, $newName = null) {
        if (!isset($config['permissions']) || !is_array($config['permissions'])) {
            throw new Exception('配置格式错误');
        }
        
        if (!$this->validateRolePermissions($config['permissions'])) {
            throw new Exception('权限配置无效');
        }
        
        $name = $newName ?: ($config['name'] ?? '导入角色');
        
        // 确保角色名称唯一
        $originalName = $name;
        $counter = 1;
        while ($this->nameExists($name)) {
            $name = $originalName . "({$counter})";
            $counter++;
        }
        
        $roleData = [
            'name' => $name,
            'description' => $config['description'] ?? '导入的角色',
            'permissions' => $config['permissions'],
            'status' => 0 // 默认禁用
        ];
        
        return $this->create($roleData);
    }
}