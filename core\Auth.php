<?php
/**
 * 用户认证类
 */
class Auth {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = require_once 'config/app.php';
    }
    
    /**
     * 用户登录
     */
    public function login($username, $password) {
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE username = ? AND status = 1",
            [$username]
        );
        
        if (!$user) {
            return ['success' => false, 'message' => '用户不存在或已被禁用'];
        }
        
        if (!password_verify($password, $user['password'])) {
            // 记录登录失败
            $this->logLoginAttempt($username, false);
            return ['success' => false, 'message' => '密码错误'];
        }
        
        // 更新最后登录时间
        $this->db->update('users', [
            'last_login' => date('Y-m-d H:i:s'),
            'login_count' => $user['login_count'] + 1
        ], 'id = ?', [$user['id']]);
        
        // 记录登录成功
        $this->logLoginAttempt($username, true);
        
        // 生成JWT Token
        $token = $this->generateJWT($user);
        
        return [
            'success' => true,
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'role' => $user['role'],
                'level' => $user['level']
            ]
        ];
    }
    
    /**
     * 用户注册
     */
    public function register($data) {
        // 检查用户名是否存在
        $existUser = $this->db->fetchOne(
            "SELECT id FROM users WHERE username = ? OR email = ?",
            [$data['username'], $data['email']]
        );
        
        if ($existUser) {
            return ['success' => false, 'message' => '用户名或邮箱已存在'];
        }
        
        // 创建用户
        $userData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'role' => 'user',
            'level' => 1,
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'api_key' => $this->generateApiKey()
        ];
        
        if ($this->db->insert('users', $userData)) {
            return ['success' => true, 'message' => '注册成功'];
        }
        
        return ['success' => false, 'message' => '注册失败'];
    }
    
    /**
     * 验证JWT Token
     */
    public function verifyToken($token) {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return false;
            }
            
            $header = json_decode(base64_decode($parts[0]), true);
            $payload = json_decode(base64_decode($parts[1]), true);
            $signature = $parts[2];
            
            // 验证签名
            $expectedSignature = base64_encode(hash_hmac('sha256', 
                $parts[0] . '.' . $parts[1], 
                $this->config['security']['jwt_secret'], 
                true
            ));
            
            if ($signature !== $expectedSignature) {
                return false;
            }
            
            // 验证过期时间
            if ($payload['exp'] < time()) {
                return false;
            }
            
            return $payload;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 生成JWT Token
     */
    private function generateJWT($user) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role'],
            'iat' => time(),
            'exp' => time() + $this->config['security']['session_lifetime']
        ]);
        
        $headerEncoded = base64_encode($header);
        $payloadEncoded = base64_encode($payload);
        
        $signature = base64_encode(hash_hmac('sha256', 
            $headerEncoded . '.' . $payloadEncoded, 
            $this->config['security']['jwt_secret'], 
            true
        ));
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signature;
    }
    
    /**
     * 生成API密钥
     */
    private function generateApiKey() {
        return 'ak_' . bin2hex(random_bytes(16));
    }
    
    /**
     * 记录登录日志
     */
    private function logLoginAttempt($username, $success) {
        $this->db->insert('login_logs', [
            'username' => $username,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'success' => $success ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}