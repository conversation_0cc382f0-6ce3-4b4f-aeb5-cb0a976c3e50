<?php
/**
 * 认证类
 */
class Auth
{
    private static $instance = null;
    private $db;
    private $session;
    
    /**
     * 构造函数
     */
    private function __construct()
    {
        $this->db = Database::getInstance();
        $this->session = Session::getInstance();
    }
    
    /**
     * 获取实例（单例模式）
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 用户登录
     */
    public function login($username, $password, $remember = false)
    {
        // 查询用户
        $user = $this->db->query("SELECT * FROM users WHERE username = :username OR email = :email LIMIT 1")
            ->bind(':username', $username)
            ->bind(':email', $username)
            ->fetch();
        
        if (!$user) {
            return ['status' => false, 'message' => '用户不存在'];
        }
        
        // 验证密码
        if (!$this->verifyPassword($password, $user['password'])) {
            return ['status' => false, 'message' => '密码错误'];
        }
        
        // 检查用户状态
        if ($user['status'] != 1) {
            return ['status' => false, 'message' => '账号已被禁用'];
        }
        
        // 更新登录信息
        $this->db->update('users', [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => $_SERVER['REMOTE_ADDR']
        ], 'id = :id', [':id' => $user['id']]);
        
        // 记录登录日志
        $this->db->insert('user_logs', [
            'user_id' => $user['id'],
            'action' => 'login',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // 设置会话
        unset($user['password']);
        $this->session->set('user', $user);
        
        // 记住登录
        if ($remember) {
            $token = $this->generateRememberToken();
            $expire = time() + 30 * 86400; // 30天
            
            $this->db->update('users', [
                'remember_token' => $token
            ], 'id = :id', [':id' => $user['id']]);
            
            setcookie('remember_token', $token, $expire, '/', '', false, true);
            setcookie('user_id', $user['id'], $expire, '/', '', false, true);
        }
        
        return ['status' => true, 'message' => '登录成功', 'user' => $user];
    }
    
    /**
     * 管理员登录
     */
    public function adminLogin($username, $password)
    {
        // 查询管理员
        $admin = $this->db->query("SELECT * FROM admins WHERE username = :username LIMIT 1")
            ->bind(':username', $username)
            ->fetch();
        
        if (!$admin) {
            return ['status' => false, 'message' => '管理员不存在'];
        }
        
        // 验证密码
        if (!$this->verifyPassword($password, $admin['password'])) {
            return ['status' => false, 'message' => '密码错误'];
        }
        
        // 检查状态
        if ($admin['status'] != 1) {
            return ['status' => false, 'message' => '账号已被禁用'];
        }
        
        // 更新登录信息
        $this->db->update('admins', [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => $_SERVER['REMOTE_ADDR']
        ], 'id = :id', [':id' => $admin['id']]);
        
        // 记录登录日志
        $this->db->insert('admin_logs', [
            'admin_id' => $admin['id'],
            'action' => 'login',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // 设置会话
        unset($admin['password']);
        $this->session->set('admin', $admin);
        
        return ['status' => true, 'message' => '登录成功', 'admin' => $admin];
    }
    
    /**
     * 商家登录
     */
    public function merchantLogin($username, $password, $remember = false)
    {
        // 查询商家
        $merchant = $this->db->query("SELECT * FROM merchants WHERE username = :username OR email = :email LIMIT 1")
            ->bind(':username', $username)
            ->bind(':email', $username)
            ->fetch();
        
        if (!$merchant) {
            return ['status' => false, 'message' => '商家不存在'];
        }
        
        // 验证密码
        if (!$this->verifyPassword($password, $merchant['password'])) {
            return ['status' => false, 'message' => '密码错误'];
        }
        
        // 检查状态
        if ($merchant['status'] != 1) {
            return ['status' => false, 'message' => '账号已被禁用'];
        }
        
        // 更新登录信息
        $this->db->update('merchants', [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => $_SERVER['REMOTE_ADDR']
        ], 'id = :id', [':id' => $merchant['id']]);
        
        // 记录登录日志
        $this->db->insert('merchant_logs', [
            'merchant_id' => $merchant['id'],
            'action' => 'login',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // 设置会话
        unset($merchant['password']);
        $this->session->set('merchant', $merchant);
        
        // 记住登录
        if ($remember) {
            $token = $this->generateRememberToken();
            $expire = time() + 30 * 86400; // 30天
            
            $this->db->update('merchants', [
                'remember_token' => $token
            ], 'id = :id', [':id' => $merchant['id']]);
            
            setcookie('merchant_remember_token', $token, $expire, '/', '', false, true);
            setcookie('merchant_id', $merchant['id'], $expire, '/', '', false, true);
        }
        
        return ['status' => true, 'message' => '登录成功', 'merchant' => $merchant];
    }
    
    /**
     * 用户注册
     */
    public function register($data)
    {
        // 检查用户名是否存在
        $exists = $this->db->query("SELECT COUNT(*) FROM users WHERE username = :username")
            ->bind(':username', $data['username'])
            ->getValue();
        
        if ($exists > 0) {
            return ['status' => false, 'message' => '用户名已存在'];
        }
        
        // 检查邮箱是否存在
        $exists = $this->db->query("SELECT COUNT(*) FROM users WHERE email = :email")
            ->bind(':email', $data['email'])
            ->getValue();
        
        if ($exists > 0) {
            return ['status' => false, 'message' => '邮箱已存在'];
        }
        
        // 密码加密
        $data['password'] = $this->hashPassword($data['password']);
        
        // 添加默认字段
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['status'] = 1;
        $data['vip_level'] = 0;
        $data['balance'] = 0;
        $data['api_key'] = $this->generateApiKey();
        
        // 插入数据
        $userId = $this->db->insert('users', $data);
        
        if (!$userId) {
            return ['status' => false, 'message' => '注册失败'];
        }
        
        // 获取用户信息
        $user = $this->db->query("SELECT * FROM users WHERE id = :id")
            ->bind(':id', $userId)
            ->fetch();
        
        // 设置会话
        unset($user['password']);
        $this->session->set('user', $user);
        
        return ['status' => true, 'message' => '注册成功', 'user' => $user];
    }
    
    /**
     * 商家注册
     */
    public function merchantRegister($data)
    {
        // 检查用户名是否存在
        $exists = $this->db->query("SELECT COUNT(*) FROM merchants WHERE username = :username")
            ->bind(':username', $data['username'])
            ->getValue();
        
        if ($exists > 0) {
            return ['status' => false, 'message' => '用户名已存在'];
        }
        
        // 检查邮箱是否存在
        $exists = $this->db->query("SELECT COUNT(*) FROM merchants WHERE email = :email")
            ->bind(':email', $data['email'])
            ->getValue();
        
        if ($exists > 0) {
            return ['status' => false, 'message' => '邮箱已存在'];
        }
        
        // 密码加密
        $data['password'] = $this->hashPassword($data['password']);
        
        // 添加默认字段
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['status'] = 0; // 默认待审核
        $data['level'] = 1;
        $data['balance'] = 0;
        $data['api_key'] = $this->generateApiKey();
        
        // 插入数据
        $merchantId = $this->db->insert('merchants', $data);
        
        if (!$merchantId) {
            return ['status' => false, 'message' => '注册失败'];
        }
        
        return ['status' => true, 'message' => '注册成功，请等待管理员审核'];
    }
    
    /**
     * 退出登录
     */
    public function logout()
    {
        $this->session->delete('user');
        $this->session->delete('admin');
        $this->session->delete('merchant');
        
        // 清除cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
            setcookie('user_id', '', time() - 3600, '/', '', false, true);
        }
        
        if (isset($_COOKIE['merchant_remember_token'])) {
            setcookie('merchant_remember_token', '', time() - 3600, '/', '', false, true);
            setcookie('merchant_id', '', time() - 3600, '/', '', false, true);
        }
        
        return ['status' => true, 'message' => '退出成功'];
    }
    
    /**
     * 检查用户是否已登录
     */
    public function isLoggedIn()
    {
        if ($this->session->has('user')) {
            return true;
        }
        
        // 检查记住登录
        if (isset($_COOKIE['remember_token']) && isset($_COOKIE['user_id'])) {
            $token = $_COOKIE['remember_token'];
            $userId = $_COOKIE['user_id'];
            
            $user = $this->db->query("SELECT * FROM users WHERE id = :id AND remember_token = :token AND status = 1")
                ->bind(':id', $userId)
                ->bind(':token', $token)
                ->fetch();
            
            if ($user) {
                unset($user['password']);
                $this->session->set('user', $user);
                return true;
            }
            
            // 清除无效cookie
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
            setcookie('user_id', '', time() - 3600, '/', '', false, true);
        }
        
        return false;
    }
    
    /**
     * 检查管理员是否已登录
     */
    public function isAdminLoggedIn()
    {
        return $this->session->has('admin');
    }
    
    /**
     * 检查商家是否已登录
     */
    public function isMerchantLoggedIn()
    {
        if ($this->session->has('merchant')) {
            return true;
        }
        
        // 检查记住登录
        if (isset($_COOKIE['merchant_remember_token']) && isset($_COOKIE['merchant_id'])) {
            $token = $_COOKIE['merchant_remember_token'];
            $merchantId = $_COOKIE['merchant_id'];
            
            $merchant = $this->db->query("SELECT * FROM merchants WHERE id = :id AND remember_token = :token AND status = 1")
                ->bind(':id', $merchantId)
                ->bind(':token', $token)
                ->fetch();
            
            if ($merchant) {
                unset($merchant['password']);
                $this->session->set('merchant', $merchant);
                return true;
            }
            
            // 清除无效cookie
            setcookie('merchant_remember_token', '', time() - 3600, '/', '', false, true);
            setcookie('merchant_id', '', time() - 3600, '/', '', false, true);
        }
        
        return false;
    }
    
    /**
     * 获取当前登录用户
     */
    public function getUser()
    {
        if ($this->isLoggedIn()) {
            return $this->session->get('user');
        }
        return null;
    }
    
    /**
     * 获取当前登录管理员
     */
    public function getAdmin()
    {
        if ($this->isAdminLoggedIn()) {
            return $this->session->get('admin');
        }
        return null;
    }
    
    /**
     * 获取当前登录商家
     */
    public function getMerchant()
    {
        if ($this->isMerchantLoggedIn()) {
            return $this->session->get('merchant');
        }
        return null;
    }
    
    /**
     * 密码加密
     */
    public function hashPassword($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * 验证密码
     */
    public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * 生成记住登录令牌
     */
    private function generateRememberToken()
    {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * 生成API密钥
     */
    public function generateApiKey()
    {
        return bin2hex(random_bytes(16));
    }
    
    /**
     * 重置密码
     */
    public function resetPassword($email, $token, $password)
    {
        $user = $this->db->query("SELECT * FROM users WHERE email = :email AND reset_token = :token AND reset_token_expires > NOW()")
            ->bind(':email', $email)
            ->bind(':token', $token)
            ->fetch();
        
        if (!$user) {
            return ['status' => false, 'message' => '无效的重置链接或链接已过期'];
        }
        
        // 更新密码
        $hashedPassword = $this->hashPassword($password);
        
        $this->db->update('users', [
            'password' => $hashedPassword,
            'reset_token' => null,
            'reset_token_expires' => null
        ], 'id = :id', [':id' => $user['id']]);
        
        return ['status' => true, 'message' => '密码重置成功，请使用新密码登录'];
    }
    
    /**
     * 发送密码重置邮件
     */
    public function sendPasswordResetEmail($email)
    {
        $user = $this->db->query("SELECT * FROM users WHERE email = :email")
            ->bind(':email', $email)
            ->fetch();
        
        if (!$user) {
            return ['status' => false, 'message' => '该邮箱未注册'];
        }
        
        // 生成重置令牌
        $token = bin2hex(random_bytes(16));
        $expires = date('Y-m-d H:i:s', time() + 3600); // 1小时后过期
        
        // 更新用户表
        $this->db->update('users', [
            'reset_token' => $token,
            'reset_token_expires' => $expires
        ], 'id = :id', [':id' => $user['id']]);
        
        // 发送邮件
        $mailer = new Mailer();
        $resetUrl = BASE_URL . '/reset-password?email=' . urlencode($email) . '&token=' . $token;
        
        $subject = '密码重置';
        $body = "尊敬的用户，<br><br>您收到此邮件是因为您请求重置密码。<br><br>请点击以下链接重置密码：<br><a href='{$resetUrl}'>{$resetUrl}</a><br><br>此链接将在1小时后失效。<br><br>如果您没有请求重置密码，请忽略此邮件。";
        
        $result = $mailer->send($email, $user['username'], $subject, $body);
        
        if ($result) {
            return ['status' => true, 'message' => '重置密码邮件已发送，请查收'];
        } else {
            return ['status' => false, 'message' => '邮件发送失败，请稍后再试'];
        }
    }
    
    /**
     * 验证API密钥
     */
    public function validateApiKey($apiKey)
    {
        // 检查用户API密钥
        $user = $this->db->query("SELECT * FROM users WHERE api_key = :api_key AND status = 1")
            ->bind(':api_key', $apiKey)
            ->fetch();
        
        if ($user) {
            return ['status' => true, 'type' => 'user', 'data' => $user];
        }
        
        // 检查商家API密钥
        $merchant = $this->db->query("SELECT * FROM merchants WHERE api_key = :api_key AND status = 1")
            ->bind(':api_key', $apiKey)
            ->fetch();
        
        if ($merchant) {
            return ['status' => true, 'type' => 'merchant', 'data' => $merchant];
        }
        
        return ['status' => false, 'message' => '无效的API密钥'];
    }
}