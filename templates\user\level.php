<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>会员等级 - <?php echo $site_config['site_name']; ?></title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/main.css"/>
    <style>
        .level-card {
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            position: relative;
            transition: all .3s;
        }
        .level-card:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-3px);
        }
        .level-card .level-icon {
            width: 60px;
            height: 60px;
            object-fit: contain;
            margin-bottom: 10px;
        }
        .level-card .level-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .level-card .level-desc {
            color: #666;
            margin-bottom: 15px;
            min-height: 40px;
        }
        .level-card .level-price {
            font-size: 16px;
            color: #FF5722;
            margin-bottom: 10px;
        }
        .level-card .level-discount {
            font-size: 14px;
            color: #1E9FFF;
            margin-bottom: 10px;
        }
        .level-card .level-features {
            margin-bottom: 15px;
            min-height: 120px;
        }
        .level-card .level-features .feature-item {
            margin-bottom: 5px;
        }
        .level-card .level-features .feature-item i {
            color: #5FB878;
            margin-right: 5px;
        }
        .level-card .level-actions {
            margin-top: 15px;
            text-align: center;
        }
        .level-card .level-badge {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #FF5722;
            color: #fff;
            padding: 5px 10px;
            font-size: 12px;
        }
        .level-card.current {
            border: 2px solid #5FB878;
        }
        .level-card.current .level-badge {
            background-color: #5FB878;
        }
        .level-compare-table {
            width: 100%;
            margin-top: 30px;
        }
        .level-compare-table th, .level-compare-table td {
            text-align: center;
            padding: 10px;
            border: 1px solid #e6e6e6;
        }
        .level-compare-table th {
            background-color: #f2f2f2;
        }
        .level-compare-table .feature-yes {
            color: #5FB878;
        }
        .level-compare-table .feature-no {
            color: #FF5722;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">会员等级</div>
        <div class="layui-card-body">
            <!-- 当前会员信息 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">当前会员信息</div>
                        <div class="layui-card-body">
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <p><strong>用户名：</strong><?php echo $user['username']; ?></p>
                                    <p><strong>当前等级：</strong>
                                        <?php if (!empty($userLevel)): ?>
                                            <?php echo $userLevel['name']; ?>
                                            <?php if ($userLevel['expire_time']): ?>
                                                （到期时间：<?php echo date('Y-m-d', $userLevel['expire_time']); ?>）
                                            <?php else: ?>
                                                （永久有效）
                                            <?php endif; ?>
                                        <?php else: ?>
                                            普通用户
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="layui-col-md6">
                                    <p><strong>账户余额：</strong>¥ <?php echo number_format($userBalance, 2); ?></p>
                                    <p><strong>API折扣：</strong>
                                        <?php if (!empty($userLevel)): ?>
                                            <?php echo $userLevel['discount_rate'] * 100; ?>%
                                        <?php else: ?>
                                            100%（无折扣）
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 会员等级列表 -->
            <div class="layui-row layui-col-space20" id="levelContainer">
                <?php foreach ($levels as $level): ?>
                <div class="layui-col-md4">
                    <div class="level-card <?php echo (!empty($userLevel) && $userLevel['level_id'] == $level['id']) ? 'current' : ''; ?>">
                        <?php if (!empty($userLevel) && $userLevel['level_id'] == $level['id']): ?>
                        <div class="level-badge">当前等级</div>
                        <?php endif; ?>
                        
                        <?php if (!empty($level['icon'])): ?>
                        <img src="<?php echo $level['icon']; ?>" class="level-icon" alt="<?php echo $level['name']; ?>">
                        <?php endif; ?>
                        
                        <div class="level-title"><?php echo $level['name']; ?></div>
                        <div class="level-desc"><?php echo $level['description'] ?: '暂无描述'; ?></div>
                        <div class="level-price">¥ <?php echo number_format($level['price'], 2); ?> / 年</div>
                        <div class="level-discount">API折扣：<?php echo $level['discount_rate'] * 100; ?>%</div>
                        
                        <div class="level-features">
                            <div class="feature-item">
                                <i class="layui-icon">&#xe63c;</i> 每日调用次数：
                                <?php echo $level['daily_request_limit'] == 0 ? '不限' : $level['daily_request_limit']; ?>
                            </div>
                            
                            <?php if (!empty($level['features'])): ?>
                                <?php if (!empty($level['features']['custom_domain'])): ?>
                                <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 自定义域名</div>
                                <?php endif; ?>
                                
                                <?php if (!empty($level['features']['api_doc'])): ?>
                                <div class="feature-item"><i class="layui-icon">&#xe63c;</i> API文档下载</div>
                                <?php endif; ?>
                                
                                <?php if (!empty($level['features']['priority_support'])): ?>
                                <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 优先技术支持</div>
                                <?php endif; ?>
                                
                                <?php if (!empty($level['features']['data_export'])): ?>
                                <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 数据导出</div>
                                <?php endif; ?>
                                
                                <?php if (!empty($level['features']['white_label'])): ?>
                                <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 白标服务</div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        
                        <div class="level-actions">
                            <?php if (empty($userLevel) || $userLevel['level_id'] != $level['id']): ?>
                                <button class="layui-btn" data-id="<?php echo $level['id']; ?>" onclick="buyLevel(<?php echo $level['id']; ?>, '<?php echo $level['name']; ?>', <?php echo $level['price']; ?>)">立即升级</button>
                            <?php elseif ($userLevel['expire_time']): ?>
                                <button class="layui-btn layui-btn-normal" data-id="<?php echo $level['id']; ?>" onclick="buyLevel(<?php echo $level['id']; ?>, '<?php echo $level['name']; ?>', <?php echo $level['price']; ?>)">续费</button>
                            <?php else: ?>
                                <button class="layui-btn layui-btn-disabled">已是永久会员</button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- 会员等级对比表 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">会员等级对比</div>
                        <div class="layui-card-body">
                            <table class="level-compare-table">
                                <thead>
                                    <tr>
                                        <th>功能</th>
                                        <?php foreach ($levels as $level): ?>
                                        <th><?php echo $level['name']; ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>价格</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td>¥ <?php echo number_format($level['price'], 2); ?> / 年</td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <td>API折扣</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td><?php echo $level['discount_rate'] * 100; ?>%</td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <td>每日调用次数</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td><?php echo $level['daily_request_limit'] == 0 ? '不限' : $level['daily_request_limit']; ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <td>自定义域名</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td>
                                            <?php if (!empty($level['features']) && !empty($level['features']['custom_domain'])): ?>
                                            <i class="layui-icon feature-yes">&#xe605;</i>
                                            <?php else: ?>
                                            <i class="layui-icon feature-no">&#x1006;</i>
                                            <?php endif; ?>
                                        </td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <td>API文档下载</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td>
                                            <?php if (!empty($level['features']) && !empty($level['features']['api_doc'])): ?>
                                            <i class="layui-icon feature-yes">&#xe605;</i>
                                            <?php else: ?>
                                            <i class="layui-icon feature-no">&#x1006;</i>
                                            <?php endif; ?>
                                        </td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <td>优先技术支持</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td>
                                            <?php if (!empty($level['features']) && !empty($level['features']['priority_support'])): ?>
                                            <i class="layui-icon feature-yes">&#xe605;</i>
                                            <?php else: ?>
                                            <i class="layui-icon feature-no">&#x1006;</i>
                                            <?php endif; ?>
                                        </td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <td>数据导出</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td>
                                            <?php if (!empty($level['features']) && !empty($level['features']['data_export'])): ?>
                                            <i class="layui-icon feature-yes">&#xe605;</i>
                                            <?php else: ?>
                                            <i class="layui-icon feature-no">&#x1006;</i>
                                            <?php endif; ?>
                                        </td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <td>白标服务</td>
                                        <?php foreach ($levels as $level): ?>
                                        <td>
                                            <?php if (!empty($level['features']) && !empty($level['features']['white_label'])): ?>
                                            <i class="layui-icon feature-yes">&#xe605;</i>
                                            <?php else: ?>
                                            <i class="layui-icon feature-no">&#x1006;</i>
                                            <?php endif; ?>
                                        </td>
                                        <?php endforeach; ?>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 购买会员弹窗 -->
<script type="text/html" id="buyLevelDialog">
    <div class="layui-form" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">会员等级</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.levelName}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">购买时长</label>
            <div class="layui-input-block">
                <select id="duration" lay-filter="duration">
                    <option value="1">1个月</option>
                    <option value="3">3个月</option>
                    <option value="6">6个月</option>
                    <option value="12" selected>12个月</option>
                    <option value="24">24个月</option>
                    <option value="36">36个月</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付金额</label>
            <div class="layui-input-block">
                <input type="text" id="payAmount" class="layui-input" value="¥ {{d.price}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">支付方式</label>
            <div class="layui-input-block">
                <input type="radio" name="payType" value="balance" title="余额支付" checked>
                <input type="radio" name="payType" value="alipay" title="支付宝">
                <input type="radio" name="payType" value="wechat" title="微信支付">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="buyLevelSubmit">立即支付</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </div>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'util'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var util = layui.util;
    
    // 全局变量
    var currentLevelId = 0;
    var currentLevelPrice = 0;
    var currentDuration = 12;
    
    // 购买会员等级
    window.buyLevel = function(levelId, levelName, price) {
        currentLevelId = levelId;
        currentLevelPrice = price;
        currentDuration = 12;
        
        layer.open({
            type: 1,
            title: '购买会员等级',
            area: ['500px', '350px'],
            content: layui.laytpl($('#buyLevelDialog').html()).render({
                levelId: levelId,
                levelName: levelName,
                price: price.toFixed(2)
            }),
            success: function() {
                form.render();
                
                // 监听购买时长变化
                form.on('select(duration)', function(data) {
                    currentDuration = parseInt(data.value);
                    var amount = (currentLevelPrice * currentDuration / 12).toFixed(2);
                    $('#payAmount').val('¥ ' + amount);
                });
                
                // 监听表单提交
                form.on('submit(buyLevelSubmit)', function(data) {
                    var payType = data.field.payType;
                    
                    // 发起请求
                    $.post('/user/level/create_upgrade_order', {
                        level_id: currentLevelId,
                        duration: currentDuration,
                        pay_type: payType
                    }, function(res) {
                        if (res.code === 0) {
                            if (payType === 'balance') {
                                layer.closeAll();
                                layer.msg(res.msg, {icon: 1});
                                setTimeout(function() {
                                    location.reload();
                                }, 1500);
                            } else {
                                // 跳转到支付页面
                                layer.closeAll();
                                window.location.href = res.data.pay_url;
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    };
});
</script>
</body>
</html>
