<?php
/**
 * 支付管理类
 * API管理系统 - 支付集成与管理
 */

require_once __DIR__ . '/../core/Model.php';

class Payment extends Model {
    protected $table = 'payments';
    protected $fillable = [
        'user_id', 'order_id', 'amount', 'payment_method', 'payment_channel',
        'transaction_id', 'status', 'callback_data', 'paid_at', 'expired_at'
    ];
    
    // 支付状态常量
    const STATUS_PENDING = 0;    // 待支付
    const STATUS_SUCCESS = 1;    // 支付成功
    const STATUS_FAILED = 2;     // 支付失败
    const STATUS_CANCELLED = 3;  // 已取消
    const STATUS_EXPIRED = 4;    // 已过期
    
    // 支付方式常量
    const METHOD_ALIPAY = 'alipay';
    const METHOD_WECHAT = 'wechat';
    const METHOD_BALANCE = 'balance';
    
    /**
     * 创建支付订单
     */
    public function createPayment($data) {
        // 生成订单号
        $data['order_id'] = $this->generateOrderId();
        
        // 设置过期时间（30分钟）
        $data['expired_at'] = date('Y-m-d H:i:s', time() + 1800);
        
        // 设置初始状态
        $data['status'] = self::STATUS_PENDING;
        
        return $this->create($data);
    }
    
    /**
     * 生成订单号
     */
    private function generateOrderId() {
        return 'PAY' . date('YmdHis') . rand(1000, 9999);
    }
    
    /**
     * 支付宝支付
     */
    public function alipayPay($paymentId, $returnUrl = '', $notifyUrl = '') {
        $payment = $this->find($paymentId);
        if (!$payment) {
            throw new Exception('支付订单不存在');
        }
        
        if ($payment['status'] != self::STATUS_PENDING) {
            throw new Exception('订单状态不正确');
        }
        
        // 支付宝配置
        $config = $this->getAlipayConfig();
        
        // 构建支付参数
        $params = [
            'app_id' => $config['app_id'],
            'method' => 'alipay.trade.page.pay',
            'charset' => 'UTF-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'notify_url' => $notifyUrl,
            'return_url' => $returnUrl,
            'biz_content' => json_encode([
                'out_trade_no' => $payment['order_id'],
                'product_code' => 'FAST_INSTANT_TRADE_PAY',
                'total_amount' => $payment['amount'],
                'subject' => 'API系统充值',
                'body' => '用户充值：' . $payment['amount'] . '元'
            ])
        ];
        
        // 生成签名
        $params['sign'] = $this->generateAlipaySign($params, $config['private_key']);
        
        // 构建支付URL
        $payUrl = $config['gateway'] . '?' . http_build_query($params);
        
        return $payUrl;
    }
    
    /**
     * 微信支付
     */
    public function wechatPay($paymentId, $notifyUrl = '') {
        $payment = $this->find($paymentId);
        if (!$payment) {
            throw new Exception('支付订单不存在');
        }
        
        if ($payment['status'] != self::STATUS_PENDING) {
            throw new Exception('订单状态不正确');
        }
        
        // 微信支付配置
        $config = $this->getWechatConfig();
        
        // 构建支付参数
        $params = [
            'appid' => $config['app_id'],
            'mch_id' => $config['mch_id'],
            'nonce_str' => $this->generateNonceStr(),
            'body' => 'API系统充值',
            'out_trade_no' => $payment['order_id'],
            'total_fee' => intval($payment['amount'] * 100), // 转换为分
            'spbill_create_ip' => System::getClientIp(),
            'notify_url' => $notifyUrl,
            'trade_type' => 'NATIVE'
        ];
        
        // 生成签名
        $params['sign'] = $this->generateWechatSign($params, $config['key']);
        
        // 构建XML请求
        $xml = $this->arrayToXml($params);
        
        // 发送请求
        $response = $this->httpPost($config['gateway'], $xml);
        $result = $this->xmlToArray($response);
        
        if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
            return [
                'code_url' => $result['code_url'],
                'prepay_id' => $result['prepay_id']
            ];
        } else {
            throw new Exception('微信支付创建失败：' . ($result['err_code_des'] ?? $result['return_msg']));
        }
    }
    
    /**
     * 余额支付
     */
    public function balancePay($paymentId, $userId) {
        $payment = $this->find($paymentId);
        if (!$payment) {
            throw new Exception('支付订单不存在');
        }
        
        if ($payment['status'] != self::STATUS_PENDING) {
            throw new Exception('订单状态不正确');
        }
        
        // 检查用户余额
        $userModel = new User();
        $user = $userModel->find($userId);
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        if ($user['balance'] < $payment['amount']) {
            throw new Exception('余额不足');
        }
        
        try {
            $this->db->getPdo()->beginTransaction();
            
            // 扣除用户余额
            $userModel->update($userId, [
                'balance' => $user['balance'] - $payment['amount']
            ]);
            
            // 更新支付状态
            $this->update($paymentId, [
                'status' => self::STATUS_SUCCESS,
                'paid_at' => date('Y-m-d H:i:s'),
                'transaction_id' => 'BALANCE_' . time()
            ]);
            
            // 记录余额变动
            $this->recordBalanceChange($userId, -$payment['amount'], '余额支付', $payment['order_id']);
            
            $this->db->getPdo()->commit();
            
            return true;
            
        } catch (Exception $e) {
            $this->db->getPdo()->rollBack();
            throw $e;
        }
    }
    
    /**
     * 处理支付宝回调
     */
    public function handleAlipayCallback($data) {
        // 验证签名
        if (!$this->verifyAlipaySign($data)) {
            throw new Exception('签名验证失败');
        }
        
        $orderId = $data['out_trade_no'];
        $tradeStatus = $data['trade_status'];
        $transactionId = $data['trade_no'];
        $amount = $data['total_amount'];
        
        // 查找支付记录
        $payment = $this->findByOrderId($orderId);
        if (!$payment) {
            throw new Exception('支付订单不存在');
        }
        
        // 验证金额
        if (abs($payment['amount'] - $amount) > 0.01) {
            throw new Exception('支付金额不匹配');
        }
        
        // 处理支付状态
        if ($tradeStatus == 'TRADE_SUCCESS' || $tradeStatus == 'TRADE_FINISHED') {
            $this->processPaymentSuccess($payment['id'], $transactionId, $data);
        } else if ($tradeStatus == 'TRADE_CLOSED') {
            $this->processPaymentFailed($payment['id'], '交易关闭');
        }
        
        return 'success';
    }
    
    /**
     * 处理微信支付回调
     */
    public function handleWechatCallback($xmlData) {
        $data = $this->xmlToArray($xmlData);
        
        // 验证签名
        if (!$this->verifyWechatSign($data)) {
            throw new Exception('签名验证失败');
        }
        
        if ($data['return_code'] != 'SUCCESS' || $data['result_code'] != 'SUCCESS') {
            throw new Exception('支付失败：' . ($data['err_code_des'] ?? '未知错误'));
        }
        
        $orderId = $data['out_trade_no'];
        $transactionId = $data['transaction_id'];
        $amount = $data['total_fee'] / 100; // 转换为元
        
        // 查找支付记录
        $payment = $this->findByOrderId($orderId);
        if (!$payment) {
            throw new Exception('支付订单不存在');
        }
        
        // 验证金额
        if (abs($payment['amount'] - $amount) > 0.01) {
            throw new Exception('支付金额不匹配');
        }
        
        $this->processPaymentSuccess($payment['id'], $transactionId, $data);
        
        return '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
    }
    
    /**
     * 处理支付成功
     */
    private function processPaymentSuccess($paymentId, $transactionId, $callbackData) {
        $payment = $this->find($paymentId);
        if ($payment['status'] == self::STATUS_SUCCESS) {
            return; // 已经处理过了
        }
        
        try {
            $this->db->getPdo()->beginTransaction();
            
            // 更新支付状态
            $this->update($paymentId, [
                'status' => self::STATUS_SUCCESS,
                'transaction_id' => $transactionId,
                'paid_at' => date('Y-m-d H:i:s'),
                'callback_data' => json_encode($callbackData)
            ]);
            
            // 增加用户余额
            $userModel = new User();
            $user = $userModel->find($payment['user_id']);
            $newBalance = $user['balance'] + $payment['amount'];
            
            $userModel->update($payment['user_id'], [
                'balance' => $newBalance
            ]);
            
            // 记录余额变动
            $this->recordBalanceChange(
                $payment['user_id'], 
                $payment['amount'], 
                '在线充值', 
                $payment['order_id']
            );
            
            $this->db->getPdo()->commit();
            
        } catch (Exception $e) {
            $this->db->getPdo()->rollBack();
            throw $e;
        }
    }
    
    /**
     * 处理支付失败
     */
    private function processPaymentFailed($paymentId, $reason) {
        $this->update($paymentId, [
            'status' => self::STATUS_FAILED,
            'callback_data' => json_encode(['reason' => $reason])
        ]);
    }
    
    /**
     * 根据订单号查找支付记录
     */
    public function findByOrderId($orderId) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE order_id = :order_id";
        return $this->db->fetchOne($sql, ['order_id' => $orderId]);
    }
    
    /**
     * 记录余额变动
     */
    private function recordBalanceChange($userId, $amount, $type, $orderId) {
        $sql = "INSERT INTO balance_logs (user_id, amount, type, order_id, created_at) VALUES (?, ?, ?, ?, ?)";
        $this->db->query($sql, [$userId, $amount, $type, $orderId, date('Y-m-d H:i:s')]);
    }
    
    /**
     * 获取支付宝配置
     */
    private function getAlipayConfig() {
        return [
            'app_id' => '2021000000000000', // 应用ID
            'gateway' => 'https://openapi.alipay.com/gateway.do',
            'private_key' => '', // 应用私钥
            'public_key' => '', // 支付宝公钥
        ];
    }
    
    /**
     * 获取微信支付配置
     */
    private function getWechatConfig() {
        return [
            'app_id' => 'wx0000000000000000', // 应用ID
            'mch_id' => '1000000000', // 商户号
            'key' => '', // 商户密钥
            'gateway' => 'https://api.mch.weixin.qq.com/pay/unifiedorder'
        ];
    }
    
    /**
     * 生成支付宝签名
     */
    private function generateAlipaySign($params, $privateKey) {
        // 移除sign参数
        unset($params['sign']);
        
        // 排序并构建签名字符串
        ksort($params);
        $signString = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');
        
        // RSA2签名
        $key = "-----BEGIN RSA PRIVATE KEY-----\n" . 
               wordwrap($privateKey, 64, "\n", true) . 
               "\n-----END RSA PRIVATE KEY-----";
        
        openssl_sign($signString, $signature, $key, OPENSSL_ALGO_SHA256);
        return base64_encode($signature);
    }
    
    /**
     * 验证支付宝签名
     */
    private function verifyAlipaySign($data) {
        // 实际项目中需要实现完整的签名验证逻辑
        return true;
    }
    
    /**
     * 生成微信签名
     */
    private function generateWechatSign($params, $key) {
        // 移除sign参数
        unset($params['sign']);
        
        // 排序并构建签名字符串
        ksort($params);
        $signString = '';
        foreach ($params as $k => $v) {
            if ($v !== '' && $v !== null) {
                $signString .= $k . '=' . $v . '&';
            }
        }
        $signString .= 'key=' . $key;
        
        return strtoupper(md5($signString));
    }
    
    /**
     * 验证微信签名
     */
    private function verifyWechatSign($data) {
        $sign = $data['sign'];
        unset($data['sign']);
        
        $config = $this->getWechatConfig();
        $calculatedSign = $this->generateWechatSign($data, $config['key']);
        
        return $sign === $calculatedSign;
    }
    
    /**
     * 生成随机字符串
     */
    private function generateNonceStr($length = 32) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }
    
    /**
     * 数组转XML
     */
    private function arrayToXml($array) {
        $xml = '<xml>';
        foreach ($array as $key => $value) {
            $xml .= '<' . $key . '><![CDATA[' . $value . ']]></' . $key . '>';
        }
        $xml .= '</xml>';
        return $xml;
    }
    
    /**
     * XML转数组
     */
    private function xmlToArray($xml) {
        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }
    
    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200) {
            throw new Exception('HTTP请求失败，状态码：' . $httpCode);
        }
        
        return $response;
    }
    
    /**
     * 获取支付统计
     */
    public function getPaymentStats($startDate = null, $endDate = null) {
        $conditions = [];
        $params = [];
        
        if ($startDate) {
            $conditions[] = "created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);
        
        $sql = "SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as total_amount,
                    AVG(CASE WHEN status = 1 THEN amount ELSE NULL END) as avg_amount,
                    payment_method,
                    COUNT(*) as method_count
                FROM {$this->getTableName()} 
                {$whereClause}
                GROUP BY payment_method";
        
        return $this->db->fetchAll($sql, $params);
    }
}