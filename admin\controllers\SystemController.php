<?php
/**
 * 系统配置控制器
 */
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

class SystemController {
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new Auth();
        
        // 检查管理员权限
        $this->checkAdminAuth();
    }
    
    /**
     * 获取系统配置
     */
    public function getConfigs() {
        $configs = $this->db->fetchAll(
            "SELECT `key`, `value` FROM system_configs"
        );
        
        $result = [];
        foreach ($configs as $config) {
            $result[$config['key']] = $config['value'];
        }
        
        $this->jsonResponse(['code' => 200, 'data' => $result]);
    }
    
    /**
     * 保存系统配置
     */
    public function saveConfigs() {
        $type = $_POST['type'] ?? '';
        $data = $_POST['data'] ?? [];
        
        if (empty($type) || empty($data)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数不完整']);
        }
        
        try {
            $this->db->getConnection()->beginTransaction();
            
            switch ($type) {
                case 'basic':
                    $this->saveBasicConfigs($data);
                    break;
                case 'function':
                    $this->saveFunctionConfigs($data);
                    break;
                case 'email':
                    $this->saveEmailConfigs($data);
                    break;
                case 'alipay':
                    $this->saveAlipayConfigs($data);
                    break;
                case 'wechat':
                    $this->saveWechatConfigs($data);
                    break;
                default:
                    throw new Exception('未知的配置类型');
            }
            
            $this->db->getConnection()->commit();
            $this->jsonResponse(['code' => 200, 'msg' => '保存成功']);
            
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            $this->jsonResponse(['code' => 500, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 保存基础配置
     */
    private function saveBasicConfigs($data) {
        $configs = [
            'site_name' => $data['site_name'] ?? '',
            'site_description' => $data['site_description'] ?? '',
            'site_keywords' => $data['site_keywords'] ?? '',
            'site_logo' => $data['site_logo'] ?? ''
        ];
        
        foreach ($configs as $key => $value) {
            $this->updateConfig($key, $value);
        }
    }
    
    /**
     * 保存功能配置
     */
    private function saveFunctionConfigs($data) {
        $configs = [
            'register_enabled' => $data['register_enabled'] ?? '0',
            'email_verify' => $data['email_verify'] ?? '0',
            'default_balance' => $data['default_balance'] ?? '0',
            'qps_limit' => $data['qps_limit'] ?? '10',
            'upload_max_size' => isset($data['upload_max_size']) ? $data['upload_max_size'] * 1024 * 1024 : 10485760
        ];
        
        foreach ($configs as $key => $value) {
            $this->updateConfig($key, $value);
        }
    }
    
    /**
     * 保存邮件配置
     */
    private function saveEmailConfigs($data) {
        $configs = [
            'smtp_host' => $data['smtp_host'] ?? '',
            'smtp_port' => $data['smtp_port'] ?? '587',
            'smtp_username' => $data['smtp_username'] ?? '',
            'smtp_password' => $data['smtp_password'] ?? '',
            'from_email' => $data['from_email'] ?? '',
            'from_name' => $data['from_name'] ?? ''
        ];
        
        foreach ($configs as $key => $value) {
            $this->updateConfig($key, $value);
        }
    }
    
    /**
     * 保存支付宝配置
     */
    private function saveAlipayConfigs($data) {
        $configs = [
            'alipay_app_id' => $data['alipay_app_id'] ?? '',
            'alipay_private_key' => $data['alipay_private_key'] ?? '',
            'alipay_public_key' => $data['alipay_public_key'] ?? ''
        ];
        
        foreach ($configs as $key => $value) {
            $this->updateConfig($key, $value);
        }
    }
    
    /**
     * 保存微信配置
     */
    private function saveWechatConfigs($data) {
        $configs = [
            'wechat_app_id' => $data['wechat_app_id'] ?? '',
            'wechat_mch_id' => $data['wechat_mch_id'] ?? '',
            'wechat_key' => $data['wechat_key'] ?? ''
        ];
        
        foreach ($configs as $key => $value) {
            $this->updateConfig($key, $value);
        }
    }
    
    /**
     * 更新配置项
     */
    private function updateConfig($key, $value) {
        $existing = $this->db->fetchOne(
            "SELECT id FROM system_configs WHERE `key` = ?",
            [$key]
        );
        
        if ($existing) {
            $this->db->update('system_configs', [
                'value' => $value,
                'updated_at' => date('Y-m-d H:i:s')
            ], '`key` = ?', [$key]);
        } else {
            $this->db->insert('system_configs', [
                'key' => $key,
                'value' => $value,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 测试邮件发送
     */
    public function testEmail() {
        $email = $_POST['email'] ?? '';
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->jsonResponse(['code' => 400, 'msg' => '邮箱地址不正确']);
        }
        
        try {
            $this->sendTestEmail($email);
            $this->jsonResponse(['code' => 200, 'msg' => '测试邮件发送成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '邮件发送失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 发送测试邮件
     */
    private function sendTestEmail($email) {
        // 获取邮件配置
        $configs = $this->db->fetchAll(
            "SELECT `key`, `value` FROM system_configs WHERE `key` LIKE 'smtp_%' OR `key` LIKE 'from_%'"
        );
        
        $mailConfig = [];
        foreach ($configs as $config) {
            $mailConfig[$config['key']] = $config['value'];
        }
        
        if (empty($mailConfig['smtp_host']) || empty($mailConfig['smtp_username'])) {
            throw new Exception('邮件配置不完整');
        }
        
        // 使用PHPMailer发送邮件（这里简化实现）
        $subject = '系统邮件测试';
        $body = '这是一封测试邮件，如果您收到此邮件，说明邮件配置正确。';
        
        // 实际项目中应该使用PHPMailer或其他邮件库
        $headers = [
            'From: ' . $mailConfig['from_name'] . ' <' . $mailConfig['from_email'] . '>',
            'Reply-To: ' . $mailConfig['from_email'],
            'Content-Type: text/html; charset=UTF-8'
        ];
        
        if (!mail($email, $subject, $body, implode("\r\n", $headers))) {
            throw new Exception('邮件发送失败');
        }
    }
    
    /**
     * 获取系统统计信息
     */
    public function getStats() {
        $stats = [];
        
        // 用户统计
        $stats['users'] = [
            'total' => $this->db->fetchOne("SELECT COUNT(*) as count FROM users")['count'],
            'today' => $this->db->fetchOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()")['count'],
            'active' => $this->db->fetchOne("SELECT COUNT(*) as count FROM users WHERE last_login > DATE_SUB(NOW(), INTERVAL 30 DAY)")['count']
        ];
        
        // API统计
        $stats['apis'] = [
            'total' => $this->db->fetchOne("SELECT COUNT(*) as count FROM apis")['count'],
            'active' => $this->db->fetchOne("SELECT COUNT(*) as count FROM apis WHERE status = 1")['count']
        ];
        
        // 请求统计
        $stats['requests'] = [
            'total' => $this->db->fetchOne("SELECT COUNT(*) as count FROM request_logs")['count'],
            'today' => $this->db->fetchOne("SELECT COUNT(*) as count FROM request_logs WHERE DATE(created_at) = CURDATE()")['count'],
            'success_rate' => $this->getSuccessRate()
        ];
        
        // 收入统计
        $stats['revenue'] = [
            'total' => $this->db->fetchOne("SELECT SUM(amount) as total FROM payment_orders WHERE status = 'completed'")['total'] ?? 0,
            'today' => $this->db->fetchOne("SELECT SUM(amount) as total FROM payment_orders WHERE status = 'completed' AND DATE(created_at) = CURDATE()")['total'] ?? 0
        ];
        
        $this->jsonResponse(['code' => 200, 'data' => $stats]);
    }
    
    /**
     * 获取成功率
     */
    private function getSuccessRate() {
        $total = $this->db->fetchOne("SELECT COUNT(*) as count FROM request_logs")['count'];
        
        if ($total == 0) {
            return 100;
        }
        
        $success = $this->db->fetchOne("SELECT COUNT(*) as count FROM request_logs WHERE success = 1")['count'];
        
        return round(($success / $total) * 100, 2);
    }
    
    /**
     * 清理系统日志
     */
    public function cleanLogs() {
        $days = $_POST['days'] ?? 30;
        
        if ($days < 1) {
            $this->jsonResponse(['code' => 400, 'msg' => '天数必须大于0']);
        }
        
        try {
            // 清理请求日志
            $this->db->query(
                "DELETE FROM request_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)",
                [$days]
            );
            
            // 清理登录日志
            $this->db->query(
                "DELETE FROM login_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)",
                [$days]
            );
            
            $this->jsonResponse(['code' => 200, 'msg' => '日志清理成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '日志清理失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 系统备份
     */
    public function backup() {
        try {
            $backupFile = 'backup_' . date('YmdHis') . '.sql';
            $backupPath = '../backups/' . $backupFile;
            
            // 创建备份目录
            if (!is_dir('../backups')) {
                mkdir('../backups', 0755, true);
            }
            
            // 执行备份命令
            $config = require_once '../../config/database.php';
            $command = sprintf(
                'mysqldump -h%s -u%s -p%s %s > %s',
                $config['host'],
                $config['username'],
                $config['password'],
                $config['dbname'],
                $backupPath
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->jsonResponse([
                    'code' => 200,
                    'msg' => '备份成功',
                    'data' => ['file' => $backupFile]
                ]);
            } else {
                throw new Exception('备份命令执行失败');
            }
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '备份失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取系统信息
     */
    public function getSystemInfo() {
        $info = [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'mysql_version' => $this->db->fetchOne("SELECT VERSION() as version")['version'],
            'disk_usage' => $this->getDiskUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'system_load' => sys_getloadavg()
        ];
        
        $this->jsonResponse(['code' => 200, 'data' => $info]);
    }
    
    /**
     * 获取磁盘使用情况
     */
    private function getDiskUsage() {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $used = $total - $free;
        
        return [
            'total' => $this->formatBytes($total),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'percentage' => round(($used / $total) * 100, 2)
        ];
    }
    
    /**
     * 获取内存使用情况
     */
    private function getMemoryUsage() {
        return [
            'current' => $this->formatBytes(memory_get_usage()),
            'peak' => $this->formatBytes(memory_get_peak_usage()),
            'limit' => ini_get('memory_limit')
        ];
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 检查管理员权限
     */
    private function checkAdminAuth() {
        $token = $_SERVER['HTTP_AUTHORIZATION'] ?? $_COOKIE['admin_token'] ?? '';
        
        if (empty($token)) {
            $this->jsonResponse(['code' => 401, 'msg' => '请先登录']);
        }
        
        $payload = $this->auth->verifyToken($token);
        
        if (!$payload || $payload['role'] !== 'admin') {
            $this->jsonResponse(['code' => 403, 'msg' => '权限不足']);
        }
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 路由处理
$action = $_GET['action'] ?? 'get';
$controller = new SystemController();

switch ($action) {
    case 'get':
        $controller->getConfigs();
        break;
    case 'save':
        $controller->saveConfigs();
        break;
    case 'test-email':
        $controller->testEmail();
        break;
    case 'stats':
        $controller->getStats();
        break;
    case 'clean-logs':
        $controller->cleanLogs();
        break;
    case 'backup':
        $controller->backup();
        break;
    case 'system-info':
        $controller->getSystemInfo();
        break;
    default:
        http_response_code(404);
        echo json_encode(['code' => 404, 'msg' => '接口不存在']);
}
