<?php
/**
 * 数据库操作类
 */
class Database
{
    /**
     * PDO实例
     * @var PDO
     */
    private $pdo;
    
    /**
     * 是否在事务中
     * @var bool
     */
    private $inTransaction = false;
    
    /**
     * 构造函数
     * 
     * @param string $dsn 数据源名称
     * @param string $username 用户名
     * @param string $password 密码
     * @param array $options PDO选项
     */
    public function __construct($dsn, $username, $password, $options = [])
    {
        // 默认选项
        $defaultOptions = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];
        
        // 合并选项
        $options = array_merge($defaultOptions, $options);
        
        try {
            $this->pdo = new PDO($dsn, $username, $password, $options);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败：' . $e->getMessage());
        }
    }
    
    /**
     * 执行SQL查询
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @param bool $single 是否只返回一条记录
     * @return array|null
     */
    public function query($sql, $params = [], $single = false)
    {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            if ($single) {
                $result = $stmt->fetch();
                return $result !== false ? $result : null;
            } else {
                return $stmt->fetchAll();
            }
        } catch (PDOException $e) {
            error_log('SQL查询失败：' . $e->getMessage() . ' - SQL: ' . $sql);
            throw new Exception('查询失败：' . $e->getMessage());
        }
    }
    
    /**
     * 执行SQL语句
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return int 影响的行数
     */
    public function execute($sql, $params = [])
    {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception('执行失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取最后插入的ID
     * 
     * @return string
     */
    public function lastInsertId()
    {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction()
    {
        if (!$this->inTransaction) {
            $this->pdo->beginTransaction();
            $this->inTransaction = true;
        }
    }
    
    /**
     * 提交事务
     */
    public function commit()
    {
        if ($this->inTransaction) {
            $this->pdo->commit();
            $this->inTransaction = false;
        }
    }
    
    /**
     * 回滚事务
     */
    public function rollBack()
    {
        if ($this->inTransaction) {
            $this->pdo->rollBack();
            $this->inTransaction = false;
        }
    }
    
    /**
     * 检查表是否存在
     * 
     * @param string $tableName 表名
     * @return bool
     */
    public function tableExists($tableName)
    {
        try {
            $result = $this->query(
                "SELECT COUNT(*) as count FROM information_schema.tables 
                WHERE table_schema = DATABASE() AND table_name = ?",
                [$tableName],
                true
            );
            return $result['count'] > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取PDO实例
     * 
     * @return PDO
     */
    public function getPdo()
    {
        return $this->pdo;
    }
}