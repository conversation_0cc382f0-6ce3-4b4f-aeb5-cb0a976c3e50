# Highlight.js

Highlight.js нужен для подсветки синтаксиса в примерах кода в блогах,
форумах и вообще на любых веб-страницах. Пользоваться им очень просто,
потому что работает он автоматически: сам находит блоки кода, сам
определяет язык, сам подсвечивает.

Автоопределением языка можно управлять, когда оно не справляется само (см.
дальше "Эвристика").


## Простое использование

Подключите библиотеку и стиль на страницу и повесть вызов подсветки на
загрузку страницы:

```html
<link rel="stylesheet" href="styles/default.css">
<script src="highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
```

Весь код на странице, обрамлённый в теги `<pre><code> .. </code></pre>`
будет автоматически подсвечен. Если вы используете другие теги или хотите
подсвечивать блоки кода динамически, читайте "Инициализацию вручную" ниже.

- Вы можете скачать собственную версию "highlight.pack.js" или сослаться
  на захостенный файл, как описано на странице загрузки:
  <http://highlightjs.org/download/>

- Стилевые темы можно найти в загруженном архиве или также использовать
  захостенные. Чтобы сделать собственный стиль для своего сайта, вам
  будет полезен [CSS classes reference][cr], который тоже есть в архиве.

[cr]: http://highlightjs.readthedocs.org/en/latest/css-classes-reference.html


## node.js

Highlight.js можно использовать в node.js. Библиотеку со всеми возможными языками можно
установить с NPM:

    npm install highlight.js

Также её можно собрать из исходников с только теми языками, которые нужны:

    python3 tools/build.py -tnode lang1 lang2 ..

Использование библиотеки:

```javascript
var hljs = require('highlight.js');

// Если вы знаете язык
hljs.highlight(lang, code).value;

// Автоопределение языка
hljs.highlightAuto(code).value;
```


## AMD

Highlight.js можно использовать с загрузчиком AMD-модулей.  Для этого его
нужно собрать из исходников следующей командой:

```bash
$ python3 tools/build.py -tamd lang1 lang2 ..
```

Она создаст файл `build/highlight.pack.js`, который является загружаемым
AMD-модулем и содержит все выбранные при сборке языки. Используется он так:

```javascript
require(["highlight.js/build/highlight.pack"], function(hljs){

  // Если вы знаете язык
  hljs.highlight(lang, code).value;

  // Автоопределение языка
  hljs.highlightAuto(code).value;
});
```


## Замена TABов

Также вы можете заменить символы TAB ('\x09'), используемые для отступов, на
фиксированное количество пробелов или на отдельный `<span>`, чтобы задать ему
какой-нибудь специальный стиль:

```html
<script type="text/javascript">
  hljs.configure({tabReplace: '    '}); // 4 spaces
  // ... or
  hljs.configure({tabReplace: '<span class="indent">\t</span>'});

  hljs.initHighlightingOnLoad();
</script>
```


## Инициализация вручную

Если вы используете другие теги для блоков кода, вы можете инициализировать их
явно с помощью функции `highlightBlock(code)`. Она принимает DOM-элемент с
текстом расцвечиваемого кода и опционально - строчку для замены символов TAB.

Например с использованием jQuery код инициализации может выглядеть так:

```javascript
$(document).ready(function() {
  $('pre code').each(function(i, e) {hljs.highlightBlock(e)});
});
```

`highlightBlock` можно также использовать, чтобы подсветить блоки кода,
добавленные на страницу динамически. Только убедитесь, что вы не делаете этого
повторно для уже раскрашенных блоков.

Если ваш блок кода использует `<br>` вместо переводов строки (т.е. если это не
`<pre>`), включите опцию `useBR`:

```javascript
hljs.configure({useBR: true});
$('div.code').each(function(i, e) {hljs.highlightBlock(e)});
```


## Эвристика

Определение языка, на котором написан фрагмент, делается с помощью
довольно простой эвристики: программа пытается расцветить фрагмент всеми
языками подряд, и для каждого языка считает количество подошедших
синтаксически конструкций и ключевых слов. Для какого языка нашлось больше,
тот и выбирается.

Это означает, что в коротких фрагментах высока вероятность ошибки, что
периодически и случается. Чтобы указать язык фрагмента явно, надо написать
его название в виде класса к элементу `<code>`:

```html
<pre><code class="html">...</code></pre>
```

Можно использовать рекомендованные в HTML5 названия классов:
"language-html", "language-php". Также можно назначать классы на элемент
`<pre>`.

Чтобы запретить расцветку фрагмента вообще, используется класс "no-highlight":

```html
<pre><code class="no-highlight">...</code></pre>
```


## Экспорт

В файле export.html находится небольшая программка, которая показывает и дает
скопировать непосредственно HTML-код подсветки для любого заданного фрагмента кода.
Это может понадобится например на сайте, на котором нельзя подключить сам скрипт
highlight.js.


## Координаты

- Версия: 8.0
- URL:    http://highlightjs.org/

Лицензионное соглашение читайте в файле LICENSE.
Список авторов и соавторов читайте в файле AUTHORS.ru.txt
