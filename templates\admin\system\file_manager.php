<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>文件管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .file-manager-container {
            display: flex;
            height: calc(100vh - 180px);
            min-height: 500px;
            border: 1px solid #e6e6e6;
        }
        .file-manager-sidebar {
            width: 250px;
            border-right: 1px solid #e6e6e6;
            overflow-y: auto;
            background-color: #f8f8f8;
        }
        .file-manager-content {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            position: relative;
        }
        .file-manager-toolbar {
            padding: 10px 15px;
            border-bottom: 1px solid #e6e6e6;
            background-color: #fff;
        }
        .file-manager-breadcrumb {
            padding: 10px 15px;
            border-bottom: 1px solid #e6e6e6;
        }
        .file-list {
            display: flex;
            flex-wrap: wrap;
        }
        .file-item {
            width: 120px;
            height: 140px;
            margin: 10px;
            text-align: center;
            cursor: pointer;
            border-radius: 2px;
            position: relative;
        }
        .file-item:hover {
            background-color: #f2f2f2;
        }
        .file-item.active {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
        }
        .file-item .file-icon {
            font-size: 50px;
            margin: 10px 0;
        }
        .file-item .file-icon.folder {
            color: #FFB800;
        }
        .file-item .file-icon.file {
            color: #1E9FFF;
        }
        .file-item .file-icon.image {
            color: #5FB878;
        }
        .file-item .file-icon.video {
            color: #FF5722;
        }
        .file-item .file-icon.audio {
            color: #01AAED;
        }
        .file-item .file-icon.document {
            color: #2F4056;
        }
        .file-item .file-icon.archive {
            color: #393D49;
        }
        .file-item .file-name {
            font-size: 12px;
            padding: 0 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .file-item .file-checkbox {
            position: absolute;
            top: 5px;
            left: 5px;
            z-index: 10;
        }
        .file-item .file-menu {
            position: absolute;
            top: 5px;
            right: 5px;
            display: none;
        }
        .file-item:hover .file-menu {
            display: block;
        }
        .file-empty {
            text-align: center;
            padding: 50px 0;
            color: #999;
        }
        .file-empty i {
            font-size: 50px;
            margin-bottom: 10px;
            display: block;
        }
        .file-tree {
            padding: 10px 0;
        }
        .file-tree-node {
            padding: 5px 15px;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .file-tree-node:hover {
            background-color: #f2f2f2;
        }
        .file-tree-node.active {
            background-color: #e6f7ff;
            color: #1E9FFF;
        }
        .file-tree-node i {
            margin-right: 5px;
        }
        .file-tree-node .folder-icon {
            color: #FFB800;
        }
        .file-preview {
            text-align: center;
            padding: 20px;
        }
        .file-preview img {
            max-width: 100%;
            max-height: 400px;
        }
        .file-preview video {
            max-width: 100%;
            max-height: 400px;
        }
        .file-preview audio {
            width: 100%;
        }
        .file-preview .file-info {
            margin-top: 15px;
            text-align: left;
        }
        .file-preview .file-info p {
            margin-bottom: 5px;
        }
        .file-upload-progress {
            margin-top: 10px;
            display: none;
        }
        .file-tree-toggle {
            cursor: pointer;
            margin-right: 5px;
        }
        .file-tree-children {
            padding-left: 20px;
            display: none;
        }
        .file-tree-open > .file-tree-children {
            display: block;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">文件管理</div>
        <div class="layui-card-body">
            <div class="file-manager-toolbar">
                <div class="layui-btn-group">
                    <button id="uploadBtn" class="layui-btn layui-btn-sm"><i class="layui-icon">&#xe67c;</i>上传文件</button>
                    <button id="newFolderBtn" class="layui-btn layui-btn-sm layui-btn-normal"><i class="layui-icon">&#xe654;</i>新建文件夹</button>
                    <button id="refreshBtn" class="layui-btn layui-btn-sm layui-btn-primary"><i class="layui-icon">&#xe669;</i>刷新</button>
                </div>
                <div class="layui-btn-group" style="margin-left: 10px;">
                    <button id="moveBtn" class="layui-btn layui-btn-sm layui-btn-primary" disabled><i class="layui-icon">&#xe630;</i>移动</button>
                    <button id="renameBtn" class="layui-btn layui-btn-sm layui-btn-primary" disabled><i class="layui-icon">&#xe642;</i>重命名</button>
                    <button id="deleteBtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-danger" disabled><i class="layui-icon">&#xe640;</i>删除</button>
                </div>
                <div class="layui-btn-group" style="margin-left: 10px;">
                    <button id="selectAllBtn" class="layui-btn layui-btn-sm layui-btn-primary"><i class="layui-icon">&#xe605;</i>全选</button>
                    <button id="selectNoneBtn" class="layui-btn layui-btn-sm layui-btn-primary"><i class="layui-icon">&#x1006;</i>取消</button>
                </div>
                <div style="float: right;">
                    <div class="layui-input-inline" style="width: 200px;">
                        <input id="searchInput" type="text" placeholder="搜索文件" class="layui-input">
                    </div>
                    <button id="searchBtn" class="layui-btn layui-btn-sm layui-btn-normal"><i class="layui-icon">&#xe615;</i>搜索</button>
                </div>
            </div>
            
            <div class="file-manager-breadcrumb">
                <span class="layui-breadcrumb" id="breadcrumb">
                    <a href="javascript:;" data-path="/">根目录</a>
                </span>
            </div>
            
            <div class="file-manager-container">
                <div class="file-manager-sidebar">
                    <div class="file-tree" id="fileTree">
                        <div class="file-tree-node file-tree-root active" data-path="/">
                            <i class="layui-icon folder-icon">&#xe68e;</i>根目录
                        </div>
                        <!-- 文件树将通过JS动态生成 -->
                    </div>
                </div>
                
                <div class="file-manager-content">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将通过JS动态生成 -->
                    </div>
                    
                    <div class="file-upload-progress">
                        <div class="layui-progress" lay-filter="uploadProgress">
                            <div class="layui-progress-bar" lay-percent="0%"></div>
                        </div>
                        <div class="upload-info">正在上传: <span id="uploadFileName"></span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新建文件夹弹窗 -->
<script type="text/html" id="newFolderDialog">
    <form id="newFolderForm" lay-filter="newFolderForm" class="layui-form model-form">
        <div class="layui-form-item">
            <label class="layui-form-label">文件夹名</label>
            <div class="layui-input-block">
                <input name="folderName" placeholder="请输入文件夹名称" type="text" class="layui-input" maxlength="100" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="newFolderSubmit" lay-submit>确定</button>
        </div>
    </form>
</script>

<!-- 重命名弹窗 -->
<script type="text/html" id="renameDialog">
    <form id="renameForm" lay-filter="renameForm" class="layui-form model-form">
        <input type="hidden" name="path" value="{{d.path}}">
        <input type="hidden" name="type" value="{{d.type}}">
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-block">
                <input name="name" placeholder="请输入新名称" type="text" class="layui-input" value="{{d.name}}" maxlength="100" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="renameSubmit" lay-submit>确定</button>
        </div>
    </form>
</script>

<!-- 移动文件弹窗 -->
<script type="text/html" id="moveDialog">
    <form id="moveForm" lay-filter="moveForm" class="layui-form model-form">
        <div class="layui-form-item">
            <label class="layui-form-label">目标位置</label>
            <div class="layui-input-block">
                <div id="moveTree" class="file-tree" style="height: 300px; overflow-y: auto; border: 1px solid #e6e6e6; padding: 10px;">
                    <!-- 移动目标文件树将通过JS动态生成 -->
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="moveSubmit" lay-submit>确定</button>
        </div>
    </form>
</script>

<!-- 文件预览弹窗 -->
<script type="text/html" id="filePreviewDialog">
    <div class="file-preview">
        {{# if(d.type === 'image'){ }}
        <img src="{{d.url}}" alt="{{d.name}}">
        {{# }else if(d.type === 'video'){ }}
        <video src="{{d.url}}" controls></video>
        {{# }else if(d.type === 'audio'){ }}
        <audio src="{{d.url}}" controls></audio>
        {{# }else{ }}
        <div style="padding: 50px 0;">
            <i class="layui-icon" style="font-size: 50px; color: #1E9FFF;">&#xe655;</i>
            <p>该文件类型不支持预览</p>
        </div>
        {{# } }}
        
        <div class="file-info">
            <p><strong>文件名：</strong>{{d.name}}</p>
            <p><strong>文件类型：</strong>{{d.mime}}</p>
            <p><strong>文件大小：</strong>{{d.size_format}}</p>
            <p><strong>修改时间：</strong>{{d.mtime}}</p>
            <p><strong>文件路径：</strong>{{d.path}}</p>
            {{# if(d.url){ }}
            <p><strong>访问地址：</strong><a href="{{d.url}}" target="_blank">{{d.url}}</a></p>
            {{# } }}
        </div>
    </div>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'element', 'upload', 'util', 'admin'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var element = layui.element;
    var upload = layui.upload;
    var util = layui.util;
    var admin = layui.admin;
    
    // 当前路径
    var currentPath = '/';
    // 选中的文件
    var selectedFiles = [];
    
    // 初始化
    loadFileTree();
    loadFileList(currentPath);
    
    // 加载文件树
    function loadFileTree() {
        $.get('/admin/system/file/tree', function(res) {
            if (res.code === 0) {
                renderFileTree(res.data, $('#fileTree'));
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 渲染文件树
    function renderFileTree(data, container) {
        if (!data || data.length === 0) return;
        
        var html = '';
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (item.type === 'dir') {
                html += '<div class="file-tree-node" data-path="' + item.path + '">';
                if (item.children && item.children.length > 0) {
                    html += '<i class="layui-icon file-tree-toggle">&#xe623;</i>';
                } else {
                    html += '<i class="layui-icon" style="visibility: hidden;">&#xe623;</i>';
                }
                html += '<i class="layui-icon folder-icon">&#xe68e;</i>' + item.name;
                html += '</div>';
                
                if (item.children && item.children.length > 0) {
                    html += '<div class="file-tree-children">';
                    html += renderFileTreeHtml(item.children);
                    html += '</div>';
                }
            }
        }
        
        if (container.hasClass('file-tree-root')) {
            container.after(html);
        } else {
            container.append(html);
        }
    }
    
    // 生成文件树HTML
    function renderFileTreeHtml(data) {
        if (!data || data.length === 0) return '';
        
        var html = '';
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (item.type === 'dir') {
                html += '<div class="file-tree-node" data-path="' + item.path + '">';
                if (item.children && item.children.length > 0) {
                    html += '<i class="layui-icon file-tree-toggle">&#xe623;</i>';
                } else {
                    html += '<i class="layui-icon" style="visibility: hidden;">&#xe623;</i>';
                }
                html += '<i class="layui-icon folder-icon">&#xe68e;</i>' + item.name;
                html += '</div>';
                
                if (item.children && item.children.length > 0) {
                    html += '<div class="file-tree-children">';
                    html += renderFileTreeHtml(item.children);
                    html += '</div>';
                }
            }
        }
        
        return html;
    }
    
    // 加载文件列表
    function loadFileList(path) {
        currentPath = path;
        selectedFiles = [];
        updateToolbarStatus();
        
        // 更新面包屑
        updateBreadcrumb(path);
        
        // 更新文件树选中状态
        $('.file-tree-node').removeClass('active');
        $('.file-tree-node[data-path="' + path + '"]').addClass('active');
        
        // 加载文件列表
        $.get('/admin/system/file/list', {
            path: path
        }, function(res) {
            if (res.code === 0) {
                renderFileList(res.data);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 渲染文件列表
    function renderFileList(data) {
        var html = '';
        
        if (!data || data.length === 0) {
            html = '<div class="file-empty"><i class="layui-icon">&#xe664;</i>当前文件夹为空</div>';
            $('#fileList').html(html);
            return;
        }
        
        // 先显示文件夹
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (item.type === 'dir') {
                html += '<div class="file-item" data-path="' + item.path + '" data-type="dir" data-name="' + item.name + '">';
                html += '<div class="file-checkbox"><input type="checkbox" lay-skin="primary"></div>';
                html += '<div class="file-menu"><i class="layui-icon">&#xe65f;</i></div>';
                html += '<div class="file-icon folder"><i class="layui-icon">&#xe68e;</i></div>';
                html += '<div class="file-name">' + item.name + '</div>';
                html += '</div>';
            }
        }
        
        // 再显示文件
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (item.type !== 'dir') {
                var iconClass = getFileIconClass(item.name);
                html += '<div class="file-item" data-path="' + item.path + '" data-type="file" data-name="' + item.name + '">';
                html += '<div class="file-checkbox"><input type="checkbox" lay-skin="primary"></div>';
                html += '<div class="file-menu"><i class="layui-icon">&#xe65f;</i></div>';
                html += '<div class="file-icon ' + iconClass + '"><i class="layui-icon">&#xe655;</i></div>';
                html += '<div class="file-name">' + item.name + '</div>';
                html += '</div>';
            }
        }
        
        $('#fileList').html(html);
        form.render('checkbox');
    }
    
    // 获取文件图标类名
    function getFileIconClass(filename) {
        var ext = filename.split('.').pop().toLowerCase();
        
        // 图片
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(ext) !== -1) {
            return 'image';
        }
        
        // 视频
        if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].indexOf(ext) !== -1) {
            return 'video';
        }
        
        // 音频
        if (['mp3', 'wav', 'ogg', 'flac', 'aac'].indexOf(ext) !== -1) {
            return 'audio';
        }
        
        // 文档
        if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'txt', 'md'].indexOf(ext) !== -1) {
            return 'document';
        }
        
        // 压缩包
        if (['zip', 'rar', '7z', 'tar', 'gz'].indexOf(ext) !== -1) {
            return 'archive';
        }
        
        return 'file';
    }
    
    // 更新面包屑
    function updateBreadcrumb(path) {
        var paths = path.split('/');
        var html = '<a href="javascript:;" data-path="/">根目录</a>';
        var currentPath = '/';
        
        for (var i = 1; i < paths.length; i++) {
            if (paths[i]) {
                currentPath += paths[i] + '/';
                html += '<a href="javascript:;" data-path="' + currentPath + '">' + paths[i] + '</a>';
            }
        }
        
        $('#breadcrumb').html(html);
    }
    
    // 更新工具栏状态
    function updateToolbarStatus() {
        if (selectedFiles.length > 0) {
            $('#moveBtn, #deleteBtn').removeAttr('disabled');
        } else {
            $('#moveBtn, #deleteBtn').attr('disabled', 'disabled');
        }
        
        if (selectedFiles.length === 1) {
            $('#renameBtn').removeAttr('disabled');
        } else {
            $('#renameBtn').attr('disabled', 'disabled');
        }
    }
    
    // 文件上传
    upload.render({
        elem: '#uploadBtn',
        url: '/admin/system/file/upload',
        data: {
            path: function() {
                return currentPath;
            }
        },
        multiple: true,
        accept: 'file',
        before: function(obj) {
            $('.file-upload-progress').show();
        },
        progress: function(n, elem, res, index) {
            var percent = n + '%';
            $('#uploadFileName').text(elem.name);
            element.progress('uploadProgress', percent);
        },
        done: function(res) {
            if (res.code === 0) {
                loadFileList(currentPath);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        },
        allDone: function() {
            $('.file-upload-progress').hide();
        },
        error: function() {
            layer.msg('上传失败', {icon: 2});
            $('.file-upload-progress').hide();
        }
    });
    
    // 新建文件夹
    $('#newFolderBtn').click(function() {
        admin.open({
            type: 1,
            title: '新建文件夹',
            content: $('#newFolderDialog').html(),
            area: ['400px', '200px'],
            success: function (layero, dIndex) {
                form.render();
                
                // 表单提交事件
                form.on('submit(newFolderSubmit)', function (data) {
                    var folderName = data.field.folderName;
                    
                    $.post('/admin/system/file/create_folder', {
                        path: currentPath,
                        name: folderName
                    }, function (res) {
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            loadFileList(currentPath);
                            loadFileTree();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    });
    
    // 刷新按钮
    $('#refreshBtn').click(function() {
        loadFileList(currentPath);
        loadFileTree();
    });
    
    // 重命名按钮
    $('#renameBtn').click(function() {
        if (selectedFiles.length !== 1) {
            layer.msg('请选择一个文件或文件夹', {icon: 2});
            return;
        }
        
        var file = selectedFiles[0];
        
        admin.open({
            type: 1,
            title: '重命名',
            content: laytpl($('#renameDialog').html()).render(file),
            area: ['400px', '200px'],
            success: function (layero, dIndex) {
                form.render();
                
                // 表单提交事件
                form.on('submit(renameSubmit)', function (data) {
                    $.post('/admin/system/file/rename', data.field, function (res) {
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            loadFileList(currentPath);
                            loadFileTree();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    });
    
    // 删除按钮
    $('#deleteBtn').click(function() {
        if (selectedFiles.length === 0) {
            layer.msg('请选择要删除的文件或文件夹', {icon: 2});
            return;
        }
        
        var paths = [];
        for (var i = 0; i < selectedFiles.length; i++) {
            paths.push(selectedFiles[i].path);
        }
        
        layer.confirm('确定要删除选中的' + selectedFiles.length + '个文件或文件夹吗？', {
            skin: 'layui-layer-admin',
            shade: .1
        }, function (i) {
            layer.close(i);
            layer.load(2);
            
            $.post('/admin/system/file/delete', {
                paths: paths.join(',')
            }, function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    loadFileList(currentPath);
                    loadFileTree();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
        });
    });
    
    // 移动按钮
    $('#moveBtn').click(function() {
        if (selectedFiles.length === 0) {
            layer.msg('请选择要移动的文件或文件夹', {icon: 2});
            return;
        }
        
        admin.open({
            type: 1,
            title: '移动文件',
            content: $('#moveDialog').html(),
            area: ['500px', '450px'],
            success: function (layero, dIndex) {
                // 加载文件树
                $.get('/admin/system/file/tree', function(res) {
                    if (res.code === 0) {
                        var html = '<div class="file-tree-node active" data-path="/">';
                        html += '<i class="layui-icon folder-icon">&#xe68e;</i>根目录';
                        html += '</div>';
                        
                        if (res.data && res.data.length > 0) {
                            html += renderMoveTreeHtml(res.data);
                        }
                        
                        $('#moveTree').html(html);
                        
                        // 绑定点击事件
                        $('#moveTree').on('click', '.file-tree-node', function() {
                            $('#moveTree .file-tree-node').removeClass('active');
                            $(this).addClass('active');
                        });
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
                
                // 表单提交事件
                form.on('submit(moveSubmit)', function () {
                    var targetPath = $('#moveTree .file-tree-node.active').data('path');
                    var paths = [];
                    
                    for (var i = 0; i < selectedFiles.length; i++) {
                        paths.push(selectedFiles[i].path);
                    }
                    
                    $.post('/admin/system/file/move', {
                        paths: paths.join(','),
                        target: targetPath
                    }, function (res) {
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            loadFileList(currentPath);
                            loadFileTree();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    });
    
    // 生成移动文件树HTML
    function renderMoveTreeHtml(data) {
        if (!data || data.length === 0) return '';
        
        var html = '';
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (item.type === 'dir') {
                html += '<div class="file-tree-node" data-path="' + item.path + '">';
                html += '<i class="layui-icon folder-icon">&#xe68e;</i>' + item.name;
                html += '</div>';
                
                if (item.children && item.children.length > 0) {
                    html += renderMoveTreeHtml(item.children);
                }
            }
        }
        
        return html;
    }
    
    // 全选按钮
    $('#selectAllBtn').click(function() {
        $('#fileList .file-item input[type="checkbox"]').prop('checked', true);
        form.render('checkbox');
        
        // 更新选中文件
        selectedFiles = [];
        $('#fileList .file-item').each(function() {
            var path = $(this).data('path');
            var type = $(this).data('type');
            var name = $(this).data('name');
            
            selectedFiles.push({
                path: path,
                type: type,
                name: name
            });
        });
        
        updateToolbarStatus();
    });
    
    // 取消选择按钮
    $('#selectNoneBtn').click(function() {
        $('#fileList .file-item input[type="checkbox"]').prop('checked', false);
        form.render('checkbox');
        
        selectedFiles = [];
        updateToolbarStatus();
    });
    
    // 搜索按钮
    $('#searchBtn').click(function() {
        var keyword = $('#searchInput').val();
        if (!keyword) {
            layer.msg('请输入搜索关键词', {icon: 2});
            return;
        }
        
        $.get('/admin/system/file/search', {
            path: currentPath,
            keyword: keyword
        }, function(res) {
            if (res.code === 0) {
                renderFileList(res.data);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    });
    
    // 文件树点击事件
    $(document).on('click', '.file-tree-node', function() {
        var path = $(this).data('path');
        loadFileList(path);
    });
    
    // 文件树展开/折叠事件
    $(document).on('click', '.file-tree-toggle', function(e) {
        e.stopPropagation();
        
        var node = $(this).closest('.file-tree-node');
        var children = node.next('.file-tree-children');
        
        if (children.is(':visible')) {
            children.hide();
            $(this).html('&#xe623;');
        } else {
            children.show();
            $(this).html('&#xe625;');
        }
    });
    
    // 面包屑点击事件
    $(document).on('click', '#breadcrumb a', function() {
        var path = $(this).data('path');
        loadFileList(path);
    });
    
    // 文件项点击事件
    $(document).on('click', '.file-item', function(e) {
        // 如果点击的是复选框或菜单，不处理
        if ($(e.target).closest('.file-checkbox').length || $(e.target).closest('.file-menu').length) {
            return;
        }
        
        var path = $(this).data('path');
        var type = $(this).data('type');
        var name = $(this).data('name');
        
        if (type === 'dir') {
            // 如果是文件夹，进入该文件夹
            loadFileList(path);
        } else {
            // 如果是文件，预览文件
            previewFile(path, name);
        }
    });
    
    // 文件复选框点击事件
    $(document).on('click', '.file-item .file-checkbox input', function(e) {
        e.stopPropagation();
        
        var item = $(this).closest('.file-item');
        var path = item.data('path');
        var type = item.data('type');
        var name = item.data('name');
        
        if ($(this).prop('checked')) {
            // 添加到选中文件
            selectedFiles.push({
                path: path,
                type: type,
                name: name
            });
        } else {
            // 从选中文件中移除
            for (var i = 0; i < selectedFiles.length; i++) {
                if (selectedFiles[i].path === path) {
                    selectedFiles.splice(i, 1);
                    break;
                }
            }
        }
        
        updateToolbarStatus();
    });
    
    // 预览文件
    function previewFile(path, name) {
        $.get('/admin/system/file/info', {
            path: path
        }, function(res) {
            if (res.code === 0) {
                var fileInfo = res.data;
                fileInfo.name = name;
                
                admin.open({
                    type: 1,
                    title: '文件预览 - ' + name,
                    content: laytpl($('#filePreviewDialog').html()).render(fileInfo),
                    area: ['800px', '600px']
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
});
</script>
</body>
</html>
