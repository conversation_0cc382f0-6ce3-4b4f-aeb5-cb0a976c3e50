<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>文章管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">文章管理</h2>
        </div>
        <div class="layui-card-body">
            <!-- 工具栏 -->
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <button class="layui-btn" id="add-article">
                            <i class="layui-icon layui-icon-add-1"></i>添加文章
                        </button>
                        <button class="layui-btn layui-btn-normal" id="manage-category">
                            <i class="layui-icon layui-icon-template"></i>分类管理
                        </button>
                        <button class="layui-btn layui-btn-danger" id="batch-delete">
                            <i class="layui-icon layui-icon-delete"></i>批量删除
                        </button>
                    </div>
                    <div class="layui-inline" style="float: right;">
                        <select name="category_id" lay-search>
                            <option value="">全部分类</option>
                        </select>
                        <input type="text" name="title" placeholder="请输入标题搜索" class="layui-input" style="width: 200px; display: inline-block;">
                        <button class="layui-btn" id="search-btn">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="article-table" lay-filter="article-table"></table>
        </div>
    </div>
</div>

<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="edit">编辑</button>
        <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="view">查看</button>
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</button>
    </div>
</script>

<script type="text/html" id="status-tpl">
    {{# if(d.status == 1){ }}
        <span class="layui-badge layui-bg-green">已发布</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">草稿</span>
    {{# } }}
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 加载分类选项
    loadCategories();

    // 渲染表格
    table.render({
        elem: '#article-table',
        url: '../controllers/ArticleController.php?action=getList',
        toolbar: '#toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', width: 50},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'title', title: '标题', width: 300},
            {field: 'category_name', title: '分类', width: 120},
            {field: 'author', title: '作者', width: 120},
            {field: 'views', title: '浏览量', width: 100, sort: true},
            {field: 'status', title: '状态', width: 100, templet: '#status-tpl'},
            {field: 'created_at', title: '创建时间', width: 180, sort: true},
            {title: '操作', width: 180, align: 'center', toolbar: '#toolbar'}
        ]],
        page: true,
        height: 'full-220'
    });

    // 加载分类
    function loadCategories() {
        $.get('../controllers/ArticleController.php?action=getCategories', function(res){
            if(res.code === 200){
                var html = '<option value="">全部分类</option>';
                res.data.forEach(function(item){
                    html += '<option value="' + item.id + '">' + item.name + '</option>';
                });
                $('select[name="category_id"]').html(html);
                form.render('select');
            }
        }, 'json');
    }

    // 添加文章
    $('#add-article').click(function(){
        window.open('edit.html', '_blank');
    });

    // 分类管理
    $('#manage-category').click(function(){
        window.open('category.html', '_blank');
    });

    // 表格工具栏事件
    table.on('tool(article-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'edit'){
            window.open('edit.html?id=' + data.id, '_blank');
        } else if(obj.event === 'view'){
            window.open('../api/article/' + data.id, '_blank');
        } else if(obj.event === 'del'){
            layer.confirm('确定删除这篇文章吗？', function(index){
                $.post('../controllers/ArticleController.php?action=delete', {id: data.id}, function(res){
                    if(res.code === 200){
                        layer.msg('删除成功');
                        table.reload('article-table');
                    } else {
                        layer.msg('删除失败: ' + res.msg);
                    }
                }, 'json');
                layer.close(index);
            });
        }
    });

    // 批量删除
    $('#batch-delete').click(function(){
        var checkStatus = table.checkStatus('article-table');
        if(checkStatus.data.length === 0){
            layer.msg('请选择要删除的数据');
            return;
        }
        layer.confirm('确定删除选中的文章吗？', function(index){
            var ids = checkStatus.data.map(function(item){
                return item.id;
            });
            $.post('../controllers/ArticleController.php?action=batchDelete', {ids: ids}, function(res){
                if(res.code === 200){
                    layer.msg('删除成功');
                    table.reload('article-table');
                } else {
                    layer.msg('删除失败: ' + res.msg);
                }
            }, 'json');
            layer.close(index);
        });
    });

    // 搜索
    $('#search-btn').click(function(){
        var title = $('input[name="title"]').val();
        var category_id = $('select[name="category_id"]').val();
        table.reload('article-table', {
            where: {title: title, category_id: category_id},
            page: {curr: 1}
        });
    });
});
</script>
</body>
</html>