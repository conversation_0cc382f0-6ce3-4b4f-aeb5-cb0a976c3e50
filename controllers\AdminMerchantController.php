<?php
/**
 * 管理员商家控制器
 */
class AdminMerchantController {
    private $db;
    private $config;
    
    public function __construct() {
        global $db, $config;
        $this->db = $db;
        $this->config = $config;
    }
    
    /**
     * 获取商家入驻申请列表
     */
    public function getApplyList() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        $name = isset($_GET['name']) ? trim($_GET['name']) : '';
        
        // 构建查询条件
        $where = "1=1";
        $params = [];
        
        if ($status !== '') {
            $where .= " AND status = ?";
            $params[] = $status;
        }
        
        if ($name !== '') {
            $where .= " AND name LIKE ?";
            $params[] = "%$name%";
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_applications WHERE $where");
        $stmt->execute($params);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT * FROM merchant_applications WHERE $where ORDER BY create_time DESC LIMIT $offset, $limit");
        $stmt->execute($params);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 审核商家入驻申请
     */
    public function auditApply() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : 0;
        $level = isset($_POST['level']) ? intval($_POST['level']) : 0;
        $auditRemark = isset($_POST['audit_remark']) ? trim($_POST['audit_remark']) : '';
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        if ($status != 1 && $status != 2) {
            return json([
                'code' => 1,
                'msg' => '状态参数错误'
            ]);
        }
        
        // 检查申请是否存在
        $stmt = $this->db->prepare("SELECT * FROM merchant_applications WHERE id = ? AND status = 0");
        $stmt->execute([$id]);
        $apply = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$apply) {
            return json([
                'code' => 1,
                'msg' => '申请不存在或已审核'
            ]);
        }
        
        // 如果是通过申请，检查商家等级是否存在
        if ($status == 1) {
            if ($level <= 0) {
                return json([
                    'code' => 1,
                    'msg' => '请选择商家等级'
                ]);
            }
            
            $stmt = $this->db->prepare("SELECT id FROM merchant_levels WHERE id = ? AND status = 1");
            $stmt->execute([$level]);
            if (!$stmt->fetch()) {
                return json([
                    'code' => 1,
                    'msg' => '所选商家等级不存在或已禁用'
                ]);
            }
        }
        
        // 更新申请状态
        $stmt = $this->db->prepare("UPDATE merchant_applications SET status = ?, audit_time = ?, audit_remark = ? WHERE id = ?");
        $stmt->execute([
            $status,
            time(),
            $auditRemark,
            $id
        ]);
        
        // 如果是通过申请，创建商家账号
        if ($status == 1) {
            $stmt = $this->db->prepare("INSERT INTO merchants (user_id, name, contact, phone, email, address, 
                                       business_license, id_card_front, id_card_back, description, level, status, create_time) 
                                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?)");
            $stmt->execute([
                $apply['user_id'],
                $apply['name'],
                $apply['contact'],
                $apply['phone'],
                $apply['email'],
                $apply['address'],
                $apply['business_license'],
                $apply['id_card_front'],
                $apply['id_card_back'],
                $apply['description'],
                $level,
                time()
            ]);
            
            // 发送通知邮件
            $this->sendAuditNotification($apply, $status, $auditRemark);
        } else {
            // 发送通知邮件
            $this->sendAuditNotification($apply, $status, $auditRemark);
        }
        
        return json([
            'code' => 0,
            'msg' => $status == 1 ? '已通过申请' : '已拒绝申请'
        ]);
    }
    
    /**
     * 发送审核通知邮件
     */
    private function sendAuditNotification($apply, $status, $auditRemark) {
        // 获取用户邮箱
        $stmt = $this->db->prepare("SELECT email FROM users WHERE id = ?");
        $stmt->execute([$apply['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user || empty($user['email'])) {
            return;
        }
        
        $subject = '商家入驻申请审核结果通知';
        $message = "尊敬的用户：\n\n";
        $message .= "您的商家入驻申请（{$apply['name']}）已审核完成，结果如下：\n\n";
        $message .= "审核结果：" . ($status == 1 ? '通过' : '拒绝') . "\n";
        
        if (!empty($auditRemark)) {
            $message .= "审核备注：{$auditRemark}\n\n";
        }
        
        if ($status == 1) {
            $message .= "恭喜您成为我们的商家合作伙伴！您现在可以登录商家中心，开始管理您的API业务。\n\n";
        } else {
            $message .= "您可以根据审核备注修改后重新提交申请。\n\n";
        }
        
        $message .= "感谢您的支持！\n\n";
        $message .= "此邮件由系统自动发送，请勿回复。";
        
        // 发送邮件
        sendMail($user['email'], $subject, $message);
    }
    
    /**
     * 获取商家等级列表
     */
    public function getLevelList() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_levels");
        $stmt->execute();
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT * FROM merchant_levels ORDER BY price ASC LIMIT $offset, $limit");
        $stmt->execute();
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 添加商家等级
     */
    public function addLevel() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
        $discountRate = isset($_POST['discount_rate']) ? floatval($_POST['discount_rate']) : 1;
        $maxApiCount = isset($_POST['max_api_count']) ? intval($_POST['max_api_count']) : 0;
        $features = isset($_POST['features']) ? $_POST['features'] : [];
        $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
        
        // 验证参数
        if (empty($name)) {
            return json([
                'code' => 1,
                'msg' => '等级名称不能为空'
            ]);
        }
        
        if ($price < 0) {
            return json([
                'code' => 1,
                'msg' => '价格不能为负数'
            ]);
        }
        
        if ($discountRate <= 0 || $discountRate > 1) {
            return json([
                'code' => 1,
                'msg' => '折扣率必须在0-1之间'
            ]);
        }
        
        if ($maxApiCount < 0) {
            return json([
                'code' => 1,
                'msg' => 'API数量上限不能为负数'
            ]);
        }
        
        // 检查等级名称是否已存在
        $stmt = $this->db->prepare("SELECT id FROM merchant_levels WHERE name = ?");
        $stmt->execute([$name]);
        if ($stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '等级名称已存在'
            ]);
        }
        
        // 将特权数组转为JSON
        $featuresJson = json_encode($features);
        
        // 添加等级
        $stmt = $this->db->prepare("INSERT INTO merchant_levels (name, description, price, discount_rate, max_api_count, features, status, create_time) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $name,
            $description,
            $price,
            $discountRate,
            $maxApiCount,
            $featuresJson,
            $status,
            time()
        ]);
        
        return json([
            'code' => 0,
            'msg' => '添加成功'
        ]);
    }
    
    /**
     * 更新商家等级
     */
    public function updateLevel() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
        $discountRate = isset($_POST['discount_rate']) ? floatval($_POST['discount_rate']) : 1;
        $maxApiCount = isset($_POST['max_api_count']) ? intval($_POST['max_api_count']) : 0;
        $features = isset($_POST['features']) ? $_POST['features'] : [];
        $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
        
        // 验证参数
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        if (empty($name)) {
            return json([
                'code' => 1,
                'msg' => '等级名称不能为空'
            ]);
        }
        
        if ($price < 0) {
            return json([
                'code' => 1,
                'msg' => '价格不能为负数'
            ]);
        }
        
        if ($discountRate <= 0 || $discountRate > 1) {
            return json([
                'code' => 1,
                'msg' => '折扣率必须在0-1之间'
            ]);
        }
        
        if ($maxApiCount < 0) {
            return json([
                'code' => 1,
                'msg' => 'API数量上限不能为负数'
            ]);
        }
        
        // 检查等级是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchant_levels WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '等级不存在'
            ]);
        }
        
        // 检查等级名称是否已存在（排除自身）
        $stmt = $this->db->prepare("SELECT id FROM merchant_levels WHERE name = ? AND id != ?");
        $stmt->execute([$name, $id]);
        if ($stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '等级名称已存在'
            ]);
        }
        
        // 将特权数组转为JSON
        $featuresJson = json_encode($features);
        
        // 更新等级
        $stmt = $this->db->prepare("UPDATE merchant_levels SET name = ?, description = ?, price = ?, discount_rate = ?, 
                                   max_api_count = ?, features = ?, status = ?, update_time = ? 
                                   WHERE id = ?");
        $stmt->execute([
            $name,
            $description,
            $price,
            $discountRate,
            $maxApiCount,
            $featuresJson,
            $status,
            time(),
            $id
        ]);
        
        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }
    
    /**
     * 删除商家等级
     */
    public function deleteLevel() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        // 检查等级是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchant_levels WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '等级不存在'
            ]);
        }
        
        // 检查是否有商家使用该等级
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE level = ?");
        $stmt->execute([$id]);
        if ($stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '该等级已被商家使用，无法删除'
            ]);
        }
        
        // 删除等级
        $stmt = $this->db->prepare("DELETE FROM merchant_levels WHERE id = ?");
        $stmt->execute([$id]);
        
        return json([
            'code' => 0,
            'msg' => '删除成功'
        ]);
    }
    
    /**
     * 获取商家列表
     */
    public function getMerchantList() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        $name = isset($_GET['name']) ? trim($_GET['name']) : '';
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        
        // 构建查询条件
        $where = "1=1";
        $params = [];
        
        if ($name !== '') {
            $where .= " AND m.name LIKE ?";
            $params[] = "%$name%";
        }
        
        if ($status !== '') {
            $where .= " AND m.status = ?";
            $params[] = $status;
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchants m WHERE $where");
        $stmt->execute($params);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT m.*, l.name as level_name, u.username 
                                   FROM merchants m 
                                   LEFT JOIN merchant_levels l ON m.level = l.id 
                                   LEFT JOIN users u ON m.user_id = u.id 
                                   WHERE $where 
                                   ORDER BY m.create_time DESC 
                                   LIMIT $offset, $limit");
        $stmt->execute($params);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 更新商家状态
     */
    public function updateMerchantStatus() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        if ($status != 0 && $status != 1) {
            return json([
                'code' => 1,
                'msg' => '状态参数错误'
            ]);
        }
        
        // 检查商家是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '商家不存在'
            ]);
        }
        
        // 更新状态
        $stmt = $this->db->prepare("UPDATE merchants SET status = ?, update_time = ? WHERE id = ?");
        $stmt->execute([
            $status,
            time(),
            $id
        ]);
        
        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }
    
    /**
     * 更新商家等级
     */
    public function updateMerchantLevel() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $level = isset($_POST['level']) ? intval($_POST['level']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        if ($level <= 0) {
            return json([
                'code' => 1,
                'msg' => '请选择商家等级'
            ]);
        }
        
        // 检查商家是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '商家不存在'
            ]);
        }
        
        // 检查等级是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchant_levels WHERE id = ? AND status = 1");
        $stmt->execute([$level]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '所选商家等级不存在或已禁用'
            ]);
        }
        
        // 更新等级
        $stmt = $this->db->prepare("UPDATE merchants SET level = ?, update_time = ? WHERE id = ?");
        $stmt->execute([
            $level,
            time(),
            $id
        ]);
        
        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }
    
    /**
     * 获取商家API列表
     */
    public function getMerchantApiList() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        if ($merchantId <= 0) {
            return json([
                'code' => 1,
                'msg' => '商家ID不能为空'
            ]);
        }
        
        // 检查商家是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE id = ?");
        $stmt->execute([$merchantId]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '商家不存在'
            ]);
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_apis WHERE merchant_id = ?");
        $stmt->execute([$merchantId]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT ma.*, a.name as api_name, a.method, a.url 
                                   FROM merchant_apis ma 
                                   LEFT JOIN apis a ON ma.api_id = a.id 
                                   WHERE ma.merchant_id = ? 
                                   ORDER BY ma.create_time DESC 
                                   LIMIT $offset, $limit");
        $stmt->execute([$merchantId]);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 获取商家收入列表
     */
    public function getMerchantIncomeList() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        if ($merchantId <= 0) {
            return json([
                'code' => 1,
                'msg' => '商家ID不能为空'
            ]);
        }
        
        // 检查商家是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE id = ?");
        $stmt->execute([$merchantId]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '商家不存在'
            ]);
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_incomes WHERE merchant_id = ?");
        $stmt->execute([$merchantId]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT mi.*, ma.api_id, a.name as api_name, a.method, a.url 
                                   FROM merchant_incomes mi 
                                   LEFT JOIN merchant_apis ma ON mi.merchant_api_id = ma.id 
                                   LEFT JOIN apis a ON ma.api_id = a.id 
                                   WHERE mi.merchant_id = ? 
                                   ORDER BY mi.create_time DESC 
                                   LIMIT $offset, $limit");
        $stmt->execute([$merchantId]);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 获取商家提现列表
     */
    public function getMerchantWithdrawList() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        
        // 构建查询条件
        $where = "1=1";
        $params = [];
        
        if ($merchantId > 0) {
            $where .= " AND w.merchant_id = ?";
            $params[] = $merchantId;
        }
        
        if ($status !== '') {
            $where .= " AND w.status = ?";
            $params[] = $status;
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_withdraws w WHERE $where");
        $stmt->execute($params);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT w.*, m.name as merchant_name 
                                   FROM merchant_withdraws w 
                                   LEFT JOIN merchants m ON w.merchant_id = m.id 
                                   WHERE $where 
                                   ORDER BY w.create_time DESC 
                                   LIMIT $offset, $limit");
        $stmt->execute($params);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 审核商家提现
     */
    public function auditWithdraw() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : 0;
        $remark = isset($_POST['remark']) ? trim($_POST['remark']) : '';
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        if ($status != 1 && $status != 2) {
            return json([
                'code' => 1,
                'msg' => '状态参数错误'
            ]);
        }
        
        // 检查提现申请是否存在
        $stmt = $this->db->prepare("SELECT * FROM merchant_withdraws WHERE id = ? AND status = 0");
        $stmt->execute([$id]);
        $withdraw = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$withdraw) {
            return json([
                'code' => 1,
                'msg' => '提现申请不存在或已审核'
            ]);
        }
        
        // 更新提现状态
        $stmt = $this->db->prepare("UPDATE merchant_withdraws SET status = ?, audit_time = ?, audit_remark = ? WHERE id = ?");
        $stmt->execute([
            $status,
            time(),
            $remark,
            $id
        ]);
        
        // 如果拒绝提现，退回余额
        if ($status == 2) {
            $stmt = $this->db->prepare("UPDATE merchants SET balance = balance + ? WHERE id = ?");
            $stmt->execute([
                $withdraw['amount'],
                $withdraw['merchant_id']
            ]);
        }
        
        // 获取商家信息
        $stmt = $this->db->prepare("SELECT m.*, u.email FROM merchants m LEFT JOIN users u ON m.user_id = u.id WHERE m.id = ?");
        $stmt->execute([$withdraw['merchant_id']]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 发送通知邮件
        if ($merchant && !empty($merchant['email'])) {
            $subject = '提现申请审核结果通知';
            $message = "尊敬的商家：\n\n";
            $message .= "您的提现申请（金额：¥{$withdraw['amount']}）已审核完成，结果如下：\n\n";
            $message .= "审核结果：" . ($status == 1 ? '通过' : '拒绝') . "\n";
            
            if (!empty($remark)) {
                $message .= "审核备注：{$remark}\n\n";
            }
            
            if ($status == 1) {
                $message .= "您的提现申请已通过，我们将尽快处理您的提现请求。\n\n";
            } else {
                $message .= "您的提现申请已被拒绝，提现金额已退回到您的账户余额中。\n\n";
            }
            
            $message .= "感谢您的支持！\n\n";
            $message .= "此邮件由系统自动发送，请勿回复。";
            
            // 发送邮件
            sendMail($merchant['email'], $subject, $message);
        }
        
        return json([
            'code' => 0,
            'msg' => $status == 1 ? '已通过提现申请' : '已拒绝提现申请'
        ]);
    }
}
