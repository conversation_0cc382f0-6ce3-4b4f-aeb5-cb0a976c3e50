<?php
/**
 * 权限管理类
 * API管理系统 - 权限验证与管理
 */

class Permission {
    private $db;
    private static $permissions = [
        // 系统管理
        'system.view' => '系统查看',
        'system.config' => '系统配置',
        'system.backup' => '系统备份',
        
        // 用户管理
        'user.view' => '用户查看',
        'user.create' => '用户创建',
        'user.edit' => '用户编辑',
        'user.delete' => '用户删除',
        'user.recharge' => '用户充值',
        
        // 管理员管理
        'admin.view' => '管理员查看',
        'admin.create' => '管理员创建',
        'admin.edit' => '管理员编辑',
        'admin.delete' => '管理员删除',
        
        // 角色管理
        'role.view' => '角色查看',
        'role.create' => '角色创建',
        'role.edit' => '角色编辑',
        'role.delete' => '角色删除',
        
        // API管理
        'api.view' => 'API查看',
        'api.create' => 'API创建',
        'api.edit' => 'API编辑',
        'api.delete' => 'API删除',
        'api.debug' => 'API调试',
        'api.stats' => 'API统计',
        
        // 商家管理
        'merchant.view' => '商家查看',
        'merchant.create' => '商家创建',
        'merchant.edit' => '商家编辑',
        'merchant.delete' => '商家删除',
        'merchant.audit' => '商家审核',
        
        // 订单管理
        'order.view' => '订单查看',
        'order.refund' => '订单退款',
        
        // 财务管理
        'finance.view' => '财务查看',
        'finance.withdraw' => '提现管理',
        'finance.stats' => '财务统计',
        
        // 日志管理
        'log.view' => '日志查看',
        'log.delete' => '日志删除',
        
        // 配置管理
        'config.view' => '配置查看',
        'config.edit' => '配置编辑',
    ];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 检查管理员权限
     */
    public function checkAdminPermission($adminId, $permission) {
        // 获取管理员角色
        $sql = "SELECT r.permissions FROM {$this->db->getPrefix()}admins a 
                LEFT JOIN {$this->db->getPrefix()}roles r ON a.role_id = r.id 
                WHERE a.id = :admin_id AND a.status = 1 AND r.status = 1";
        
        $result = $this->db->fetchOne($sql, ['admin_id' => $adminId]);
        
        if (!$result) {
            return false;
        }
        
        $permissions = json_decode($result['permissions'], true);
        
        // 超级管理员拥有所有权限
        if (in_array('*', $permissions)) {
            return true;
        }
        
        // 检查具体权限
        return in_array($permission, $permissions);
    }
    
    /**
     * 获取管理员所有权限
     */
    public function getAdminPermissions($adminId) {
        $sql = "SELECT r.permissions FROM {$this->db->getPrefix()}admins a 
                LEFT JOIN {$this->db->getPrefix()}roles r ON a.role_id = r.id 
                WHERE a.id = :admin_id AND a.status = 1 AND r.status = 1";
        
        $result = $this->db->fetchOne($sql, ['admin_id' => $adminId]);
        
        if (!$result) {
            return [];
        }
        
        $permissions = json_decode($result['permissions'], true);
        
        // 超级管理员返回所有权限
        if (in_array('*', $permissions)) {
            return array_keys(self::$permissions);
        }
        
        return $permissions;
    }
    
    /**
     * 权限中间件
     */
    public function requirePermission($permission) {
        if (!isset($_SESSION['admin_id'])) {
            System::jsonResponse(null, 401, '请先登录');
        }
        
        if (!$this->checkAdminPermission($_SESSION['admin_id'], $permission)) {
            System::jsonResponse(null, 403, '权限不足');
        }
        
        return true;
    }
    
    /**
     * 获取所有可用权限
     */
    public static function getAllPermissions() {
        return self::$permissions;
    }
    
    /**
     * 获取权限分组
     */
    public static function getPermissionGroups() {
        $groups = [];
        
        foreach (self::$permissions as $key => $name) {
            $parts = explode('.', $key);
            $group = $parts[0];
            
            if (!isset($groups[$group])) {
                $groups[$group] = [
                    'name' => self::getGroupName($group),
                    'permissions' => []
                ];
            }
            
            $groups[$group]['permissions'][$key] = $name;
        }
        
        return $groups;
    }
    
    /**
     * 获取分组名称
     */
    private static function getGroupName($group) {
        $groupNames = [
            'system' => '系统管理',
            'user' => '用户管理',
            'admin' => '管理员管理',
            'role' => '角色管理',
            'api' => 'API管理',
            'merchant' => '商家管理',
            'order' => '订单管理',
            'finance' => '财务管理',
            'log' => '日志管理',
            'config' => '配置管理'
        ];
        
        return $groupNames[$group] ?? $group;
    }
    
    /**
     * 验证权限格式
     */
    public static function validatePermissions($permissions) {
        if (!is_array($permissions)) {
            return false;
        }
        
        foreach ($permissions as $permission) {
            if ($permission === '*') {
                continue;
            }
            
            if (!isset(self::$permissions[$permission])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 记录权限操作日志
     */
    public function logPermissionAction($adminId, $action, $target, $details = '') {
        $sql = "INSERT INTO {$this->db->getPrefix()}permission_logs 
                (admin_id, action, target, details, ip, created_at) 
                VALUES (:admin_id, :action, :target, :details, :ip, :created_at)";
        
        $this->db->query($sql, [
            'admin_id' => $adminId,
            'action' => $action,
            'target' => $target,
            'details' => $details,
            'ip' => System::getClientIp(),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 检查IP白名单
     */
    public function checkIpWhitelist($ip = null) {
        if (!$ip) {
            $ip = System::getClientIp();
        }
        
        // 获取IP白名单配置
        $sql = "SELECT value FROM {$this->db->getPrefix()}configs WHERE `key` = 'ip_whitelist'";
        $result = $this->db->fetchOne($sql);
        
        if (!$result) {
            return true; // 未配置白名单，允许所有IP
        }
        
        $whitelist = json_decode($result['value'], true);
        if (empty($whitelist)) {
            return true;
        }
        
        // 检查IP是否在白名单中
        foreach ($whitelist as $allowedIp) {
            if ($this->matchIp($ip, $allowedIp)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查IP黑名单
     */
    public function checkIpBlacklist($ip = null) {
        if (!$ip) {
            $ip = System::getClientIp();
        }
        
        // 获取IP黑名单配置
        $sql = "SELECT value FROM {$this->db->getPrefix()}configs WHERE `key` = 'ip_blacklist'";
        $result = $this->db->fetchOne($sql);
        
        if (!$result) {
            return true; // 未配置黑名单，允许所有IP
        }
        
        $blacklist = json_decode($result['value'], true);
        if (empty($blacklist)) {
            return true;
        }
        
        // 检查IP是否在黑名单中
        foreach ($blacklist as $blockedIp) {
            if ($this->matchIp($ip, $blockedIp)) {
                return false; // 在黑名单中，拒绝访问
            }
        }
        
        return true;
    }
    
    /**
     * IP匹配检查（支持CIDR格式）
     */
    private function matchIp($ip, $pattern) {
        if ($ip === $pattern) {
            return true;
        }
        
        // 检查CIDR格式
        if (strpos($pattern, '/') !== false) {
            list($subnet, $mask) = explode('/', $pattern);
            $ipLong = ip2long($ip);
            $subnetLong = ip2long($subnet);
            $maskLong = -1 << (32 - $mask);
            
            return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
        }
        
        // 检查通配符
        if (strpos($pattern, '*') !== false) {
            $pattern = str_replace('*', '.*', $pattern);
            return preg_match('/^' . $pattern . '$/', $ip);
        }
        
        return false;
    }
    
    /**
     * 速率限制检查
     */
    public function checkRateLimit($key, $limit, $window = 60) {
        $cacheKey = "rate_limit_{$key}";
        
        // 简单的基于文件的缓存实现
        $cacheFile = __DIR__ . "/../logs/rate_limit_{$key}.cache";
        $now = time();
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            
            // 清理过期记录
            $data = array_filter($data, function($timestamp) use ($now, $window) {
                return ($now - $timestamp) < $window;
            });
            
            if (count($data) >= $limit) {
                return false; // 超过限制
            }
            
            $data[] = $now;
        } else {
            $data = [$now];
        }
        
        file_put_contents($cacheFile, json_encode($data));
        return true;
    }
}