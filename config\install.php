<?php
/**
 * 数据库安装脚本
 * API管理系统 - 数据库初始化安装
 */

// 引入系统核心文件
require_once __DIR__ . '/../core/System.php';

class DatabaseInstaller {
    private $config;
    private $pdo;
    
    public function __construct() {
        $this->config = require_once __DIR__ . '/database.php';
    }
    
    /**
     * 执行安装
     */
    public function install() {
        try {
            echo "开始安装API管理系统数据库...\n";
            
            // 连接MySQL服务器（不指定数据库）
            $this->connectServer();
            
            // 读取SQL文件
            $sqlFile = __DIR__ . '/database.sql';
            if (!file_exists($sqlFile)) {
                throw new Exception("SQL文件不存在: {$sqlFile}");
            }
            
            $sql = file_get_contents($sqlFile);
            if (empty($sql)) {
                throw new Exception("SQL文件内容为空");
            }
            
            // 执行SQL语句
            $this->executeSql($sql);
            
            echo "数据库安装完成！\n";
            echo "默认管理员账号: admin\n";
            echo "默认管理员密码: 123456\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "安装失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 连接MySQL服务器
     */
    private function connectServer() {
        $db = $this->config['database'];
        $dsn = "mysql:host={$db['host']};port={$db['port']};charset={$db['charset']}";
        
        try {
            $this->pdo = new PDO($dsn, $db['username'], $db['password'], $db['options']);
            echo "MySQL服务器连接成功\n";
        } catch (PDOException $e) {
            throw new Exception("MySQL服务器连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 执行SQL语句
     */
    private function executeSql($sql) {
        // 分割SQL语句
        $statements = $this->splitSql($sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || substr($statement, 0, 2) === '--') {
                continue;
            }
            
            try {
                $this->pdo->exec($statement);
                
                // 输出执行进度
                if (stripos($statement, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?`([^`]+)`/i', $statement, $matches);
                    if (isset($matches[1])) {
                        echo "创建表: {$matches[1]}\n";
                    }
                } elseif (stripos($statement, 'INSERT INTO') !== false) {
                    preg_match('/INSERT INTO.*?`([^`]+)`/i', $statement, $matches);
                    if (isset($matches[1])) {
                        echo "插入数据: {$matches[1]}\n";
                    }
                } elseif (stripos($statement, 'CREATE DATABASE') !== false) {
                    echo "创建数据库: api_system\n";
                }
                
            } catch (PDOException $e) {
                // 忽略数据库已存在的错误
                if (strpos($e->getMessage(), 'database exists') === false) {
                    throw new Exception("SQL执行失败: " . $e->getMessage() . "\nSQL: " . substr($statement, 0, 100));
                }
            }
        }
    }
    
    /**
     * 分割SQL语句
     */
    private function splitSql($sql) {
        // 移除注释
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // 按分号分割
        $statements = explode(';', $sql);
        
        return array_filter($statements, function($statement) {
            return !empty(trim($statement));
        });
    }
    
    /**
     * 检查安装状态
     */
    public function checkInstallation() {
        try {
            $db = Database::getInstance();
            $result = $db->fetchOne("SELECT COUNT(*) as count FROM api_admins");
            return $result['count'] > 0;
        } catch (Exception $e) {
            return false;
        }
    }
}

// 命令行执行安装
if (php_sapi_name() === 'cli') {
    $installer = new DatabaseInstaller();
    $installer->install();
} else {
    // Web界面安装
    if (isset($_POST['install'])) {
        $installer = new DatabaseInstaller();
        $result = $installer->install();
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => '安装成功']);
        } else {
            echo json_encode(['success' => false, 'message' => '安装失败']);
        }
    } else {
        // 显示安装界面
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>API管理系统 - 数据库安装</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 50px; }
                .container { max-width: 600px; margin: 0 auto; }
                .btn { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
                .btn:hover { background: #005a87; }
                .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
                .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
                .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>API管理系统 - 数据库安装</h1>
                <p>点击下面的按钮开始安装数据库</p>
                <button class="btn" onclick="install()">开始安装</button>
                <div id="result"></div>
            </div>
            
            <script>
                function install() {
                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '', true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            var result = JSON.parse(xhr.responseText);
                            var resultDiv = document.getElementById('result');
                            if (result.success) {
                                resultDiv.innerHTML = '<div class="result success">' + result.message + '</div>';
                            } else {
                                resultDiv.innerHTML = '<div class="result error">' + result.message + '</div>';
                            }
                        }
                    };
                    xhr.send('install=1');
                }
            </script>
        </body>
        </html>
        <?php
    }
}
?>