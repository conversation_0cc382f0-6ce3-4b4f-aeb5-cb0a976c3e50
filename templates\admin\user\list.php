<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>用户管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .layui-card-body {
            padding: 15px;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-table-tool-temp {
            padding-right: 0;
        }
        .layui-table-cell {
            height: auto;
            line-height: 28px;
            padding: 6px 15px;
            position: relative;
            box-sizing: border-box;
        }
        .user-status-normal {
            color: #5FB878;
        }
        .user-status-disabled {
            color: #FF5722;
        }
        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
        }
        .user-vip {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            color: #fff;
            font-size: 12px;
            background-color: #FFB800;
        }
        .user-vip.vip0 {
            background-color: #999;
        }
        .user-vip.vip1 {
            background-color: #01AAED;
        }
        .user-vip.vip2 {
            background-color: #1E9FFF;
        }
        .user-vip.vip3 {
            background-color: #5FB878;
        }
        .user-vip.vip4 {
            background-color: #FFB800;
        }
        .user-vip.vip5 {
            background-color: #FF5722;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">用户管理</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <input type="text" name="keyword" placeholder="用户名/邮箱/手机号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="1">正常</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <select name="vip_level">
                                <option value="">全部会员等级</option>
                                <option value="0">普通用户</option>
                                <?php foreach ($vipLevels as $level): ?>
                                <option value="<?php echo $level['id']; ?>"><?php echo $level['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="searchBtn" lay-submit>
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button class="layui-btn icon-btn" id="addBtn">
                            <i class="layui-icon layui-icon-add-1"></i>添加
                        </button>
                    </div>
                </div>
            </div>
            
            <table id="userTable" lay-filter="userTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="apikeys">API密钥</a>
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="recharge">充值</a>
    {{# if(d.status == 1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable">禁用</a>
    {{# } else { }}
    <a class="layui-btn layui-btn-xs layui-btn-success" lay-event="enable">启用</a>
    {{# } }}
</script>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status == 1){ }}
    <span class="user-status-normal">正常</span>
    {{# } else { }}
    <span class="user-status-disabled">禁用</span>
    {{# } }}
</script>

<!-- 表格会员等级列 -->
<script type="text/html" id="vipLevelTpl">
    {{# if(d.vip_level == 0){ }}
    <span class="user-vip vip0">普通用户</span>
    {{# } else if(d.vip_level == 1){ }}
    <span class="user-vip vip1">{{d.vip_name}}</span>
    {{# } else if(d.vip_level == 2){ }}
    <span class="user-vip vip2">{{d.vip_name}}</span>
    {{# } else if(d.vip_level == 3){ }}
    <span class="user-vip vip3">{{d.vip_name}}</span>
    {{# } else if(d.vip_level == 4){ }}
    <span class="user-vip vip4">{{d.vip_name}}</span>
    {{# } else if(d.vip_level == 5){ }}
    <span class="user-vip vip5">{{d.vip_name}}</span>
    {{# } }}
</script>

<!-- 表格头像列 -->
<script type="text/html" id="avatarTpl">
    <img src="{{d.avatar || '/assets/images/default-avatar.png'}}" class="user-avatar" alt="头像">
</script>

<!-- 添加/编辑用户弹窗 -->
<script type="text/html" id="userForm">
    <form class="layui-form" lay-filter="userForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        
        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <input type="text" name="username" placeholder="请输入用户名" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-block">
                <input type="text" name="email" placeholder="请输入邮箱" lay-verify="email" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
                <input type="text" name="phone" placeholder="请输入手机号" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <input type="password" name="password" placeholder="请输入密码，不修改请留空" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">会员等级</label>
            <div class="layui-input-block">
                <select name="vip_level">
                    <option value="0">普通用户</option>
                    <?php foreach ($vipLevels as $level): ?>
                    <option value="<?php echo $level['id']; ?>"><?php echo $level['name']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">余额</label>
            <div class="layui-input-block">
                <input type="number" name="balance" placeholder="请输入余额" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="正常" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="userSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- 充值弹窗 -->
<script type="text/html" id="rechargeForm">
    <form class="layui-form" lay-filter="rechargeForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="user_id">
        
        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <input type="text" name="username" readonly class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">当前余额</label>
            <div class="layui-input-block">
                <input type="text" name="current_balance" readonly class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">充值金额</label>
            <div class="layui-input-block">
                <input type="number" name="amount" placeholder="请输入充值金额" lay-verify="required|number" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="rechargeSubmit" lay-submit>确认充值</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- API密钥管理弹窗 -->
<script type="text/html" id="apiKeysForm">
    <div style="padding: 20px 30px 0 0;">
        <div class="layui-form toolbar" style="margin-bottom: 15px;">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <input type="text" name="keyword" placeholder="密钥名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn icon-btn" lay-filter="searchApiKeyBtn" lay-submit>
                        <i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button class="layui-btn icon-btn" id="addApiKeyBtn">
                        <i class="layui-icon layui-icon-add-1"></i>添加
                    </button>
                </div>
            </div>
        </div>
        
        <table id="apiKeyTable" lay-filter="apiKeyTable"></table>
    </div>
</script>

<!-- API密钥表格操作列 -->
<script type="text/html" id="apiKeyTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">查看密钥</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
</script>

<!-- 添加/编辑API密钥弹窗 -->
<script type="text/html" id="apiKeyEditForm">
    <form class="layui-form" lay-filter="apiKeyEditForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <input type="hidden" name="user_id">
        
        <div class="layui-form-item">
            <label class="layui-form-label">密钥名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入密钥名称" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">IP白名单</label>
            <div class="layui-input-block">
                <textarea name="ip_whitelist" placeholder="请输入IP白名单，多个IP用英文逗号分隔，留空表示不限制" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">过期时间</label>
            <div class="layui-input-block">
                <input type="text" name="expire_time" id="expireTime" placeholder="请选择过期时间，留空表示永不过期" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="apiKeySubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'laydate'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var laydate = layui.laydate;
    
    // 渲染用户表格
    var userTable = table.render({
        elem: '#userTable',
        url: '/admin/user/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'avatar', title: '头像', templet: '#avatarTpl', width: 80, align: 'center'},
            {field: 'username', title: '用户名', sort: true},
            {field: 'email', title: '邮箱'},
            {field: 'phone', title: '手机号'},
            {field: 'vip_level', title: '会员等级', templet: '#vipLevelTpl', sort: true},
            {field: 'balance', title: '余额', sort: true},
            {field: 'api_count', title: 'API数量', sort: true},
            {field: 'register_time', title: '注册时间', sort: true, templet: function(d) {
                return util.toDateString(d.register_time * 1000);
            }},
            {field: 'last_login_time', title: '最后登录', sort: true, templet: function(d) {
                return d.last_login_time ? util.toDateString(d.last_login_time * 1000) : '从未登录';
            }},
            {field: 'status', title: '状态', templet: '#statusTpl', sort: true},
            {title: '操作', toolbar: '#tableBar', width: 250, align: 'center'}
        ]]
    });
    
    // 搜索按钮点击事件
    form.on('submit(searchBtn)', function(data) {
        userTable.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 添加按钮点击事件
    $('#addBtn').click(function() {
        showUserForm();
    });
    
    // 监听工具条点击事件
    table.on('tool(userTable)', function(obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showUserForm(data);
        } else if (layEvent === 'disable') { // 禁用
            layer.confirm('确定要禁用该用户吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(index) {
                layer.close(index);
                var loadIndex = layer.load(2);
                
                $.post('/admin/user/disable', {
                    id: data.id
                }, function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        userTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        } else if (layEvent === 'enable') { // 启用
            layer.confirm('确定要启用该用户吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(index) {
                layer.close(index);
                var loadIndex = layer.load(2);
                
                $.post('/admin/user/enable', {
                    id: data.id
                }, function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        userTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        } else if (layEvent === 'recharge') { // 充值
            showRechargeForm(data);
        } else if (layEvent === 'apikeys') { // API密钥管理
            showApiKeysForm(data);
        }
    });
    
    // 显示用户表单弹窗
    function showUserForm(data) {
        admin.open({
            type: 1,
            title: (data ? '修改' : '添加') + '用户',
            content: $('#userForm').html(),
            area: ['500px', '550px'],
            success: function(layero, dIndex) {
                // 回显表单数据
                if (data) {
                    form.val('userForm', data);
                }
                
                // 表单提交事件
                form.on('submit(userSubmit)', function(data) {
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/user/' + (data.field.id ? 'update' : 'add'), data.field, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            userTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    }
    
    // 显示充值表单弹窗
    function showRechargeForm(data) {
        admin.open({
            type: 1,
            title: '用户充值',
            content: $('#rechargeForm').html(),
            area: ['500px', '400px'],
            success: function(layero, dIndex) {
                // 回显表单数据
                form.val('rechargeForm', {
                    user_id: data.id,
                    username: data.username,
                    current_balance: data.balance
                });
                
                // 表单提交事件
                form.on('submit(rechargeSubmit)', function(data) {
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/user/recharge', data.field, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            userTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    }
    
    // 显示API密钥管理弹窗
    function showApiKeysForm(userData) {
        var apiKeyTable = null;
        
        admin.open({
            type: 1,
            title: userData.username + ' - API密钥管理',
            content: $('#apiKeysForm').html(),
            area: ['800px', '500px'],
            success: function(layero, dIndex) {
                // 渲染API密钥表格
                apiKeyTable = table.render({
                    elem: '#apiKeyTable',
                    url: '/admin/user/api_keys?user_id=' + userData.id,
                    page: true,
                    toolbar: true,
                    cellMinWidth: 100,
                    cols: [[
                        {type: 'numbers'},
                        {field: 'name', title: '密钥名称'},
                        {field: 'create_time', title: '创建时间', templet: function(d) {
                            return util.toDateString(d.create_time * 1000);
                        }},
                        {field: 'expire_time', title: '过期时间', templet: function(d) {
                            return d.expire_time ? util.toDateString(d.expire_time * 1000) : '永不过期';
                        }},
                        {field: 'ip_whitelist', title: 'IP白名单', templet: function(d) {
                            return d.ip_whitelist || '不限制';
                        }},
                        {field: 'status', title: '状态', templet: function(d) {
                            return d.status == 1 ? '<span class="user-status-normal">启用</span>' : '<span class="user-status-disabled">禁用</span>';
                        }},
                        {title: '操作', toolbar: '#apiKeyTableBar', width: 200, align: 'center'}
                    ]]
                });
                
                // 搜索按钮点击事件
                form.on('submit(searchApiKeyBtn)', function(data) {
                    apiKeyTable.reload({where: data.field, page: {curr: 1}});
                    return false;
                });
                
                // 添加API密钥按钮点击事件
                $('#addApiKeyBtn').click(function() {
                    showApiKeyEditForm(null, userData.id);
                });
                
                // 监听API密钥表格工具条
                table.on('tool(apiKeyTable)', function(obj) {
                    var data = obj.data;
                    var layEvent = obj.event;
                    
                    if (layEvent === 'edit') { // 修改
                        showApiKeyEditForm(data, userData.id);
                    } else if (layEvent === 'del') { // 删除
                        layer.confirm('确定要删除该API密钥吗？', {
                            skin: 'layui-layer-admin',
                            shade: .1
                        }, function(index) {
                            layer.close(index);
                            var loadIndex = layer.load(2);
                            
                            $.post('/admin/user/api_key_delete', {
                                id: data.id
                            }, function(res) {
                                layer.close(loadIndex);
                                if (res.code === 0) {
                                    layer.msg(res.msg, {icon: 1});
                                    apiKeyTable.reload();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }, 'json');
                        });
                    } else if (layEvent === 'view') { // 查看密钥
                        layer.prompt({
                            title: '查看API密钥',
                            value: data.key,
                            formType: 0,
                            btn: ['复制', '关闭'],
                            btnAlign: 'c',
                            shade: 0.3,
                            yes: function(index) {
                                // 复制到剪贴板
                                var copyInput = document.querySelector('.layui-layer-input');
                                copyInput.select();
                                document.execCommand('copy');
                                layer.msg('复制成功', {icon: 1});
                                return false; // 不关闭弹窗
                            }
                        });
                    }
                });
            }
        });
    }
    
    // 显示API密钥编辑弹窗
    function showApiKeyEditForm(data, userId) {
        admin.open({
            type: 1,
            title: (data ? '修改' : '添加') + 'API密钥',
            content: $('#apiKeyEditForm').html(),
            area: ['500px', '450px'],
            success: function(layero, dIndex) {
                // 初始化日期选择器
                laydate.render({
                    elem: '#expireTime',
                    type: 'datetime'
                });
                
                // 回显表单数据
                if (data) {
                    form.val('apiKeyEditForm', data);
                } else {
                    form.val('apiKeyEditForm', {
                        user_id: userId
                    });
                }
                
                // 表单提交事件
                form.on('submit(apiKeySubmit)', function(data) {
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/user/api_key_' + (data.field.id ? 'update' : 'add'), data.field, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            table.reload('apiKeyTable');
                            
                            // 如果是添加操作，显示生成的密钥
                            if (!data.field.id && res.data && res.data.key) {
                                layer.prompt({
                                    title: '新API密钥已生成',
                                    value: res.data.key,
                                    formType: 0,
                                    btn: ['复制', '关闭'],
                                    btnAlign: 'c',
                                    shade: 0.3,
                                    yes: function(index) {
                                        // 复制到剪贴板
                                        var copyInput = document.querySelector('.layui-layer-input');
                                        copyInput.select();
                                        document.execCommand('copy');
                                        layer.msg('复制成功', {icon: 1});
                                        return false; // 不关闭弹窗
                                    }
                                });
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    }
});
</script>
</body>
</html>