<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>引导插件</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=315"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        #LAY_DEMO_INTROJS * {
            font-family: "Raleway", "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }

        #LAY_DEMO_INTROJS h1 {
            font-size: 48px;
            height: 100px;
            line-height: 100px;
            color: #333333;
            font-weight: 300;
            display: inline-block;
            min-width: 300px;
        }

        #LAY_DEMO_INTROJS .sub-title {
            font-size: 22px;
            color: #555555;
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid" id="LAY_DEMO_INTROJS">
    <div class="layui-card">
        <div class="layui-card-body text-center">
            <div>
                <h1 data-step="1" data-intro="Hello world! I'm Intro.js" data-hint="Get it, use it."
                    data-hintPosition="top-middle" data-position="bottom">Intro.js</h1>
            </div>
            <div>
                <p class="sub-title layui-text" data-step="2"
                   data-intro="This is a simple step-by-step guide made using Intro.js">
                    Step-by-step guide and feature introduction
                </p>
            </div>
            <div class="layui-btn-container">
                <button id="demoIntroBtnStart" class="layui-btn">
                    开始指引
                </button>
                <button id="demoIntroBtnStart2" class="layui-btn" data-step="5" data-intro="Get it, use it.">开启进度条
                </button>
                <button id="demoIntroBtnAdd" class="layui-btn">添加标注点</button>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-body" style="padding: 50px;">
            <div class="layui-row layui-col-space30" data-step="3" data-intro="Ok, wasn't that fun?">
                <div class="layui-col-sm6" data-hint="This is a tooltip!" data-hintPosition="top-right"
                     data-position="left">
                    <h2>Section One</h2>
                    <p class="layui-text">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis mollis augue a neque cursus ac
                        blandit orci faucibus. Phasellus nec metus purus.
                    </p>
                </div>
                <div class="layui-col-sm6" data-step="4" data-intro="More features, more fun.">
                    <h2>Section Two</h2>
                    <p class="layui-text">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis mollis augue a neque cursus ac
                        blandit orci faucibus. Phasellus nec metus purus.
                    </p>
                </div>
                <div class="layui-col-sm6">
                    <h2>Section Three</h2>
                    <p class="layui-text">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis mollis augue a neque cursus ac
                        blandit orci faucibus. Phasellus nec metus purus.
                    </p>
                </div>
                <div class="layui-col-sm6">
                    <h2>Section Four</h2>
                    <p class="layui-text">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis mollis augue a neque cursus ac
                        blandit orci faucibus. Phasellus nec metus purus.
                    </p>
                </div>
                <div class="layui-col-sm6">
                    <h2>Section Five</h2>
                    <p class="layui-text">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis mollis augue a neque cursus ac
                        blandit orci faucibus. Phasellus nec metus purus.
                    </p>
                </div>
                <div class="layui-col-sm6" data-hint="This is a tooltip!" data-hintPosition="top"
                     data-position="top">
                    <h2>Section Six</h2>
                    <p class="layui-text">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis mollis augue a neque cursus ac
                        blandit orci faucibus. Phasellus nec metus purus.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=315"></script>
<script>
    layui.use(['layer', 'introJs'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var introJs = layui.introJs;

        // 开始指引
        $('#demoIntroBtnStart').click(function () {
            introJs().start();
        });

        // 带进度条
        $('#demoIntroBtnStart2').click(function () {
            introJs().setOption('showProgress', true).start();
        });

        // 添加标注点
        $('#demoIntroBtnAdd').click(function () {
            introJs().addHints();
        });

    });
</script>
</body>
</html>