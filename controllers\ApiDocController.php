<?php
/**
 * API文档控制器
 */
class ApiDocController {
    private $db;
    
    public function __construct() {
        global $db;
        $this->db = $db;
    }
    
    /**
     * 获取API文档列表
     */
    public function getList() {
        // 查询API分类
        $stmt = $this->db->prepare("SELECT id, name, description FROM api_categories ORDER BY sort ASC");
        $stmt->execute();
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [
            'categories' => []
        ];
        
        // 查询每个分类下的API
        foreach ($categories as $category) {
            $stmt = $this->db->prepare("SELECT id, name, method, url, description FROM apis WHERE category_id = ? AND status = 1 ORDER BY sort ASC");
            $stmt->execute([$category['id']]);
            $apis = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $result['categories'][] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'description' => $category['description'],
                'apis' => $apis
            ];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $result
        ]);
    }
    
    /**
     * 获取API文档详情
     */
    public function getDetail() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'API ID不能为空'
            ]);
        }
        
        // 查询API信息
        $stmt = $this->db->prepare("SELECT a.*, c.name as category_name 
                                   FROM apis a 
                                   LEFT JOIN api_categories c ON a.category_id = c.id 
                                   WHERE a.id = ? AND a.status = 1");
        $stmt->execute([$id]);
        $api = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$api) {
            return json([
                'code' => 1,
                'msg' => 'API不存在或已被禁用'
            ]);
        }
        
        // 查询API参数
        $stmt = $this->db->prepare("SELECT id, name, type, required, description, default_value 
                                   FROM api_params 
                                   WHERE api_id = ? 
                                   ORDER BY sort ASC");
        $stmt->execute([$id]);
        $params = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $api['params'] = $params;
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $api
        ]);
    }
    
    /**
     * 生成API文档
     */
    public function generateDoc() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $apiId = isset($_POST['api_id']) ? intval($_POST['api_id']) : 0;
        
        if ($apiId <= 0) {
            return json([
                'code' => 1,
                'msg' => 'API ID不能为空'
            ]);
        }
        
        // 查询API信息
        $stmt = $this->db->prepare("SELECT * FROM apis WHERE id = ?");
        $stmt->execute([$apiId]);
        $api = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$api) {
            return json([
                'code' => 1,
                'msg' => 'API不存在'
            ]);
        }
        
        // 查询API参数
        $stmt = $this->db->prepare("SELECT * FROM api_params WHERE api_id = ? ORDER BY sort ASC");
        $stmt->execute([$apiId]);
        $params = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 生成示例参数
        $exampleParams = [];
        foreach ($params as $param) {
            $exampleParams[$param['name']] = $this->generateExampleValue($param['type'], $param['default_value']);
        }
        
        // 生成示例响应
        $exampleResponse = [
            'code' => 0,
            'msg' => '请求成功',
            'data' => $this->generateExampleResponseData($params)
        ];
        
        // 更新API示例
        $stmt = $this->db->prepare("UPDATE apis SET example_params = ?, example_response = ? WHERE id = ?");
        $stmt->execute([
            json_encode($exampleParams),
            json_encode($exampleResponse),
            $apiId
        ]);
        
        return json([
            'code' => 0,
            'msg' => '文档生成成功',
            'data' => [
                'example_params' => $exampleParams,
                'example_response' => $exampleResponse
            ]
        ]);
    }
    
    /**
     * 生成示例值
     */
    private function generateExampleValue($type, $defaultValue = null) {
        if ($defaultValue !== null) {
            return $defaultValue;
        }
        
        switch ($type) {
            case 'int':
            case 'integer':
                return 1;
            case 'float':
            case 'double':
                return 1.0;
            case 'string':
                return '示例文本';
            case 'boolean':
            case 'bool':
                return true;
            case 'array':
                return [];
            case 'object':
                return new stdClass();
            default:
                return null;
        }
    }
    
    /**
     * 生成示例响应数据
     */
    private function generateExampleResponseData($params) {
        $data = [];
        
        // 根据参数生成响应数据
        foreach ($params as $param) {
            if (strpos($param['name'], 'id') !== false) {
                $data['id'] = 1;
            } elseif (strpos($param['name'], 'name') !== false) {
                $data['name'] = '示例名称';
            } elseif (strpos($param['name'], 'title') !== false) {
                $data['title'] = '示例标题';
            } elseif (strpos($param['name'], 'content') !== false) {
                $data['content'] = '示例内容';
            } elseif (strpos($param['name'], 'time') !== false || strpos($param['name'], 'date') !== false) {
                $data['time'] = time();
            } elseif (strpos($param['name'], 'list') !== false) {
                $data['list'] = [
                    ['id' => 1, 'name' => '示例项目1'],
                    ['id' => 2, 'name' => '示例项目2']
                ];
            } elseif (strpos($param['name'], 'count') !== false || strpos($param['name'], 'total') !== false) {
                $data['count'] = 100;
            }
        }
        
        // 如果没有生成任何数据，添加一些默认数据
        if (empty($data)) {
            $data = [
                'id' => 1,
                'name' => '示例数据',
                'create_time' => time()
            ];
        }
        
        return $data;
    }
    
    /**
     * 导出API文档
     */
    public function exportDoc() {
        // 检查管理员登录状态
        checkAdminLogin();
        
        $format = isset($_GET['format']) ? trim($_GET['format']) : 'html';
        $categoryId = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        
        // 查询API分类
        if ($categoryId > 0) {
            $stmt = $this->db->prepare("SELECT id, name, description FROM api_categories WHERE id = ?");
            $stmt->execute([$categoryId]);
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $stmt = $this->db->prepare("SELECT id, name, description FROM api_categories ORDER BY sort ASC");
            $stmt->execute();
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        $result = [
            'categories' => []
        ];
        
        // 查询每个分类下的API
        foreach ($categories as $category) {
            $stmt = $this->db->prepare("SELECT id, name, method, url, description, example_params, example_response 
                                       FROM apis 
                                       WHERE category_id = ? AND status = 1 
                                       ORDER BY sort ASC");
            $stmt->execute([$category['id']]);
            $apis = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 查询每个API的参数
            foreach ($apis as &$api) {
                $stmt = $this->db->prepare("SELECT id, name, type, required, description, default_value 
                                           FROM api_params 
                                           WHERE api_id = ? 
                                           ORDER BY sort ASC");
                $stmt->execute([$api['id']]);
                $params = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $api['params'] = $params;
                
                // 解析JSON字段
                $api['example_params'] = json_decode($api['example_params'], true);
                $api['example_response'] = json_decode($api['example_response'], true);
            }
            
            $result['categories'][] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'description' => $category['description'],
                'apis' => $apis
            ];
        }
        
        // 根据格式导出文档
        switch ($format) {
            case 'json':
                header('Content-Type: application/json; charset=utf-8');
                header('Content-Disposition: attachment; filename="api_doc.json"');
                echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                break;
            
            case 'markdown':
                header('Content-Type: text/markdown; charset=utf-8');
                header('Content-Disposition: attachment; filename="api_doc.md"');
                echo $this->generateMarkdownDoc($result);
                break;
            
            case 'html':
            default:
                header('Content-Type: text/html; charset=utf-8');
                header('Content-Disposition: attachment; filename="api_doc.html"');
                echo $this->generateHtmlDoc($result);
                break;
        }
        
        exit;
    }
    
    /**
     * 生成Markdown格式文档
     */
    private function generateMarkdownDoc($data) {
        $markdown = "# API文档\n\n";
        $markdown .= "生成时间：" . date('Y-m-d H:i:s') . "\n\n";
        
        foreach ($data['categories'] as $category) {
            $markdown .= "## " . $category['name'] . "\n\n";
            
            if (!empty($category['description'])) {
                $markdown .= $category['description'] . "\n\n";
            }
            
            foreach ($category['apis'] as $api) {
                $markdown .= "### " . $api['name'] . "\n\n";
                
                if (!empty($api['description'])) {
                    $markdown .= $api['description'] . "\n\n";
                }
                
                $markdown .= "**请求方式：** " . $api['method'] . "\n\n";
                $markdown .= "**请求URL：** " . $api['url'] . "\n\n";
                
                // 请求参数
                $markdown .= "#### 请求参数\n\n";
                $markdown .= "| 参数名 | 类型 | 必填 | 说明 | 默认值 |\n";
                $markdown .= "| ------ | ---- | ---- | ---- | ------ |\n";
                
                // 添加API密钥参数
                $markdown .= "| api_key | string | 是 | API密钥 | - |\n";
                
                foreach ($api['params'] as $param) {
                    $required = $param['required'] ? '是' : '否';
                    $defaultValue = $param['default_value'] ? $param['default_value'] : '-';
                    $markdown .= "| " . $param['name'] . " | " . $param['type'] . " | " . $required . " | " . $param['description'] . " | " . $defaultValue . " |\n";
                }
                
                $markdown .= "\n";
                
                // 返回参数
                $markdown .= "#### 返回参数\n\n";
                $markdown .= "| 参数名 | 类型 | 说明 |\n";
                $markdown .= "| ------ | ---- | ---- |\n";
                $markdown .= "| code | int | 状态码，0表示成功，非0表示失败 |\n";
                $markdown .= "| msg | string | 状态信息 |\n";
                $markdown .= "| data | object/array | 返回数据 |\n\n";
                
                // 请求示例
                if (!empty($api['example_params'])) {
                    $markdown .= "#### 请求示例\n\n";
                    $markdown .= "```json\n" . json_encode($api['example_params'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n```\n\n";
                }
                
                // 返回示例
                if (!empty($api['example_response'])) {
                    $markdown .= "#### 返回示例\n\n";
                    $markdown .= "```json\n" . json_encode($api['example_response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n```\n\n";
                }
                
                $markdown .= "---\n\n";
            }
        }
        
        return $markdown;
    }
    
    /**
     * 生成HTML格式文档
     */
    private function generateHtmlDoc($data) {
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API文档</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        h3 {
            margin-top: 20px;
        }
        .api-method {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            color: #fff;
            font-size: 12px;
            margin-right: 10px;
        }
        .api-method-get {
            background-color: #409eff;
        }
        .api-method-post {
            background-color: #67c23a;
        }
        .api-method-put {
            background-color: #e6a23c;
        }
        .api-method-delete {
            background-color: #f56c6c;
        }
        .api-url {
            font-family: Consolas, Monaco, monospace;
            background-color: #f8f8f8;
            padding: 5px 10px;
            border-radius: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .required {
            color: #f56c6c;
        }
        pre {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .footer {
            margin-top: 50px;
            border-top: 1px solid #eee;
            padding-top: 20px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API文档</h1>
        <p>生成时间：' . date('Y-m-d H:i:s') . '</p>
        
        <div class="toc">
            <h2>目录</h2>
            <ul>';
        
        foreach ($data['categories'] as $category) {
            $html .= '<li><a href="#category-' . $category['id'] . '">' . $category['name'] . '</a>
                <ul>';
            
            foreach ($category['apis'] as $api) {
                $html .= '<li><a href="#api-' . $api['id'] . '">' . $api['name'] . '</a></li>';
            }
            
            $html .= '</ul>
            </li>';
        }
        
        $html .= '</ul>
        </div>';
        
        foreach ($data['categories'] as $category) {
            $html .= '<h2 id="category-' . $category['id'] . '">' . $category['name'] . '</h2>';
            
            if (!empty($category['description'])) {
                $html .= '<p>' . $category['description'] . '</p>';
            }
            
            foreach ($category['apis'] as $api) {
                $methodClass = 'api-method-' . strtolower($api['method']);
                
                $html .= '<h3 id="api-' . $api['id'] . '">' . $api['name'] . '</h3>';
                
                if (!empty($api['description'])) {
                    $html .= '<p>' . $api['description'] . '</p>';
                }
                
                $html .= '<p>
                    <span class="api-method ' . $methodClass . '">' . $api['method'] . '</span>
                    <span class="api-url">' . $api['url'] . '</span>
                </p>';
                
                // 请求参数
                $html .= '<h4>请求参数</h4>
                <table>
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>默认值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>api_key</td>
                            <td>string</td>
                            <td><span class="required">是</span></td>
                            <td>API密钥</td>
                            <td>-</td>
                        </tr>';
                
                foreach ($api['params'] as $param) {
                    $required = $param['required'] ? '<span class="required">是</span>' : '否';
                    $defaultValue = $param['default_value'] ? $param['default_value'] : '-';
                    $html .= '<tr>
                            <td>' . $param['name'] . '</td>
                            <td>' . $param['type'] . '</td>
                            <td>' . $required . '</td>
                            <td>' . $param['description'] . '</td>
                            <td>' . $defaultValue . '</td>
                        </tr>';
                }
                
                $html .= '</tbody>
                </table>';
                
                // 返回参数
                $html .= '<h4>返回参数</h4>
                <table>
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>code</td>
                            <td>int</td>
                            <td>状态码，0表示成功，非0表示失败</td>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>string</td>
                            <td>状态信息</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>object/array</td>
                            <td>返回数据</td>
                        </tr>
                    </tbody>
                </table>';
                
                // 请求示例
                if (!empty($api['example_params'])) {
                    $html .= '<h4>请求示例</h4>
                    <pre>' . json_encode($api['example_params'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
                }
                
                // 返回示例
                if (!empty($api['example_response'])) {
                    $html .= '<h4>返回示例</h4>
                    <pre>' . json_encode($api['example_response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
                }
                
                $html .= '<hr>';
            }
        }
        
        $html .= '<div class="footer">
            <p>API文档由系统自动生成</p>
        </div>
    </div>
</body>
</html>';
        
        return $html;
    }
}