<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API接口列表 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .layui-card-body {
            padding: 15px;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-table-tool-temp {
            padding-right: 0;
        }
        .layui-table-cell {
            height: auto;
            line-height: 28px;
            padding: 6px 15px;
            position: relative;
            box-sizing: border-box;
        }
        .api-status {
            cursor: pointer;
        }
        .api-sort {
            width: 60px;
            height: 30px;
            text-align: center;
        }
        .api-price {
            font-weight: bold;
            color: #FF5722;
        }
        .api-price.free {
            color: #5FB878;
        }
        .api-method {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            color: #fff;
            font-size: 12px;
        }
        .api-method.get {
            background-color: #5FB878;
        }
        .api-method.post {
            background-color: #1E9FFF;
        }
        .api-method.put {
            background-color: #FFB800;
        }
        .api-method.delete {
            background-color: #FF5722;
        }
        .api-method.patch {
            background-color: #01AAED;
        }
        .api-method.options {
            background-color: #2F4056;
        }
        .api-method.head {
            background-color: #393D49;
        }
        .api-badge {
            position: relative;
            display: inline-block;
            padding: 0 6px;
            font-size: 12px;
            text-align: center;
            background-color: #FF5722;
            color: #fff;
            border-radius: 2px;
            line-height: 18px;
        }
        .api-badge.blue {
            background-color: #1E9FFF;
        }
        .api-badge.green {
            background-color: #5FB878;
        }
        .api-badge.orange {
            background-color: #FFB800;
        }
        .api-badge.gray {
            background-color: #d2d2d2;
            color: #666;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">API接口列表</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <input type="text" name="keyword" placeholder="接口名称/标识" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <select name="category_id">
                                <option value="">全部分类</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="1">上线</option>
                                <option value="0">下线</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <select name="is_free">
                                <option value="">全部类型</option>
                                <option value="1">免费接口</option>
                                <option value="0">付费接口</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <select name="merchant_id">
                                <option value="">全部商家</option>
                                <option value="0">平台自营</option>
                                <?php foreach ($merchants as $merchant): ?>
                                <option value="<?php echo $merchant['id']; ?>"><?php echo $merchant['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="searchBtn" lay-submit>
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button class="layui-btn icon-btn" id="addBtn">
                            <i class="layui-icon layui-icon-add-1"></i>添加
                        </button>
                        <button class="layui-btn icon-btn layui-btn-danger" id="batchDelBtn">
                            <i class="layui-icon layui-icon-delete"></i>批量删除
                        </button>
                    </div>
                </div>
            </div>
            
            <table id="apiTable" lay-filter="apiTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="params">参数</a>
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="doc">文档</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="statusSwitch" value="{{d.id}}" lay-skin="switch" lay-text="上线|下线" {{d.status==1?'checked':''}} />
</script>

<!-- 表格价格列 -->
<script type="text/html" id="priceTpl">
    {{# if(d.is_free == 1){ }}
    <span class="api-price free">免费</span>
    {{# } else { }}
    <span class="api-price">￥{{d.price}}/{{d.price_unit}}</span>
    {{# } }}
</script>

<!-- 表格请求方式列 -->
<script type="text/html" id="methodTpl">
    <span class="api-method {{d.method.toLowerCase()}}">{{d.method}}</span>
</script>

<!-- 表格商家列 -->
<script type="text/html" id="merchantTpl">
    {{# if(d.merchant_id == 0){ }}
    <span class="api-badge green">平台自营</span>
    {{# } else { }}
    <span class="api-badge blue">{{d.merchant_name}}</span>
    {{# } }}
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#apiTable',
        url: '/admin/api/list/data',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'name', title: '接口名称', sort: true},
            {field: 'category_name', title: '分类', sort: true},
            {field: 'method', title: '请求方式', templet: '#methodTpl', width: 100},
            {field: 'price', title: '价格', templet: '#priceTpl', width: 120, sort: true},
            {field: 'call_count', title: '调用次数', width: 100, sort: true},
            {field: 'merchant_name', title: '所属商家', templet: '#merchantTpl', width: 120},
            {field: 'sort', title: '排序', sort: true, width: 80, templet: function(d) {
                return '<input type="number" class="api-sort" data-id="' + d.id + '" value="' + d.sort + '">';
            }},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100},
            {field: 'created_at', title: '创建时间', sort: true, width: 160, templet: function(d) {
                return util.toDateString(d.created_at * 1000);
            }},
            {title: '操作', toolbar: '#tableBar', width: 220, align: 'center', fixed: 'right'}
        ]]
    });
    
    // 搜索按钮点击事件
    form.on('submit(searchBtn)', function(data) {
        insTb.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 添加按钮点击事件
    $('#addBtn').click(function() {
        location.href = '/admin/api/add';
    });
    
    // 批量删除按钮点击事件
    $('#batchDelBtn').click(function() {
        var checkRows = table.checkStatus('apiTable');
        if (checkRows.data.length === 0) {
            layer.msg('请选择要删除的数据', {icon: 2});
            return;
        }
        
        var ids = checkRows.data.map(function(d) {
            return d.id;
        });
        
        layer.confirm('确定要删除选中的接口吗？', {
            skin: 'layui-layer-admin',
            shade: .1
        }, function(index) {
            layer.close(index);
            var loadIndex = layer.load(2);
            
            $.post('/admin/api/batch_delete', {
                ids: ids.join(',')
            }, function(res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
        });
    });
    
    // 工具条点击事件
    table.on('tool(apiTable)', function(obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            location.href = '/admin/api/edit?id=' + data.id;
        } else if (layEvent === 'params') { // 参数管理
            location.href = '/admin/api/params?api_id=' + data.id;
        } else if (layEvent === 'doc') { // 文档管理
            location.href = '/admin/api/doc?api_id=' + data.id;
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该接口吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(index) {
                layer.close(index);
                var loadIndex = layer.load(2);
                
                $.post('/admin/api/delete', {
                    id: data.id
                }, function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 修改状态
    form.on('switch(statusSwitch)', function(obj) {
        var loadIndex = layer.load(2);
        
        $.post('/admin/api/status', {
            id: obj.value,
            status: obj.elem.checked ? 1 : 0
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });
    
    // 监听排序修改
    $(document).on('change', '.api-sort', function() {
        var id = $(this).data('id');
        var sort = $(this).val();
        
        var loadIndex = layer.load(2);
        $.post('/admin/api/sort', {
            id: id,
            sort: sort
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                insTb.reload();
            }
        }, 'json');
    });
});
</script>
</body>
</html>