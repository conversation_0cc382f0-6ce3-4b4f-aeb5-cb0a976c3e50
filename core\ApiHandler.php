<?php
/**
 * API处理类
 */
class ApiHandler
{
    private $db;
    private $auth;
    private $config;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->auth = Auth::getInstance();
        $this->config = require_once __DIR__ . '/../config/app.php';
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest($path, $params)
    {
        // 验证API密钥
        if (!isset($params['api_key'])) {
            return $this->error('缺少API密钥');
        }
        
        $apiKey = $params['api_key'];
        $keyValidation = $this->auth->validateApiKey($apiKey);
        
        if (!$keyValidation['status']) {
            return $this->error($keyValidation['message']);
        }
        
        // 获取API信息
        $api = $this->db->query("SELECT * FROM apis WHERE url = :url AND status = 1")
            ->bind(':url', $path)
            ->fetch();
        
        if (!$api) {
            return $this->error('API不存在或已禁用');
        }
        
        // 检查IP黑白名单
        if (!$this->checkIpAccess($api['id'])) {
            return $this->error('您的IP地址无权访问此API');
        }
        
        // 检查QPS限制
        if (!$this->checkQpsLimit($keyValidation['data']['id'], $keyValidation['type'])) {
            return $this->error('超出QPS限制，请稍后再试');
        }
        
        // 检查参数
        $requiredParams = json_decode($api['required_params'], true) ?: [];
        foreach ($requiredParams as $param) {
            if (!isset($params[$param]) || empty($params[$param])) {
                return $this->error("缺少必要参数: {$param}");
            }
        }
        
        // 检查用户权限和余额
        if ($api['is_free'] == 0) {
            $result = $this->checkUserAccess($keyValidation['data'], $keyValidation['type'], $api);
            if (!$result['status']) {
                return $this->error($result['message']);
            }
        }
        
        // 记录调用日志
        $logId = $this->logApiCall($keyValidation['data']['id'], $keyValidation['type'], $api['id'], $params);
        
        try {
            // 处理API请求
            if ($api['type'] == 'local') {
                // 本地API
                $result = $this->handleLocalApi($api, $params);
            } else {
                // 远程API
                $result = $this->handleRemoteApi($api, $params);
            }
            
            // 更新调用日志
            $this->updateApiCallLog($logId, $result);
            
            // 如果API不是免费的，扣除用户余额
            if ($api['is_free'] == 0) {
                $this->deductBalance($keyValidation['data']['id'], $keyValidation['type'], $api);
            }
            
            // 更新API调用次数
            $this->db->update('apis', [
                'call_count' => $api['call_count'] + 1,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = :id', [':id' => $api['id']]);
            
            return $result;
        } catch (Exception $e) {
            // 记录错误日志
            $this->updateApiCallLog($logId, ['error' => $e->getMessage()], false);
            
            // 发送异常通知
            $this->sendExceptionNotification($api, $e->getMessage());
            
            return $this->error('API调用失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理本地API
     */
    private function handleLocalApi($api, $params)
    {
        $handlerFile = __DIR__ . '/../api_handlers/' . $api['handler_file'];
        
        if (!file_exists($handlerFile)) {
            throw new Exception('API处理程序不存在');
        }
        
        require_once $handlerFile;
        
        $handlerClass = $api['handler_class'];
        
        if (!class_exists($handlerClass)) {
            throw new Exception('API处理类不存在');
        }
        
        $handler = new $handlerClass();
        
        if (!method_exists($handler, 'handle')) {
            throw new Exception('API处理类缺少handle方法');
        }
        
        return $handler->handle($params);
    }
    
    /**
     * 处理远程API
     */
    private function handleRemoteApi($api, $params)
    {
        $url = $api['remote_url'];
        $method = strtoupper($api['method']);
        $headers = json_decode($api['remote_headers'], true) ?: [];
        
        // 替换URL中的参数占位符
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', urlencode($value), $url);
        }
        
        // 创建cURL资源
        $ch = curl_init();
        
        // 设置URL和其他选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        // 设置请求头
        $curlHeaders = [];
        foreach ($headers as $key => $value) {
            $curlHeaders[] = $key . ': ' . $value;
        }
        
        if (!empty($curlHeaders)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $curlHeaders);
        }
        
        // 根据请求方法设置不同的选项
        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        } elseif ($method != 'GET') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        }
        
        // 执行请求并获取响应
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        // 关闭cURL资源
        curl_close($ch);
        
        if ($error) {
            throw new Exception('远程API请求失败: ' . $error);
        }
        
        if ($httpCode >= 400) {
            throw new Exception('远程API返回错误状态码: ' . $httpCode);
        }
        
        // 验证响应格式
        $responseData = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            // 如果不是JSON格式，尝试进行格式转换
            $responseData = ['data' => $response];
        }
        
        // 验证响应结构
        $expectedResponse = json_decode($api['expected_response'], true) ?: [];
        if (!empty($expectedResponse)) {
            $this->validateResponse($responseData, $expectedResponse);
        }
        
        return $responseData;
    }
    
    /**
     * 验证响应结构
     */
    private function validateResponse($response, $expected, $path = '')
    {
        foreach ($expected as $key => $value) {
            $currentPath = $path ? $path . '.' . $key : $key;
            
            if (!isset($response[$key])) {
                throw new Exception("响应缺少必要字段: {$currentPath}");
            }
            
            if (is_array($value) && !empty($value)) {
                if (!is_array($response[$key])) {
                    throw new Exception("响应字段类型不匹配: {$currentPath}");
                }
                
                // 如果是索引数组且有元素，验证第一个元素
                if (isset($value[0]) && !empty($response[$key])) {
                    $this->validateResponse($response[$key][0], $value[0], $currentPath . '[0]');
                } else {
                    // 关联数组，递归验证
                    $this->validateResponse($response[$key], $value, $currentPath);
                }
            }
        }
    }
    
    /**
     * 检查用户访问权限和余额
     */
    private function checkUserAccess($user, $type, $api)
    {
        // 检查用户余额
        if ($user['balance'] < $api['price']) {
            return ['status' => false, 'message' => '余额不足，请充值'];
        }
        
        // 检查用户是否购买了此API
        if ($api['need_purchase'] == 1) {
            $tableName = $type == 'user' ? 'user_purchases' : 'merchant_purchases';
            $idField = $type == 'user' ? 'user_id' : 'merchant_id';
            
            $purchased = $this->db->query("SELECT COUNT(*) FROM {$tableName} WHERE {$idField} = :id AND api_id = :api_id AND expires_at > NOW()")
                ->bind(':id', $user['id'])
                ->bind(':api_id', $api['id'])
                ->getValue();
            
            if ($purchased == 0) {
                return ['status' => false, 'message' => '您尚未购买此API或购买已过期'];
            }
        }
        
        // 检查用户VIP等级
        if ($api['min_vip_level'] > 0) {
            $userLevel = $type == 'user' ? $user['vip_level'] : $user['level'];
            
            if ($userLevel < $api['min_vip_level']) {
                return ['status' => false, 'message' => '您的会员等级不足，无法使用此API'];
            }
        }
        
        return ['status' => true];
    }
    
    /**
     * 扣除用户余额
     */
    private function deductBalance($userId, $type, $api)
    {
        $tableName = $type == 'user' ? 'users' : 'merchants';
        $idField = 'id';
        
        // 获取用户当前余额
        $user = $this->db->query("SELECT balance FROM {$tableName} WHERE {$idField} = :id FOR UPDATE")
            ->bind(':id', $userId)
            ->fetch();
        
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        $newBalance = $user['balance'] - $api['price'];
        
        if ($newBalance < 0) {
            throw new Exception('余额不足');
        }
        
        // 更新用户余额
        $this->db->beginTransaction();
        
        try {
            $this->db->update($tableName, [
                'balance' => $newBalance
            ], "{$idField} = :id", [':id' => $userId]);
            
            // 记录交易日志
            $this->db->insert('transactions', [
                'user_id' => $type == 'user' ? $userId : null,
                'merchant_id' => $type == 'merchant' ? $userId : null,
                'type' => 'api_call',
                'amount' => -$api['price'],
                'balance' => $newBalance,
                'description' => "调用API: {$api['name']}",
                'api_id' => $api['id'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 如果API属于某个商家，给商家分成
            if ($api['creator_id']) {
                // 获取商家分成比例
                $merchant = $this->db->query("SELECT * FROM merchants WHERE id = :id")
                    ->bind(':id', $api['creator_id'])
                    ->fetch();
                
                if ($merchant) {
                    // 计算分成金额
                    $commission = $api['price'] * $merchant['commission_rate'] / 100;
                    
                    // 更新商家余额
                    $this->db->update('merchants', [
                        'balance' => $merchant['balance'] + $commission
                    ], 'id = :id', [':id' => $merchant['id']]);
                    
                    // 记录商家收入
                    $this->db->insert('transactions', [
                        'merchant_id' => $merchant['id'],
                        'type' => 'api_income',
                        'amount' => $commission,
                        'balance' => $merchant['balance'] + $commission,
                        'description' => "API调用收入: {$api['name']}",
                        'api_id' => $api['id'],
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }
            
            $this->db->commit();
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    /**
     * 记录API调用日志
     */
    private function logApiCall($userId, $userType, $apiId, $params)
    {
        $data = [
            'api_id' => $apiId,
            'user_id' => $userType == 'user' ? $userId : null,
            'merchant_id' => $userType == 'merchant' ? $userId : null,
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_params' => json_encode($params, JSON_UNESCAPED_UNICODE),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->db->insert('api_logs', $data);
    }
    
    /**
     * 更新API调用日志
     */
    private function updateApiCallLog($logId, $response, $success = true)
    {
        $data = [
            'response_data' => json_encode($response, JSON_UNESCAPED_UNICODE),
            'status' => $success ? 1 : 0,
            'response_time' => date('Y-m-d H:i:s')
        ];
        
        $this->db->update('api_logs', $data, 'id = :id', [':id' => $logId]);
    }
    
    /**
     * 检查IP黑白名单
     */
    private function checkIpAccess($apiId)
    {
        $ip = $_SERVER['REMOTE_ADDR'];
        
        // 检查黑名单
        $blacklisted = $this->db->query("SELECT COUNT(*) FROM ip_blacklist WHERE (api_id = :api_id OR api_id IS NULL) AND ip = :ip")
            ->bind(':api_id', $apiId)
            ->bind(':ip', $ip)
            ->getValue();
        
        if ($blacklisted > 0) {
            return false;
        }
        
        // 检查白名单
        $whitelisted = $this->db->query("SELECT COUNT(*) FROM ip_whitelist WHERE api_id = :api_id AND ip = :ip")
            ->bind(':api_id', $apiId)
            ->bind(':ip', $ip)
            ->getValue();
        
        // 如果API有白名单设置，则IP必须在白名单中
        $hasWhitelist = $this->db->query("SELECT COUNT(*) FROM ip_whitelist WHERE api_id = :api_id")
            ->bind(':api_id', $apiId)
            ->getValue();
        
        if ($hasWhitelist > 0 && $whitelisted == 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查QPS限制
     */
    private function checkQpsLimit($userId, $userType)
    {
        $ip = $_SERVER['REMOTE_ADDR'];
        $now = time();
        $oneSecondAgo = date('Y-m-d H:i:s', $now - 1);
        
        // 获取用户QPS限制
        $qpsLimit = 10; // 默认限制
        
        if ($userType == 'user') {
            $user = $this->db->query("SELECT qps_limit FROM users WHERE id = :id")
                ->bind(':id', $userId)
                ->fetch();
            
            if ($user && $user['qps_limit'] > 0) {
                $qpsLimit = $user['qps_limit'];
            }
        } else {
            $merchant = $this->db->query("SELECT qps_limit FROM merchants WHERE id = :id")
                ->bind(':id', $userId)
                ->fetch();
            
            if ($merchant && $merchant['qps_limit'] > 0) {
                $qpsLimit = $merchant['qps_limit'];
            }
        }
        
        // 检查最近1秒内的请求数
        $idField = $userType == 'user' ? 'user_id' : 'merchant_id';
        $recentCalls = $this->db->query("SELECT COUNT(*) FROM api_logs WHERE {$idField} = :id AND created_at >= :time")
            ->bind(':id', $userId)
            ->bind(':time', $oneSecondAgo)
            ->getValue();
        
        return $recentCalls < $qpsLimit;
    }
    
    /**
     * 发送异常通知
     */
    private function sendExceptionNotification($api, $errorMessage)
    {
        // 获取系统配置
        $siteConfig = $this->db->query("SELECT * FROM site_config WHERE id = 1")->fetch();
        
        if (!$siteConfig || $siteConfig['exception_notify'] != 1) {
            return;
        }
        
        // 创建工单
        $this->db->insert('tickets', [
            'title' => "API异常: {$api['name']}",
            'content' => "API调用出现异常:\n\nAPI: {$api['name']}\n错误信息: {$errorMessage}\n时间: " . date('Y-m-d H:i:s'),
            'status' => 0,
            'priority' => 2,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // 发送邮件通知
        if ($siteConfig['exception_email'] == 1 && !empty($siteConfig['admin_email'])) {
            $mailer = new Mailer();
            $subject = "API异常通知: {$api['name']}";
            $body = "尊敬的管理员，<br><br>系统检测到API调用异常：<br><br>API名称：{$api['name']}<br>API路径：{$api['url']}<br>错误信息：{$errorMessage}<br>时间：" . date('Y-m-d H:i:s') . "<br><br>请尽快处理。";
            
            $mailer->send($siteConfig['admin_email'], '系统管理员', $subject, $body);
        }
    }
    
    /**
     * 返回错误信息
     */
    private function error($message)
    {
        return [
            'code' => 500,
            'message' => $message,
            'data' => null
        ];
    }
}