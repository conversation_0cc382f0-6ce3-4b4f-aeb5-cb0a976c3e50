<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>系统概况</title>
    <link rel="stylesheet" href="../../../Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../Easyweb/assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /** 统计快捷方式样式 */
        .console-link-block {
            font-size: 16px;
            padding: 20px 20px;
            border-radius: 4px;
            background-color: #40D4B0;
            color: #FFFFFF !important;
            box-shadow: 0 2px 3px rgba(0, 0, 0, .05);
            position: relative;
            overflow: hidden;
            display: block;
        }

        .console-link-block .console-link-block-num {
            font-size: 40px;
            margin-bottom: 5px;
            opacity: .9;
        }

        .console-link-block .console-link-block-text {
            opacity: .8;
        }

        .console-link-block .console-link-block-icon {
            position: absolute;
            top: 50%;
            right: 20px;
            width: 50px;
            height: 50px;
            font-size: 50px;
            line-height: 50px;
            margin-top: -25px;
            color: #FFFFFF;
            opacity: .8;
        }

        .console-link-block .console-link-block-band {
            color: #fff;
            width: 100px;
            font-size: 12px;
            padding: 2px 0 3px 0;
            background-color: #E32A16;
            line-height: inherit;
            text-align: center;
            position: absolute;
            top: 8px;
            right: -30px;
            transform-origin: center;
            transform: rotate(45deg) scale(.8);
            opacity: .95;
            z-index: 2;
        }

        /** 设置每个快捷块的颜色 */
        .layui-row > div:nth-child(2) .console-link-block {
            background-color: #55A5EA;
        }

        .layui-row > div:nth-child(3) .console-link-block {
            background-color: #9DAFFF;
        }

        .layui-row > div:nth-child(4) .console-link-block {
            background-color: #F591A2;
        }

        .layui-row > div:nth-child(5) .console-link-block {
            background-color: #FEAA4F;
        }

        .layui-row > div:last-child .console-link-block {
            background-color: #9BC539;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid ew-console-wrapper">
    <!-- 快捷方式 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">156</div>
                <div class="console-link-block-text">API总数</div>
                <i class="console-link-block-icon layui-icon layui-icon-component"></i>
                <div class="console-link-block-band">管理</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">2,345</div>
                <div class="console-link-block-text">用户总数</div>
                <i class="console-link-block-icon layui-icon layui-icon-user"></i>
                <div class="console-link-block-band">管理</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">89</div>
                <div class="console-link-block-text">商家总数</div>
                <i class="console-link-block-icon layui-icon layui-icon-shop"></i>
                <div class="console-link-block-band">管理</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">12,890</div>
                <div class="console-link-block-text">今日调用</div>
                <i class="console-link-block-icon layui-icon layui-icon-chart"></i>
                <div class="console-link-block-band">查看</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">¥1,234</div>
                <div class="console-link-block-text">今日收入</div>
                <i class="console-link-block-icon layui-icon layui-icon-dollar"></i>
                <div class="console-link-block-band">查看</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">15</div>
                <div class="console-link-block-text">待处理工单</div>
                <i class="console-link-block-icon layui-icon layui-icon-survey"></i>
                <div class="console-link-block-band">处理</div>
            </div>
        </div>
    </div>
    
    <!-- 统计图表 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">API调用统计</div>
                <div class="layui-card-body">
                    <div id="consoleChartsDay" style="height: 300px;"></div>
                    <div style="color: #10B4E8;font-size: 18px;position: absolute;bottom: 85px;left: 0;right:0;text-align: center;cursor: pointer;">
                        调用详情<i class="layui-icon layui-icon-right" style="font-size: 16px;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">用户增长趋势</div>
                <div class="layui-card-body">
                    <div id="consoleChartsWeek" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">收入统计</div>
                <div class="layui-card-body">
                    <div id="consoleChartsMonth" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">热门API排行</div>
                <div class="layui-card-body">
                    <div id="consoleChartsWord" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md8 layui-col-sm12">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">用户地区分布</div>
                <div class="layui-card-body">
                    <div class="layui-row">
                        <div class="layui-col-md8 layui-col-sm7">
                            <div id="consoleChartsMap" style="height: 300px;"></div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm5">
                            <table class="layui-table" lay-skin="line" style="margin-top: 15px;">
                                <thead>
                                <tr>
                                    <td>排名</td>
                                    <td>地区</td>
                                    <td>用户数</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>广东</td>
                                    <td>623</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>北京</td>
                                    <td>591</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>上海</td>
                                    <td>558</td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>浙江</td>
                                    <td>519</td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>江苏</td>
                                    <td>392</td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>山东</td>
                                    <td>371</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最新动态 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">最新动态</div>
                <div class="layui-card-body">
                    <ul class="layui-timeline">
                        <li class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                            <div class="layui-timeline-content layui-text">
                                <h3 class="layui-timeline-title">用户 张三 注册成功</h3>
                                <p>刚刚</p>
                            </div>
                        </li>
                        <li class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                            <div class="layui-timeline-content layui-text">
                                <h3 class="layui-timeline-title">新增API接口：手机号归属地查询</h3>
                                <p>5分钟前</p>
                            </div>
                        </li>
                        <li class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                            <div class="layui-timeline-content layui-text">
                                <h3 class="layui-timeline-title">商家 ABC科技 申请入驻</h3>
                                <p>10分钟前</p>
                            </div>
                        </li>
                        <li class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                            <div class="layui-timeline-content layui-text">
                                <h3 class="layui-timeline-title">系统完成自动备份</h3>
                                <p>1小时前</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">系统状态</div>
                <div class="layui-card-body">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                        <tr>
                            <td>服务器状态</td>
                            <td><span class="layui-badge layui-bg-green">正常</span></td>
                        </tr>
                        <tr>
                            <td>数据库状态</td>
                            <td><span class="layui-badge layui-bg-green">正常</span></td>
                        </tr>
                        <tr>
                            <td>缓存状态</td>
                            <td><span class="layui-badge layui-bg-green">正常</span></td>
                        </tr>
                        <tr>
                            <td>队列状态</td>
                            <td><span class="layui-badge layui-bg-green">正常</span></td>
                        </tr>
                        <tr>
                            <td>存储空间</td>
                            <td>75.6% (剩余 2.4GB)</td>
                        </tr>
                        <tr>
                            <td>内存使用</td>
                            <td>68.2% (剩余 1.2GB)</td>
                        </tr>
                        <tr>
                            <td>CPU使用率</td>
                            <td>23.5%</td>
                        </tr>
                        <tr>
                            <td>系统负载</td>
                            <td>0.85</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../Easyweb/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../Easyweb/assets/js/common.js?v=318"></script>
<script src="../../../Easyweb/assets/libs/echarts/echarts.min.js"></script>
<script src="../../../Easyweb/assets/libs/echarts/china.js"></script>
<script src="../../../Easyweb/assets/libs/echarts/echarts-wordcloud.min.js"></script>
<script>
    layui.use(['layer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;

        /** 渲染API调用统计图表 */
        var myCharts1 = echarts.init(document.getElementById('consoleChartsDay'));
        var options1 = {
            title: {
                text: '成功/失败调用', x: 'center', y: '32%',
                textStyle: {fontSize: 18, color: '#262626', fontWeight: 'normal'},
                subtextStyle: {fontSize: 56, color: '#10B4E8'}, itemGap: 20
            },
            color: ['#10B4E8', '#E0E0E0'],
            tooltip: {trigger: 'item'},
            legend: {
                orient: 'vertical', right: '0px', top: '0px',
                data: ['成功调用', '失败调用'], textStyle: {color: '#595959'}
            },
            series: [{name: '调用次数', type: 'pie', radius: ['75%', '80%'], label: {normal: {show: false}}}]
        };
        myCharts1.setOption(options1);
        // 赋值
        myCharts1.setOption({
            title: {subtext: '11,234/1,656'}, 
            series: [{data: [{name: '成功调用', value: 11234}, {name: '失败调用', value: 1656}]}]
        });

        /** 渲染用户增长趋势图表 */
        var myCharts2 = echarts.init(document.getElementById('consoleChartsWeek'));
        var options2 = {
            tooltip: {trigger: 'axis', axisPointer: {lineStyle: {color: '#E0E0E0'}}},
            color: ['#10B4E8', '#FFA800'],
            legend: {
                orient: 'vertical', right: '0px', top: '0px',
                data: ['新增用户', '活跃用户'], textStyle: {color: '#595959'}
            },
            grid: {top: '75px', left: '35px', right: '55px', bottom: '40px'},
            xAxis: {
                name: '日期',
                nameTextStyle: {color: '#595959'},
                type: 'category',
                data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {color: '#8c8c8c'},
                axisTick: {alignWithLabel: true}
            },
            yAxis: {
                name: '人数',
                nameTextStyle: {color: '#595959'},
                type: 'value',
                boundaryGap: ['0', '20%'],
                axisTick: {show: false},
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {color: '#8c8c8c'},
                splitLine: {show: false},
                splitArea: {show: false},
                minInterval: 1
            },
            series: [{
                name: '新增用户', type: 'bar', stack: 'one', barMaxWidth: '30px',
                label: {normal: {show: true, position: 'inside'}}
            }, {
                name: '活跃用户', type: 'bar', stack: 'one', barMaxWidth: '30px',
                label: {normal: {show: true, position: 'inside'}}
            }]
        };
        myCharts2.setOption(options2);
        // 赋值
        myCharts2.setOption({series: [{data: [45, 89, 76, 123, 156, 134, 98]}, {data: [156, 234, 189, 267, 298, 245, 187]}]});

        /** 渲染收入统计图表 */
        var myCharts3 = echarts.init(document.getElementById('consoleChartsMonth'));
        var options3 = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {lineStyle: {color: '#E0E0E0'}},
                formatter: '{b}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#10B4E8;"></span>{a0}: ¥{c0}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FFA800;"></span>{a1}: ¥{c1}'
            },
            color: ['#10B4E8', '#FFA800'],
            legend: {
                orient: 'vertical', right: '0px', top: '0px',
                data: ['API收入', '会员收入'], textStyle: {color: '#595959'}
            },
            grid: {top: '75px', left: '35px', right: '55px', bottom: '40px'},
            xAxis: {
                name: '日期',
                nameTextStyle: {color: '#595959'},
                type: 'category',
                data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30'],
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {
                    color: '#8c8c8c', interval: function (index, value) {
                        return index === 0 || ((index + 1) % 5 === 0);
                    }
                },
                axisTick: {alignWithLabel: true}
            },
            yAxis: {
                name: '金额(元)',
                nameTextStyle: {color: '#595959'},
                type: 'value',
                boundaryGap: ['0', '20%'],
                axisTick: {show: false},
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {color: '#8c8c8c'},
                splitLine: {show: false},
                splitArea: {show: false},
                minInterval: 1
            },
            series: [
                {name: 'API收入', type: 'line', smooth: false},
                {name: '会员收入', type: 'line', smooth: false}
            ]
        };
        myCharts3.setOption(options3);
        // 赋值
        myCharts3.setOption({
            series: [
                {data: [150, 140, 130, 130, 130, 140, 150, 160, 170, 180, 190, 180, 180, 190, 200, 190, 180, 160, 140, 120, 100, 100, 120, 140, 160, 160, 140, 130, 120, 110]},
                {data: [240, 220, 200, 180, 160, 140, 130, 120, 110, 110, 100, 100, 110, 120, 130, 140, 150, 160, 180, 200, 220, 230, 240, 250, 260, 260, 240, 220, 200, 180]}
            ]
        });

        /** 渲染热门API排行图表 */
        var myCharts4 = echarts.init(document.getElementById('consoleChartsWord'));
        var options4 = {
            tooltip: {show: true},
            series: [{
                name: "调用量",
                type: 'wordCloud',
                shape: 'diamond',
                width: '100%',
                height: '100%',
                sizeRange: [12, 23],
                gridSize: 6,
                textStyle: {
                    normal: {
                        color: function () {
                            return 'rgb(' + [
                                Math.round(Math.random() * 160),
                                Math.round(Math.random() * 160),
                                Math.round(Math.random() * 160)
                            ].join(',') + ')';
                        }
                    },
                    emphasis: {shadowBlur: 10, shadowColor: '#666'}
                }, data: []
            }]
        };
        myCharts4.setOption(options4);
        // 赋值
        myCharts4.setOption({
            series: [{
                data: [
                    {name: "天气查询", value: 2300},
                    {name: "IP查询", value: 1890},
                    {name: "短链生成", value: 1560},
                    {name: "二维码生成", value: 1234},
                    {name: "身份证查询", value: 987},
                    {name: "手机归属地", value: 876},
                    {name: "快递查询", value: 765},
                    {name: "汇率查询", value: 654},
                    {name: "翻译接口", value: 543},
                    {name: "图片识别", value: 432},
                    {name: "语音合成", value: 321},
                    {name: "文本分析", value: 234},
                    {name: "邮箱验证", value: 198},
                    {name: "密码生成", value: 156},
                    {name: "时间戳", value: 123}
                ]
            }]
        });

        /** 渲染地图图表 */
        var myCharts5 = echarts.init(document.getElementById('consoleChartsMap'));
        var options5 = {
            tooltip: {trigger: 'item'},
            dataRange: {
                min: 0, max: 1000, text: ['高', '低'], color: ['#2395FF', '#f2f2f2'], itemHeight: 60, itemWidth: 12
            }, series: [{
                name: '用户数量', type: 'map', mapType: "china",
                itemStyle: {normal: {label: {show: true, color: '#262626'}, borderColor: '#dddddd'}},
                emphasis: {label: {show: true, color: '#fff'}, itemStyle: {areaColor: '#FACF20'}},
                top: '0px', left: '15px', bottom: '0px'
            }]
        };
        myCharts5.setOption(options5);
        // 赋值
        myCharts5.setOption({
            series: [{
                data: [
                    {name: "西藏", value: 6},
                    {name: "青海", value: 16},
                    {name: "宁夏", value: 21},
                    {name: "海南", value: 25},
                    {name: "甘肃", value: 50},
                    {name: "贵州", value: 57},
                    {name: "新疆", value: 66},
                    {name: "云南", value: 89},
                    {name: "重庆", value: 100},
                    {name: "吉林", value: 105},
                    {name: "山西", value: 112},
                    {name: "天津", value: 130},
                    {name: "江西", value: 170},
                    {name: "广西", value: 172},
                    {name: "陕西", value: 251},
                    {name: "黑龙江", value: 125},
                    {name: "内蒙古", value: 143},
                    {name: "安徽", value: 253},
                    {name: "北京", value: 591},
                    {name: "福建", value: 375},
                    {name: "上海", value: 558},
                    {name: "湖北", value: 371},
                    {name: "湖南", value: 296},
                    {name: "四川", value: 310},
                    {name: "辽宁", value: 222},
                    {name: "河北", value: 145},
                    {name: "河南", value: 269},
                    {name: "浙江", value: 519},
                    {name: "山东", value: 392},
                    {name: "江苏", value: 359},
                    {name: "广东", value: 623}
                ]
            }]
        });

        /** 窗口大小改变事件 */
        window.onresize = function () {
            myCharts1.resize();
            myCharts2.resize();
            myCharts3.resize();
            myCharts4.resize();
            myCharts5.resize();
        };

    });
</script>
</body>
</html>
