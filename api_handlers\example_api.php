<?php
/**
 * 示例API处理程序
 */
class ExampleApi
{
    /**
     * 获取当前时间
     * 
     * @param array $params 请求参数
     * @return array 响应数据
     */
    public function getCurrentTime($params)
    {
        $format = isset($params['format']) ? $params['format'] : 'Y-m-d H:i:s';
        
        return [
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'timestamp' => time(),
                'datetime' => date($format),
                'timezone' => date_default_timezone_get()
            ]
        ];
    }
    
    /**
     * 生成随机字符串
     * 
     * @param array $params 请求参数
     * @return array 响应数据
     */
    public function generateRandomString($params)
    {
        $length = isset($params['length']) ? intval($params['length']) : 10;
        $type = isset($params['type']) ? $params['type'] : 'mixed';
        
        // 限制长度范围
        if ($length < 1) {
            $length = 1;
        } elseif ($length > 100) {
            $length = 100;
        }
        
        // 根据类型选择字符集
        switch ($type) {
            case 'alpha':
                $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                break;
            case 'numeric':
                $chars = '0123456789';
                break;
            case 'alpha_numeric':
                $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                break;
            case 'special':
                $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_-=+;:,.?';
                break;
            default:
                $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                break;
        }
        
        $randomString = '';
        $charsLength = strlen($chars);
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $chars[rand(0, $charsLength - 1)];
        }
        
        return [
            'code' => 200,
            'message' => '生成成功',
            'data' => [
                'random_string' => $randomString,
                'length' => $length,
                'type' => $type
            ]
        ];
    }
    
    /**
     * 计算两个数的和
     * 
     * @param array $params 请求参数
     * @return array 响应数据
     */
    public function calculateSum($params)
    {
        if (!isset($params['a']) || !isset($params['b'])) {
            return [
                'code' => 400,
                'message' => '缺少必要参数',
                'data' => null
            ];
        }
        
        $a = is_numeric($params['a']) ? floatval($params['a']) : 0;
        $b = is_numeric($params['b']) ? floatval($params['b']) : 0;
        
        return [
            'code' => 200,
            'message' => '计算成功',
            'data' => [
                'a' => $a,
                'b' => $b,
                'sum' => $a + $b
            ]
        ];
    }
}