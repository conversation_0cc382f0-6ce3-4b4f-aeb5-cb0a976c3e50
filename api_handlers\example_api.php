<?php
/**
 * 示例API处理器
 */

/**
 * 处理GET请求
 * @param array $params 请求参数
 * @return array 响应数据
 */
function handle_get_example_api($params) {
    // 这里是示例API的处理逻辑
    $name = isset($params['name']) ? $params['name'] : '世界';
    
    return [
        'message' => "你好，{$name}！",
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s')
    ];
}

/**
 * 处理POST请求
 * @param array $params 请求参数
 * @return array 响应数据
 */
function handle_post_example_api($params) {
    // 这里是示例API的处理逻辑
    $data = isset($params['data']) ? $params['data'] : null;
    
    return [
        'received' => $data,
        'success' => true,
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s')
    ];
}