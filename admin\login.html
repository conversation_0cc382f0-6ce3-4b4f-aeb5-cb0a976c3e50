<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录 - API商业系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../Easyweb/assets/css/login.css">
    <style>
        body {
            background-image: url(../Easyweb/assets/images/bg-login.jpg);
            background-size: cover;
        }
        .login-wrapper {
            max-width: 420px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
            top: 50%;
            transform: translateY(-60%);
        }
        .login-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .login-header h2 {
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 24px;
            color: #333;
        }
        .login-header p {
            font-weight: lighter;
            color: #999;
        }
        .login-form {
            background-color: #fff;
            padding: 30px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
        }
        .login-captcha {
            width: 100%;
            height: 38px;
        }
    </style>
</head>
<body>
<div class="login-wrapper">
    <div class="login-header">
        <h2>API商业系统</h2>
        <p>专业的API接口管理平台</p>
    </div>
    <div class="login-form">
        <form class="layui-form" action="javascript:;">
            <div class="layui-form-item">
                <label class="layui-icon layui-icon-username" for="username"></label>
                <input type="text" name="username" id="username" lay-verify="required" placeholder="用户名" class="layui-input">
            </div>
            <div class="layui-form-item">
                <label class="layui-icon layui-icon-password" for="password"></label>
                <input type="password" name="password" id="password" lay-verify="required" placeholder="密码" class="layui-input">
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-xs7">
                        <label class="layui-icon layui-icon-vercode" for="captcha"></label>
                        <input type="text" name="captcha" id="captcha" lay-verify="required" placeholder="验证码" class="layui-input">
                    </div>
                    <div class="layui-col-xs5">
                        <img class="login-captcha" src="controllers/AuthController.php?action=captcha" alt="验证码">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <input type="checkbox" name="remember" title="记住密码" lay-skin="primary">
                <a href="javascript:;" class="layadmin-user-jump-change layui-hide-xs" style="float: right;">忘记密码？</a>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="login-submit">登 录</button>
            </div>
        </form>
    </div>
</div>

<script src="../Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['form', 'layer', 'jquery'], function() {
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 表单提交
    form.on('submit(login-submit)', function(data) {
        $.ajax({
            url: 'controllers/AuthController.php?action=login',
            type: 'POST',
            data: data.field,
            dataType: 'json',
            success: function(res) {
                if (res.code === 200) {
                    layer.msg('登录成功', {icon: 1, time: 1000}, function() {
                        location.href = 'index.php';
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                    // 刷新验证码
                    $('.login-captcha').attr('src', 'controllers/AuthController.php?action=captcha&t=' + new Date().getTime());
                }
            },
            error: function() {
                layer.msg('服务器错误', {icon: 2});
            }
        });
        return false;
    });
    
    // 点击刷新验证码
    $('.login-captcha').click(function() {
        this.src = 'controllers/AuthController.php?action=captcha&t=' + new Date().getTime();
    });
});
</script>
</body>
</html>