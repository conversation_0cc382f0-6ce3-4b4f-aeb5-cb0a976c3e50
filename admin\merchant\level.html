<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商家等级管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">商家等级管理</h2>
        </div>
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <div class="layui-btn-group">
                <button class="layui-btn" id="add-btn"><i class="layui-icon">&#xe654;</i>添加等级</button>
                <button class="layui-btn layui-btn-danger" id="del-btn"><i class="layui-icon">&#xe640;</i>批量删除</button>
            </div>
            
            <!-- 数据表格 -->
            <table class="layui-hide" id="level-table" lay-filter="level-table"></table>
            
            <!-- 表格操作列 -->
            <script type="text/html" id="table-bar">
                <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
            </script>
        </div>
    </div>
</div>

<!-- 添加/编辑等级弹窗 -->
<script type="text/html" id="level-form-tpl">
    <form class="layui-form" id="level-form" lay-filter="level-form" style="padding: 20px;">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">等级名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入等级名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">等级值</label>
            <div class="layui-input-block">
                <input type="number" name="level" placeholder="请输入等级值" class="layui-input" lay-verify="required|number" min="1">
                <div class="layui-form-mid layui-word-aux">数字越大等级越高</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">佣金比例</label>
            <div class="layui-input-block">
                <input type="number" name="commission_rate" placeholder="请输入默认佣金比例" class="layui-input" lay-verify="required|number" min="0" max="100" step="0.1">
                <div class="layui-form-mid layui-word-aux">百分比，例如：30表示30%</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">API数量限制</label>
            <div class="layui-input-block">
                <input type="number" name="api_limit" placeholder="请输入API数量限制" class="layui-input" lay-verify="required|number" min="0">
                <div class="layui-form-mid layui-word-aux">0表示不限制</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">QPS限制</label>
            <div class="layui-input-block">
                <input type="number" name="qps_limit" placeholder="请输入QPS限制" class="layui-input" lay-verify="required|number" min="0">
                <div class="layui-form-mid layui-word-aux">0表示不限制</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">自定义域名</label>
            <div class="layui-input-block">
                <input type="checkbox" name="allow_custom_domain" lay-skin="switch" lay-text="允许|禁止">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">技术支持</label>
            <div class="layui-input-block">
                <input type="checkbox" name="tech_support" lay-skin="switch" lay-text="提供|不提供">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">优先结算</label>
            <div class="layui-input-block">
                <input type="checkbox" name="priority_settlement" lay-skin="switch" lay-text="是|否">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">等级说明</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入等级说明" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="level-form-submit">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 渲染表格
    table.render({
        elem: '#level-table',
        url: '../controllers/MerchantController.php?action=getLevelList',
        page: true,
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'name', title: '等级名称', width: 120},
            {field: 'level', title: '等级值', width: 100, sort: true},
            {field: 'commission_rate', title: '佣金比例', width: 100, templet: function(d){
                return d.commission_rate + '%';
            }},
            {field: 'api_limit', title: 'API数量限制', width: 120, templet: function(d){
                return d.api_limit > 0 ? d.api_limit : '不限';
            }},
            {field: 'qps_limit', title: 'QPS限制', width: 100, templet: function(d){
                return d.qps_limit > 0 ? d.qps_limit : '不限';
            }},
            {field: 'allow_custom_domain', title: '自定义域名', width: 100, templet: function(d){
                return d.allow_custom_domain == 1 ? '<span class="layui-badge layui-bg-green">允许</span>' : '<span class="layui-badge layui-bg-gray">禁止</span>';
            }},
            {field: 'tech_support', title: '技术支持', width: 100, templet: function(d){
                return d.tech_support == 1 ? '<span class="layui-badge layui-bg-green">提供</span>' : '<span class="layui-badge layui-bg-gray">不提供</span>';
            }},
            {field: 'priority_settlement', title: '优先结算', width: 100, templet: function(d){
                return d.priority_settlement == 1 ? '<span class="layui-badge layui-bg-green">是</span>' : '<span class="layui-badge layui-bg-gray">否</span>';
            }},
            {field: 'status', title: '状态', width: 80, templet: function(d){
                return d.status == 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge layui-bg-gray">禁用</span>';
            }},
            {field: 'merchant_count', title: '商家数量', width: 100},
            {title: '操作', width: 120, align: 'center', toolbar: '#table-bar'}
        ]]
    });
    
    // 表格工具条事件
    table.on('tool(level-table)', function(obj){
        var data = obj.data;
        var event = obj.event;
        
        if(event === 'edit'){
            // 编辑等级
            showEditForm(data);
        } else if(event === 'del'){
            // 删除等级
            layer.confirm('确定要删除该等级吗？', function(index){
                $.ajax({
                    url: '../controllers/MerchantController.php?action=deleteLevel',
                    type: 'POST',
                    data: {id: data.id},
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 200){
                            layer.msg('删除成功');
                            obj.del();
                        } else {
                            layer.msg(res.msg || '删除失败');
                        }
                    },
                    error: function(){
                        layer.msg('服务器错误');
                    }
                });
                layer.close(index);
            });
        }
    });
    
    // 添加等级按钮点击事件
    $('#add-btn').click(function(){
        showEditForm();
    });
    
    // 批量删除按钮点击事件
    $('#del-btn').click(function(){
        var checkStatus = table.checkStatus('level-table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要删除的等级');
            return;
        }
        
        layer.confirm('确定要删除选中的 ' + data.length + ' 个等级吗？', function(index){
            var ids = data.map(function(item){
                return item.id;
            });
            
            $.ajax({
                url: '../controllers/MerchantController.php?action=batchDeleteLevel',
                type: 'POST',
                data: {ids: ids.join(',')},
                dataType: 'json',
                success: function(res){
                    if(res.code === 200){
                        layer.msg('删除成功');
                        table.reload('level-table');
                    } else {
                        layer.msg(res.msg || '删除失败');
                    }
                },
                error: function(){
                    layer.msg('服务器错误');
                }
            });
            
            layer.close(index);
        });
    });
    
    // 表单提交事件
    form.on('submit(level-form-submit)', function(data){
        var formData = data.field;
        
        // 处理复选框值
        formData.allow_custom_domain = formData.allow_custom_domain ? 1 : 0;
        formData.tech_support = formData.tech_support ? 1 : 0;
        formData.priority_settlement = formData.priority_settlement ? 1 : 0;
        
        var url = formData.id ? '../controllers/MerchantController.php?action=updateLevel' : '../controllers/MerchantController.php?action=addLevel';
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(res){
                if(res.code === 200){
                    layer.msg('保存成功');
                    layer.closeAll('page');
                    table.reload('level-table');
                } else {
                    layer.msg(res.msg || '保存失败');
                }
            },
            error: function(){
                layer.msg('服务器错误');
            }
        });
        
        return false;
    });
    
    // 显示编辑表单
    function showEditForm(data){
        var title = data ? '编辑等级' : '添加等级';
        
        layer.open({
            type: 1,
            title: title,
            area: ['600px', '600px'],
            content: $('#level-form-tpl').html(),
            success: function(){
                form.render();
                
                if(data){
                    // 设置表单值
                    form.val('level-form', {
                        'id': data.id,
                        'name': data.name,
                        'level': data.level,
                        'commission_rate': data.commission_rate,
                        'api_limit': data.api_limit,
                        'qps_limit': data.qps_limit,
                        'allow_custom_domain': data.allow_custom_domain == 1,
                        'tech_support': data.tech_support == 1,
                        'priority_settlement': data.priority_settlement == 1,
                        'status': data.status.toString(),
                        'description': data.description
                    });
                }
            }
        });
    }
});
</script>
</body>
</html>