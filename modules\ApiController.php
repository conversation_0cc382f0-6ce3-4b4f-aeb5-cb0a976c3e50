<?php
/**
 * API控制器
 * API管理系统 - API接口管理控制器
 */

require_once __DIR__ . '/ApiInterface.php';
require_once __DIR__ . '/ApiCategory.php';
require_once __DIR__ . '/ApiCallLog.php';
require_once __DIR__ . '/AuthController.php';
require_once __DIR__ . '/Permission.php';

class ApiController {
    private $apiModel;
    private $categoryModel;
    private $callLogModel;
    private $authController;
    private $permission;
    
    public function __construct() {
        $this->apiModel = new ApiInterface();
        $this->categoryModel = new ApiCategory();
        $this->callLogModel = new ApiCallLog();
        $this->authController = new AuthController();
        $this->permission = new Permission();
    }
    
    /**
     * 获取API列表
     */
    public function getApiList() {
        try {
            // 检查权限
            $this->permission->requirePermission('api.view');
            
            $page = $_GET['page'] ?? 1;
            $perPage = $_GET['per_page'] ?? 20;
            $conditions = [];
            
            // 搜索条件
            if (!empty($_GET['name'])) {
                $conditions['name'] = $_GET['name'];
            }
            
            if (!empty($_GET['category_id'])) {
                $conditions['category_id'] = $_GET['category_id'];
            }
            
            if (!empty($_GET['status'])) {
                $conditions['status'] = $_GET['status'];
            }
            
            if (!empty($_GET['method'])) {
                $conditions['method'] = $_GET['method'];
            }
            
            $result = $this->apiModel->getListWithDetails($page, $perPage, $conditions);
            
            System::jsonResponse($result, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取API详情
     */
    public function getApiDetail() {
        try {
            $this->permission->requirePermission('api.view');
            
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception('API ID不能为空');
            }
            
            $api = $this->apiModel->getDetailWithStats($id);
            if (!$api) {
                throw new Exception('API不存在');
            }
            
            System::jsonResponse($api, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 创建API
     */
    public function createApi() {
        try {
            $this->permission->requirePermission('api.create');
            
            $data = [
                'name' => $_POST['name'] ?? '',
                'path' => $_POST['path'] ?? '',
                'method' => $_POST['method'] ?? 'GET',
                'description' => $_POST['description'] ?? '',
                'category_id' => $_POST['category_id'] ?? null,
                'merchant_id' => $_POST['merchant_id'] ?? null,
                'price' => $_POST['price'] ?? 0,
                'request_params' => $_POST['request_params'] ?? [],
                'response_example' => $_POST['response_example'] ?? '',
                'request_headers' => $_POST['request_headers'] ?? [],
                'timeout' => $_POST['timeout'] ?? 30,
                'retry_count' => $_POST['retry_count'] ?? 0,
                'rate_limit' => $_POST['rate_limit'] ?? 60,
                'status' => $_POST['status'] ?? 1,
                'is_public' => $_POST['is_public'] ?? 1
            ];
            
            // 验证必填字段
            if (empty($data['name']) || empty($data['path'])) {
                throw new Exception('API名称和路径不能为空');
            }
            
            // 验证路径格式
            if (!preg_match('/^\/[a-zA-Z0-9\/_-]+$/', $data['path'])) {
                throw new Exception('API路径格式不正确');
            }
            
            $api = $this->apiModel->create($data);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'create_api',
                "API:{$api['id']}",
                "创建API: {$data['name']}"
            );
            
            System::jsonResponse($api, 200, 'API创建成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 更新API
     */
    public function updateApi() {
        try {
            $this->permission->requirePermission('api.edit');
            
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception('API ID不能为空');
            }
            
            $data = [
                'name' => $_POST['name'] ?? '',
                'path' => $_POST['path'] ?? '',
                'method' => $_POST['method'] ?? 'GET',
                'description' => $_POST['description'] ?? '',
                'category_id' => $_POST['category_id'] ?? null,
                'merchant_id' => $_POST['merchant_id'] ?? null,
                'price' => $_POST['price'] ?? 0,
                'request_params' => $_POST['request_params'] ?? [],
                'response_example' => $_POST['response_example'] ?? '',
                'request_headers' => $_POST['request_headers'] ?? [],
                'timeout' => $_POST['timeout'] ?? 30,
                'retry_count' => $_POST['retry_count'] ?? 0,
                'rate_limit' => $_POST['rate_limit'] ?? 60,
                'status' => $_POST['status'] ?? 1,
                'is_public' => $_POST['is_public'] ?? 1
            ];
            
            // 验证必填字段
            if (empty($data['name']) || empty($data['path'])) {
                throw new Exception('API名称和路径不能为空');
            }
            
            $api = $this->apiModel->update($id, $data);
            if (!$api) {
                throw new Exception('API更新失败');
            }
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'update_api',
                "API:{$id}",
                "更新API: {$data['name']}"
            );
            
            System::jsonResponse($api, 200, 'API更新成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 删除API
     */
    public function deleteApi() {
        try {
            $this->permission->requirePermission('api.delete');
            
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception('API ID不能为空');
            }
            
            $api = $this->apiModel->find($id);
            if (!$api) {
                throw new Exception('API不存在');
            }
            
            $result = $this->apiModel->delete($id);
            if (!$result) {
                throw new Exception('API删除失败');
            }
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'delete_api',
                "API:{$id}",
                "删除API: {$api['name']}"
            );
            
            System::jsonResponse(null, 200, 'API删除成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 批量操作API
     */
    public function batchApiOperation() {
        try {
            $this->permission->requirePermission('api.edit');
            
            $action = $_POST['action'] ?? '';
            $ids = $_POST['ids'] ?? [];
            
            if (empty($action) || empty($ids)) {
                throw new Exception('操作类型和ID列表不能为空');
            }
            
            $result = false;
            
            switch ($action) {
                case 'enable':
                    $result = $this->apiModel->batchUpdateStatus($ids, 1);
                    $message = 'API批量启用成功';
                    break;
                    
                case 'disable':
                    $result = $this->apiModel->batchUpdateStatus($ids, 0);
                    $message = 'API批量禁用成功';
                    break;
                    
                case 'delete':
                    $this->permission->requirePermission('api.delete');
                    $result = $this->apiModel->deleteMany($ids);
                    $message = 'API批量删除成功';
                    break;
                    
                default:
                    throw new Exception('不支持的操作类型');
            }
            
            if (!$result) {
                throw new Exception('批量操作失败');
            }
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                "batch_{$action}_api",
                "APIs:" . implode(',', $ids),
                "批量{$action}API"
            );
            
            System::jsonResponse(null, 200, $message);
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * API在线调试
     */
    public function debugApi() {
        try {
            $this->permission->requirePermission('api.debug');
            
            $id = $_POST['id'] ?? 0;
            $params = $_POST['params'] ?? [];
            $headers = $_POST['headers'] ?? [];
            
            if (!$id) {
                throw new Exception('API ID不能为空');
            }
            
            $api = $this->apiModel->find($id);
            if (!$api) {
                throw new Exception('API不存在');
            }
            
            // 验证参数
            $errors = $this->apiModel->validateApiParams($id, $params);
            if (!empty($errors)) {
                throw new Exception('参数验证失败: ' . implode(', ', $errors));
            }
            
            // 执行API调用
            $startTime = microtime(true);
            $result = $this->executeApiCall($api, $params, $headers);
            $responseTime = round((microtime(true) - $startTime) * 1000);
            
            // 记录调试日志
            $this->callLogModel->logApiCall([
                'user_id' => null, // 管理员调试
                'api_id' => $id,
                'request_ip' => System::getClientIp(),
                'request_method' => $api['method'],
                'request_params' => $params,
                'request_headers' => $headers,
                'response_code' => $result['code'],
                'response_data' => $result['data'],
                'response_time' => $responseTime,
                'cost' => 0, // 调试不收费
                'status' => $result['success'] ? 1 : 0,
                'error_message' => $result['error'] ?? null
            ]);
            
            System::jsonResponse([
                'response_code' => $result['code'],
                'response_data' => $result['data'],
                'response_time' => $responseTime,
                'success' => $result['success'],
                'error' => $result['error'] ?? null
            ], 200, '调试完成');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 执行API调用
     */
    private function executeApiCall($api, $params, $headers) {
        try {
            // 构建请求URL
            $url = $api['path'];
            
            // 初始化cURL
            $ch = curl_init();
            
            // 设置基本选项
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $api['timeout'],
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_USERAGENT => 'API-System-Debug/1.0'
            ]);
            
            // 设置请求方法和参数
            switch (strtoupper($api['method'])) {
                case 'POST':
                    curl_setopt($ch, CURLOPT_POST, true);
                    if (!empty($params)) {
                        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
                        $headers['Content-Type'] = 'application/json';
                    }
                    break;
                    
                case 'PUT':
                    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                    if (!empty($params)) {
                        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
                        $headers['Content-Type'] = 'application/json';
                    }
                    break;
                    
                case 'DELETE':
                    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                    break;
                    
                default: // GET
                    if (!empty($params)) {
                        $url .= '?' . http_build_query($params);
                        curl_setopt($ch, CURLOPT_URL, $url);
                    }
                    break;
            }
            
            // 设置请求头
            if (!empty($headers)) {
                $headerArray = [];
                foreach ($headers as $key => $value) {
                    $headerArray[] = "{$key}: {$value}";
                }
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
            }
            
            // 执行请求
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            
            curl_close($ch);
            
            if ($error) {
                return [
                    'success' => false,
                    'code' => 0,
                    'data' => null,
                    'error' => $error
                ];
            }
            
            // 尝试解析JSON响应
            $decodedResponse = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $response = $decodedResponse;
            }
            
            return [
                'success' => $httpCode >= 200 && $httpCode < 300,
                'code' => $httpCode,
                'data' => $response,
                'error' => $httpCode >= 400 ? "HTTP {$httpCode}" : null
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'code' => 0,
                'data' => null,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取API统计信息
     */
    public function getApiStats() {
        try {
            $this->permission->requirePermission('api.stats');
            
            $stats = $this->apiModel->getApiStats();
            $hotApis = $this->apiModel->getHotApis(10);
            $callTrend = $this->apiModel->getCallTrend(null, 7);
            
            System::jsonResponse([
                'stats' => $stats,
                'hot_apis' => $hotApis,
                'call_trend' => $callTrend
            ], 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 复制API
     */
    public function copyApi() {
        try {
            $this->permission->requirePermission('api.create');
            
            $id = $_POST['id'] ?? 0;
            $newName = $_POST['new_name'] ?? null;
            $newPath = $_POST['new_path'] ?? null;
            
            if (!$id) {
                throw new Exception('API ID不能为空');
            }
            
            $newApi = $this->apiModel->copyApi($id, $newName, $newPath);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'copy_api',
                "API:{$id} -> API:{$newApi['id']}",
                "复制API"
            );
            
            System::jsonResponse($newApi, 200, 'API复制成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 导出API配置
     */
    public function exportApi() {
        try {
            $this->permission->requirePermission('api.view');
            
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception('API ID不能为空');
            }
            
            $config = $this->apiModel->exportApi($id);
            
            // 设置下载头
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="api_' . $id . '_config.json"');
            
            echo json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            exit;
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 导入API配置
     */
    public function importApi() {
        try {
            $this->permission->requirePermission('api.create');
            
            if (!isset($_FILES['config_file'])) {
                throw new Exception('请选择配置文件');
            }
            
            $file = $_FILES['config_file'];
            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('文件上传失败');
            }
            
            $config = json_decode(file_get_contents($file['tmp_name']), true);
            if (!$config) {
                throw new Exception('配置文件格式错误');
            }
            
            $categoryId = $_POST['category_id'] ?? null;
            $merchantId = $_POST['merchant_id'] ?? null;
            
            $api = $this->apiModel->importApi($config, $categoryId, $merchantId);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'import_api',
                "API:{$api['id']}",
                "导入API: {$config['name']}"
            );
            
            System::jsonResponse($api, 200, 'API导入成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取API分类列表
     */
    public function getCategoryList() {
        try {
            $this->permission->requirePermission('api.view');
            
            $categories = $this->categoryModel->getActiveCategories();
            
            System::jsonResponse($categories, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 生成API文档
     */
    public function generateApiDoc() {
        try {
            $this->permission->requirePermission('api.view');
            
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception('API ID不能为空');
            }
            
            $api = $this->apiModel->getDetailWithStats($id);
            if (!$api) {
                throw new Exception('API不存在');
            }
            
            // 生成Markdown格式文档
            $doc = $this->generateMarkdownDoc($api);
            
            System::jsonResponse([
                'markdown' => $doc,
                'html' => $this->markdownToHtml($doc)
            ], 200, '文档生成成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 生成Markdown文档
     */
    private function generateMarkdownDoc($api) {
        $doc = "# {$api['name']}\n\n";
        $doc .= "## 基本信息\n\n";
        $doc .= "- **接口路径**: `{$api['path']}`\n";
        $doc .= "- **请求方法**: `{$api['method']}`\n";
        $doc .= "- **接口价格**: ¥{$api['price']}\n";
        $doc .= "- **超时时间**: {$api['timeout']}秒\n";
        $doc .= "- **频率限制**: {$api['rate_limit']}次/分钟\n\n";
        
        if (!empty($api['description'])) {
            $doc .= "## 接口描述\n\n";
            $doc .= "{$api['description']}\n\n";
        }
        
        if (!empty($api['request_params'])) {
            $doc .= "## 请求参数\n\n";
            $doc .= "| 参数名 | 类型 | 必填 | 描述 |\n";
            $doc .= "|--------|------|------|------|\n";
            
            foreach ($api['request_params'] as $param) {
                $required = ($param['required'] ?? false) ? '是' : '否';
                $doc .= "| {$param['name']} | {$param['type']} | {$required} | {$param['description']} |\n";
            }
            $doc .= "\n";
        }
        
        if (!empty($api['response_example'])) {
            $doc .= "## 响应示例\n\n";
            $doc .= "```json\n";
            $doc .= $api['response_example'];
            $doc .= "\n```\n\n";
        }
        
        if (!empty($api['stats'])) {
            $doc .= "## 统计信息\n\n";
            $doc .= "- **总调用次数**: {$api['stats']['total_calls']}\n";
            $doc .= "- **成功次数**: {$api['stats']['success_calls']}\n";
            $doc .= "- **成功率**: {$api['success_rate']}%\n";
            $doc .= "- **平均响应时间**: " . round($api['stats']['avg_response_time']) . "ms\n\n";
        }
        
        return $doc;
    }
    
    /**
     * Markdown转HTML（简单实现）
     */
    private function markdownToHtml($markdown) {
        // 简单的Markdown转HTML实现
        $html = $markdown;
        
        // 标题
        $html = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $html);
        $html = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $html);
        $html = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $html);
        
        // 代码块
        $html = preg_replace('/```(\w+)?\n(.*?)\n```/s', '<pre><code>$2</code></pre>', $html);
        
        // 行内代码
        $html = preg_replace('/`([^`]+)`/', '<code>$1</code>', $html);
        
        // 表格（简单处理）
        $html = preg_replace('/\|(.+)\|/', '<tr><td>$1</td></tr>', $html);
        
        // 换行
        $html = nl2br($html);
        
        return $html;
    }
}