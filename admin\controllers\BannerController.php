<?php
/**
 * 轮播图管理控制器
 */
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

class BannerController {
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new Auth();
        
        // 检查管理员权限
        $this->checkAdminAuth();
    }
    
    /**
     * 获取轮播图列表
     */
    public function getList() {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $title = $_GET['title'] ?? '';
        
        $offset = ($page - 1) * $limit;
        $where = "1=1";
        $params = [];
        
        if (!empty($title)) {
            $where .= " AND title LIKE ?";
            $params[] = '%' . $title . '%';
        }
        
        // 获取总数
        $total = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM banners WHERE {$where}",
            $params
        )['count'];
        
        // 获取数据
        $data = $this->db->fetchAll(
            "SELECT * FROM banners WHERE {$where} ORDER BY sort_order ASC, id DESC LIMIT {$limit} OFFSET {$offset}",
            $params
        );
        
        $this->jsonResponse([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data
        ]);
    }
    
    /**
     * 添加轮播图
     */
    public function add() {
        $title = $_POST['title'] ?? '';
        $image_url = $_POST['image_url'] ?? '';
        $link_url = $_POST['link_url'] ?? '';
        $sort_order = $_POST['sort_order'] ?? 0;
        $status = $_POST['status'] ?? 1;
        $description = $_POST['description'] ?? '';
        
        if (empty($title) || empty($image_url)) {
            $this->jsonResponse(['code' => 400, 'msg' => '标题和图片不能为空']);
        }
        
        try {
            $this->db->insert('banners', [
                'title' => $title,
                'image_url' => $image_url,
                'link_url' => $link_url,
                'sort_order' => $sort_order,
                'status' => $status,
                'description' => $description,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $this->jsonResponse(['code' => 200, 'msg' => '添加成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '添加失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 更新轮播图
     */
    public function update() {
        $id = $_POST['id'] ?? 0;
        $title = $_POST['title'] ?? '';
        $image_url = $_POST['image_url'] ?? '';
        $link_url = $_POST['link_url'] ?? '';
        $sort_order = $_POST['sort_order'] ?? 0;
        $status = $_POST['status'] ?? 1;
        $description = $_POST['description'] ?? '';
        
        if (empty($id) || empty($title) || empty($image_url)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数不完整']);
        }
        
        try {
            $this->db->update('banners', [
                'title' => $title,
                'image_url' => $image_url,
                'link_url' => $link_url,
                'sort_order' => $sort_order,
                'status' => $status,
                'description' => $description,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$id]);
            
            $this->jsonResponse(['code' => 200, 'msg' => '更新成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 删除轮播图
     */
    public function delete() {
        $id = $_POST['id'] ?? 0;
        
        if (empty($id)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        try {
            $this->db->delete('banners', 'id = ?', [$id]);
            $this->jsonResponse(['code' => 200, 'msg' => '删除成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 批量删除轮播图
     */
    public function batchDelete() {
        $ids = $_POST['ids'] ?? [];
        
        if (empty($ids) || !is_array($ids)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        try {
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $this->db->getConnection()->prepare("DELETE FROM banners WHERE id IN ({$placeholders})")->execute($ids);
            
            $this->jsonResponse(['code' => 200, 'msg' => '批量删除成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '批量删除失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 检查管理员权限
     */
    private function checkAdminAuth() {
        if (!$this->auth->checkAuth() || !$this->auth->isAdmin()) {
            $this->jsonResponse(['code' => 401, 'msg' => '无权限访问']);
        }
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// 处理请求
$action = $_GET['action'] ?? '';
$controller = new BannerController();

switch ($action) {
    case 'getList':
        $controller->getList();
        break;
    case 'add':
        $controller->add();
        break;
    case 'update':
        $controller->update();
        break;
    case 'delete':
        $controller->delete();
        break;
    case 'batchDelete':
        $controller->batchDelete();
        break;
    default:
        header('HTTP/1.1 404 Not Found');
        echo json_encode(['code' => 404, 'msg' => '接口不存在']);
        break;
}