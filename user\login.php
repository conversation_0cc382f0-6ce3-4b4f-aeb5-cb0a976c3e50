<?php
// 检查是否已登录
if (isset($_SESSION['user_id'])) {
    header('Location: /user/profile');
    exit;
}

// 处理登录请求
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = '用户名和密码不能为空';
    } else {
        // 查询用户
        $stmt = $pdo->prepare("SELECT * FROM api_users WHERE (username = ? OR email = ?) AND status = 1");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // 登录成功
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_avatar'] = $user['avatar'];
            
            // 更新最后登录时间和IP
            $stmt = $pdo->prepare("UPDATE api_users SET last_login = NOW(), last_ip = ? WHERE id = ?");
            $stmt->execute([$_SERVER['REMOTE_ADDR'], $user['id']]);
            
            // 跳转到用户中心
            header('Location: /user/profile');
            exit;
        } else {
            $error = '用户名或密码错误';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="/public/css/font-awesome.min.css">
    <link rel="stylesheet" href="/public/css/style.css">
</head>
<body class="bg-light">
    <!-- 头部导航 -->
    <?php include '../templates/common/header.php'; ?>
    
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">用户登录</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <form method="post" action="">
                            <div class="form-group">
                                <label for="username">用户名/邮箱</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-user"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名或邮箱" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="password">密码</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-lock"></i></span>
                                    </div>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                                </div>
                            </div>
                            <div class="form-group form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                                <label class="form-check-label" for="remember">记住我</label>
                                <a href="/user/forgot-password" class="float-right">忘记密码?</a>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block">登录</button>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <p>还没有账号? <a href="/user/register">立即注册</a></p>
                        </div>
                        
                        <?php if (!empty($siteConfig['enable_social_login'])): ?>
                        <div class="mt-4">
                            <p class="text-center">使用第三方账号登录</p>
                            <div class="d-flex justify-content-center">
                                <?php if (!empty($siteConfig['enable_qq_login'])): ?>
                                <a href="/user/oauth?type=qq" class="btn btn-light mx-2"><i class="fa fa-qq text-primary"></i> QQ登录</a>
                                <?php endif; ?>
                                
                                <?php if (!empty($siteConfig['enable_wechat_login'])): ?>
                                <a href="/user/oauth?type=wechat" class="btn btn-light mx-2"><i class="fa fa-wechat text-success"></i> 微信登录</a>
                                <?php endif; ?>
                                
                                <?php if (!empty($siteConfig['enable_weibo_login'])): ?>
                                <a href="/user/oauth?type=weibo" class="btn btn-light mx-2"><i class="fa fa-weibo text-danger"></i> 微博登录</a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <?php include '../templates/common/footer.php'; ?>
    
    <script src="/public/js/jquery-3.5.1.min.js"></script>
    <script src="/public/js/bootstrap.bundle.min.js"></script>
    <script src="/public/js/main.js"></script>
</body>
</html>