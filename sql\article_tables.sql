-- 文章分类表
CREATE TABLE IF NOT EXISTS `article_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表';

-- 文章表
CREATE TABLE IF NOT EXISTS `articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '文章标题',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `summary` varchar(500) DEFAULT NULL COMMENT '摘要',
  `content` text NOT NULL COMMENT '文章内容',
  `keywords` varchar(255) DEFAULT NULL COMMENT '关键词',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览量',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0草稿，1发布',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_time` (`sort`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 插入一些测试数据
INSERT INTO `article_categories` (`id`, `name`, `sort`, `description`, `create_time`) VALUES
(1, 'API教程', 0, 'API使用教程和最佳实践', UNIX_TIMESTAMP()),
(2, '技术资讯', 0, '最新技术资讯和行业动态', UNIX_TIMESTAMP()),
(3, '常见问题', 0, '常见问题解答和故障排除', UNIX_TIMESTAMP());

INSERT INTO `articles` (`title`, `category_id`, `author`, `cover`, `summary`, `content`, `keywords`, `view_count`, `sort`, `status`, `create_time`, `update_time`) VALUES
('API系统使用入门指南', 1, '管理员', '/assets/images/article/api-guide.jpg', '本文将介绍如何快速上手使用我们的API商业系统，包括账号注册、API调用、参数配置等基础知识。', '<h2>API系统使用入门指南</h2><p>欢迎使用我们的API商业系统，本文将帮助您快速上手使用我们的服务。</p><h3>1. 注册账号</h3><p>首先，您需要在我们的网站上注册一个账号。注册过程非常简单，只需提供您的邮箱地址和设置密码即可。</p><h3>2. 获取API密钥</h3><p>注册成功后，登录到您的控制面板，在"API密钥"页面可以生成您的API密钥。请妥善保管您的密钥，不要泄露给他人。</p><h3>3. 调用API</h3><p>使用您的API密钥，您可以开始调用我们提供的各种API服务。我们支持多种编程语言的调用方式，包括PHP、Python、Java等。</p><h3>4. 查看调用统计</h3><p>在控制面板中，您可以实时查看API的调用统计信息，包括调用次数、成功率、响应时间等指标。</p><h3>5. 账单与支付</h3><p>我们采用按量计费的模式，您可以在"账单中心"查看您的消费情况，并进行在线充值。</p><p>如果您在使用过程中遇到任何问题，欢迎联系我们的客服团队。</p>', 'API,入门指南,教程', 100, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('如何提高API调用效率', 1, '技术专家', '/assets/images/article/api-efficiency.jpg', '本文将分享一些提高API调用效率的技巧和最佳实践，帮助您优化API使用体验。', '<h2>如何提高API调用效率</h2><p>在使用API服务时，提高调用效率不仅可以节省成本，还能提升用户体验。本文将分享几个实用技巧。</p><h3>1. 使用批量接口</h3><p>尽量使用批量接口，一次请求处理多条数据，减少HTTP请求次数。</p><h3>2. 实施缓存策略</h3><p>对于不经常变化的数据，可以在客户端实施缓存策略，避免重复请求。</p><h3>3. 压缩传输数据</h3><p>使用gzip等压缩算法压缩请求和响应数据，减少传输量。</p><h3>4. 选择合适的数据格式</h3><p>根据实际需求选择合适的数据格式，如JSON、XML等。</p><h3>5. 优化请求频率</h3><p>合理控制请求频率，避免频繁请求导致的性能问题。</p><p>通过以上技巧，您可以显著提高API调用的效率和性能。</p>', 'API,效率,优化,最佳实践', 85, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('API安全最佳实践', 1, '安全专家', '/assets/images/article/api-security.jpg', '保障API调用的安全性至关重要，本文将介绍几种常见的API安全防护措施和最佳实践。', '<h2>API安全最佳实践</h2><p>随着API使用的普及，API安全问题也日益凸显。本文将介绍几种保障API安全的最佳实践。</p><h3>1. 使用HTTPS</h3><p>始终使用HTTPS协议进行API通信，确保数据传输的安全性。</p><h3>2. 实施身份认证</h3><p>使用OAuth、JWT等认证机制，确保只有授权用户才能访问API。</p><h3>3. 实施访问控制</h3><p>根据用户角色和权限，限制对特定API的访问。</p><h3>4. 防范常见攻击</h3><p>防范SQL注入、XSS、CSRF等常见Web攻击。</p><h3>5. 限制请求频率</h3><p>实施请求频率限制，防止恶意用户进行DoS攻击。</p><p>通过以上措施，可以有效提高API的安全性，保护您的数据和系统。</p>', 'API,安全,最佳实践,防护', 120, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('API行业发展趋势分析', 2, '行业分析师', '/assets/images/article/api-trends.jpg', '本文将分析当前API行业的发展趋势，包括技术演进、商业模式创新等方面的最新动态。', '<h2>API行业发展趋势分析</h2><p>近年来，API经济蓬勃发展，成为数字化转型的重要推动力。本文将分析当前API行业的几个主要发展趋势。</p><h3>1. API优先策略</h3><p>越来越多的企业采用"API优先"策略，将API作为产品设计和开发的核心。</p><h3>2. 微服务架构</h3><p>微服务架构的普及推动了API的广泛应用，使系统更加灵活和可扩展。</p><h3>3. 无服务器计算</h3><p>无服务器计算（Serverless）的兴起为API提供了新的部署和扩展方式。</p><h3>4. API市场化</h3><p>API作为商品进行交易的模式日益成熟，形成了丰富的API经济生态。</p><h3>5. 人工智能API</h3><p>AI和机器学习API的兴起，使开发者能够轻松集成智能功能。</p><p>了解这些趋势，有助于企业制定更加前瞻性的API战略。</p>', 'API,趋势,发展,分析', 95, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('常见API调用问题及解决方案', 3, '技术支持', '/assets/images/article/api-faq.jpg', '本文总结了用户在使用API过程中常见的问题和对应的解决方案，帮助您快速排除故障。', '<h2>常见API调用问题及解决方案</h2><p>在使用API的过程中，用户可能会遇到各种问题。本文总结了几个常见问题及其解决方案。</p><h3>1. 认证失败</h3><p><strong>问题：</strong>API返回401错误，表示认证失败。</p><p><strong>解决方案：</strong>检查API密钥是否正确，是否已过期，以及认证方式是否正确。</p><h3>2. 请求超时</h3><p><strong>问题：</strong>API请求长时间无响应，最终超时。</p><p><strong>解决方案：</strong>检查网络连接，增加超时设置，或者联系API提供商查询服务器状态。</p><h3>3. 参数错误</h3><p><strong>问题：</strong>API返回400错误，表示请求参数有误。</p><p><strong>解决方案：</strong>仔细检查API文档，确保所有必需参数都已提供，并且格式正确。</p><h3>4. 请求频率限制</h3><p><strong>问题：</strong>API返回429错误，表示请求频率超过限制。</p><p><strong>解决方案：</strong>实施请求队列或延迟机制，控制请求频率。</p><h3>5. 服务器错误</h3><p><strong>问题：</strong>API返回500错误，表示服务器内部错误。</p><p><strong>解决方案：</strong>联系API提供商报告问题，同时可以尝试稍后重试。</p><p>如果您遇到其他问题，欢迎联系我们的技术支持团队。</p>', 'API,问题,解决方案,故障排除', 150, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());