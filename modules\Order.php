<?php
/**
 * 订单管理模型
 * API管理系统 - 订单管理
 */

require_once __DIR__ . '/../core/Model.php';

class Order extends Model {
    protected $table = 'orders';
    protected $fillable = [
        'user_id', 'api_id', 'merchant_id', 'order_no', 'amount', 'quantity',
        'unit_price', 'status', 'payment_method', 'payment_status', 'paid_at',
        'expired_at', 'refund_amount', 'refund_reason', 'refunded_at'
    ];
    
    // 订单状态常量
    const STATUS_PENDING = 0;    // 待支付
    const STATUS_PAID = 1;       // 已支付
    const STATUS_PROCESSING = 2; // 处理中
    const STATUS_COMPLETED = 3;  // 已完成
    const STATUS_CANCELLED = 4;  // 已取消
    const STATUS_REFUNDED = 5;   // 已退款
    
    // 支付状态常量
    const PAYMENT_UNPAID = 0;    // 未支付
    const PAYMENT_PAID = 1;      // 已支付
    const PAYMENT_REFUNDED = 2;  // 已退款
    
    /**
     * 创建订单
     */
    public function createOrder($data) {
        // 生成订单号
        $data['order_no'] = $this->generateOrderNo();
        
        // 设置过期时间（30分钟）
        $data['expired_at'] = date('Y-m-d H:i:s', time() + 1800);
        
        // 设置初始状态
        $data['status'] = self::STATUS_PENDING;
        $data['payment_status'] = self::PAYMENT_UNPAID;
        
        // 计算总金额
        $data['amount'] = $data['unit_price'] * $data['quantity'];
        
        return $this->create($data);
    }
    
    /**
     * 生成订单号
     */
    private function generateOrderNo() {
        return 'ORD' . date('YmdHis') . rand(1000, 9999);
    }
    
    /**
     * 获取订单列表（带用户和API信息）
     */
    public function getListWithDetails($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        $whereClause = [];
        $params = [];
        
        // 构建查询条件
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "o.user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['api_id'])) {
            $whereClause[] = "o.api_id = :api_id";
            $params['api_id'] = $conditions['api_id'];
        }
        
        if (!empty($conditions['merchant_id'])) {
            $whereClause[] = "o.merchant_id = :merchant_id";
            $params['merchant_id'] = $conditions['merchant_id'];
        }
        
        if (!empty($conditions['status'])) {
            $whereClause[] = "o.status = :status";
            $params['status'] = $conditions['status'];
        }
        
        if (!empty($conditions['payment_status'])) {
            $whereClause[] = "o.payment_status = :payment_status";
            $params['payment_status'] = $conditions['payment_status'];
        }
        
        if (!empty($conditions['order_no'])) {
            $whereClause[] = "o.order_no LIKE :order_no";
            $params['order_no'] = '%' . $conditions['order_no'] . '%';
        }
        
        if (!empty($conditions['start_date'])) {
            $whereClause[] = "o.created_at >= :start_date";
            $params['start_date'] = $conditions['start_date'];
        }
        
        if (!empty($conditions['end_date'])) {
            $whereClause[] = "o.created_at <= :end_date";
            $params['end_date'] = $conditions['end_date'];
        }
        
        $whereSQL = empty($whereClause) ? '' : 'WHERE ' . implode(' AND ', $whereClause);
        
        // 查询总数
        $countSQL = "SELECT COUNT(*) as total 
                     FROM {$this->getTableName()} o 
                     LEFT JOIN users u ON o.user_id = u.id 
                     LEFT JOIN apis a ON o.api_id = a.id 
                     LEFT JOIN merchants m ON o.merchant_id = m.id 
                     {$whereSQL}";
        
        $totalResult = $this->db->fetchOne($countSQL, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSQL = "SELECT o.*, 
                           u.username, u.email as user_email,
                           a.name as api_name, a.path as api_path,
                           m.company_name as merchant_name
                    FROM {$this->getTableName()} o 
                    LEFT JOIN users u ON o.user_id = u.id 
                    LEFT JOIN apis a ON o.api_id = a.id 
                    LEFT JOIN merchants m ON o.merchant_id = m.id 
                    {$whereSQL}
                    ORDER BY o.id DESC 
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSQL, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取订单详情
     */
    public function getOrderDetail($id) {
        $sql = "SELECT o.*, 
                       u.username, u.email as user_email, u.phone as user_phone,
                       a.name as api_name, a.path as api_path, a.description as api_description,
                       m.company_name as merchant_name, m.contact_name as merchant_contact
                FROM {$this->getTableName()} o 
                LEFT JOIN users u ON o.user_id = u.id 
                LEFT JOIN apis a ON o.api_id = a.id 
                LEFT JOIN merchants m ON o.merchant_id = m.id 
                WHERE o.id = :id";
        
        return $this->db->fetchOne($sql, ['id' => $id]);
    }
    
    /**
     * 支付订单
     */
    public function payOrder($orderId, $paymentMethod = 'balance') {
        $order = $this->find($orderId);
        if (!$order) {
            throw new Exception('订单不存在');
        }
        
        if ($order['status'] != self::STATUS_PENDING) {
            throw new Exception('订单状态不正确');
        }
        
        if ($order['payment_status'] == self::PAYMENT_PAID) {
            throw new Exception('订单已支付');
        }
        
        // 检查订单是否过期
        if (strtotime($order['expired_at']) < time()) {
            $this->update($orderId, ['status' => self::STATUS_CANCELLED]);
            throw new Exception('订单已过期');
        }
        
        try {
            $this->db->getPdo()->beginTransaction();
            
            if ($paymentMethod == 'balance') {
                // 余额支付
                $userModel = new User();
                $user = $userModel->find($order['user_id']);
                
                if ($user['balance'] < $order['amount']) {
                    throw new Exception('余额不足');
                }
                
                // 扣除用户余额
                $userModel->update($order['user_id'], [
                    'balance' => $user['balance'] - $order['amount']
                ]);
                
                // 更新订单状态
                $this->update($orderId, [
                    'status' => self::STATUS_PAID,
                    'payment_status' => self::PAYMENT_PAID,
                    'payment_method' => $paymentMethod,
                    'paid_at' => date('Y-m-d H:i:s')
                ]);
                
                // 记录余额变动
                $this->recordBalanceChange($order['user_id'], -$order['amount'], '购买API', $order['order_no']);
                
                // 增加商家收入
                if ($order['merchant_id']) {
                    $this->addMerchantRevenue($order['merchant_id'], $order['amount'], $orderId);
                }
                
            } else {
                // 第三方支付，创建支付记录
                $paymentModel = new Payment();
                $payment = $paymentModel->createPayment([
                    'user_id' => $order['user_id'],
                    'amount' => $order['amount'],
                    'payment_method' => $paymentMethod,
                    'payment_channel' => $paymentMethod
                ]);
                
                // 更新订单支付方式
                $this->update($orderId, [
                    'payment_method' => $paymentMethod
                ]);
                
                $this->db->getPdo()->commit();
                
                // 返回支付信息
                if ($paymentMethod == Payment::METHOD_ALIPAY) {
                    return $paymentModel->alipayPay($payment['id']);
                } else if ($paymentMethod == Payment::METHOD_WECHAT) {
                    return $paymentModel->wechatPay($payment['id']);
                }
            }
            
            $this->db->getPdo()->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->getPdo()->rollBack();
            throw $e;
        }
    }
    
    /**
     * 取消订单
     */
    public function cancelOrder($orderId, $reason = '') {
        $order = $this->find($orderId);
        if (!$order) {
            throw new Exception('订单不存在');
        }
        
        if ($order['status'] != self::STATUS_PENDING) {
            throw new Exception('只能取消待支付的订单');
        }
        
        $this->update($orderId, [
            'status' => self::STATUS_CANCELLED,
            'refund_reason' => $reason
        ]);
        
        return true;
    }
    
    /**
     * 退款订单
     */
    public function refundOrder($orderId, $refundAmount = null, $reason = '') {
        $order = $this->find($orderId);
        if (!$order) {
            throw new Exception('订单不存在');
        }
        
        if ($order['payment_status'] != self::PAYMENT_PAID) {
            throw new Exception('订单未支付，无法退款');
        }
        
        if ($order['status'] == self::STATUS_REFUNDED) {
            throw new Exception('订单已退款');
        }
        
        // 如果未指定退款金额，则全额退款
        if ($refundAmount === null) {
            $refundAmount = $order['amount'];
        }
        
        if ($refundAmount > $order['amount']) {
            throw new Exception('退款金额不能超过订单金额');
        }
        
        try {
            $this->db->getPdo()->beginTransaction();
            
            // 更新订单状态
            $this->update($orderId, [
                'status' => self::STATUS_REFUNDED,
                'payment_status' => self::PAYMENT_REFUNDED,
                'refund_amount' => $refundAmount,
                'refund_reason' => $reason,
                'refunded_at' => date('Y-m-d H:i:s')
            ]);
            
            // 退还用户余额
            $userModel = new User();
            $user = $userModel->find($order['user_id']);
            $userModel->update($order['user_id'], [
                'balance' => $user['balance'] + $refundAmount
            ]);
            
            // 记录余额变动
            $this->recordBalanceChange($order['user_id'], $refundAmount, '订单退款', $order['order_no']);
            
            // 扣除商家收入
            if ($order['merchant_id']) {
                $this->deductMerchantRevenue($order['merchant_id'], $refundAmount, $orderId);
            }
            
            $this->db->getPdo()->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->getPdo()->rollBack();
            throw $e;
        }
    }
    
    /**
     * 完成订单
     */
    public function completeOrder($orderId) {
        $order = $this->find($orderId);
        if (!$order) {
            throw new Exception('订单不存在');
        }
        
        if ($order['status'] != self::STATUS_PAID) {
            throw new Exception('订单状态不正确');
        }
        
        $this->update($orderId, [
            'status' => self::STATUS_COMPLETED
        ]);
        
        return true;
    }
    
    /**
     * 获取订单统计
     */
    public function getOrderStats($startDate = null, $endDate = null, $userId = null) {
        $conditions = [];
        $params = [];
        
        if ($startDate) {
            $conditions[] = "created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        if ($userId) {
            $conditions[] = "user_id = :user_id";
            $params['user_id'] = $userId;
        }
        
        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);
        
        $sql = "SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,
                    SUM(CASE WHEN payment_status = 1 THEN amount ELSE 0 END) as total_amount,
                    SUM(CASE WHEN status = 5 THEN refund_amount ELSE 0 END) as total_refund,
                    AVG(CASE WHEN payment_status = 1 THEN amount ELSE NULL END) as avg_amount
                FROM {$this->getTableName()} 
                {$whereClause}";
        
        return $this->db->fetchOne($sql, $params);
    }
    
    /**
     * 获取热门API统计
     */
    public function getPopularApis($limit = 10, $startDate = null, $endDate = null) {
        $conditions = ['payment_status = 1']; // 只统计已支付订单
        $params = [];
        
        if ($startDate) {
            $conditions[] = "o.created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "o.created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        
        $sql = "SELECT 
                    a.id, a.name, a.path,
                    COUNT(*) as order_count,
                    SUM(o.quantity) as total_quantity,
                    SUM(o.amount) as total_amount
                FROM {$this->getTableName()} o
                LEFT JOIN apis a ON o.api_id = a.id
                {$whereClause}
                GROUP BY o.api_id
                ORDER BY order_count DESC, total_amount DESC
                LIMIT {$limit}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取用户订单历史
     */
    public function getUserOrders($userId, $page = 1, $perPage = 20, $status = null) {
        $conditions = ['o.user_id = :user_id'];
        $params = ['user_id' => $userId];
        
        if ($status !== null) {
            $conditions[] = "o.status = :status";
            $params['status'] = $status;
        }
        
        return $this->getListWithDetails($page, $perPage, $params);
    }
    
    /**
     * 获取商家订单
     */
    public function getMerchantOrders($merchantId, $page = 1, $perPage = 20, $status = null) {
        $conditions = ['o.merchant_id = :merchant_id'];
        $params = ['merchant_id' => $merchantId];
        
        if ($status !== null) {
            $conditions[] = "o.status = :status";
            $params['status'] = $status;
        }
        
        return $this->getListWithDetails($page, $perPage, $params);
    }
    
    /**
     * 记录余额变动
     */
    private function recordBalanceChange($userId, $amount, $type, $orderNo) {
        $sql = "INSERT INTO balance_logs (user_id, amount, type, order_no, created_at) VALUES (?, ?, ?, ?, ?)";
        $this->db->query($sql, [$userId, $amount, $type, $orderNo, date('Y-m-d H:i:s')]);
    }
    
    /**
     * 增加商家收入
     */
    private function addMerchantRevenue($merchantId, $amount, $orderId) {
        // 获取商家信息
        $merchantModel = new Merchant();
        $merchant = $merchantModel->find($merchantId);
        if (!$merchant) {
            return;
        }
        
        // 计算佣金
        $commissionRate = $merchant['commission_rate'] / 100;
        $commission = $amount * $commissionRate;
        $revenue = $amount - $commission;
        
        // 更新商家余额和总收入
        $merchantModel->update($merchantId, [
            'balance' => $merchant['balance'] + $revenue,
            'total_revenue' => $merchant['total_revenue'] + $revenue
        ]);
        
        // 记录收入日志
        $sql = "INSERT INTO merchant_revenue_logs (merchant_id, order_id, amount, commission, revenue, created_at) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $this->db->query($sql, [$merchantId, $orderId, $amount, $commission, $revenue, date('Y-m-d H:i:s')]);
    }
    
    /**
     * 扣除商家收入
     */
    private function deductMerchantRevenue($merchantId, $amount, $orderId) {
        // 获取商家信息
        $merchantModel = new Merchant();
        $merchant = $merchantModel->find($merchantId);
        if (!$merchant) {
            return;
        }
        
        // 计算需要扣除的收入
        $commissionRate = $merchant['commission_rate'] / 100;
        $revenue = $amount * (1 - $commissionRate);
        
        // 更新商家余额
        $newBalance = max(0, $merchant['balance'] - $revenue);
        $merchantModel->update($merchantId, [
            'balance' => $newBalance
        ]);
        
        // 记录扣除日志
        $sql = "INSERT INTO merchant_revenue_logs (merchant_id, order_id, amount, commission, revenue, type, created_at) 
                VALUES (?, ?, ?, ?, ?, 'refund', ?)";
        $this->db->query($sql, [$merchantId, $orderId, $amount, 0, -$revenue, date('Y-m-d H:i:s')]);
    }
    
    /**
     * 自动取消过期订单
     */
    public function cancelExpiredOrders() {
        $sql = "UPDATE {$this->getTableName()} 
                SET status = :cancelled_status 
                WHERE status = :pending_status 
                AND expired_at < :current_time";
        
        $params = [
            'cancelled_status' => self::STATUS_CANCELLED,
            'pending_status' => self::STATUS_PENDING,
            'current_time' => date('Y-m-d H:i:s')
        ];
        
        $stmt = $this->db->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 获取订单状态文本
     */
    public static function getStatusText($status) {
        $statusMap = [
            self::STATUS_PENDING => '待支付',
            self::STATUS_PAID => '已支付',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_REFUNDED => '已退款'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
    
    /**
     * 获取支付状态文本
     */
    public static function getPaymentStatusText($status) {
        $statusMap = [
            self::PAYMENT_UNPAID => '未支付',
            self::PAYMENT_PAID => '已支付',
            self::PAYMENT_REFUNDED => '已退款'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
}
