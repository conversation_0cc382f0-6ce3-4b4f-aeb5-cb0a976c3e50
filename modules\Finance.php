<?php
/**
 * 财务管理模型
 * API管理系统 - 财务统计与管理
 */

require_once __DIR__ . '/../core/Model.php';

class Finance extends Model {
    
    /**
     * 获取财务概览
     */
    public function getFinanceOverview($startDate = null, $endDate = null) {
        $conditions = [];
        $params = [];
        
        if ($startDate) {
            $conditions[] = "created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);
        
        // 订单统计
        $orderStats = $this->getOrderStats($whereClause, $params);
        
        // 支付统计
        $paymentStats = $this->getPaymentStats($whereClause, $params);
        
        // 商家收入统计
        $merchantStats = $this->getMerchantStats($whereClause, $params);
        
        // API调用统计
        $apiStats = $this->getApiCallStats($whereClause, $params);
        
        return [
            'orders' => $orderStats,
            'payments' => $paymentStats,
            'merchants' => $merchantStats,
            'api_calls' => $apiStats,
            'summary' => [
                'total_revenue' => $orderStats['total_amount'],
                'total_orders' => $orderStats['total_orders'],
                'total_merchants' => $merchantStats['total_merchants'],
                'total_api_calls' => $apiStats['total_calls'],
                'platform_commission' => $merchantStats['total_commission']
            ]
        ];
    }
    
    /**
     * 获取订单统计
     */
    private function getOrderStats($whereClause, $params) {
        $sql = "SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN payment_status = 1 THEN amount ELSE 0 END) as total_amount,
                    SUM(CASE WHEN status = 5 THEN refund_amount ELSE 0 END) as total_refund,
                    AVG(CASE WHEN payment_status = 1 THEN amount ELSE NULL END) as avg_amount,
                    COUNT(CASE WHEN payment_status = 1 THEN 1 END) as paid_orders,
                    COUNT(CASE WHEN status = 4 THEN 1 END) as cancelled_orders
                FROM orders 
                {$whereClause}";
        
        return $this->db->fetchOne($sql, $params);
    }
    
    /**
     * 获取支付统计
     */
    private function getPaymentStats($whereClause, $params) {
        $sql = "SELECT 
                    COUNT(*) as total_payments,
                    SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as success_amount,
                    COUNT(CASE WHEN status = 1 THEN 1 END) as success_count,
                    COUNT(CASE WHEN payment_method = 'alipay' AND status = 1 THEN 1 END) as alipay_count,
                    COUNT(CASE WHEN payment_method = 'wechat' AND status = 1 THEN 1 END) as wechat_count,
                    COUNT(CASE WHEN payment_method = 'balance' AND status = 1 THEN 1 END) as balance_count,
                    SUM(CASE WHEN payment_method = 'alipay' AND status = 1 THEN amount ELSE 0 END) as alipay_amount,
                    SUM(CASE WHEN payment_method = 'wechat' AND status = 1 THEN amount ELSE 0 END) as wechat_amount,
                    SUM(CASE WHEN payment_method = 'balance' AND status = 1 THEN amount ELSE 0 END) as balance_amount
                FROM payments 
                {$whereClause}";
        
        return $this->db->fetchOne($sql, $params);
    }
    
    /**
     * 获取商家统计
     */
    private function getMerchantStats($whereClause, $params) {
        // 修改WHERE子句以适应merchant_revenue_logs表
        $merchantWhereClause = str_replace('created_at', 'mrl.created_at', $whereClause);
        
        $sql = "SELECT 
                    COUNT(DISTINCT m.id) as total_merchants,
                    SUM(CASE WHEN mrl.type != 'refund' THEN mrl.revenue ELSE 0 END) as total_merchant_revenue,
                    SUM(CASE WHEN mrl.type != 'refund' THEN mrl.commission ELSE 0 END) as total_commission,
                    AVG(m.balance) as avg_merchant_balance,
                    COUNT(CASE WHEN m.status = 1 THEN 1 END) as active_merchants
                FROM merchants m
                LEFT JOIN merchant_revenue_logs mrl ON m.id = mrl.merchant_id
                {$merchantWhereClause}";
        
        return $this->db->fetchOne($sql, $params);
    }
    
    /**
     * 获取API调用统计
     */
    private function getApiCallStats($whereClause, $params) {
        $sql = "SELECT 
                    COUNT(*) as total_calls,
                    SUM(cost) as total_cost,
                    COUNT(CASE WHEN status = 1 THEN 1 END) as success_calls,
                    COUNT(CASE WHEN status = 0 THEN 1 END) as failed_calls,
                    AVG(response_time) as avg_response_time,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT api_id) as used_apis
                FROM call_logs 
                {$whereClause}";
        
        return $this->db->fetchOne($sql, $params);
    }
    
    /**
     * 获取收入趋势
     */
    public function getRevenueTrend($days = 30) {
        $sql = "SELECT 
                    DATE(created_at) as date,
                    SUM(CASE WHEN payment_status = 1 THEN amount ELSE 0 END) as daily_revenue,
                    COUNT(CASE WHEN payment_status = 1 THEN 1 END) as daily_orders
                FROM orders 
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取支付方式分布
     */
    public function getPaymentMethodDistribution($startDate = null, $endDate = null) {
        $conditions = ['status = 1']; // 只统计成功支付
        $params = [];
        
        if ($startDate) {
            $conditions[] = "created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        
        $sql = "SELECT 
                    payment_method,
                    COUNT(*) as count,
                    SUM(amount) as total_amount,
                    AVG(amount) as avg_amount
                FROM payments 
                {$whereClause}
                GROUP BY payment_method
                ORDER BY total_amount DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取商家收入排行
     */
    public function getMerchantRevenueRanking($limit = 10, $startDate = null, $endDate = null) {
        $conditions = ["mrl.type != 'refund'"]; // 排除退款记录
        $params = ['limit' => $limit];
        
        if ($startDate) {
            $conditions[] = "mrl.created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "mrl.created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        
        $sql = "SELECT 
                    m.id,
                    m.company_name,
                    m.level,
                    m.commission_rate,
                    SUM(mrl.revenue) as total_revenue,
                    SUM(mrl.commission) as total_commission,
                    COUNT(mrl.id) as order_count,
                    m.balance as current_balance
                FROM merchants m
                LEFT JOIN merchant_revenue_logs mrl ON m.id = mrl.merchant_id
                {$whereClause}
                GROUP BY m.id
                ORDER BY total_revenue DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取用户消费排行
     */
    public function getUserSpendingRanking($limit = 10, $startDate = null, $endDate = null) {
        $conditions = ['o.payment_status = 1']; // 只统计已支付订单
        $params = ['limit' => $limit];
        
        if ($startDate) {
            $conditions[] = "o.created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "o.created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        
        $sql = "SELECT 
                    u.id,
                    u.username,
                    u.email,
                    u.level,
                    SUM(o.amount) as total_spending,
                    COUNT(o.id) as order_count,
                    AVG(o.amount) as avg_order_amount,
                    u.balance as current_balance
                FROM users u
                LEFT JOIN orders o ON u.id = o.user_id
                {$whereClause}
                GROUP BY u.id
                ORDER BY total_spending DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取API收入排行
     */
    public function getApiRevenueRanking($limit = 10, $startDate = null, $endDate = null) {
        $conditions = ['o.payment_status = 1']; // 只统计已支付订单
        $params = ['limit' => $limit];
        
        if ($startDate) {
            $conditions[] = "o.created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "o.created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        
        $sql = "SELECT 
                    a.id,
                    a.name,
                    a.path,
                    a.price,
                    m.company_name as merchant_name,
                    SUM(o.amount) as total_revenue,
                    COUNT(o.id) as order_count,
                    SUM(o.quantity) as total_calls
                FROM apis a
                LEFT JOIN orders o ON a.id = o.api_id
                LEFT JOIN merchants m ON a.merchant_id = m.id
                {$whereClause}
                GROUP BY a.id
                ORDER BY total_revenue DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取余额变动记录
     */
    public function getBalanceHistory($userId = null, $page = 1, $perPage = 20, $type = null) {
        $offset = ($page - 1) * $perPage;
        $conditions = [];
        $params = [];
        
        if ($userId) {
            $conditions[] = "bl.user_id = :user_id";
            $params['user_id'] = $userId;
        }
        
        if ($type) {
            $conditions[] = "bl.type = :type";
            $params['type'] = $type;
        }
        
        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM balance_logs bl {$whereClause}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSql = "SELECT 
                        bl.*,
                        u.username,
                        u.email
                    FROM balance_logs bl
                    LEFT JOIN users u ON bl.user_id = u.id
                    {$whereClause}
                    ORDER BY bl.id DESC
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取商家结算记录
     */
    public function getMerchantSettlements($merchantId = null, $page = 1, $perPage = 20) {
        $offset = ($page - 1) * $perPage;
        $conditions = [];
        $params = [];
        
        if ($merchantId) {
            $conditions[] = "ms.merchant_id = :merchant_id";
            $params['merchant_id'] = $merchantId;
        }
        
        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM merchant_settlements ms {$whereClause}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSql = "SELECT 
                        ms.*,
                        m.company_name,
                        m.contact_name
                    FROM merchant_settlements ms
                    LEFT JOIN merchants m ON ms.merchant_id = m.id
                    {$whereClause}
                    ORDER BY ms.id DESC
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 创建商家结算
     */
    public function createMerchantSettlement($merchantId, $amount, $type = 'manual', $note = '') {
        // 获取商家信息
        $merchantModel = new Merchant();
        $merchant = $merchantModel->find($merchantId);
        if (!$merchant) {
            throw new Exception('商家不存在');
        }
        
        if ($merchant['balance'] < $amount) {
            throw new Exception('商家余额不足');
        }
        
        try {
            $this->db->getPdo()->beginTransaction();
            
            // 扣除商家余额
            $merchantModel->update($merchantId, [
                'balance' => $merchant['balance'] - $amount
            ]);
            
            // 创建结算记录
            $settlementData = [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'type' => $type,
                'status' => 1, // 已结算
                'note' => $note,
                'settled_at' => date('Y-m-d H:i:s')
            ];
            
            $sql = "INSERT INTO merchant_settlements (merchant_id, amount, type, status, note, settled_at, created_at) 
                    VALUES (:merchant_id, :amount, :type, :status, :note, :settled_at, :created_at)";
            
            $settlementData['created_at'] = date('Y-m-d H:i:s');
            $this->db->query($sql, $settlementData);
            
            $this->db->getPdo()->commit();
            
            return true;
            
        } catch (Exception $e) {
            $this->db->getPdo()->rollBack();
            throw $e;
        }
    }
    
    /**
     * 导出财务报表
     */
    public function exportFinanceReport($startDate, $endDate, $type = 'overview') {
        switch ($type) {
            case 'overview':
                return $this->exportOverviewReport($startDate, $endDate);
            case 'orders':
                return $this->exportOrdersReport($startDate, $endDate);
            case 'merchants':
                return $this->exportMerchantsReport($startDate, $endDate);
            case 'apis':
                return $this->exportApisReport($startDate, $endDate);
            default:
                throw new Exception('不支持的报表类型');
        }
    }
    
    /**
     * 导出概览报表
     */
    private function exportOverviewReport($startDate, $endDate) {
        $overview = $this->getFinanceOverview($startDate, $endDate);
        
        $data = [
            ['指标', '数值'],
            ['总订单数', $overview['orders']['total_orders']],
            ['总收入', '¥' . number_format($overview['orders']['total_amount'], 2)],
            ['总退款', '¥' . number_format($overview['orders']['total_refund'], 2)],
            ['平台佣金', '¥' . number_format($overview['summary']['platform_commission'], 2)],
            ['活跃商家', $overview['merchants']['active_merchants']],
            ['API调用次数', $overview['api_calls']['total_calls']],
            ['成功调用次数', $overview['api_calls']['success_calls']],
            ['平均响应时间', $overview['api_calls']['avg_response_time'] . 'ms']
        ];
        
        return $data;
    }
    
    /**
     * 导出订单报表
     */
    private function exportOrdersReport($startDate, $endDate) {
        $sql = "SELECT 
                    o.order_no,
                    u.username,
                    a.name as api_name,
                    m.company_name as merchant_name,
                    o.amount,
                    o.quantity,
                    o.unit_price,
                    CASE o.status 
                        WHEN 0 THEN '待支付'
                        WHEN 1 THEN '已支付'
                        WHEN 2 THEN '处理中'
                        WHEN 3 THEN '已完成'
                        WHEN 4 THEN '已取消'
                        WHEN 5 THEN '已退款'
                    END as status_text,
                    o.created_at
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                LEFT JOIN apis a ON o.api_id = a.id
                LEFT JOIN merchants m ON o.merchant_id = m.id
                WHERE o.created_at >= :start_date AND o.created_at <= :end_date
                ORDER BY o.created_at DESC";
        
        $orders = $this->db->fetchAll($sql, [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
        
        $data = [['订单号', '用户', 'API名称', '商家', '金额', '数量', '单价', '状态', '创建时间']];
        
        foreach ($orders as $order) {
            $data[] = [
                $order['order_no'],
                $order['username'],
                $order['api_name'],
                $order['merchant_name'],
                '¥' . number_format($order['amount'], 2),
                $order['quantity'],
                '¥' . number_format($order['unit_price'], 2),
                $order['status_text'],
                $order['created_at']
            ];
        }
        
        return $data;
    }
    
    /**
     * 导出商家报表
     */
    private function exportMerchantsReport($startDate, $endDate) {
        $merchants = $this->getMerchantRevenueRanking(100, $startDate, $endDate);
        
        $data = [['商家名称', '等级', '佣金率', '总收入', '平台佣金', '订单数', '当前余额']];
        
        $levelMap = [1 => '铜牌', 2 => '银牌', 3 => '金牌', 4 => '钻石'];
        
        foreach ($merchants as $merchant) {
            $data[] = [
                $merchant['company_name'],
                $levelMap[$merchant['level']] ?? '未知',
                $merchant['commission_rate'] . '%',
                '¥' . number_format($merchant['total_revenue'], 2),
                '¥' . number_format($merchant['total_commission'], 2),
                $merchant['order_count'],
                '¥' . number_format($merchant['current_balance'], 2)
            ];
        }
        
        return $data;
    }
    
    /**
     * 导出API报表
     */
    private function exportApisReport($startDate, $endDate) {
        $apis = $this->getApiRevenueRanking(100, $startDate, $endDate);
        
        $data = [['API名称', '路径', '单价', '商家', '总收入', '订单数', '调用次数']];
        
        foreach ($apis as $api) {
            $data[] = [
                $api['name'],
                $api['path'],
                '¥' . number_format($api['price'], 2),
                $api['merchant_name'],
                '¥' . number_format($api['total_revenue'], 2),
                $api['order_count'],
                $api['total_calls']
            ];
        }
        
        return $data;
    }
}