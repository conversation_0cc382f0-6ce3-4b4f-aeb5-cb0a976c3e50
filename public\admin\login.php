<?php
/**
 * 后台登录处理
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['code' => 400, 'msg' => '请求方法错误']);
    exit;
}

// 获取POST数据
$username = $_POST['username'] ?? '';
$password = $_POST['password'] ?? '';
$captcha = $_POST['captcha'] ?? '';

// 验证必填字段
if (empty($username) || empty($password) || empty($captcha)) {
    echo json_encode(['code' => 400, 'msg' => '请填写完整信息']);
    exit;
}

// 验证验证码
if (!isset($_SESSION['captcha']) || strtolower($captcha) !== $_SESSION['captcha']) {
    echo json_encode(['code' => 400, 'msg' => '验证码错误']);
    exit;
}

// 清除验证码
unset($_SESSION['captcha']);

try {
    // 连接数据库
    $config = require_once '../../config/database.php';
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    // 查询管理员
    $stmt = $pdo->prepare("SELECT * FROM api_admins WHERE username = ? AND status = 1");
    $stmt->execute([$username]);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo json_encode(['code' => 400, 'msg' => '用户名不存在或已被禁用']);
        exit;
    }
    
    // 验证密码
    if (!password_verify($password, $admin['password'])) {
        echo json_encode(['code' => 400, 'msg' => '密码错误']);
        exit;
    }
    
    // 更新登录信息
    $updateStmt = $pdo->prepare("UPDATE api_admins SET last_login_time = NOW(), last_login_ip = ?, login_count = login_count + 1 WHERE id = ?");
    $updateStmt->execute([$_SERVER['REMOTE_ADDR'], $admin['id']]);
    
    // 设置session
    $_SESSION['admin_id'] = $admin['id'];
    $_SESSION['admin_username'] = $admin['username'];
    $_SESSION['admin_nickname'] = $admin['nickname'] ?: $admin['username'];
    $_SESSION['admin_role_id'] = $admin['role_id'];
    $_SESSION['login_time'] = time();
    
    echo json_encode([
        'code' => 200, 
        'msg' => '登录成功',
        'data' => [
            'username' => $admin['username'],
            'nickname' => $admin['nickname'] ?: $admin['username'],
            'redirect' => 'dashboard.html'
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
}
?>