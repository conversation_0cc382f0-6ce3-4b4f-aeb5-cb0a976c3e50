<?php
/**
 * 日志管理模块
 * API管理系统 - 系统日志记录与管理
 */

require_once __DIR__ . '/../core/Model.php';

class Logger extends Model {
    
    // 日志级别
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';
    
    // 日志类型
    const TYPE_SYSTEM = 'system';
    const TYPE_API = 'api';
    const TYPE_USER = 'user';
    const TYPE_ADMIN = 'admin';
    const TYPE_PAYMENT = 'payment';
    const TYPE_SECURITY = 'security';
    const TYPE_DATABASE = 'database';
    
    private $logPath;
    
    public function __construct() {
        parent::__construct();
        $this->logPath = __DIR__ . '/../logs/';
        
        // 确保日志目录存在
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * 记录日志到数据库
     */
    public function log($level, $type, $message, $context = [], $userId = null, $adminId = null) {
        $data = [
            'level' => $level,
            'type' => $type,
            'message' => $message,
            'context' => json_encode($context),
            'user_id' => $userId,
            'admin_id' => $adminId,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $sql = "INSERT INTO system_logs (level, type, message, context, user_id, admin_id, ip_address, user_agent, request_uri, created_at) 
                VALUES (:level, :type, :message, :context, :user_id, :admin_id, :ip_address, :user_agent, :request_uri, :created_at)";
        
        $result = $this->db->execute($sql, $data);
        
        // 同时写入文件日志
        $this->writeToFile($level, $type, $message, $context);
        
        return $result;
    }
    
    /**
     * 写入文件日志
     */
    private function writeToFile($level, $type, $message, $context = []) {
        $date = date('Y-m-d');
        $filename = $this->logPath . "{$type}_{$date}.log";
        
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'ip' => $this->getClientIP(),
            'uri' => $_SERVER['REQUEST_URI'] ?? ''
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        
        file_put_contents($filename, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 调试日志
     */
    public function debug($type, $message, $context = [], $userId = null, $adminId = null) {
        return $this->log(self::LEVEL_DEBUG, $type, $message, $context, $userId, $adminId);
    }
    
    /**
     * 信息日志
     */
    public function info($type, $message, $context = [], $userId = null, $adminId = null) {
        return $this->log(self::LEVEL_INFO, $type, $message, $context, $userId, $adminId);
    }
    
    /**
     * 警告日志
     */
    public function warning($type, $message, $context = [], $userId = null, $adminId = null) {
        return $this->log(self::LEVEL_WARNING, $type, $message, $context, $userId, $adminId);
    }
    
    /**
     * 错误日志
     */
    public function error($type, $message, $context = [], $userId = null, $adminId = null) {
        return $this->log(self::LEVEL_ERROR, $type, $message, $context, $userId, $adminId);
    }
    
    /**
     * 严重错误日志
     */
    public function critical($type, $message, $context = [], $userId = null, $adminId = null) {
        return $this->log(self::LEVEL_CRITICAL, $type, $message, $context, $userId, $adminId);
    }
    
    /**
     * API调用日志
     */
    public function logAPICall($apiId, $userId, $status, $responseTime, $requestData = null, $responseData = null) {
        $context = [
            'api_id' => $apiId,
            'status' => $status,
            'response_time' => $responseTime,
            'request_data' => $requestData,
            'response_data' => $responseData
        ];
        
        return $this->info(self::TYPE_API, "API调用: API ID {$apiId}, 状态: {$status}, 响应时间: {$responseTime}ms", $context, $userId);
    }
    
    /**
     * 用户操作日志
     */
    public function logUserAction($userId, $action, $description, $context = []) {
        return $this->info(self::TYPE_USER, "用户操作: {$action} - {$description}", $context, $userId);
    }
    
    /**
     * 管理员操作日志
     */
    public function logAdminAction($adminId, $action, $description, $context = []) {
        return $this->info(self::TYPE_ADMIN, "管理员操作: {$action} - {$description}", $context, null, $adminId);
    }
    
    /**
     * 支付日志
     */
    public function logPayment($orderId, $amount, $method, $status, $context = []) {
        $context['order_id'] = $orderId;
        $context['amount'] = $amount;
        $context['method'] = $method;
        $context['status'] = $status;
        
        return $this->info(self::TYPE_PAYMENT, "支付操作: 订单 {$orderId}, 金额 {$amount}, 方式 {$method}, 状态 {$status}", $context);
    }
    
    /**
     * 数据库操作日志
     */
    public function logDatabaseOperation($operation, $table, $description, $context = []) {
        $context['operation'] = $operation;
        $context['table'] = $table;
        
        return $this->info(self::TYPE_DATABASE, "数据库操作: {$operation} - {$table} - {$description}", $context);
    }
    
    /**
     * 获取系统日志
     */
    public function getSystemLogs($page = 1, $perPage = 50, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['level'])) {
            $whereClause[] = "level = :level";
            $params['level'] = $conditions['level'];
        }
        
        if (!empty($conditions['type'])) {
            $whereClause[] = "type = :type";
            $params['type'] = $conditions['type'];
        }
        
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['admin_id'])) {
            $whereClause[] = "admin_id = :admin_id";
            $params['admin_id'] = $conditions['admin_id'];
        }
        
        if (!empty($conditions['start_date'])) {
            $whereClause[] = "created_at >= :start_date";
            $params['start_date'] = $conditions['start_date'];
        }
        
        if (!empty($conditions['end_date'])) {
            $whereClause[] = "created_at <= :end_date";
            $params['end_date'] = $conditions['end_date'];
        }
        
        if (!empty($conditions['keyword'])) {
            $whereClause[] = "message LIKE :keyword";
            $params['keyword'] = '%' . $conditions['keyword'] . '%';
        }
        
        $whereSQL = empty($whereClause) ? '' : 'WHERE ' . implode(' AND ', $whereClause);
        
        // 查询总数
        $countSQL = "SELECT COUNT(*) as total FROM system_logs {$whereSQL}";
        $totalResult = $this->db->fetchOne($countSQL, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSQL = "SELECT sl.*, u.username, a.username as admin_name 
                    FROM system_logs sl 
                    LEFT JOIN users u ON sl.user_id = u.id 
                    LEFT JOIN admins a ON sl.admin_id = a.id 
                    {$whereSQL}
                    ORDER BY sl.id DESC 
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSQL, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取日志统计
     */
    public function getLogStatistics($startDate = null, $endDate = null) {
        $conditions = [];
        $params = [];
        
        if ($startDate) {
            $conditions[] = "created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);
        
        // 按级别统计
        $levelStats = $this->db->fetchAll(
            "SELECT level, COUNT(*) as count FROM system_logs {$whereClause} GROUP BY level",
            $params
        );
        
        // 按类型统计
        $typeStats = $this->db->fetchAll(
            "SELECT type, COUNT(*) as count FROM system_logs {$whereClause} GROUP BY type",
            $params
        );
        
        // 按日期统计
        $dateStats = $this->db->fetchAll(
            "SELECT DATE(created_at) as date, COUNT(*) as count FROM system_logs {$whereClause} GROUP BY DATE(created_at) ORDER BY date DESC LIMIT 30",
            $params
        );
        
        return [
            'level_stats' => $levelStats,
            'type_stats' => $typeStats,
            'date_stats' => $dateStats
        ];
    }
    
    /**
     * 清理过期日志
     */
    public function cleanupLogs($days = 30) {
        $cutoffDate = date('Y-m-d H:i:s', time() - ($days * 24 * 3600));
        
        // 清理数据库日志
        $sql = "DELETE FROM system_logs WHERE created_at < :cutoff_date";
        $dbResult = $this->db->execute($sql, ['cutoff_date' => $cutoffDate]);
        
        // 清理文件日志
        $filesCleaned = 0;
        $files = glob($this->logPath . '*.log');
        
        foreach ($files as $file) {
            $fileDate = filemtime($file);
            if ($fileDate < (time() - ($days * 24 * 3600))) {
                if (unlink($file)) {
                    $filesCleaned++;
                }
            }
        }
        
        $this->info(self::TYPE_SYSTEM, "日志清理完成", [
            'days' => $days,
            'db_records_deleted' => $dbResult,
            'files_cleaned' => $filesCleaned
        ]);
        
        return [
            'db_records_deleted' => $dbResult,
            'files_cleaned' => $filesCleaned
        ];
    }
    
    /**
     * 导出日志
     */
    public function exportLogs($conditions = [], $format = 'csv') {
        $logs = $this->getSystemLogs(1, 10000, $conditions);
        
        if ($format === 'csv') {
            return $this->exportToCSV($logs['data']);
        } elseif ($format === 'json') {
            return json_encode($logs['data'], JSON_UNESCAPED_UNICODE);
        }
        
        return false;
    }
    
    /**
     * 导出为CSV格式
     */
    private function exportToCSV($data) {
        $output = fopen('php://temp', 'r+');
        
        // 写入CSV头部
        fputcsv($output, ['ID', '级别', '类型', '消息', '用户', '管理员', 'IP地址', '创建时间']);
        
        // 写入数据
        foreach ($data as $row) {
            fputcsv($output, [
                $row['id'],
                $row['level'],
                $row['type'],
                $row['message'],
                $row['username'] ?? '',
                $row['admin_name'] ?? '',
                $row['ip_address'],
                $row['created_at']
            ]);
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 获取文件日志内容
     */
    public function getFileLog($type, $date = null) {
        $date = $date ?: date('Y-m-d');
        $filename = $this->logPath . "{$type}_{$date}.log";
        
        if (!file_exists($filename)) {
            return [];
        }
        
        $lines = file($filename, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $logs = [];
        
        foreach ($lines as $line) {
            $log = json_decode($line, true);
            if ($log) {
                $logs[] = $log;
            }
        }
        
        return array_reverse($logs); // 最新的在前面
    }
    
    /**
     * 获取日志文件列表
     */
    public function getLogFiles() {
        $files = glob($this->logPath . '*.log');
        $logFiles = [];
        
        foreach ($files as $file) {
            $basename = basename($file, '.log');
            $parts = explode('_', $basename);
            
            if (count($parts) >= 2) {
                $type = $parts[0];
                $date = $parts[1];
                
                $logFiles[] = [
                    'filename' => $basename . '.log',
                    'type' => $type,
                    'date' => $date,
                    'size' => filesize($file),
                    'modified' => filemtime($file)
                ];
            }
        }
        
        // 按修改时间排序
        usort($logFiles, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
        
        return $logFiles;
    }
    
    /**
     * 监控系统性能
     */
    public function logPerformance($operation, $startTime, $context = []) {
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
        
        $context['execution_time'] = $executionTime;
        $context['memory_usage'] = memory_get_usage(true);
        $context['peak_memory'] = memory_get_peak_usage(true);
        
        $level = $executionTime > 1000 ? self::LEVEL_WARNING : self::LEVEL_INFO;
        
        return $this->log($level, self::TYPE_SYSTEM, "性能监控: {$operation} 执行时间 {$executionTime}ms", $context);
    }
    
    /**
     * 记录异常
     */
    public function logException($exception, $context = []) {
        $context['exception_class'] = get_class($exception);
        $context['file'] = $exception->getFile();
        $context['line'] = $exception->getLine();
        $context['trace'] = $exception->getTraceAsString();
        
        return $this->error(self::TYPE_SYSTEM, "异常: " . $exception->getMessage(), $context);
    }
    
    /**
     * 记录慢查询
     */
    public function logSlowQuery($sql, $executionTime, $params = []) {
        $context = [
            'sql' => $sql,
            'execution_time' => $executionTime,
            'params' => $params
        ];
        
        return $this->warning(self::TYPE_DATABASE, "慢查询: 执行时间 {$executionTime}ms", $context);
    }
}
