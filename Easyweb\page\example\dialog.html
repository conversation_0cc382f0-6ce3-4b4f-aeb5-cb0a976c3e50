<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>弹窗实例</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /** 查看详情弹窗样式 */
        .paper-info-group {
            padding: 15px 25px;
        }

        .paper-info-group h3 {
            font-weight: bold;
            color: #000000;
            padding-bottom: 8px;
        }

        .paper-info-group p {
            color: #666;
            padding-bottom: 12px;
            font-size: 16px;
        }

        .paper-info-group .paper-info-group-imgs {
            padding-top: 5px;
        }

        .paper-info-group .paper-info-group-imgs img {
            margin-right: 15px;
            margin-bottom: 15px;
            cursor: zoom-in;
        }

        .paper-info-group .paper-info-group-imgs img:last-child {
            margin-right: 0;
        }

        /* 日期组件不显示秒 */
        .laydate-time-list li:last-child {
            display: none;
        }

        .laydate-time-list li {
            width: 50% !important;
        }

        .laydate-time-list ol li {
            padding-left: 55px !important;
            width: 100% !important;
        }

    </style>
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">表单弹窗实例</div>
        <div class="layui-card-body">
            <button id="eDialogAddStuBtn" class="layui-btn">添加学生</button>
            <button id="eDialogAddCouBtn" class="layui-btn">添加排课</button>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">表格+弹窗实例</div>
        <div class="layui-card-body">
            <table id="eDialogTable" lay-filter="eDialogTable"></table>
        </div>
    </div>
</div>

<!-- 添加学生表单弹窗 -->
<script type="text/html" id="eDialogStuEditDialog">
    <form id="eDialogStuEditForm" lay-filter="eDialogStuEditForm" class="layui-form model-form layui-row">
        <input name="studentId" type="hidden"/>
        <div class="layui-col-md6">
            <div class="layui-form-item">
                <label class="layui-form-label">学院专业</label>
                <div class="layui-input-block">
                    <input name="profession" placeholder="请选择学院专业" lay-verType="tips" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">班级</label>
                <div class="layui-input-block">
                    <input name="className" placeholder="请输入班级" type="text" class="layui-input"
                           lay-verType="tips" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">入学年份</label>
                <div class="layui-input-block">
                    <input name="schoolYear" placeholder="请选择入学年份" type="text" class="layui-input date-icon"
                           readonly="readonly" lay-verType="tips" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">学号</label>
                <div class="layui-input-block">
                    <input name="username" placeholder="请输入学号" type="text" class="layui-input" maxlength="20"
                           lay-verType="tips" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-block">
                    <input name="nickName" placeholder="请输入姓名" type="text" class="layui-input" maxlength="20"
                           lay-verType="tips" lay-verify="required" required/>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding-left: 0;padding-right: 0;width: 98px;">个性标签</label>
                <div class="layui-input-block">
                    <input name="label" value="学霸,萌妹" class="layui-hide" lay-verType="tips"
                           lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">手机号</label>
                <div class="layui-input-block">
                    <input name="phone" placeholder="请输入手机号" type="text" class="layui-input"
                           lay-verType="tips" lay-verify="required|phone" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">性别</label>
                <div class="layui-input-block">
                    <input type="radio" name="sex" value="男" title="男" checked/>
                    <input type="radio" name="sex" value="女" title="女"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">紧急联系人</label>
                <div class="layui-input-block">
                    <input name="jjContactUser" placeholder="请输入紧急联系人" type="text" class="layui-input"
                           lay-verType="tips" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding-left: 0;padding-right: 0;width: 98px;">紧急联系方式</label>
                <div class="layui-input-block">
                    <input name="jjContact" placeholder="请输入紧急联系方式" type="text" class="layui-input"
                           lay-verType="tips" lay-verify="required|phone" required/>
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="eDialogStuEditSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- 添加排课表单弹窗 -->
<script type="text/html" id="eDialogCouEditDialog">
    <form id="eDialogCouEditForm" lay-filter="eDialogCouEditForm" class="layui-form model-form layui-row">
        <input name="schedulingId" type="hidden"/>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">课程:</label>
                    <div class="layui-input-block">
                        <select name="courseId" lay-verType="tips" lay-verify="required" required>
                            <option value="">请选择课程</option>
                            <option value="1">Java初级编程</option>
                            <option value="2">php编程</option>
                            <option value="14">ui</option>
                            <option value="22">C++</option>
                            <option value="23">VUE</option>
                            <option value="24">波形图检测</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">老师:</label>
                    <div class="layui-input-block">
                        <select name="teacherId" lay-verType="tips" lay-verify="required" required>
                            <option value="">请选择老师</option>
                            <option value="2">教师一</option>
                            <option value="8">teacher007</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">教室:</label>
                    <div class="layui-input-block">
                        <input name="classroom" placeholder="请输入教室" type="text" class="layui-input" maxlength="20"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">人数:</label>
                    <div class="layui-input-block">
                        <input name="maxNum" placeholder="请输入人数" type="text" class="layui-input" min="1" max="9999"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">起止日期:</label>
                    <div class="layui-input-block">
                        <input id="EDdateRange" name="dateRange" placeholder="请选择起止日期" autocomplete="off"
                               class="layui-input date-icon" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">时间段:</label>
                    <div class="layui-input-block">
                        <input id="EDtimeRange" name="timeRange" placeholder="请选择上课时间段" autocomplete="off"
                               class="layui-input date-icon" lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-form-item">
                    <label class="layui-form-label">重复:</label>
                    <div class="layui-input-block">
                        <input type="checkbox" lay-skin="primary" name="week_1" value="1" title="周一">
                        <input type="checkbox" lay-skin="primary" name="week_2" value="2" title="周二">
                        <input type="checkbox" lay-skin="primary" name="week_3" value="3" title="周三">
                        <input type="checkbox" lay-skin="primary" name="week_4" value="4" title="周四">
                        <input type="checkbox" lay-skin="primary" name="week_5" value="5" title="周五">
                        <input type="checkbox" lay-skin="primary" name="week_6" value="6" title="周六">
                        <input type="checkbox" lay-skin="primary" name="week_7" value="7" title="周日">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">班级:</label>
                    <div class="layui-input-block">
                        <input name="className" placeholder="请输入班级" type="text" class="layui-input" maxlength="20"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="eDialogCouEditSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="eDialogTbBar">
    <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="view"><i class="layui-icon">&#xe6b2;</i>查看</a>
    <a class="layui-btn layui-btn-sm" lay-event="comments"><i class="layui-icon">&#xe63a;</i>评论</a>
</script>

<!-- 查看评论表格操作列 -->
<script type="text/html" id="eDialogCommentTbBar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 查看评论弹窗 -->
<script type="text/html" id="eDialogCommentDialog">
    <table id="eDialogCommentTable" lay-filter="eDialogCommentTable" class="layui-hide"></table>
    <div class="btn-circle" id="eDialogCommentBtnAdd" style="position: absolute; bottom: 60px;" title="发表评论">
        <i class="layui-icon layui-icon-edit"></i>
    </div>
</script>

<!-- 查看详情弹窗 -->
<script type="text/html" id="eDialogInfoDialog">
    <div class="paper-info-group">
        <h3>本周工作总结</h3>
        <p>{{d.summary}}</p>
        <h3>下周工作计划</h3>
        <p>{{d.plan}}</p>
        <h3>备注</h3>
        <p>{{d.comments}}</p>
        <div class="paper-info-group-imgs" id="eDialogInfoImgs">
            <img src="{{d.img1}}" width="130px" height="130px">
            <img src="{{d.img2}}" width="130px" height="130px">
            <img src="{{d.img3}}" width="130px" height="130px">
        </div>
    </div>
</script>

<!-- 表格状态列 -->
<script type="text/html" id="eDialogTbState">
    {{# if(d.state==0){ }}
    <span style="color: orange;cursor: default;" lay-tips="正在审核中，请耐心等待">待审核</span>
    {{# }else if(d.state==1){ }}
    <span class="icon-text" style="color: green;cursor: pointer;" lay-event="checkList">
        已通过<i class="layui-icon layui-icon-tips"></i>
    </span>
    {{# } }}
</script>

<!-- 审核记录 -->
<script id="eDialogCheckDialog" type="text/html">
    <div style="padding: 25px 0 0 30px;">
        <ul class="layui-timeline">
            <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                <div class="layui-timeline-content layui-text">
                    <div class="layui-timeline-title">
                        <h3 class="inline-block">曲丽丽</h3>&emsp;2019-06-03 13:29
                    </div>
                    <p>提交了外出申请</p>
                </div>
            </li>
            <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                <div class="layui-timeline-content layui-text">
                    <div class="layui-timeline-title">
                        <h3 class="inline-block">
                            林东东&nbsp;<span class="layui-badge layui-bg-green">组长</span>
                        </h3>&emsp;2019-06-03 13:48
                    </div>
                    <p>同意了申请（速去速回！）</p>
                </div>
            </li>
            <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                <div class="layui-timeline-content layui-text">
                    <div class="layui-timeline-title">
                        <h3 class="inline-block">
                            周星星&nbsp;<span class="layui-badge layui-bg-blue">经理</span>
                        </h3>&emsp;2019-06-03 13:48
                    </div>
                    <p>同意了申请</p>
                </div>
            </li>
            <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                <div class="layui-timeline-content layui-text">
                    <div class="layui-timeline-title">
                        <h3 class="inline-block">
                            付小小&nbsp;<span class="layui-badge layui-bg-orange">人事</span>
                        </h3>&emsp;2019-06-03 13:48
                    </div>
                    <p>同意了申请</p>
                </div>
            </li>
        </ul>
    </div>
</script>

<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=317"></script>
<script>
    layui.use(['layer', 'admin', 'form', 'table', 'laytpl', 'util', 'laydate', 'cascader', 'tagsInput'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var admin = layui.admin;
        var form = layui.form;
        var table = layui.table;
        var laytpl = layui.laytpl;
        var util = layui.util;
        var laydate = layui.laydate;
        var cascader = layui.cascader;

        // 添加学生
        $('#eDialogAddStuBtn').click(function () {
            admin.open({
                type: 1,
                title: '添加学生',
                area: '700px',
                content: $('#eDialogStuEditDialog').html(),
                success: function (layero, dIndex) {
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    form.render('radio');
                    // 表单提交事件
                    form.on('submit(eDialogStuEditSubmit)', function (data) {
                        layer.msg("表单验证通过", {icon: 1});
                        return false;
                    });
                    // 专业选择
                    cascader.render({
                        elem: '#eDialogStuEditForm input[name="profession"]',
                        itemHeight: '220px',
                        data: [{
                            label: '计算机学院',
                            value: '1',
                            children: [{
                                label: '软件技术',
                                value: '1-1'
                            }, {
                                label: '计算机网络技术',
                                value: '1-2'
                            }, {
                                label: '计算机信息管理',
                                value: '1-3'
                            }, {
                                label: '物联网应用技术',
                                value: '1-4'
                            }, {
                                label: '数字媒体应用技术',
                                value: '1-5'
                            }, {
                                label: '移动互联应用技术',
                                value: '1-6'
                            }]
                        }, {
                            label: '机械工程学院',
                            value: '2',
                            children: [{
                                label: '模具设计与制造',
                                value: '2-1'
                            }, {
                                label: '机械设计与制造',
                                value: '2-2'
                            }, {
                                label: '数控技术',
                                value: '2-3'
                            }, {
                                label: '机械制造与自动化',
                                value: '2-4'
                            }]
                        }, {
                            label: '商学院',
                            value: '3',
                            children: [{
                                label: '电子商务',
                                value: '3-1'
                            }, {
                                label: '物流管理',
                                value: '3-2'
                            }, {
                                label: '报关与国际货运',
                                value: '3-3'
                            }, {
                                label: '连锁经营管理',
                                value: '3-4'
                            }, {
                                label: '旅游管理',
                                value: '3-5'
                            }, {
                                label: '商务英语',
                                value: '3-6'
                            }, {
                                label: '会计',
                                value: '3-7'
                            }, {
                                label: '金融管理',
                                value: '3-8'
                            }, {
                                label: '工商企业管理',
                                value: '3-9'
                            }]
                        }]
                    });
                    // 年选择器
                    laydate.render({
                        elem: '#eDialogStuEditForm input[name="schoolYear"]',
                        type: 'year',
                        trigger: 'click'
                    });
                    // 标签输入框
                    $('#eDialogStuEditForm input[name="label"]').tagsInput({
                        skin: 'tagsinput-default',
                        autocomplete_url: '../../json/tagsInput.json'
                    });
                }
            });

        });

        // 添加排课
        $('#eDialogAddCouBtn').click(function () {
            var mData = {weekdays: '3,5,7'};  // 回显数据
            admin.open({
                type: 1,
                title: '添加排课',
                area: '670px',
                content: $('#eDialogCouEditDialog').html(),
                success: function (layero, dIndex) {
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    if (mData) {  // 将逗号分隔的星期转成复选框对应的格式
                        var weekdays = mData.weekdays.split(',');
                        for (var i = 0; i < weekdays.length; i++) {
                            mData['week_' + weekdays[i]] = weekdays[i];
                        }
                    }
                    form.val('eDialogCouEditForm', mData);  // 回显数据
                    // 表单提交事件
                    form.on('submit(eDialogCouEditSubmit)', function (data) {
                        data.field.weekdays = '';  // 将星期复选框选中值变成逗号分隔
                        for (var f in data.field) {
                            if (f.indexOf('week_') == 0) {
                                data.field.weekdays && (data.field.weekdays += ',');
                                data.field.weekdays += data.field[f];
                            }
                        }
                        console.log(data.field);
                        layer.msg("表单验证通过", {icon: 1});
                        return false;
                    });
                    // 日期范围
                    laydate.render({
                        elem: '#EDdateRange',
                        range: true,
                        trigger: 'click'
                    });
                    // 时间范围
                    laydate.render({
                        elem: '#EDtimeRange',
                        type: 'time',
                        format: 'HH:mm',
                        range: true,
                        trigger: 'click'
                    });
                }
            });

        });

        // 渲染表格
        var insTb = table.render({
            elem: '#eDialogTable',
            url: '../../json/e-dialog-tb1.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers', title: '#'},
                {field: 'department', sort: true, title: '部门'},
                {field: 'position', sort: true, title: '职位'},
                {field: 'name', sort: true, title: '姓名'},
                {field: 'summary', sort: true, title: '本周工作总结'},
                {field: 'plan', sort: true, title: '下周工作计划'},
                {field: 'comments', sort: true, title: '备注'},
                {
                    sort: true, title: '发布时间', templet: function (d) {
                        return util.toDateString(d.createTime);
                    }
                },
                {toolbar: '#eDialogTbState', sort: true, title: '状态'},
                {align: 'center', toolbar: '#eDialogTbBar', title: '操作', minWidth: 180}
            ]],
            size: 'lg'
        });

        // 工具条点击事件
        table.on('tool(eDialogTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            if (layEvent === 'comments') {  // 查看评论
                showComments(data.id);
            } else if (layEvent == 'view') {
                showInfo(data);
            } else if (layEvent == 'checkList') {
                openCheckList(data);
            }
        });

        // 查看详情
        function showInfo(data) {
            laytpl(eDialogInfoDialog.innerHTML).render(data, function (html) {
                admin.open({
                    type: 1,
                    title: '查看详情',
                    area: '550px',
                    content: html,
                    success: function () {
                        layer.photos({
                            photos: '#eDialogInfoImgs',
                            shade: .1,
                            closeBtn: true
                        });
                    }
                });
            });
        }

        // 查看评论
        function showComments(id) {
            admin.open({
                type: 1,
                area: '650px',
                offset: '65px',
                title: '查看评论',
                content: $('#eDialogCommentDialog').html(),
                success: function (layero) {
                    // 渲染表格
                    var insTbCom = table.render({
                        elem: '#eDialogCommentTable',
                        url: '../../json/e-dialog-tb2.json?id=' + id,
                        page: true,
                        height: 400,
                        cellMinWidth: 100,
                        cols: [[
                            {type: 'numbers', title: '#'},
                            {field: 'nickName', sort: true, title: '评论人', width: 100},
                            {field: 'content', sort: true, title: '评论内容'},
                            {
                                title: '评论时间', sort: true, templet: function (d) {
                                    return util.toDateString(d.createTime);
                                }
                            },
                            {align: 'center', toolbar: '#eDialogCommentTbBar', title: '操作', minWidth: 80, width: 80}
                        ]],
                        done: function () {
                            $(layero).find('.layui-table-view').css('margin', '0');
                        }
                    });

                    // 查看评论工具条点击事件
                    table.on('tool(eDialogCommentTable)', function (obj) {
                        var data = obj.data;
                        var layEvent = obj.event;
                        if (layEvent === 'del') { // 删除
                            layer.msg('删除成功', {icon: 1});
                        }
                    });

                    // 发表评论
                    $('#eDialogCommentBtnAdd').click(function () {
                        layer.prompt({
                            title: '发表评论',
                            shade: .1,
                            offset: '165px',
                            skin: 'layui-layer-admin layui-layer-prompt',
                            formType: 2
                        }, function (value, index, elem) {
                            layer.close(index);
                            layer.msg('评论成功', {icon: 1});
                        });
                    });

                }
            });
        }

        // 审核记录弹窗
        function openCheckList(d) {
            laytpl(eDialogCheckDialog.innerHTML).render(d, function (html) {
                admin.open({
                    type: 1,
                    title: '审核详情',
                    content: html
                });
            });
        }

    });
</script>
</body>
</html>