<?php
/**
 * 路由配置
 */
return [
    // 前台路由
    'GET:/article/list' => ['ArticleController', 'getList'],
    'GET:/article/detail' => ['ArticleController', 'getDetail'],
    'POST:/article/view' => ['ArticleController', 'updateView'],
    'GET:/article/related' => ['ArticleController', 'getRelated'],
    'GET:/article/category/list' => ['ArticleController', 'getCategoryList'],
    
    // 文件上传
    'POST:/upload/image' => ['UploadController', 'uploadImage'],
    
    // 管理员认证路由
    'GET:/admin/login' => ['AdminAuthController', 'loginPage'],
    'POST:/admin/login' => ['AdminAuthController', 'login'],
    'GET:/admin/logout' => ['AdminAuthController', 'logout'],
    'GET:/admin/captcha' => ['AdminAuthController', 'captcha'],
    
    // 后台路由
    'GET:/admin' => ['AdminDashboardController', 'index'],
    'GET:/admin/dashboard' => ['AdminDashboardController', 'index'],
    'GET:/admin/article/list' => ['AdminArticleController', 'getList'],
    'GET:/admin/article/detail' => ['AdminArticleController', 'getDetail'],
    'POST:/admin/article/save' => ['AdminArticleController', 'saveArticle'],
    'POST:/admin/article/delete' => ['AdminArticleController', 'deleteArticle'],
    'POST:/admin/article/update_status' => ['AdminArticleController', 'updateStatus'],
    'GET:/admin/article/category/list_all' => ['AdminArticleController', 'getAllCategories'],
    'GET:/admin/article/category/list' => ['AdminArticleController', 'getCategoryList'],
    'POST:/admin/article/category/save' => ['AdminArticleController', 'saveCategory'],
    'POST:/admin/article/category/delete' => ['AdminArticleController', 'deleteCategory'],
    'POST:/admin/upload/image' => ['UploadController', 'adminUploadImage'],
    
    // 会员等级管理路由
    'GET:/admin/user/level' => ['AdminUserLevelController', 'index'],
    'GET:/admin/user/level/list' => ['AdminUserLevelController', 'list'],
    'GET:/admin/user/level/detail' => ['AdminUserLevelController', 'detail'],
    'POST:/admin/user/level/save' => ['AdminUserLevelController', 'save'],
    'POST:/admin/user/level/delete' => ['AdminUserLevelController', 'delete'],
    'POST:/admin/user/level/update_status' => ['AdminUserLevelController', 'updateStatus'],
    'POST:/admin/user/level/set_default' => ['AdminUserLevelController', 'setDefault'],
    'POST:/admin/user/level/sort' => ['AdminUserLevelController', 'sort'],
    
    // 用户会员等级路由
    'GET:/user/level' => ['UserLevelController', 'index'],
    'GET:/user/level/detail' => ['UserLevelController', 'detail'],
    'POST:/user/level/create_upgrade_order' => ['UserLevelController', 'createUpgradeOrder'],
    'POST:/user/level/pay_callback' => ['UserLevelController', 'payCallback'],
    
    // 用户余额管理路由
    'GET:/user/balance' => ['UserBalanceController', 'index'],
    'GET:/user/balance/logs' => ['UserBalanceController', 'logs'],
    'POST:/user/balance/create_recharge_order' => ['UserBalanceController', 'createRechargeOrder'],
    'POST:/user/balance/recharge_callback' => ['UserBalanceController', 'rechargeCallback'],
    
    // API路由
    'GET:/api/article/list' => ['ApiArticleController', 'getList'],
    'GET:/api/article/detail' => ['ApiArticleController', 'getDetail'],
    'GET:/api/article/category/list' => ['ApiArticleController', 'getCategoryList'],
    
    // 其他路由...
];