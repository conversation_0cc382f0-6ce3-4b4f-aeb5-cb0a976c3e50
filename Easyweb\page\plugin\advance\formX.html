<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>表单扩展</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-form" style="padding-bottom:  70px;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">常用验证，填了才会验证，可用作非必填项</div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">手机非必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入手机号"
                                       lay-verType="tips" lay-verify="phoneX"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">邮箱非必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入邮箱"
                                       lay-verType="tips" lay-verify="emailX"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网址非必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入网址"
                                       lay-verType="tips" lay-verify="urlX"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">数字非必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入数字"
                                       lay-verType="tips" lay-verify="numberX"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">身份证非必</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入身份证号"
                                       lay-verType="tips" lay-verify="identityX"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">layui自带验证，可用作必填项
                        <i id="closeFormBtn" class="layui-icon" style="font-weight: 600;float: right;cursor: pointer;">&#x1006;</i>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">手机号必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入手机号"
                                       lay-verType="tips" lay-verify="phone"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">邮箱必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入邮箱"
                                       lay-verType="tips" lay-verify="email"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网址必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入网址"
                                       lay-verType="tips" lay-verify="url"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">数字必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入数字"
                                       lay-verType="tips" lay-verify="number"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">身份证必填</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入身份证号"
                                       lay-verType="tips" lay-verify="identity"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">常用数字验证规则</div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">整数</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入整数"
                                       lay-verType="tips" lay-verify="digits"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">正整数</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入正整数"
                                       lay-verType="tips" lay-verify="digitsP"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">负整数</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入负整数"
                                       lay-verType="tips" lay-verify="digitsN"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">正整数或0</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入正整数或0"
                                       lay-verType="tips" lay-verify="digitsPZ"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">负整数或0</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请输入负整数或0"
                                       lay-verType="tips" lay-verify="digitsNZ"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">更多验证规则</div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">密码</label>
                            <div class="layui-input-block">
                                <input id="edtPsw" class="layui-input" placeholder="密码必须5到12位，且不能出现空格"
                                       lay-verType="tips" lay-verify="psw"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">重复</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="请再次输入密码"
                                       lay-verType="tips" lay-verify="equalTo" lay-equalTo="#edtPsw"
                                       lay-equalToText="两次输入密码不一致"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">最小长度</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="最少输入5个字符" minlength="5"
                                       lay-verType="tips" lay-verify="required|h5"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">最大长度</label>
                            <div class="layui-input-block">
                                <input class="layui-input" placeholder="最多输入10个字符" maxlength="10"
                                       lay-verType="tips" lay-verify="h5"/>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">最大最小值</label>
                            <div class="layui-input-block">
                                <input class="layui-input" type="number" placeholder="值只能在-9到9之间" min="-9" max="9"
                                       lay-verType="tips" lay-verify="required|numberX|h5"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs12">
                <div class="layui-card">
                    <div class="layui-card-body text-danger">
                        表单扩展formX模块还封装了动态渲染select、验证码倒计时、获取表单修改过的字段等实用功能。
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center"
             style="position: fixed;bottom: 0;left: 0;right: 0;background-color: #fff;box-shadow: 0 -1px 5px rgba(0,0,0,.15);padding: 15px;">
            <button class="layui-btn layui-btn-primary" type="reset">重置表单</button>
            <button class="layui-btn" lay-filter="submitDemo" lay-submit>表单验证</button>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['form', 'formX'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var formX = layui.formX;

        // 监听表单提交
        form.on('submit(submitDemo)', function (data) {
            layer.msg('表单验证通过', {icon: 1});
            return false;
        });

        $('#closeFormBtn').click(function () {
            $(this).parent().parent().parent().remove();
        });

    });
</script>
</body>
</html>