<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>实验课程管理</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=317"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格顶部工具栏 -->
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">课程名称：</label>
                        <div class="layui-input-inline mr0">
                            <input name="experimentTypeName" class="layui-input" type="text" placeholder="输入课程名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="eCourseTbSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button id="eCourseAddBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加
                        </button>
                    </div>
                </div>
            </div>
            <table id="eCourseTable" lay-filter="eCourseTable"></table>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-body">
            这个示例展示了两个关联表在同一个表单添加、修改的操作，添加时因主表还未添加，从表无法获得关联id，
            所以实现了主表添加时前端对从表进行添加、修改、删除，主表修改时再使用后端对从表进行添加、修改、删除。
            <span class="text-danger">表格内复选框使用easyweb提供的ew-tb-cell-ck公共类包裹可解决layui的样式bug</span>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="eCourseTbBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<!-- 表单弹窗 -->
<script type="text/html" id="eCourseEditDialog">
    <form id="eCourseEditForm" lay-filter="eCourseEditForm" class="layui-form model-form">
        <input name="experimentTypeId" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label">课程名称：</label>
            <div class="layui-input-block">
                <input name="experimentTypeName" placeholder="输入课程名称" type="text" class="layui-input" maxlength="20"
                       lay-verType="tips" lay-verify="required"/>
            </div>
        </div>
        <div class="layui-form-item" style="position: relative;">
            <label class="layui-form-label">实验项目：</label>
            <div class="layui-input-block">
                <table id="formSSXMTable" lay-filter="formSSXMTable"></table>
            </div>
            <button class="layui-btn layui-btn-sm icon-btn" id="demoEDeCourseAddBtnComment"
                    style="position: absolute; left: 20px;top: 60px;padding: 0 5px;" type="button">
                <i class="layui-icon">&#xe654;</i>添加项目
            </button>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
            <button class="layui-btn" lay-filter="eCourseEditSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>
<!-- 表格操作列 -->
<script type="text/html" id="eCourseCouTbBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<!-- 表格操作列 -->
<script type="text/html" id="eCourseTbLook">
    <span class="layui-text">
        <a href="javascript:;" lay-event="look">
            <i class="layui-icon" style="font-size: 12px;">&#xe61a;</i> 查看实验项目
        </a>
    </span>
</script>
<!-- 表格操作列 -->
<script type="text/html" id="eCourseTbWeek">
    <div class="ew-tb-cell-ck">
        <input type="checkbox" lay-skin="primary" title="周一" checked/>
        <input type="checkbox" lay-skin="primary" title="周二" checked/>
        <input type="checkbox" lay-skin="primary" title="周三"/>
        <input type="checkbox" lay-skin="primary" title="周四"/>
        <input type="checkbox" lay-skin="primary" title="周五" checked/>
    </div>
</script>
<!-- 表单弹窗 -->
<script type="text/html" id="eCourseCouEditDialog">
    <form id="eCourseCouEditForm" lay-filter="eCourseCouEditForm" class="layui-form model-form">
        <input name="experimentId" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label">项目名称：</label>
            <div class="layui-input-block">
                <input name="experimentName" placeholder="请输入项目名称" type="text" class="layui-input" maxlength="20"
                       lay-verType="tips" lay-verify="required"/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
            <button class="layui-btn" lay-filter="eCourseCouEditSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=317"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;

        // 渲染表格
        var insTb = table.render({
            elem: '#eCourseTable',
            url: '../../json/e-course-tb1.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'experimentTypeName', title: '课程名称', sort: true},
                {align: 'center', title: '星期', templet: '#eCourseTbWeek', minWidth: 350, width: 350},
                {align: 'center', title: '实验项目', toolbar: '#eCourseTbLook', minWidth: 140, width: 140},
                {
                    field: 'createTime', title: '创建时间', sort: true, templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, width: 180
                },
                {align: 'center', title: '操作', toolbar: '#eCourseTbBar', minWidth: 130, width: 130}
            ]]
        });

        // 搜索
        form.on('submit(eCourseTbSearch)', function (data) {
            insTb.reload({where: data.field, page: {curr: 1}});
        });

        // 添加
        $("#eCourseAddBtn").click(function () {
            showEditModel();
        });

        // 工具条点击事件
        table.on('tool(eCourseTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            if (layEvent == 'edit') {
                showEditModel(data);
            } else if (layEvent == 'del') {
                doDel(data.experimentTypeId);
            } else if (layEvent == 'look') {
                var $a = $(obj.tr).find('a[lay-event="look"]');
                var offset = $a.offset();
                var top = offset.top;
                var left = offset.left;
                layer.open({
                    type: 1,
                    title: false,
                    area: '530px',
                    offset: [top + 'px', (left - 530 + $a.outerWidth()) + 'px'],
                    shade: .01,
                    shadeClose: true,
                    fixed: false,
                    content: '<table id="lookSSXMTable" lay-filter="lookSSXMTable"></table>',
                    success: function (layero) {
                        table.render({
                            elem: '#lookSSXMTable',
                            url: '../../json/e-course-tb2.json?experimentTypeId=' + data.experimentTypeId,
                            page: true,
                            cellMinWidth: 100,
                            cols: [[
                                {type: 'numbers'},
                                {field: 'experimentName', title: '项目名称', sort: true},
                                {
                                    field: 'createTime', title: '创建时间', sort: true, templet: function (d) {
                                        return util.toDateString(d.createTime);
                                    }
                                }
                            ]],
                            done: function () {
                                $(layero).find('.layui-table-view').css('margin', '0');
                            },
                            size: ''
                        });
                    }
                });
            }
        });

        // 显示表单弹窗
        function showEditModel(expTpe) {
            admin.open({
                type: 1,
                title: (expTpe ? '修改' : '添加') + '实验课程',
                content: $('#eCourseEditDialog').html(),
                area: '700px',
                success: function (layero, dIndex) {
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    var url = expTpe ? '../../json/ok.json' : '../../json/ok.json';
                    var isExpAdd = expTpe ? false : true;
                    // 回显数据
                    form.val('eCourseEditForm', expTpe);
                    // 表单提交事件
                    form.on('submit(eCourseEditSubmit)', function (data) {
                        if (isExpAdd) {
                            if (xxDataList.length <= 0) {
                                layer.tips('请添加实验项目', '#demoEDeCourseAddBtnComment', {tips: [1, '#ff4c4c']});
                                return false;
                            }
                            var nList = admin.util.deepClone(xxDataList);
                            for (var xi = 0; xi < nList.length; xi++) {
                                nList[xi].experimentId = undefined;
                            }
                            data.field.expListJson = JSON.stringify(nList);
                        }
                        layer.load(2);
                        $.get(url, data.field, function (res) {
                            layer.closeAll('loading');
                            if (res.code == 200) {
                                layer.close(dIndex);
                                insTb.reload({page: {curr: 1}});
                                layer.msg(res.msg, {icon: 1});
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');
                        return false;
                    });
                    //
                    var xxDataList = [];
                    var tbOptions = {
                        elem: '#formSSXMTable',
                        data: xxDataList,
                        page: true,
                        height: '350px;',
                        cellMinWidth: 100,
                        cols: [[
                            {type: 'numbers'},
                            {field: 'experimentName', title: '项目名称', sort: true},
                            {
                                field: 'createTime', title: '创建时间', sort: true, templet: function (d) {
                                    return util.toDateString(d.createTime);
                                }
                            },
                            {align: 'center', title: '操作', toolbar: '#eCourseCouTbBar', minWidth: 120, width: 120}
                        ]],
                        done: function () {
                            $(layero).find('.layui-table-view').css('margin', '0');
                        },
                        size: ''
                    };
                    if (isExpAdd) {

                    } else {
                        tbOptions.data = undefined;
                        tbOptions.url = '../../json/e-course-tb2.json?experimentTypeId=' + expTpe.experimentTypeId;
                    }
                    var insTbSSXM = table.render(tbOptions);
                    // 工具条点击事件
                    table.on('tool(formSSXMTable)', function (obj) {
                        var data = obj.data;
                        var layEvent = obj.event;
                        if (layEvent == 'edit') {
                            showEditModel2(data);
                        } else if (layEvent == 'del') {
                            layer.confirm('确定要删除吗？', {
                                shade: .1,
                                skin: 'layui-layer-admin'
                            }, function (i) {
                                layer.close(i);
                                if (isExpAdd) {  // 前端处理删除
                                    for (var j = 0; j < xxDataList.length; j++) {
                                        if (xxDataList[j].experimentId = data.experimentId) {
                                            xxDataList.splice(j, 1);
                                        }
                                    }
                                    insTbSSXM.reload({data: xxDataList, page: {curr: 1}});
                                } else {  // 后端处理删除
                                    layer.load(2);
                                    $.get('../../json/ok.json', {
                                        experimentId: data.experimentId
                                    }, function (res) {
                                        layer.closeAll('loading');
                                        if (res.code == 200) {
                                            layer.msg(res.msg, {icon: 1});
                                            insTb.reload({page: {curr: 1}});
                                            insTbSSXM.reload({page: {curr: 1}});
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    }, 'json');
                                }
                            });
                        }
                    });
                    //
                    $('#demoEDeCourseAddBtnComment').click(function () {
                        showEditModel2();
                    });

                    // 显示表单弹窗
                    function showEditModel2(exp) {
                        admin.open({
                            type: 1,
                            offset: '150px',
                            title: (exp ? '修改' : '添加') + '项目',
                            content: $('#eCourseCouEditDialog').html(),
                            success: function (layero, dIndex) {
                                $(layero).children('.layui-layer-content').css('overflow', 'visible');
                                var url = exp ? '../../json/ok.json' : '../../json/ok.json';
                                // 回显数据
                                form.val('eCourseCouEditForm', exp);
                                // 表单提交事件
                                form.on('submit(eCourseCouEditSubmit)', function (data) {
                                    if (isExpAdd) {  // 前端处理添加、修改
                                        if (exp) {  // 前端修改
                                            for (var i = 0; i < xxDataList.length; i++) {
                                                if (xxDataList[i].experimentName == data.field.experimentName && xxDataList[i].experimentId != data.field.experimentId) {
                                                    layer.msg('实验项目名称已存在', {icon: 2});
                                                    return false;
                                                }
                                            }
                                            layer.close(dIndex);
                                            for (var j = 0; j < xxDataList.length; j++) {
                                                if (xxDataList[j].experimentId == data.field.experimentId) {
                                                    xxDataList[j].experimentName = data.field.experimentName;
                                                }
                                            }
                                            insTbSSXM.reload({data: xxDataList, page: {curr: 1}});
                                        } else {  // 前端添加
                                            for (var i = 0; i < xxDataList.length; i++) {
                                                if (xxDataList[i].experimentName == data.field.experimentName) {
                                                    layer.msg('实验项目名称已存在', {icon: 2});
                                                    return false;
                                                }
                                            }
                                            layer.close(dIndex);
                                            data.field.experimentId = new Date().getTime();
                                            xxDataList.push(data.field);
                                            insTbSSXM.reload({data: xxDataList, page: {curr: 1}});
                                        }
                                    } else {  // 后端处理添加修改
                                        layer.load(2);
                                        if (!exp) {
                                            data.field.experimentTypeId = expTpe.experimentTypeId;
                                        }
                                        $.get(url, {json: JSON.stringify(data.field)}, function (res) {
                                            layer.closeAll('loading');
                                            if (res.code == 200) {
                                                layer.close(dIndex);
                                                insTbSSXM.reload({page: {curr: 1}});
                                                layer.msg(res.msg, {icon: 1});
                                            } else {
                                                layer.msg(res.msg, {icon: 2});
                                            }
                                        }, 'json');
                                    }
                                    return false;
                                });
                            }
                        });
                    }

                }
            });
        }

        // 删除
        function doDel(experimentTypeId) {
            layer.confirm('确定要删除吗？', {
                shade: .1,
                skin: 'layui-layer-admin'
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('../../json/ok.json', {
                    experimentTypeId: experimentTypeId
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code == 200) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload({page: {curr: 1}});
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }

    });
</script>
</body>
</html>