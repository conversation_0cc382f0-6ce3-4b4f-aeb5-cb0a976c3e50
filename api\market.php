<?php
// 获取分类列表
$stmt = $pdo->prepare("SELECT * FROM api_categories WHERE status = 1 ORDER BY sort_order ASC");
$stmt->execute();
$categories = $stmt->fetchAll();

// 获取当前分类
$category_id = isset($_GET['category']) ? intval($_GET['category']) : 0;

// 搜索关键词
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';

// 构建查询条件
$where = "WHERE a.status = 1";
$params = [];

if ($category_id > 0) {
    $where .= " AND a.category_id = ?";
    $params[] = $category_id;
}

if (!empty($keyword)) {
    $where .= " AND (a.name LIKE ? OR a.description LIKE ?)";
    $params[] = "%{$keyword}%";
    $params[] = "%{$keyword}%";
}

// 分页
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 12;
$offset = ($page - 1) * $limit;

// 获取API总数
$stmt = $pdo->prepare("SELECT COUNT(*) FROM api_apis a {$where}");
$stmt->execute($params);
$total = $stmt->fetchColumn();

// 计算总页数
$total_pages = ceil($total / $limit);

// 获取API列表
$sql = "SELECT a.*, c.name as category_name 
        FROM api_apis a 
        LEFT JOIN api_categories c ON a.category_id = c.id 
        {$where} 
        ORDER BY a.sort_order ASC, a.id DESC 
        LIMIT {$offset}, {$limit}";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$apis = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API市场 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="/public/css/font-awesome.min.css">
    <link rel="stylesheet" href="/public/css/style.css">
</head>
<body>
    <!-- 头部导航 -->
    <?php include '../templates/common/header.php'; ?>
    
    <!-- 页面标题 -->
    <div class="bg-primary text-white py-5">
        <div class="container">
            <h1 class="display-4">API市场</h1>
            <p class="lead">发现和使用各种强大的API接口，助力您的应用开发</p>
            
            <!-- 搜索框 -->
            <div class="mt-4">
                <form action="/api/market" method="get" class="form-inline">
                    <div class="input-group w-100">
                        <input type="text" name="keyword" class="form-control form-control-lg" placeholder="搜索API..." value="<?php echo htmlspecialchars($keyword); ?>">
                        <div class="input-group-append">
                            <button class="btn btn-light btn-lg" type="submit"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container py-5">
        <div class="row">
            <!-- 左侧分类 -->
            <div class="col-md-3">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">API分类</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="/api/market" class="list-group-item list-group-item-action <?php echo $category_id === 0 ? 'active' : ''; ?>">
                            全部分类
                        </a>
                        <?php foreach ($categories as $category): ?>
                        <a href="/api/market?category=<?php echo $category['id']; ?>" class="list-group-item list-group-item-action <?php echo $category_id === $category['id'] ? 'active' : ''; ?>">
                            <?php if (!empty($category['icon'])): ?>
                            <i class="<?php echo $category['icon']; ?> mr-2"></i>
                            <?php endif; ?>
                            <?php echo $category['name']; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- 热门API -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">热门API</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <?php
                        $stmt = $pdo->prepare("SELECT * FROM api_apis WHERE status = 1 ORDER BY call_count DESC LIMIT 5");
                        $stmt->execute();
                        $hotApis = $stmt->fetchAll();
                        ?>
                        <?php foreach ($hotApis as $api): ?>
                        <a href="/api/detail?id=<?php echo $api['id']; ?>" class="list-group-item list-group-item-action">
                            <?php echo $api['name']; ?>
                            <span class="badge badge-primary float-right"><?php echo number_format($api['call_count']); ?>次调用</span>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- 右侧API列表 -->
            <div class="col-md-9">
                <?php if (!empty($keyword)): ?>
                <div class="alert alert-info">
                    搜索 "<?php echo htmlspecialchars($keyword); ?>" 的结果，共找到 <?php echo $total; ?> 个API
                </div>
                <?php endif; ?>
                
                <?php if ($category_id > 0): ?>
                <?php
                $stmt = $pdo->prepare("SELECT * FROM api_categories WHERE id = ?");
                $stmt->execute([$category_id]);
                $currentCategory = $stmt->fetch();
                ?>
                <?php if ($currentCategory): ?>
                <div class="mb-4">
                    <h3><?php echo $currentCategory['name']; ?></h3>
                    <p class="text-muted"><?php echo $currentCategory['description']; ?></p>
                </div>
                <?php endif; ?>
                <?php endif; ?>
                
                <?php if (empty($apis)): ?>
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-circle mr-2"></i> 暂无API数据
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($apis as $api): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <?php echo $api['name']; ?>
                                    <?php if ($api['is_free'] == 1): ?>
                                    <span class="badge badge-success float-right">免费</span>
                                    <?php else: ?>
                                    <span class="badge badge-warning float-right">付费</span>
                                    <?php endif; ?>
                                </h5>
                                <p class="card-text text-muted small">
                                    <span class="badge badge-light"><?php echo $api['category_name']; ?></span>
                                    <span class="badge badge-light"><?php echo $api['method']; ?></span>
                                </p>
                                <p class="card-text" style="height: 48px; overflow: hidden;">
                                    <?php echo mb_substr($api['description'], 0, 50); ?><?php echo mb_strlen($api['description']) > 50 ? '...' : ''; ?>
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="text-muted small">
                                        <i class="fa fa-bar-chart"></i> <?php echo number_format($api['call_count']); ?>次调用
                                    </div>
                                    <?php if ($api['is_free'] == 0): ?>
                                    <div class="text-danger font-weight-bold">
                                        ¥<?php echo number_format($api['price'], 2); ?>/次
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-footer bg-white">
                                <a href="/api/detail?id=<?php echo $api['id']; ?>" class="btn btn-primary btn-sm">查看详情</a>
                                <a href="/api/debug?id=<?php echo $api['id']; ?>" class="btn btn-outline-secondary btn-sm">在线调试</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- 分页 -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="/api/market?page=1<?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo !empty($keyword) ? '&keyword='.urlencode($keyword) : ''; ?>">首页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="/api/market?page=<?php echo $page-1; ?><?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo !empty($keyword) ? '&keyword='.urlencode($keyword) : ''; ?>">上一页</a>
                        </li>
                        <?php endif; ?>
                        
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        
                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="/api/market?page=<?php echo $i; ?><?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo !empty($keyword) ? '&keyword='.urlencode($keyword) : ''; ?>"><?php echo $i; ?></a>
                        </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="/api/market?page=<?php echo $page+1; ?><?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo !empty($keyword) ? '&keyword='.urlencode($keyword) : ''; ?>">下一页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="/api/market?page=<?php echo $total_pages; ?><?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo !empty($keyword) ? '&keyword='.urlencode($keyword) : ''; ?>">末页</a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <?php include '../templates/common/footer.php'; ?>
    
    <script src="/public/js/jquery-3.5.1.min.js"></script>
    <script src="/public/js/bootstrap.bundle.min.js"></script>
    <script src="/public/js/main.js"></script>
</body>
</html>