<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>管理员登录测试</h2>
    <form id="loginForm">
        <div>
            <label>用户名:</label>
            <input type="text" name="username" value="admin" required>
        </div>
        <div>
            <label>密码:</label>
            <input type="password" name="password" value="admin123" required>
        </div>
        <div>
            <label>验证码:</label>
            <input type="text" name="captcha" required>
            <img id="captcha" src="admin/controllers/AuthController.php?action=captcha" alt="验证码" style="cursor: pointer;">
        </div>
        <div>
            <button type="submit">登录</button>
        </div>
    </form>
    
    <div id="result"></div>
    
    <script>
    $(document).ready(function() {
        // 点击验证码刷新
        $('#captcha').click(function() {
            this.src = 'admin/controllers/AuthController.php?action=captcha&t=' + new Date().getTime();
        });
        
        // 表单提交
        $('#loginForm').submit(function(e) {
            e.preventDefault();
            
            $.ajax({
                url: 'admin/controllers/AuthController.php?action=login',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(res) {
                    $('#result').html('<p style="color: ' + (res.code === 200 ? 'green' : 'red') + '">' + res.msg + '</p>');
                    if (res.code === 200) {
                        setTimeout(function() {
                            window.location.href = 'admin/index.php';
                        }, 1000);
                    } else {
                        // 刷新验证码
                        $('#captcha')[0].src = 'admin/controllers/AuthController.php?action=captcha&t=' + new Date().getTime();
                    }
                },
                error: function() {
                    $('#result').html('<p style="color: red">服务器错误</p>');
                }
            });
        });
    });
    </script>
</body>
</html>
