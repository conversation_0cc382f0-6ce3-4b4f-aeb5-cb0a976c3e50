<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>工单管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .ticket-detail-container {
            padding: 20px;
        }
        .ticket-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .ticket-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .ticket-info {
            color: #666;
            font-size: 13px;
        }
        .ticket-info span {
            margin-right: 15px;
        }
        .ticket-content {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .ticket-reply-list {
            margin-bottom: 20px;
        }
        .ticket-reply-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #f0f0f0;
        }
        .ticket-reply-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .ticket-reply-user {
            font-weight: bold;
        }
        .ticket-reply-time {
            color: #999;
            font-size: 12px;
        }
        .ticket-reply-content {
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        .ticket-reply-admin {
            background-color: #e6f7ff;
        }
        .ticket-status-badge {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
        }
        .ticket-status-pending {
            background-color: #FFB800;
        }
        .ticket-status-processing {
            background-color: #1E9FFF;
        }
        .ticket-status-resolved {
            background-color: #5FB878;
        }
        .ticket-status-closed {
            background-color: #999;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">工单管理</div>
        <div class="layui-card-body">
            <div class="layui-tab" lay-filter="ticketTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">全部工单</li>
                    <li>待处理</li>
                    <li>处理中</li>
                    <li>已解决</li>
                    <li>已关闭</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input id="searchKeyword" class="layui-input" type="text" placeholder="工单标题/内容"/>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <select id="searchType">
                                        <option value="">工单类型</option>
                                        <option value="api_error">API错误</option>
                                        <option value="account_issue">账号问题</option>
                                        <option value="billing_issue">计费问题</option>
                                        <option value="feature_request">功能建议</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <select id="searchPriority">
                                        <option value="">优先级</option>
                                        <option value="low">低</option>
                                        <option value="medium">中</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <button id="searchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                    <button id="exportBtn" class="layui-btn layui-btn-primary icon-btn"><i class="layui-icon">&#xe67d;</i>导出</button>
                                </div>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="ticketTable" lay-filter="ticketTable"></table>
                    </div>
                    
                    <div class="layui-tab-item">
                        <!-- 待处理工单 -->
                        <table class="layui-table" id="pendingTable" lay-filter="ticketTable"></table>
                    </div>
                    
                    <div class="layui-tab-item">
                        <!-- 处理中工单 -->
                        <table class="layui-table" id="processingTable" lay-filter="ticketTable"></table>
                    </div>
                    
                    <div class="layui-tab-item">
                        <!-- 已解决工单 -->
                        <table class="layui-table" id="resolvedTable" lay-filter="ticketTable"></table>
                    </div>
                    
                    <div class="layui-tab-item">
                        <!-- 已关闭工单 -->
                        <table class="layui-table" id="closedTable" lay-filter="ticketTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="view">查看</a>
    {{# if(d.status === 'pending' || d.status === 'processing'){ }}
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="reply">回复</a>
    {{# } }}
    {{# if(d.status !== 'closed'){ }}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="close">关闭</a>
    {{# } }}
</script>

<!-- 工单状态 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status === 'pending'){ }}
    <span class="ticket-status-badge ticket-status-pending">待处理</span>
    {{# }else if(d.status === 'processing'){ }}
    <span class="ticket-status-badge ticket-status-processing">处理中</span>
    {{# }else if(d.status === 'resolved'){ }}
    <span class="ticket-status-badge ticket-status-resolved">已解决</span>
    {{# }else if(d.status === 'closed'){ }}
    <span class="ticket-status-badge ticket-status-closed">已关闭</span>
    {{# } }}
</script>

<!-- 工单优先级 -->
<script type="text/html" id="priorityTpl">
    {{# if(d.priority === 'low'){ }}
    <span class="layui-badge layui-bg-gray">低</span>
    {{# }else if(d.priority === 'medium'){ }}
    <span class="layui-badge layui-bg-blue">中</span>
    {{# }else if(d.priority === 'high'){ }}
    <span class="layui-badge layui-bg-orange">高</span>
    {{# }else if(d.priority === 'urgent'){ }}
    <span class="layui-badge">紧急</span>
    {{# } }}
</script>

<!-- 工单详情弹窗 -->
<script type="text/html" id="ticketDetailDialog">
    <div class="ticket-detail-container">
        <div class="ticket-header">
            <div class="ticket-title">{{d.title}}</div>
            <div class="ticket-info">
                <span>提交人：{{d.user_name}}</span>
                <span>提交时间：{{d.create_time}}</span>
                <span>工单类型：{{d.type_text}}</span>
                <span>优先级：
                    {{# if(d.priority === 'low'){ }}
                    <span class="layui-badge layui-bg-gray">低</span>
                    {{# }else if(d.priority === 'medium'){ }}
                    <span class="layui-badge layui-bg-blue">中</span>
                    {{# }else if(d.priority === 'high'){ }}
                    <span class="layui-badge layui-bg-orange">高</span>
                    {{# }else if(d.priority === 'urgent'){ }}
                    <span class="layui-badge">紧急</span>
                    {{# } }}
                </span>
                <span>状态：
                    {{# if(d.status === 'pending'){ }}
                    <span class="ticket-status-badge ticket-status-pending">待处理</span>
                    {{# }else if(d.status === 'processing'){ }}
                    <span class="ticket-status-badge ticket-status-processing">处理中</span>
                    {{# }else if(d.status === 'resolved'){ }}
                    <span class="ticket-status-badge ticket-status-resolved">已解决</span>
                    {{# }else if(d.status === 'closed'){ }}
                    <span class="ticket-status-badge ticket-status-closed">已关闭</span>
                    {{# } }}
                </span>
            </div>
        </div>
        
        <div class="ticket-content">
            {{d.content}}
        </div>
        
        {{# if(d.attachments && d.attachments.length > 0){ }}
        <div class="ticket-attachments">
            <div class="layui-form-item">
                <label class="layui-form-label">附件：</label>
                <div class="layui-input-block">
                    {{# layui.each(d.attachments, function(index, item){ }}
                    <a href="{{item.url}}" target="_blank" class="layui-btn layui-btn-xs">{{item.name}}</a>
                    {{# }); }}
                </div>
            </div>
        </div>
        {{# } }}
        
        <div class="ticket-reply-list">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>回复记录</legend>
            </fieldset>
            
            {{# if(d.replies && d.replies.length > 0){ }}
                {{# layui.each(d.replies, function(index, item){ }}
                <div class="ticket-reply-item">
                    <div class="ticket-reply-header">
                        <div class="ticket-reply-user">{{item.user_name}} {{item.is_admin ? '(客服)' : '(用户)'}}</div>
                        <div class="ticket-reply-time">{{item.create_time}}</div>
                    </div>
                    <div class="ticket-reply-content {{item.is_admin ? 'ticket-reply-admin' : ''}}">
                        {{item.content}}
                    </div>
                    
                    {{# if(item.attachments && item.attachments.length > 0){ }}
                    <div class="ticket-reply-attachments">
                        <div class="layui-form-item">
                            <label class="layui-form-label">附件：</label>
                            <div class="layui-input-block">
                                {{# layui.each(item.attachments, function(i, attachment){ }}
                                <a href="{{attachment.url}}" target="_blank" class="layui-btn layui-btn-xs">{{attachment.name}}</a>
                                {{# }); }}
                            </div>
                        </div>
                    </div>
                    {{# } }}
                </div>
                {{# }); }}
            {{# }else{ }}
                <div class="layui-form-mid layui-word-aux">暂无回复记录</div>
            {{# } }}
        </div>
        
        {{# if(d.status !== 'closed'){ }}
        <div class="ticket-reply-form">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>回复工单</legend>
            </fieldset>
            
            <form id="replyForm" lay-filter="replyForm" class="layui-form">
                <input type="hidden" name="ticket_id" value="{{d.id}}">
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">回复内容</label>
                    <div class="layui-input-block">
                        <textarea name="content" placeholder="请输入回复内容" class="layui-textarea" style="min-height: 150px;"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">上传附件</label>
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" id="uploadAttachment">
                            <i class="layui-icon">&#xe67c;</i>上传附件
                        </button>
                        <div class="layui-upload-list" id="attachmentList"></div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">更改状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="processing" title="处理中" {{d.status === 'processing' ? 'checked' : ''}}>
                        <input type="radio" name="status" value="resolved" title="已解决" {{d.status === 'resolved' ? 'checked' : ''}}>
                        <input type="radio" name="status" value="closed" title="关闭工单">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-filter="replySubmit" lay-submit>提交回复</button>
                    </div>
                </div>
            </form>
        </div>
        {{# } }}
    </div>
</script>

<!-- 回复工单弹窗 -->
<script type="text/html" id="replyDialog">
    <form id="replyForm" lay-filter="replyForm" class="layui-form model-form">
        <input name="ticket_id" type="hidden" value="{{d.id}}"/>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">回复内容</label>
            <div class="layui-input-block">
                <textarea name="content" placeholder="请输入回复内容" class="layui-textarea" style="min-height: 200px;" lay-verify="required" required></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">上传附件</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="uploadAttachment">
                    <i class="layui-icon">&#xe67c;</i>上传附件
                </button>
                <div class="layui-upload-list" id="attachmentList"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">更改状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="processing" title="处理中" checked>
                <input type="radio" name="status" value="resolved" title="已解决">
                <input type="radio" name="status" value="closed" title="关闭工单">
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="replySubmit" lay-submit>提交回复</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'element', 'upload'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var element = layui.element;
    var upload = layui.upload;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#ticketTable',
        url: '/admin/support/tickets/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'id', title: '工单编号', width: 100},
            {field: 'title', title: '工单标题'},
            {field: 'user_name', title: '提交人', width: 120},
            {field: 'type_text', title: '工单类型', width: 120},
            {field: 'priority', title: '优先级', templet: '#priorityTpl', width: 80},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100},
            {field: 'create_time', title: '提交时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {field: 'last_reply_time', title: '最后回复', templet: function (d) {
                return d.last_reply_time ? util.toDateString(d.last_reply_time * 1000) : '-';
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 180}
        ]]
    });
    
    // 渲染待处理工单表格
    var pendingTb = table.render({
        elem: '#pendingTable',
        url: '/admin/support/tickets/list',
        where: {status: 'pending'},
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'id', title: '工单编号', width: 100},
            {field: 'title', title: '工单标题'},
            {field: 'user_name', title: '提交人', width: 120},
            {field: 'type_text', title: '工单类型', width: 120},
            {field: 'priority', title: '优先级', templet: '#priorityTpl', width: 80},
            {field: 'create_time', title: '提交时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 180}
        ]]
    });
    
    // 渲染处理中工单表格
    var processingTb = table.render({
        elem: '#processingTable',
        url: '/admin/support/tickets/list',
        where: {status: 'processing'},
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'id', title: '工单编号', width: 100},
            {field: 'title', title: '工单标题'},
            {field: 'user_name', title: '提交人', width: 120},
            {field: 'type_text', title: '工单类型', width: 120},
            {field: 'priority', title: '优先级', templet: '#priorityTpl', width: 80},
            {field: 'create_time', title: '提交时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {field: 'last_reply_time', title: '最后回复', templet: function (d) {
                return d.last_reply_time ? util.toDateString(d.last_reply_time * 1000) : '-';
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 180}
        ]]
    });
    
    // 渲染已解决工单表格
    var resolvedTb = table.render({
        elem: '#resolvedTable',
        url: '/admin/support/tickets/list',
        where: {status: 'resolved'},
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'id', title: '工单编号', width: 100},
            {field: 'title', title: '工单标题'},
            {field: 'user_name', title: '提交人', width: 120},
            {field: 'type_text', title: '工单类型', width: 120},
            {field: 'priority', title: '优先级', templet: '#priorityTpl', width: 80},
            {field: 'create_time', title: '提交时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {field: 'resolve_time', title: '解决时间', templet: function (d) {
                return d.resolve_time ? util.toDateString(d.resolve_time * 1000) : '-';
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 180}
        ]]
    });
    
    // 渲染已关闭工单表格
    var closedTb = table.render({
        elem: '#closedTable',
        url: '/admin/support/tickets/list',
        where: {status: 'closed'},
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'id', title: '工单编号', width: 100},
            {field: 'title', title: '工单标题'},
            {field: 'user_name', title: '提交人', width: 120},
            {field: 'type_text', title: '工单类型', width: 120},
            {field: 'priority', title: '优先级', templet: '#priorityTpl', width: 80},
            {field: 'create_time', title: '提交时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {field: 'close_time', title: '关闭时间', templet: function (d) {
                return d.close_time ? util.toDateString(d.close_time * 1000) : '-';
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 180}
        ]]
    });
    
    // 搜索按钮点击事件
    $('#searchBtn').click(function () {
        var keyword = $('#searchKeyword').val();
        var type = $('#searchType').val();
        var priority = $('#searchPriority').val();
        
        insTb.reload({
            where: {
                keyword: keyword,
                type: type,
                priority: priority
            },
            page: {curr: 1}
        });
    });
    
    // 导出按钮点击事件
    $('#exportBtn').click(function () {
        var keyword = $('#searchKeyword').val();
        var type = $('#searchType').val();
        var priority = $('#searchPriority').val();
        
        window.location.href = '/admin/support/tickets/export?keyword=' + keyword + '&type=' + type + '&priority=' + priority;
    });
    
    // 监听Tab切换
    element.on('tab(ticketTab)', function(data) {
        // 切换到对应的表格
        if (data.index === 0) {
            insTb.reload();
        } else if (data.index === 1) {
            pendingTb.reload();
        } else if (data.index === 2) {
            processingTb.reload();
        } else if (data.index === 3) {
            resolvedTb.reload();
        } else if (data.index === 4) {
            closedTb.reload();
        }
    });
    
    // 工具条点击事件
    table.on('tool(ticketTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'view') { // 查看
            showTicketDetail(data.id);
        } else if (layEvent === 'reply') { // 回复
            showReplyDialog(data);
        } else if (layEvent === 'close') { // 关闭
            layer.confirm('确定要关闭该工单吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/support/tickets/close', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                                    insTb.reload();
                                    pendingTb.reload();
                                    processingTb.reload();
                                    resolvedTb.reload();
                                    closedTb.reload();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }, 'json');
                            return false;
                        });
                    }
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 显示回复弹窗
    function showReplyDialog(data) {
        admin.open({
            type: 1,
            title: '回复工单',
            content: laytpl($('#replyDialog').html()).render(data),
            area: ['600px', '500px'],
            success: function (layero, dIndex) {
                form.render();
                
                // 上传附件
                upload.render({
                    elem: '#uploadAttachment',
                    url: '/admin/upload/file',
                    accept: 'file',
                    multiple: true,
                    before: function(obj) {
                        layer.load(2);
                    },
                    done: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            $('#attachmentList').append(
                                '<div class="layui-upload-item">' +
                                '<a href="' + res.data.url + '" target="_blank" class="layui-btn layui-btn-xs">' + res.data.name + '</a>' +
                                '<input type="hidden" name="attachments[]" value="' + res.data.url + '">' +
                                '<input type="hidden" name="attachment_names[]" value="' + res.data.name + '">' +
                                '<i class="layui-icon layui-icon-close attachment-delete"></i>' +
                                '</div>'
                            );
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.msg('上传失败', {icon: 2});
                    }
                });
                
                // 删除附件
                $(layero).on('click', '.attachment-delete', function() {
                    $(this).parent().remove();
                });
                
                // 表单提交事件
                form.on('submit(replySubmit)', function (data) {
                    layer.load(2);
                    $.post('/admin/support/tickets/reply', data.field, function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            insTb.reload();
                            pendingTb.reload();
                            processingTb.reload();
                            resolvedTb.reload();
                            closedTb.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    return false;
                });
            }
        });
    }
});
</script>
</body>
</html>
                        resolvedTb.reload();
                        closedTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 显示工单详情
    function showTicketDetail(id) {
        layer.load(2);
        $.get('/admin/support/tickets/detail', {
            id: id
        }, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                // 格式化时间
                res.data.create_time = util.toDateString(res.data.create_time * 1000);
                if (res.data.replies) {
                    for (var i = 0; i < res.data.replies.length; i++) {
                        res.data.replies[i].create_time = util.toDateString(res.data.replies[i].create_time * 1000);
                    }
                }
                
                admin.open({
                    type: 1,
                    title: '工单详情',
                    content: laytpl($('#ticketDetailDialog').html()).render(res.data),
                    area: ['800px', '600px'],
                    success: function (layero, dIndex) {
                        form.render();
                        
                        // 上传附件
                        upload.render({
                            elem: '#uploadAttachment',
                            url: '/admin/upload/file',
                            accept: 'file',
                            multiple: true,
                            before: function(obj) {
                                layer.load(2);
                            },
                            done: function(res) {
                                layer.closeAll('loading');
                                if (res.code === 0) {
                                    $('#attachmentList').append(
                                        '<div class="layui-upload-item">' +
                                        '<a href="' + res.data.url + '" target="_blank" class="layui-btn layui-btn-xs">' + res.data.name + '</a>' +
                                        '<input type="hidden" name="attachments[]" value="' + res.data.url + '">' +
                                        '<input type="hidden" name="attachment_names[]" value="' + res.data.name + '">' +
                                        '<i class="layui-icon layui-icon-close attachment-delete"></i>' +
                                        '</div>'
                                    );
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.closeAll('loading');
                                layer.msg('上传失败', {icon: 2});
                            }
                        });
                        
                        // 删除附件
                        $(layero).on('click', '.attachment-delete', function() {
                            $(this).parent().remove();
                        });
                        
                        // 表单提交事件
                        form.on('submit(replySubmit)', function (data) {
                            layer.load(2);
                            $.post('/admin/support/tickets/reply', data.field, function (res) {
                                layer.closeAll('loading');
                                if (res.code === 0) {
                                    layer.close(dIndex);
                                    layer.msg(res.msg, {icon: 1});
                                    insTb.reload();
                                    pendingTb.reload();
                                    processingTb.reload();