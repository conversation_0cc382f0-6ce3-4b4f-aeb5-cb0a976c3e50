<?php
/**
 * API调用日志模型
 * API管理系统 - API调用日志数据模型
 */

require_once __DIR__ . '/../core/Model.php';

class ApiCallLog extends Model {
    protected $table = 'call_logs';
    protected $fillable = [
        'user_id', 'api_id', 'request_ip', 'request_method', 'request_params',
        'request_headers', 'response_code', 'response_data', 'response_time',
        'cost', 'status', 'error_message'
    ];
    
    /**
     * 记录API调用日志
     */
    public function logApiCall($data) {
        // 处理JSON字段
        if (isset($data['request_params']) && is_array($data['request_params'])) {
            $data['request_params'] = json_encode($data['request_params'], JSON_UNESCAPED_UNICODE);
        }
        
        if (isset($data['request_headers']) && is_array($data['request_headers'])) {
            $data['request_headers'] = json_encode($data['request_headers'], JSON_UNESCAPED_UNICODE);
        }
        
        if (isset($data['response_data']) && is_array($data['response_data'])) {
            $data['response_data'] = json_encode($data['response_data'], JSON_UNESCAPED_UNICODE);
        }
        
        return parent::create($data);
    }
    
    /**
     * 获取调用日志列表（带用户和API信息）
     */
    public function getListWithDetails($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "cl.user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['api_id'])) {
            $whereClause[] = "cl.api_id = :api_id";
            $params['api_id'] = $conditions['api_id'];
        }
        
        if (!empty($conditions['status'])) {
            $whereClause[] = "cl.status = :status";
            $params['status'] = $conditions['status'];
        }
        
        if (!empty($conditions['request_ip'])) {
            $whereClause[] = "cl.request_ip = :request_ip";
            $params['request_ip'] = $conditions['request_ip'];
        }
        
        if (!empty($conditions['date_from'])) {
            $whereClause[] = "DATE(cl.created_at) >= :date_from";
            $params['date_from'] = $conditions['date_from'];
        }
        
        if (!empty($conditions['date_to'])) {
            $whereClause[] = "DATE(cl.created_at) <= :date_to";
            $params['date_to'] = $conditions['date_to'];
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->getTableName()} cl {$whereStr}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT cl.*, 
                       u.username as user_name,
                       i.name as api_name,
                       i.path as api_path
                FROM {$this->getTableName()} cl 
                LEFT JOIN {$this->db->getPrefix()}users u ON cl.user_id = u.id
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON cl.api_id = i.id
                {$whereStr}
                ORDER BY cl.id DESC 
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        // 解析JSON字段
        foreach ($data as &$item) {
            $item['request_params'] = json_decode($item['request_params'], true) ?: [];
            $item['request_headers'] = json_decode($item['request_headers'], true) ?: [];
            $item['response_data'] = json_decode($item['response_data'], true) ?: $item['response_data'];
        }
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取调用统计信息
     */
    public function getCallStats($conditions = []) {
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['api_id'])) {
            $whereClause[] = "api_id = :api_id";
            $params['api_id'] = $conditions['api_id'];
        }
        
        if (!empty($conditions['date_from'])) {
            $whereClause[] = "DATE(created_at) >= :date_from";
            $params['date_from'] = $conditions['date_from'];
        }
        
        if (!empty($conditions['date_to'])) {
            $whereClause[] = "DATE(created_at) <= :date_to";
            $params['date_to'] = $conditions['date_to'];
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        $sql = "SELECT 
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as error_calls,
                    AVG(response_time) as avg_response_time,
                    SUM(cost) as total_cost,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT api_id) as unique_apis
                FROM {$this->getTableName()} 
                {$whereStr}";
        
        $result = $this->db->fetchOne($sql, $params);
        
        // 计算成功率
        if ($result['total_calls'] > 0) {
            $result['success_rate'] = round(($result['success_calls'] / $result['total_calls']) * 100, 2);
        } else {
            $result['success_rate'] = 0;
        }
        
        return $result;
    }
    
    /**
     * 获取每日调用趋势
     */
    public function getDailyCallTrend($days = 30, $conditions = []) {
        // 构建查询条件
        $whereClause = ['created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)'];
        $params = ['days' => $days];
        
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['api_id'])) {
            $whereClause[] = "api_id = :api_id";
            $params['api_id'] = $conditions['api_id'];
        }
        
        $whereStr = 'WHERE ' . implode(' AND ', $whereClause);
        
        $sql = "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as error_calls,
                    AVG(response_time) as avg_response_time,
                    SUM(cost) as total_cost
                FROM {$this->getTableName()} 
                {$whereStr}
                GROUP BY DATE(created_at)
                ORDER BY date ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取每小时调用趋势
     */
    public function getHourlyCallTrend($date = null, $conditions = []) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        // 构建查询条件
        $whereClause = ['DATE(created_at) = :date'];
        $params = ['date' => $date];
        
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['api_id'])) {
            $whereClause[] = "api_id = :api_id";
            $params['api_id'] = $conditions['api_id'];
        }
        
        $whereStr = 'WHERE ' . implode(' AND ', $whereClause);
        
        $sql = "SELECT 
                    HOUR(created_at) as hour,
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as error_calls,
                    AVG(response_time) as avg_response_time
                FROM {$this->getTableName()} 
                {$whereStr}
                GROUP BY HOUR(created_at)
                ORDER BY hour ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取热门API排行
     */
    public function getHotApiRanking($limit = 10, $days = 30) {
        $sql = "SELECT 
                    cl.api_id,
                    i.name as api_name,
                    i.path as api_path,
                    COUNT(*) as call_count,
                    SUM(CASE WHEN cl.status = 1 THEN 1 ELSE 0 END) as success_count,
                    AVG(cl.response_time) as avg_response_time,
                    SUM(cl.cost) as total_revenue
                FROM {$this->getTableName()} cl
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON cl.api_id = i.id
                WHERE cl.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                GROUP BY cl.api_id
                ORDER BY call_count DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
    }
    
    /**
     * 获取活跃用户排行
     */
    public function getActiveUserRanking($limit = 10, $days = 30) {
        $sql = "SELECT 
                    cl.user_id,
                    u.username,
                    u.nickname,
                    COUNT(*) as call_count,
                    SUM(cl.cost) as total_cost,
                    COUNT(DISTINCT cl.api_id) as used_apis
                FROM {$this->getTableName()} cl
                LEFT JOIN {$this->db->getPrefix()}users u ON cl.user_id = u.id
                WHERE cl.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                GROUP BY cl.user_id
                ORDER BY call_count DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
    }
    
    /**
     * 获取错误统计
     */
    public function getErrorStats($days = 7) {
        $sql = "SELECT 
                    error_message,
                    COUNT(*) as error_count,
                    COUNT(DISTINCT api_id) as affected_apis,
                    COUNT(DISTINCT user_id) as affected_users
                FROM {$this->getTableName()} 
                WHERE status = 0 
                AND created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                AND error_message IS NOT NULL
                GROUP BY error_message
                ORDER BY error_count DESC
                LIMIT 20";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取响应时间分布
     */
    public function getResponseTimeDistribution($days = 7) {
        $sql = "SELECT 
                    CASE 
                        WHEN response_time < 100 THEN '< 100ms'
                        WHEN response_time < 500 THEN '100-500ms'
                        WHEN response_time < 1000 THEN '500ms-1s'
                        WHEN response_time < 3000 THEN '1-3s'
                        ELSE '> 3s'
                    END as time_range,
                    COUNT(*) as count
                FROM {$this->getTableName()} 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                AND response_time IS NOT NULL
                GROUP BY time_range
                ORDER BY 
                    CASE time_range
                        WHEN '< 100ms' THEN 1
                        WHEN '100-500ms' THEN 2
                        WHEN '500ms-1s' THEN 3
                        WHEN '1-3s' THEN 4
                        ELSE 5
                    END";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 清理过期日志
     */
    public function cleanExpiredLogs($days = 90) {
        $sql = "DELETE FROM {$this->getTableName()} WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        $stmt = $this->db->query($sql, ['days' => $days]);
        return $stmt->rowCount();
    }
    
    /**
     * 获取IP访问统计
     */
    public function getIpStats($limit = 20, $days = 7) {
        $sql = "SELECT 
                    request_ip,
                    COUNT(*) as call_count,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
                    COUNT(DISTINCT api_id) as used_apis,
                    COUNT(DISTINCT user_id) as user_count,
                    MAX(created_at) as last_access
                FROM {$this->getTableName()} 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                GROUP BY request_ip
                ORDER BY call_count DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
    }
    
    /**
     * 导出调用日志
     */
    public function exportLogs($conditions = [], $format = 'csv') {
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['date_from'])) {
            $whereClause[] = "DATE(cl.created_at) >= :date_from";
            $params['date_from'] = $conditions['date_from'];
        }
        
        if (!empty($conditions['date_to'])) {
            $whereClause[] = "DATE(cl.created_at) <= :date_to";
            $params['date_to'] = $conditions['date_to'];
        }
        
        if (!empty($conditions['api_id'])) {
            $whereClause[] = "cl.api_id = :api_id";
            $params['api_id'] = $conditions['api_id'];
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        $sql = "SELECT 
                    cl.id,
                    u.username,
                    i.name as api_name,
                    i.path as api_path,
                    cl.request_ip,
                    cl.request_method,
                    cl.response_code,
                    cl.response_time,
                    cl.cost,
                    cl.status,
                    cl.error_message,
                    cl.created_at
                FROM {$this->getTableName()} cl 
                LEFT JOIN {$this->db->getPrefix()}users u ON cl.user_id = u.id
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON cl.api_id = i.id
                {$whereStr}
                ORDER BY cl.id DESC
                LIMIT 10000"; // 限制导出数量
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取实时调用监控数据
     */
    public function getRealTimeMonitor($minutes = 5) {
        $sql = "SELECT 
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as error_calls,
                    AVG(response_time) as avg_response_time,
                    COUNT(DISTINCT user_id) as active_users,
                    COUNT(DISTINCT api_id) as active_apis
                FROM {$this->getTableName()} 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL :minutes MINUTE)";
        
        return $this->db->fetchOne($sql, ['minutes' => $minutes]);
    }
    
    /**
     * 检测异常调用
     */
    public function detectAbnormalCalls($threshold = 100, $minutes = 5) {
        // 检测高频调用
        $sql = "SELECT 
                    user_id,
                    request_ip,
                    COUNT(*) as call_count,
                    u.username
                FROM {$this->getTableName()} cl
                LEFT JOIN {$this->db->getPrefix()}users u ON cl.user_id = u.id
                WHERE cl.created_at >= DATE_SUB(NOW(), INTERVAL :minutes MINUTE)
                GROUP BY user_id, request_ip
                HAVING call_count > :threshold
                ORDER BY call_count DESC";
        
        return $this->db->fetchAll($sql, ['minutes' => $minutes, 'threshold' => $threshold]);
    }
}