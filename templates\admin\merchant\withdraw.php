<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>商家提现管理 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
</head>
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-dollar"></i> 商家提现管理
        </div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">提现状态:</label>
                        <div class="layui-input-inline">
                            <select id="status">
                                <option value="">所有状态</option>
                                <option value="0">待审核</option>
                                <option value="1">已通过</option>
                                <option value="2">已拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">商家名称:</label>
                        <div class="layui-input-inline">
                            <input id="merchantName" class="layui-input" placeholder="输入商家名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" id="searchBtn">
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                    </div>
                </div>
            </div>
            <table id="withdrawTable" lay-filter="withdrawTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
    {{# if(d.status === 0){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="pass">通过</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="reject">拒绝</a>
    {{# } }}
</script>

<!-- 状态列 -->
<script type="text/html" id="statusTpl">
    {{#  if(d.status === 0){ }}
    <span class="layui-badge layui-bg-orange">待审核</span>
    {{#  } else if(d.status === 1){ }}
    <span class="layui-badge layui-bg-green">已通过</span>
    {{#  } else if(d.status === 2){ }}
    <span class="layui-badge">已拒绝</span>
    {{#  } }}
</script>

<!-- 查看提现弹窗 -->
<script type="text/html" id="viewDialog">
    <div style="padding: 20px;">
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">商家名称</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.merchant_name}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">提现金额</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="¥{{d.amount}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">收款人</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.account_name}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">收款账号</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.account_number}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">开户银行</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.bank_name}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">申请时间</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{layui.util.toDateString(d.create_time * 1000)}}" readonly>
                </div>
            </div>
            {{# if(d.status !== 0){ }}
            <div class="layui-form-item">
                <label class="layui-form-label">审核时间</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{layui.util.toDateString(d.audit_time * 1000)}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">审核备注</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" readonly>{{d.audit_remark}}</textarea>
                </div>
            </div>
            {{# } }}
        </div>
    </div>
</script>

<!-- 审核弹窗 -->
<script type="text/html" id="auditDialog">
    <form id="auditForm" lay-filter="auditForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id}}"/>
        <input name="status" type="hidden" value="{{d.status}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">商家名称</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.merchant_name}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">提现金额</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="¥{{d.amount}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">收款人</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.account_name}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">收款账号</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.account_number}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">开户银行</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.bank_name}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">审核备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入审核备注" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="auditSubmit" lay-submit>确定</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script type="text/javascript" src="/Easyweb/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="/Easyweb/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'admin', 'laytpl'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;
        var laytpl = layui.laytpl;

        // 渲染表格
        var insTb = table.render({
            elem: '#withdrawTable',
            url: '/admin/merchant/withdraw_list',
            page: true,
            toolbar: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'id', title: 'ID', sort: true},
                {field: 'merchant_name', title: '商家名称'},
                {field: 'amount', title: '提现金额', templet: function(d) {
                    return '¥' + d.amount;
                }},
                {field: 'account_name', title: '收款人'},
                {field: 'account_number', title: '收款账号'},
                {field: 'bank_name', title: '开户银行'},
                {field: 'status', title: '状态', templet: '#statusTpl'},
                {field: 'create_time', title: '申请时间', templet: function(d) {
                    return util.toDateString(d.create_time * 1000);
                }},
                {title: '操作', toolbar: '#tableBar', align: 'center', width: 180}
            ]]
        });

        // 搜索
        $('#searchBtn').click(function () {
            insTb.reload({
                where: {
                    status: $('#status').val(),
                    merchant_name: $('#merchantName').val()
                },
                page: {curr: 1}
            });
        });

        // 工具条点击事件
        table.on('tool(withdrawTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'view') { // 查看
                viewWithdraw(data);
            } else if (layEvent === 'pass') { // 通过
                auditWithdraw(data, 1);
            } else if (layEvent === 'reject') { // 拒绝
                auditWithdraw(data, 2);
            }
        });

        // 查看提现
        function viewWithdraw(data) {
            laytpl($('#viewDialog').html()).render(data, function(html) {
                admin.open({
                    type: 1,
                    title: '查看提现',
                    area: ['600px', '80%'],
                    content: html
                });
            });
        }

        // 审核提现
        function auditWithdraw(data, status) {
            if (data.status !== 0) {
                layer.msg('该提现已审核', {icon: 2});
                return;
            }
            
            data.status = status;
            
            laytpl($('#auditDialog').html()).render(data, function(html) {
                admin.open({
                    type: 1,
                    title: status === 1 ? '通过提现' : '拒绝提现',
                    area: ['500px', '500px'],
                    content: html
                });
            });
        }

        // 审核表单提交
        form.on('submit(auditSubmit)', function(data) {
            var loadIndex = layer.load(2);
            
            $.post('/admin/merchant/audit_withdraw', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.closeAll('page');
                    layer.msg(res.msg, {icon: 1});
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
            
            return false;
        });
    });
</script>
</body>
</html>