<?php
/**
 * 后台管理入口文件
 */
session_start();

// 检查是否已安装
if (!file_exists('../config/installed.lock')) {
    header('Location: ../install/install.php');
    exit;
}

// 引入核心文件
require_once '../classes/Database.php';
require_once '../core/AdminAuth.php';

// 获取请求路径
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = dirname($scriptName);

// 移除基础路径
if ($basePath !== '/') {
    $requestUri = substr($requestUri, strlen($basePath));
}

// 移除查询字符串
$path = strtok($requestUri, '?');

// 移除前导斜杠
$path = ltrim($path, '/');

// 如果路径为空或者是admin，显示主页面
if (empty($path) || $path === 'admin' || $path === 'admin/') {
    showAdminPage();
    exit;
}

// 处理API请求
if (strpos($path, 'controllers/') === 0) {
    handleControllerRequest($path);
    exit;
}

// 处理静态页面请求
if (file_exists($path) && pathinfo($path, PATHINFO_EXTENSION) === 'html') {
    include $path;
    exit;
}

// 404错误
http_response_code(404);
echo json_encode(['code' => 404, 'msg' => '页面不存在']);

/**
 * 显示管理后台主页面
 */
function showAdminPage() {
    // 检查登录状态
    $auth = new AdminAuth();
    $token = $_COOKIE['admin_token'] ?? '';
    
    if (empty($token)) {
        // 未登录，跳转到登录页
        header('Location: login.html');
        exit;
    }
    
    $payload = $auth->verifyToken($token);
    if (!$payload || $payload['role'] !== 'admin') {
        // 登录失效，跳转到登录页
        header('Location: login.html');
        exit;
    }
    
    // 获取管理员信息
    $db = Database::getInstance();
    $admin = $db->query("SELECT * FROM users WHERE id = ? AND role = 'admin'", [$payload['user_id']], true);
    
    if (!$admin) {
        header('Location: login.html');
        exit;
    }
    
    // 获取系统配置
    $siteConfig = [
        'site_name' => 'API商业系统',
        'admin_logo' => '',
    ];
    
    // 包含模板文件
    include '../templates/admin/index.php';
}

/**
 * 处理控制器请求
 */
function handleControllerRequest($path) {
    // 解析控制器路径
    $parts = explode('/', $path);
    if (count($parts) < 2) {
        http_response_code(404);
        echo json_encode(['code' => 404, 'msg' => '控制器不存在']);
        return;
    }
    
    $controllerFile = $parts[1];
    $controllerPath = 'controllers/' . $controllerFile;
    
    if (!file_exists($controllerPath)) {
        http_response_code(404);
        echo json_encode(['code' => 404, 'msg' => '控制器文件不存在']);
        return;
    }
    
    // 包含控制器文件
    include $controllerPath;
}
?>
