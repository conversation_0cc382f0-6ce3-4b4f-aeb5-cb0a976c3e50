<?php
/**
 * 工具类
 */
class Utils
{
    /**
     * 生成随机字符串
     * 
     * @param int $length 长度
     * @param string $chars 字符集
     * @return string 随机字符串
     */
    public static function randomString($length = 8, $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')
    {
        $charsLength = strlen($chars);
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $chars[random_int(0, $charsLength - 1)];
        }
        
        return $randomString;
    }
    
    /**
     * 生成唯一ID
     * 
     * @param string $prefix 前缀
     * @return string 唯一ID
     */
    public static function generateUniqueId($prefix = '')
    {
        return uniqid($prefix) . self::randomString(8);
    }
    
    /**
     * 生成订单号
     * 
     * @return string 订单号
     */
    public static function generateOrderNo()
    {
        return date('YmdHis') . random_int(1000, 9999);
    }
    
    /**
     * 格式化金额
     * 
     * @param float $amount 金额
     * @param int $decimals 小数位数
     * @return string 格式化后的金额
     */
    public static function formatMoney($amount, $decimals = 2)
    {
        return number_format($amount, $decimals, '.', '');
    }
    
    /**
     * 格式化日期时间
     * 
     * @param string $datetime 日期时间
     * @param string $format 格式
     * @return string 格式化后的日期时间
     */
    public static function formatDatetime($datetime, $format = 'Y-m-d H:i:s')
    {
        if (empty($datetime)) {
            return '';
        }
        
        $timestamp = is_numeric($datetime) ? $datetime : strtotime($datetime);
        return date($format, $timestamp);
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @return string IP地址
     */
    public static function getClientIp()
    {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipAddresses = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            return trim($ipAddresses[0]);
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '';
        }
    }
    
    /**
     * 获取客户端浏览器信息
     * 
     * @return string 浏览器信息
     */
    public static function getUserAgent()
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? '';
    }
    
    /**
     * 检查是否为AJAX请求
     * 
     * @return bool 是否为AJAX请求
     */
    public static function isAjaxRequest()
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * 获取当前URL
     * 
     * @param bool $withQueryString 是否包含查询字符串
     * @return string 当前URL
     */
    public static function getCurrentUrl($withQueryString = true)
    {
        $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];
        
        if (!$withQueryString) {
            $uri = parse_url($uri, PHP_URL_PATH);
        }
        
        return "{$protocol}://{$host}{$uri}";
    }
    
    /**
     * 获取基础URL
     * 
     * @return string 基础URL
     */
    public static function getBaseUrl()
    {
        $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        
        return "{$protocol}://{$host}";
    }
    
    /**
     * 过滤HTML标签
     * 
     * @param string $content 内容
     * @return string 过滤后的内容
     */
    public static function filterHtml($content)
    {
        return htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 截取字符串
     * 
     * @param string $string 字符串
     * @param int $length 长度
     * @param string $suffix 后缀
     * @return string 截取后的字符串
     */
    public static function subString($string, $length, $suffix = '...')
    {
        if (mb_strlen($string, 'UTF-8') <= $length) {
            return $string;
        }
        
        return mb_substr($string, 0, $length, 'UTF-8') . $suffix;
    }
    
    /**
     * 检查手机号格式
     * 
     * @param string $mobile 手机号
     * @return bool 是否为有效的手机号
     */
    public static function isValidMobile($mobile)
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile) === 1;
    }
    
    /**
     * 检查邮箱格式
     * 
     * @param string $email 邮箱
     * @return bool 是否为有效的邮箱
     */
    public static function isValidEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * 检查URL格式
     * 
     * @param string $url URL
     * @return bool 是否为有效的URL
     */
    public static function isValidUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * 加密数据
     * 
     * @param mixed $data 数据
     * @param string $key 密钥
     * @return string 加密后的数据
     */
    public static function encrypt($data, $key)
    {
        $data = json_encode($data);
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * 解密数据
     * 
     * @param string $data 加密的数据
     * @param string $key 密钥
     * @return mixed 解密后的数据
     */
    public static function decrypt($data, $key)
    {
        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
        
        return json_decode($decrypted, true);
    }
    
    /**
     * 生成验证码
     * 
     * @param int $length 长度
     * @return string 验证码
     */
    public static function generateVerificationCode($length = 6)
    {
        return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param string $filename 文件名
     * @return string 扩展名
     */
    public static function getFileExtension($filename)
    {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }
    
    /**
     * 检查文件类型是否允许
     * 
     * @param string $filename 文件名
     * @param array $allowedTypes 允许的类型
     * @return bool 是否允许
     */
    public static function isAllowedFileType($filename, $allowedTypes)
    {
        $extension = self::getFileExtension($filename);
        return in_array($extension, $allowedTypes);
    }
    
    /**
     * 生成缩略图
     * 
     * @param string $source 源文件路径
     * @param string $destination 目标文件路径
     * @param int $width 宽度
     * @param int $height 高度
     * @return bool 是否成功
     */
    public static function createThumbnail($source, $destination, $width, $height)
    {
        if (!extension_loaded('gd')) {
            return false;
        }
        
        // 获取图片信息
        $info = getimagesize($source);
        if (!$info) {
            return false;
        }
        
        // 创建图片资源
        switch ($info[2]) {
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg($source);
                break;
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng($source);
                break;
            case IMAGETYPE_GIF:
                $image = imagecreatefromgif($source);
                break;
            default:
                return false;
        }
        
        // 计算缩放比例
        $sourceWidth = $info[0];
        $sourceHeight = $info[1];
        
        $ratio = min($width / $sourceWidth, $height / $sourceHeight);
        
        $targetWidth = round($sourceWidth * $ratio);
        $targetHeight = round($sourceHeight * $ratio);
        
        // 创建缩略图
        $thumbnail = imagecreatetruecolor($targetWidth, $targetHeight);
        
        // 保持PNG透明度
        if ($info[2] == IMAGETYPE_PNG) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
        }
        
        // 调整图片大小
        imagecopyresampled($thumbnail, $image, 0, 0, 0, 0, $targetWidth, $targetHeight, $sourceWidth, $sourceHeight);
        
        // 保存缩略图
        switch ($info[2]) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($thumbnail, $destination, 90);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($thumbnail, $destination);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($thumbnail, $destination);
                break;
            default:
                $result = false;
        }
        
        // 释放资源
        imagedestroy($image);
        imagedestroy($thumbnail);
        
        return $result;
    }
    
    /**
     * 获取文件大小的可读形式
     * 
     * @param int $bytes 字节数
     * @param int $precision 精度
     * @return string 可读的文件大小
     */
    public static function formatFileSize($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, $precision) . ' ' . $units[$pow];
    }
    
    /**
     * 获取远程文件内容
     * 
     * @param string $url URL
     * @return string|false 文件内容或失败
     */
    public static function getRemoteContent($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $content = curl_exec($ch);
        curl_close($ch);
        
        return $content;
    }
    
    /**
     * 发送POST请求
     * 
     * @param string $url URL
     * @param array $data 数据
     * @param array $headers 请求头
     * @return string|false 响应内容或失败
     */
    public static function postRequest($url, $data = [], $headers = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }
    
    /**
     * 发送GET请求
     * 
     * @param string $url URL
     * @param array $params 参数
     * @param array $headers 请求头
     * @return string|false 响应内容或失败
     */
    public static function getRequest($url, $params = [], $headers = [])
    {
        if (!empty($params)) {
            $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }
}