<?php
/**
 * 入口文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 启动会话
session_start();

// 自动加载类
spl_autoload_register(function ($className) {
    // 控制器类
    if (strpos($className, 'Controller') !== false) {
        $file = __DIR__ . '/controllers/' . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
    
    // 模型类
    if (strpos($className, 'Model') !== false) {
        $file = __DIR__ . '/models/' . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
    
    // 其他类
    $file = __DIR__ . '/classes/' . $className . '.php';
    if (file_exists($file)) {
        require_once $file;
        return;
    }
});

// 获取请求URI
$requestUri = $_SERVER['REQUEST_URI'];

// 移除查询字符串
if (($pos = strpos($requestUri, '?')) !== false) {
    $requestUri = substr($requestUri, 0, $pos);
}

// 移除基础路径
$basePath = dirname($_SERVER['SCRIPT_NAME']);
if ($basePath !== '/' && strpos($requestUri, $basePath) === 0) {
    $requestUri = substr($requestUri, strlen($basePath));
}

// 确保以/开头
if (empty($requestUri) || $requestUri[0] !== '/') {
    $requestUri = '/' . $requestUri;
}

// 获取请求方法
$requestMethod = $_SERVER['REQUEST_METHOD'];

// 路由键
$routeKey = $requestMethod . ':' . $requestUri;

// 加载路由配置
$routes = require_once __DIR__ . '/config/routes.php';

try {
    // 检查路由是否存在
    if (isset($routes[$routeKey])) {
        // 获取控制器和方法
        list($controllerName, $methodName) = $routes[$routeKey];
        
        // 实例化控制器
        $controller = new $controllerName();
        
        // 调用方法
        $controller->$methodName();
    } else {
        // 检查是否有通配符路由
        $matched = false;
        foreach ($routes as $pattern => $handler) {
            // 检查是否是通配符路由
            if (strpos($pattern, '*') !== false) {
                // 将通配符转换为正则表达式
                $regex = '#^' . str_replace(['*', '/'], ['.+', '\\/'], $pattern) . '$#';
                
                // 检查是否匹配
                if (preg_match($regex, $routeKey, $matches)) {
                    // 获取控制器和方法
                    list($controllerName, $methodName) = $handler;
                    
                    // 实例化控制器
                    $controller = new $controllerName();
                    
                    // 调用方法
                    $controller->$methodName();
                    
                    $matched = true;
                    break;
                }
            }
        }
        
        // 如果没有匹配的路由，显示404页面
        if (!$matched) {
            header('HTTP/1.0 404 Not Found');
            echo '<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>404 - 页面未找到</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
                    h1 { color: #d9534f; }
                    .error-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin-bottom: 20px; }
                    .back-link { display: inline-block; margin-top: 20px; color: #007bff; text-decoration: none; }
                    .back-link:hover { text-decoration: underline; }
                </style>
            </head>
            <body>
                <h1>404 - 页面未找到</h1>
                <div class="error-box">
                    <p>您请求的页面 <strong>' . htmlspecialchars($requestUri) . '</strong> 不存在。</p>
                </div>
                <p>请检查URL是否正确，或返回首页。</p>
                <a href="/" class="back-link">返回首页</a>
                <a href="javascript:history.back()" class="back-link" style="margin-left: 20px;">返回上一页</a>
            </body>
            </html>';
        }
    }
} catch (Exception $e) {
    // 显示错误页面
    header('HTTP/1.0 500 Internal Server Error');
    echo '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>服务器错误</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1 { color: #d9534f; }
            .error-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin-bottom: 20px; }
            .error-message { font-weight: bold; }
            .back-link { display: inline-block; margin-top: 20px; color: #007bff; text-decoration: none; }
            .back-link:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <h1>服务器错误</h1>
        <div class="error-box">
            <p class="error-message">服务器错误，请稍后再试</p>
            ' . (defined('DEBUG') && DEBUG ? '<p>' . htmlspecialchars($e->getMessage()) . '</p>' : '') . '
        </div>
        <p>请稍后再试或联系网站管理员。</p>
        <a href="javascript:history.back()" class="back-link">返回上一页</a>
    </body>
    </html>';
    
    // 记录错误日志
    error_log($e->getMessage() . "\n" . $e->getTraceAsString());
}