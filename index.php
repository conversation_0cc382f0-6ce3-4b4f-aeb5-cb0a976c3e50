<?php
/**
 * API商业系统入口文件
 */
session_start();

// 检查系统是否已安装
if (!file_exists('config/installed.lock')) {
    header('Location: install/install.php');
    exit;
}

// 加载配置文件
$dbConfig = require 'config/database.php';
$appConfig = require 'config/app.php';

// 设置时区
date_default_timezone_set($appConfig['timezone']);

// 连接数据库
try {
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
    
    // 获取网站配置
    $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}configs");
    $siteConfig = [];
    while ($row = $stmt->fetch()) {
        $siteConfig[$row['key']] = $row['value'];
    }
    
    // 加载首页模板
    include 'templates/index.php';
} catch (PDOException $e) {
    die('数据库连接失败: ' . $e->getMessage());
}