<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>文章管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">文章管理</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <input id="searchKeyword" class="layui-input" type="text" placeholder="标题/作者"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <select id="searchCategory">
                            <option value="">所有分类</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <select id="searchStatus">
                            <option value="">所有状态</option>
                            <option value="1">已发布</option>
                            <option value="0">草稿</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <button id="searchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                        <button id="addBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                        <button id="categoryBtn" class="layui-btn layui-btn-primary icon-btn"><i class="layui-icon">&#xe653;</i>分类管理</button>
                    </div>
                </div>
            </div>

            <table class="layui-table" id="articleTable" lay-filter="articleTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="view">查看</a>
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="statusSwitch" value="{{d.id}}" lay-skin="switch" lay-text="已发布|草稿" {{d.status==1?'checked':''}}/>
</script>

<!-- 表格封面列 -->
<script type="text/html" id="coverTpl">
    {{#  if(d.cover){ }}
    <img src="{{d.cover}}" style="height: 30px;" class="article-cover-img">
    {{#  } else { }}
    <span>无封面</span>
    {{#  } }}
</script>

<!-- 分类管理弹窗 -->
<script type="text/html" id="categoryDialog">
    <div class="layui-form toolbar">
        <div class="layui-form-item">
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input id="categoryKeyword" class="layui-input" type="text" placeholder="分类名称"/>
                </div>
            </div>
            <div class="layui-inline">
                <button id="categorySearchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                <button id="addCategoryBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
            </div>
        </div>
    </div>
    <table class="layui-table" id="categoryTable" lay-filter="categoryTable"></table>
</script>

<!-- 分类表格操作列 -->
<script type="text/html" id="categoryTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 添加/编辑分类弹窗 -->
<script type="text/html" id="categoryEditDialog">
    <form id="categoryEditForm" lay-filter="categoryEditForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id || ''}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">分类名称</label>
            <div class="layui-input-block">
                <input name="name" placeholder="请输入分类名称" type="text" class="layui-input" value="{{d.name || ''}}" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序号</label>
            <div class="layui-input-block">
                <input name="sort" placeholder="请输入排序号" type="number" class="layui-input" value="{{d.sort || '0'}}"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入分类描述" class="layui-textarea">{{d.description || ''}}</textarea>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="categoryEditSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#articleTable',
        url: '/admin/article/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80},
            {field: 'cover', title: '封面', templet: '#coverTpl', width: 100},
            {field: 'title', title: '标题', sort: true},
            {field: 'category_name', title: '分类', sort: true, width: 120},
            {field: 'author', title: '作者', width: 120},
            {field: 'view_count', title: '浏览量', width: 100, sort: true},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100},
            {field: 'create_time', title: '创建时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160, sort: true},
            {title: '操作', toolbar: '#tableBar', width: 180}
        ]]
    });
    
    // 加载文章分类
    $.get('/admin/article/category/list_all', function(res) {
        if (res.code === 0) {
            var options = '<option value="">所有分类</option>';
            for (var i = 0; i < res.data.length; i++) {
                options += '<option value="' + res.data[i].id + '">' + res.data[i].name + '</option>';
            }
            $('#searchCategory').html(options);
            form.render('select');
        }
    }, 'json');
    
    // 搜索按钮点击事件
    $('#searchBtn').click(function () {
        var keyword = $('#searchKeyword').val();
        var category = $('#searchCategory').val();
        var status = $('#searchStatus').val();
        
        insTb.reload({
            where: {
                keyword: keyword,
                category_id: category,
                status: status
            },
            page: {curr: 1}
        });
    });
    
    // 添加按钮点击事件
    $('#addBtn').click(function () {
        location.href = '/admin/article/edit';
    });
    
    // 分类管理按钮点击事件
    $('#categoryBtn').click(function () {
        admin.open({
            type: 1,
            title: '文章分类管理',
            content: $('#categoryDialog').html(),
            area: ['800px', '500px'],
            success: function (layero, dIndex) {
                // 渲染分类表格
                var categoryTb = table.render({
                    elem: '#categoryTable',
                    url: '/admin/article/category/list',
                    page: true,
                    toolbar: true,
                    cellMinWidth: 100,
                    cols: [[
                        {type: 'numbers'},
                        {field: 'id', title: 'ID', width: 80},
                        {field: 'name', title: '分类名称'},
                        {field: 'article_count', title: '文章数量', width: 100},
                        {field: 'sort', title: '排序号', width: 100},
                        {field: 'description', title: '描述'},
                        {field: 'create_time', title: '创建时间', templet: function (d) {
                            return util.toDateString(d.create_time * 1000);
                        }, width: 160},
                        {title: '操作', toolbar: '#categoryTableBar', width: 120}
                    ]]
                });
                
                // 分类搜索按钮点击事件
                $('#categorySearchBtn').click(function () {
                    categoryTb.reload({
                        where: {
                            keyword: $('#categoryKeyword').val()
                        },
                        page: {curr: 1}
                    });
                });
                
                // 添加分类按钮点击事件
                $('#addCategoryBtn').click(function () {
                    showCategoryEditDialog();
                });
                
                // 分类表格工具条点击事件
                table.on('tool(categoryTable)', function (obj) {
                    var data = obj.data;
                    var layEvent = obj.event;
                    
                    if (layEvent === 'edit') { // 修改
                        showCategoryEditDialog(data);
                    } else if (layEvent === 'del') { // 删除
                        layer.confirm('确定要删除该分类吗？', {
                            skin: 'layui-layer-admin',
                            shade: .1
                        }, function (i) {
                            layer.close(i);
                            layer.load(2);
                            $.post('/admin/article/category/delete', {
                                id: data.id
                            }, function (res) {
                                layer.closeAll('loading');
                                if (res.code === 0) {
                                    layer.msg(res.msg, {icon: 1});
                                    categoryTb.reload();
                                    // 重新加载分类下拉框
                                    loadCategories();
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }, 'json');
                        });
                    }
                });
                
                // 显示分类编辑弹窗
                function showCategoryEditDialog(data) {
                    admin.open({
                        type: 1,
                        title: data ? '编辑分类' : '添加分类',
                        content: laytpl($('#categoryEditDialog').html()).render(data || {}),
                        area: ['500px', '350px'],
                        success: function (layero, dIndex) {
                            form.render();
                            
                            // 表单提交事件
                            form.on('submit(categoryEditSubmit)', function (data) {
                                layer.load(2);
                                $.post('/admin/article/category/save', data.field, function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 0) {
                                        layer.close(dIndex);
                                        layer.msg(res.msg, {icon: 1});
                                        categoryTb.reload();
                                        // 重新加载分类下拉框
                                        loadCategories();
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }, 'json');
                                return false;
                            });
                        }
                    });
                }
            }
        });
    });
    
    // 工具条点击事件
    table.on('tool(articleTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'view') { // 查看
            window.open('/article/detail?id=' + data.id);
        } else if (layEvent === 'edit') { // 修改
            location.href = '/admin/article/edit?id=' + data.id;
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该文章吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/article/delete', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 修改状态
    form.on('switch(statusSwitch)', function (obj) {
        layer.load(2);
        $.post('/admin/article/update_status', {
            id: obj.value,
            status: obj.elem.checked ? 1 : 0
        }, function (res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });
    
    // 重新加载分类下拉框
    function loadCategories() {
        $.get('/admin/article/category/list_all', function(res) {
            if (res.code === 0) {
                var options = '<option value="">所有分类</option>';
                for (var i = 0; i < res.data.length; i++) {
                    options += '<option value="' + res.data[i].id + '">' + res.data[i].name + '</option>';
                }
                $('#searchCategory').html(options);
                form.render('select');
            }
        }, 'json');
    }
});
</script>
</body>
</html>