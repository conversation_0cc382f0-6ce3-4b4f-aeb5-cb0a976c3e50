<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>商家中心 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/site.css"/>
    <style>
        .merchant-card {
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .merchant-card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
        }
        .merchant-card-title {
            font-size: 16px;
            font-weight: bold;
        }
        .merchant-card-body {
            padding: 20px;
        }
        .merchant-stat-card {
            text-align: center;
            padding: 20px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .merchant-stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .merchant-stat-title {
            color: #666;
        }
        .merchant-level-card {
            background-color: #393D49;
            color: #fff;
            padding: 20px;
            border-radius: 4px;
        }
        .merchant-level-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .merchant-level-feature {
            margin-top: 5px;
        }
        .merchant-api-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .merchant-api-item:last-child {
            border-bottom: none;
        }
        .merchant-api-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .merchant-api-url {
            color: #666;
            margin-bottom: 5px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .merchant-api-price {
            color: #FF5722;
            font-weight: bold;
        }
        .merchant-api-status {
            float: right;
        }
    </style>
</head>
<body>
<!-- 引入网站头部 -->
<div id="header"></div>

<div class="layui-container" style="padding-top: 30px; padding-bottom: 30px;">
    <div class="layui-row layui-col-space20">
        <!-- 左侧菜单 -->
        <div class="layui-col-md3">
            <div class="merchant-card">
                <div class="merchant-card-header">
                    <div class="merchant-card-title">商家中心</div>
                </div>
                <div class="merchant-card-body" style="padding: 0;">
                    <ul class="layui-nav layui-nav-tree layui-inline" lay-filter="merchant-nav" style="width: 100%; margin-right: 10px;">
                        <li class="layui-nav-item layui-this">
                            <a href="javascript:;" data-url="/merchant/center" data-id="dashboard">
                                <i class="layui-icon layui-icon-home"></i> 控制台
                            </a>
                        </li>
                        <li class="layui-nav-item">
                            <a href="javascript:;" data-url="/merchant/api_list" data-id="api">
                                <i class="layui-icon layui-icon-app"></i> API管理
                            </a>
                        </li>
                        <li class="layui-nav-item">
                            <a href="javascript:;" data-url="/merchant/income_list" data-id="income">
                                <i class="layui-icon layui-icon-rmb"></i> 收入管理
                            </a>
                        </li>
                        <li class="layui-nav-item">
                            <a href="javascript:;" data-url="/merchant/withdraw_list" data-id="withdraw">
                                <i class="layui-icon layui-icon-dollar"></i> 提现管理
                            </a>
                        </li>
                        <li class="layui-nav-item">
                            <a href="javascript:;" data-url="/merchant/settings" data-id="settings">
                                <i class="layui-icon layui-icon-set"></i> 商家设置
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 商家等级信息 -->
            <div class="merchant-level-card" id="merchantLevel">
                <div class="merchant-level-name">加载中...</div>
                <div class="merchant-level-discount">折扣：<span id="discountRate">-</span></div>
                <div class="merchant-level-limit">API数量上限：<span id="maxApiCount">-</span></div>
                <div class="layui-form-item" style="margin-top: 15px;">
                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="upgradeBtn">升级等级</button>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容 -->
        <div class="layui-col-md9">
            <!-- 控制台 -->
            <div id="dashboard" class="merchant-page">
                <!-- 统计数据 -->
                <div class="layui-row layui-col-space20">
                    <div class="layui-col-md3">
                        <div class="merchant-stat-card">
                            <div class="merchant-stat-title">今日收入</div>
                            <div class="merchant-stat-value" id="todayIncome">¥0.00</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="merchant-stat-card">
                            <div class="merchant-stat-title">本月收入</div>
                            <div class="merchant-stat-value" id="monthIncome">¥0.00</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="merchant-stat-card">
                            <div class="merchant-stat-title">总收入</div>
                            <div class="merchant-stat-value" id="totalIncome">¥0.00</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="merchant-stat-card">
                            <div class="merchant-stat-title">API数量</div>
                            <div class="merchant-stat-value" id="apiCount">0</div>
                        </div>
                    </div>
                </div>
                
                <!-- 收入趋势图 -->
                <div class="merchant-card">
                    <div class="merchant-card-header">
                        <div class="merchant-card-title">收入趋势</div>
                    </div>
                    <div class="merchant-card-body">
                        <div id="incomeChart" style="height: 300px;"></div>
                    </div>
                </div>
                
                <!-- API收入占比 -->
                <div class="merchant-card">
                    <div class="merchant-card-header">
                        <div class="merchant-card-title">API收入占比</div>
                    </div>
                    <div class="merchant-card-body">
                        <div id="apiIncomeChart" style="height: 300px;"></div>
                    </div>
                </div>
                
                <!-- 最近添加的API -->
                <div class="merchant-card">
                    <div class="merchant-card-header">
                        <div class="merchant-card-title">最近添加的API</div>
                        <a href="javascript:;" class="layui-btn layui-btn-xs layui-btn-normal" style="position: absolute; right: 15px; top: 10px;" id="addApiBtn">添加API</a>
                    </div>
                    <div class="merchant-card-body" id="recentApiList">
                        <div class="layui-text">
                            <p>加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- API管理 -->
            <div id="api" class="merchant-page" style="display: none;">
                <div class="merchant-card">
                    <div class="merchant-card-header">
                        <div class="merchant-card-title">API管理</div>
                        <div style="position: absolute; right: 15px; top: 10px;">
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="addApiBtn2">添加API</button>
                        </div>
                    </div>
                    <div class="merchant-card-body">
                        <table id="apiTable" lay-filter="apiTable"></table>
                        
                        <script type="text/html" id="apiTableBar">
                            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                            <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
                        </script>
                        
                        <script type="text/html" id="apiStatusTpl">
                            {{#  if(d.status === 1){ }}
                            <span class="layui-badge layui-bg-green">启用</span>
                            {{#  } else { }}
                            <span class="layui-badge">禁用</span>
                            {{#  } }}
                        </script>
                    </div>
                </div>
            </div>
            
            <!-- 收入管理 -->
            <div id="income" class="merchant-page" style="display: none;">
                <div class="merchant-card">
                    <div class="merchant-card-header">
                        <div class="merchant-card-title">收入管理</div>
                    </div>
                    <div class="merchant-card-body">
                        <table id="incomeTable" lay-filter="incomeTable"></table>
                    </div>
                </div>
            </div>
            
            <!-- 提现管理 -->
            <div id="withdraw" class="merchant-page" style="display: none;">
                <div class="merchant-card">
                    <div class="merchant-card-header">
                        <div class="merchant-card-title">提现管理</div>
                        <div style="position: absolute; right: 15px; top: 10px;">
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="withdrawBtn">申请提现</button>
                        </div>
                    </div>
                    <div class="merchant-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">当前余额</label>
                            <div class="layui-input-inline">
                                <input type="text" class="layui-input" id="currentBalance" value="¥0.00" readonly>
                            </div>
                        </div>
                        
                        <table id="withdrawTable" lay-filter="withdrawTable"></table>
                        
                        <script type="text/html" id="withdrawStatusTpl">
                            {{#  if(d.status === 0){ }}
                            <span class="layui-badge layui-bg-orange">审核中</span>
                            {{#  } else if(d.status === 1){ }}
                            <span class="layui-badge layui-bg-green">已通过</span>
                            {{#  } else if(d.status === 2){ }}
                            <span class="layui-badge">已拒绝</span>
                            {{#  } }}
                        </script>
                    </div>
                </div>
            </div>
            
            <!-- 商家设置 -->
            <div id="settings" class="merchant-page" style="display: none;">
                <div class="merchant-card">
                    <div class="merchant-card-header">
                        <div class="merchant-card-title">商家设置</div>
                    </div>
                    <div class="merchant-card-body">
                        <form class="layui-form" id="settingsForm" lay-filter="settingsForm">
                            <div class="layui-form-item">
                                <label class="layui-form-label">商家名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="name" class="layui-input" readonly>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系人</label>
                                <div class="layui-input-block">
                                    <input type="text" name="contact" required lay-verify="required" placeholder="请输入联系人" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系电话</label>
                                <div class="layui-input-block">
                                    <input type="text" name="phone" required lay-verify="required|phone" placeholder="请输入联系电话" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系邮箱</label>
                                <div class="layui-input-block">
                                    <input type="text" name="email" required lay-verify="required|email" placeholder="请输入联系邮箱" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系地址</label>
                                <div class="layui-input-block">
                                    <input type="text" name="address" placeholder="请输入联系地址" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">商家Logo</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn" id="uploadLogo">上传Logo</button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="logoImg" style="max-width: 200px;">
                                            <p id="logoText"></p>
                                        </div>
                                        <input type="hidden" name="logo">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">商家介绍</label>
                                <div class="layui-input-block">
                                    <textarea name="description" placeholder="请输入商家介绍" class="layui-textarea"></textarea>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="settingsSubmit">保存设置</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加API弹窗 -->
<script type="text/html" id="addApiDialog">
    <form class="layui-form" id="addApiForm" lay-filter="addApiForm" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">选择API</label>
            <div class="layui-input-block">
                <select name="api_id" lay-verify="required" lay-filter="apiSelect">
                    <option value="">请选择API</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">价格</label>
            <div class="layui-input-block">
                <input type="number" name="price" required lay-verify="required|number" placeholder="请输入价格" autocomplete="off" class="layui-input" step="0.01" min="0">
            </div>
        </div>
        
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入描述" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="addApiSubmit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<!-- 编辑API弹窗 -->
<script type="text/html" id="editApiDialog">
    <form class="layui-form" id="editApiForm" lay-filter="editApiForm" style="padding: 20px;">
        <input type="hidden" name="id">
        
        <div class="layui-form-item">
            <label class="layui-form-label">API名称</label>
            <div class="layui-input-block">
                <input type="text" name="api_name" class="layui-input" readonly>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">价格</label>
            <div class="layui-input-block">
                <input type="number" name="price" required lay-verify="required|number" placeholder="请输入价格" autocomplete="off" class="layui-input" step="0.01" min="0">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入描述" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="editApiSubmit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<!-- 申请提现弹窗 -->
<script type="text/html" id="withdrawDialog">
    <form class="layui-form" id="withdrawForm" lay-filter="withdrawForm" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">当前余额</label>
            <div class="layui-input-block">
                <input type="text" id="withdrawBalance" class="layui-input" readonly>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">提现金额</label>
            <div class="layui-input-block">
                <input type="number" name="amount" required lay-verify="required|number" placeholder="请输入提现金额" autocomplete="off" class="layui-input" step="0.01" min="1">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">收款人</label>
            <div class="layui-input-block">
                <input type="text" name="account_name" required lay-verify="required" placeholder="请输入收款人姓名" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">收款账号</label>
            <div class="layui-input-block">
                <input type="text" name="account_number" required lay-verify="required" placeholder="请输入收款账号" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">开户银行</label>
            <div class="layui-input-block">
                <input type="text" name="bank_name" required lay-verify="required" placeholder="请输入开户银行" autocomplete="off" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="withdrawSubmit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<!-- 引入网站底部 -->
<div id="footer"></div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['layer', 'form', 'element', 'table', 'upload', 'jquery', 'echarts'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var element = layui.element;
    var table = layui.table;
    var upload = layui.upload;
    var echarts = layui.echarts;
    
    // 加载头部和底部
    $('#header').load('/common/header.html');
    $('#footer').load('/common/footer.html');
    
    // 检查登录状态
    $.get('/user/check_login', function(res) {
        if (res.code !== 0) {
            layer.msg('请先登录', {icon: 2}, function() {
                window.location.href = '/user/login?redirect=' + encodeURIComponent(window.location.href);
            });
        } else {
            // 获取商家信息
            getMerchantInfo();
        }
    }, 'json');
    
    // 导航切换
    element.on('nav(merchant-nav)', function(elem) {
        var id = $(elem).data('id');
        $('.merchant-page').hide();
        $('#' + id).show();
        
        // 加载对应页面数据
        switch (id) {
            case 'api':
                loadApiTable();
                break;
            case 'income':
                loadIncomeTable();
                break;
            case 'withdraw':
                loadWithdrawTable();
                break;
            case 'settings':
                loadMerchantSettings();
                break;
        }
    });
    
    // 获取商家信息
    function getMerchantInfo() {
        $.get('/merchant/get_info', function(res) {
            if (res.code === 0) {
                var data = res.data;
                
                // 更新商家等级信息
                $('#merchantLevel .merchant-level-name').text(data.level_name);
                $('#discountRate').text(data.discount_rate * 100 + '%');
                $('#maxApiCount').text(data.max_api_count > 0 ? data.max_api_count : '无限制');
                
                // 更新统计数据
                $('#todayIncome').text('¥' + data.today_income.toFixed(2));
                $('#monthIncome').text('¥' + data.month_income.toFixed(2));
                $('#totalIncome').text('¥' + data.total_income.toFixed(2));
                $('#apiCount').text(data.api_count);
                
                // 加载收入趋势图
                loadIncomeChart();
                
                // 加载API收入占比图
                loadApiIncomeChart();
                
                // 加载最近添加的API
                loadRecentApis();
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 加载收入趋势图
    function loadIncomeChart() {
        $.get('/merchant/get_income_stats', function(res) {
            if (res.code === 0) {
                var data = res.data;
                var dates = [];
                var amounts = [];
                
                for (var i = 0; i < data.trends.length; i++) {
                    dates.push(data.trends[i].date);
                    amounts.push(data.trends[i].amount);
                }
                
                var incomeChart = echarts.init(document.getElementById('incomeChart'));
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return params[0].name + '<br/>' + params[0].seriesName + ': ¥' + params[0].value.toFixed(2);
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: dates
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    series: [{
                        name: '收入',
                        data: amounts,
                        type: 'line',
                        smooth: true,
                        itemStyle: {
                            color: '#1E9FFF'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: 'rgba(30, 159, 255, 0.5)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(30, 159, 255, 0.1)'
                                }]
                            }
                        }
                    }]
                };
                incomeChart.setOption(option);
                
                // 窗口大小变化时重绘图表
                window.addEventListener('resize', function() {
                    incomeChart.resize();
                });
            }
        }, 'json');
    }
    
    // 加载API收入占比图
    function loadApiIncomeChart() {
        $.get('/merchant/get_income_stats', function(res) {
            if (res.code === 0) {
                var data = res.data;
                var apiIncomes = data.api_incomes;
                var chartData = [];
                
                for (var i = 0; i < apiIncomes.length; i++) {
                    chartData.push({
                        name: apiIncomes[i].api_name,
                        value: apiIncomes[i].amount
                    });
                }
                
                var apiIncomeChart = echarts.init(document.getElementById('apiIncomeChart'));
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        right: 10,
                        top: 'center',
                        data: chartData.map(function(item) {
                            return item.name;
                        })
                    },
                    series: [
                        {
                            name: 'API收入',
                            type: 'pie',
                            radius: ['50%', '70%'],
                            avoidLabelOverlap: false,
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '18',
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: chartData
                        }
                    ]
                };
                apiIncomeChart.setOption(option);
                
                // 窗口大小变化时重绘图表
                window.addEventListener('resize', function() {
                    apiIncomeChart.resize();
                });
            }
        }, 'json');
    }
    
    // 加载最近添加的API
    function loadRecentApis() {
        $.get('/merchant/get_api_list', {page: 1, limit: 5}, function(res) {
            if (res.code === 0) {
                var data = res.data;
                var html = '';
                
                if (data.list.length === 0) {
                    html = '<div class="layui-text"><p>暂无API，请点击右上角按钮添加API。</p></div>';
                } else {
                    for (var i = 0; i < data.list.length; i++) {
                        var api = data.list[i];
                        var statusHtml = api.status === 1 ? 
                            '<span class="layui-badge layui-bg-green">启用</span>' : 
                            '<span class="layui-badge">禁用</span>';
                        
                        html += '<div class="merchant-api-item">';
                        html += '<div class="merchant-api-status">' + statusHtml + '</div>';
                        html += '<div class="merchant-api-name">' + api.api_name + '</div>';
                        html += '<div class="merchant-api-url"><span class="layui-badge">' + api.method + '</span> ' + api.url + '</div>';
                        html += '<div class="merchant-api-price">价格：¥' + api.price.toFixed(2) + '</div>';
                        html += '</div>';
                    }
                }
                
                $('#recentApiList').html(html);
            }
        }, 'json');
    }
    
    // 加载API表格
    function loadApiTable() {
        table.render({
            elem: '#apiTable',
            url: '/merchant/get_api_list',
            page: true,
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'api_name', title: 'API名称'},
                {field: 'method', title: '请求方法', width: 100},
                {field: 'url', title: '请求URL'},
                {field: 'price', title: '价格', width: 100, templet: function(d) {
                    return '¥' + d.price.toFixed(2);
                }},
                {field: 'status', title: '状态', width: 80, templet: '#apiStatusTpl'},
                {field: 'create_time', title: '添加时间', width: 160, templet: function(d) {
                    return formatDate(d.create_time * 1000);
                }},
                {title: '操作', width: 150, align: 'center', toolbar: '#apiTableBar'}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count,
                    "data": res.data.list
                };
            }
        });
        
        // 监听工具条
        table.on('tool(apiTable)', function(obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                // 编辑API
                editApi(data);
            } else if (obj.event === 'del') {
                // 删除API
                layer.confirm('确定删除该API吗？', function(index) {
                    $.post('/merchant/delete_api', {id: data.id}, function(res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    layer.close(index);
                });
            }
        });
    }
    
    // 加载收入表格
    function loadIncomeTable() {
        table.render({
            elem: '#incomeTable',
            url: '/merchant/get_income_list',
            page: true,
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'api_name', title: 'API名称'},
                {field: 'amount', title: '金额', width: 100, templet: function(d) {
                    return '¥' + d.amount.toFixed(2);
                }},
                {field: 'user_id', title: '用户ID', width: 100},
                {field: 'create_time', title: '时间', width: 160, templet: function(d) {
                    return formatDate(d.create_time * 1000);
                }}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count,
                    "data": res.data.list
                };
            }
        });
    }
    
    // 加载提现表格
    function loadWithdrawTable() {
        $.get('/merchant/get_withdraw_list', {page: 1, limit: 1}, function(res) {
            if (res.code === 0) {
                $('#currentBalance').val('¥' + res.data.balance.toFixed(2));
            }
        }, 'json');
        
        table.render({
            elem: '#withdrawTable',
            url: '/merchant/get_withdraw_list',
            page: true,
            cols: [[
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'amount', title: '金额', width: 100, templet: function(d) {
                    return '¥' + d.amount.toFixed(2);
                }},
                {field: 'account_name', title: '收款人'},
                {field: 'account_number', title: '收款账号'},
                {field: 'bank_name', title: '开户银行'},
                {field: 'status', title: '状态', width: 100, templet: '#withdrawStatusTpl'},
                {field: 'create_time', title: '申请时间', width: 160, templet: function(d) {
                    return formatDate(d.create_time * 1000);
                }},
                {field: 'audit_time', title: '审核时间', width: 160, templet: function(d) {
                    return d.audit_time ? formatDate(d.audit_time * 1000) : '-';
                }}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count,
                    "data": res.data.list
                };
            }
        });
    }
    
    // 加载商家设置
    function loadMerchantSettings() {
        $.get('/merchant/get_info', function(res) {
            if (res.code === 0) {
                var data = res.data;
                
                // 填充表单
                form.val('settingsForm', {
                    'name': data.name,
                    'contact': data.contact,
                    'phone': data.phone,
                    'email': data.email,
                    'address': data.address,
                    'description': data.description,
                    'logo': data.logo
                });
                
                // 显示Logo
                if (data.logo) {
                    $('#logoImg').attr('src', data.logo);
                }
            }
        }, 'json');
    }
    
    // 上传Logo
    upload.render({
        elem: '#uploadLogo',
        url: '/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        before: function(obj) {
            obj.preview(function(index, file, result) {
                $('#logoImg').attr('src', result);
            });
            layer.load();
        },
        done: function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                $('#logoText').html('上传成功');
                $('input[name="logo"]').val(res.data.url);
            } else {
                $('#logoText').html('上传失败：' + res.msg);
            }
        },
        error: function() {
            layer.closeAll('loading');
            $('#logoText').html('上传失败，请重试');
        }
    });
    
    // 保存设置
    form.on('submit(settingsSubmit)', function(data) {
        $.post('/merchant/update_info', data.field, function(res) {
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 添加API按钮点击事件
    $('#addApiBtn, #addApiBtn2').click(function() {
        // 获取可添加的API列表
        $.get('/api/list', function(res) {
            if (res.code === 0) {
                var apis = res.data.list;
                var options = '<option value="">请选择API</option>';
                
                for (var i = 0; i < apis.length; i++) {
                    options += '<option value="' + apis[i].id + '">' + apis[i].name + ' (' + apis[i].method + ' ' + apis[i].url + ')</option>';
                }
                
                $('select[name="api_id"]').html(options);
                form.render('select');
                
                // 打开添加API弹窗
                layer.open({
                    type: 1,
                    title: '添加API',
                    area: ['500px', '400px'],
                    content: $('#addApiDialog').html(),
                    success: function() {
                        form.render();
                    }
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    });
    
    // 添加API表单提交
    form.on('submit(addApiSubmit)', function(data) {
        $.post('/merchant/add_api', data.field, function(res) {
            if (res.code === 0) {
                layer.closeAll('page');
                layer.msg(res.msg, {icon: 1}, function() {
                    // 刷新API列表
                    if ($('#api').is(':visible')) {
                        table.reload('apiTable');
                    } else {
                        loadRecentApis();
                    }
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 编辑API
    function editApi(data) {
        form.val('editApiForm', {
            'id': data.id,
            'api_name': data.api_name,
            'price': data.price,
            'status': data.status.toString(),
            'description': data.description
        });
        
        layer.open({
            type: 1,
            title: '编辑API',
            area: ['500px', '400px'],
            content: $('#editApiDialog').html(),
            success: function() {
                form.render();
            }
        });
    }
    
    // 编辑API表单提交
    form.on('submit(editApiSubmit)', function(data) {
        $.post('/merchant/update_api', data.field, function(res) {
            if (res.code === 0) {
                layer.closeAll('page');
                layer.msg(res.msg, {icon: 1}, function() {
                    // 刷新API列表
                    table.reload('apiTable');
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 申请提现按钮点击事件
    $('#withdrawBtn').click(function() {
        $.get('/merchant/get_withdraw_list', {page: 1, limit: 1}, function(res) {
            if (res.code === 0) {
                $('#withdrawBalance').val('¥' + res.data.balance.toFixed(2));
                
                layer.open({
                    type: 1,
                    title: '申请提现',
                    area: ['500px', '500px'],
                    content: $('#withdrawDialog').html(),
                    success: function() {
                        form.render();
                    }
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    });
    
    // 提现表单提交
    form.on('submit(withdrawSubmit)', function(data) {
        $.post('/merchant/apply_withdraw', data.field, function(res) {
            if (res.code === 0) {
                layer.closeAll('page');
                layer.msg(res.msg, {icon: 1}, function() {
                    // 刷新提现列表
                    table.reload('withdrawTable');
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 升级等级按钮点击事件
    $('#upgradeBtn').click(function() {
        $.get('/merchant/get_level_list', function(res) {
            if (res.code === 0) {
                var levels = res.data;
                var html = '<div class="layui-form" style="padding: 20px;">';
                
                for (var i = 0; i < levels.length; i++) {
                    var level = levels[i];
                    
                    html += '<div class="layui-card" style="margin-bottom: 15px;">';
                    html += '<div class="layui-card-header">' + level.name + ' <span style="float: right; color: #FF5722;">¥' + level.price.toFixed(2) + '</span></div>';
                    html += '<div class="layui-card-body">';
                    html += '<p>' + level.description + '</p>';
                    html += '<p>折扣率：' + (level.discount_rate * 100) + '%</p>';
                    html += '<p>API数量上限：' + (level.max_api_count > 0 ? level.max_api_count : '无限制') + '</p>';
                    
                    if (level.features && level.features.length > 0) {
                        html += '<p>特权：</p>';
                        html += '<ul>';
                        for (var j = 0; j < level.features.length; j++) {
                            html += '<li>' + level.features[j] + '</li>';
                        }
                        html += '</ul>';
                    }
                    
                    html += '<div class="layui-form-item" style="margin-top: 10px;">';
                    html += '<button type="button" class="layui-btn layui-btn-sm upgrade-level-btn" data-id="' + level.id + '">升级到此等级</button>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                }
                
                html += '</div>';
                
                layer.open({
                    type: 1,
                    title: '升级商家等级',
                    area: ['600px', '500px'],
                    content: html,
                    success: function() {
                        // 升级按钮点击事件
                        $('.upgrade-level-btn').click(function() {
                            var levelId = $(this).data('id');
                            
                            layer.confirm('确定要升级到此等级吗？', function(index) {
                                $.post('/merchant/upgrade_level', {level_id: levelId}, function(res) {
                                    if (res.code === 0) {
                                        layer.closeAll();
                                        layer.msg(res.msg, {icon: 1}, function() {
                                            // 跳转到支付页面
                                            window.location.href = '/pay/order?order_id=' + res.data.order_id;
                                        });
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }, 'json');
                            });
                        });
                    }
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    });
    
    // 格式化日期
    function formatDate(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        var seconds = ('0' + date.getSeconds()).slice(-2);
        
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }
});
</script>
</body>
</html>
