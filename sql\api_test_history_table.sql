-- API测试历史记录表
CREATE TABLE IF NOT EXISTS `api_test_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `url` varchar(255) NOT NULL COMMENT '请求URL',
  `params` text COMMENT '请求参数',
  `headers` text COMMENT '请求头',
  `body` text COMMENT '请求体',
  `status_code` int(11) NOT NULL COMMENT '状态码',
  `response` text COMMENT '响应内容',
  `test_time` int(11) NOT NULL COMMENT '测试时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_time` (`test_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API测试历史记录表';

-- API分类表
CREATE TABLE IF NOT EXISTS `api_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API分类表';

-- API接口表
CREATE TABLE IF NOT EXISTS `apis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '接口名称',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `url` varchar(255) NOT NULL COMMENT '接口URL',
  `description` text COMMENT '接口描述',
  `example_params` text COMMENT '示例参数',
  `example_response` text COMMENT '示例响应',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API接口表';

-- API参数表
CREATE TABLE IF NOT EXISTS `api_params` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_id` int(11) NOT NULL COMMENT 'API接口ID',
  `name` varchar(50) NOT NULL COMMENT '参数名称',
  `type` varchar(20) NOT NULL COMMENT '参数类型',
  `required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必须：0否，1是',
  `description` varchar(255) DEFAULT NULL COMMENT '参数描述',
  `default_value` varchar(100) DEFAULT NULL COMMENT '默认值',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  PRIMARY KEY (`id`),
  KEY `idx_api` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API参数表';

-- 插入测试数据
INSERT INTO `api_categories` (`id`, `name`, `description`, `sort`, `create_time`) VALUES
(1, '用户接口', '用户相关的API接口', 0, UNIX_TIMESTAMP()),
(2, '文章接口', '文章相关的API接口', 0, UNIX_TIMESTAMP()),
(3, '系统接口', '系统相关的API接口', 0, UNIX_TIMESTAMP());

INSERT INTO `apis` (`category_id`, `name`, `method`, `url`, `description`, `example_params`, `example_response`, `sort`, `status`, `create_time`, `update_time`) VALUES
(1, '用户登录', 'POST', '/api/user/login', '用户登录接口', '{"username":"test","password":"123456"}', '{"code":0,"msg":"登录成功","data":{"token":"xxx"}}', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '用户注册', 'POST', '/api/user/register', '用户注册接口', '{"username":"test","password":"123456","email":"<EMAIL>"}', '{"code":0,"msg":"注册成功","data":{"user_id":1}}', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '获取文章列表', 'GET', '/api/article/list', '获取文章列表接口', '{"page":1,"limit":10,"category_id":1}', '{"code":0,"msg":"获取成功","data":{"count":100,"list":[...]}}', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '获取文章详情', 'GET', '/api/article/detail', '获取文章详情接口', '{"id":1}', '{"code":0,"msg":"获取成功","data":{...}}', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '获取系统配置', 'GET', '/api/system/config', '获取系统配置接口', '{"type":"site"}', '{"code":0,"msg":"获取成功","data":{...}}', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

INSERT INTO `api_params` (`api_id`, `name`, `type`, `required`, `description`, `default_value`, `sort`) VALUES
(1, 'username', 'string', 1, '用户名', NULL, 0),
(1, 'password', 'string', 1, '密码', NULL, 0),
(2, 'username', 'string', 1, '用户名', NULL, 0),
(2, 'password', 'string', 1, '密码', NULL, 0),
(2, 'email', 'string', 1, '邮箱', NULL, 0),
(3, 'page', 'int', 0, '页码', '1', 0),
(3, 'limit', 'int', 0, '每页数量', '10', 0),
(3, 'category_id', 'int', 0, '分类ID', NULL, 0),
(4, 'id', 'int', 1, '文章ID', NULL, 0),
(5, 'type', 'string', 0, '配置类型', 'site', 0);