<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>文章列表 - API商业系统</title>
    <meta name="keywords" content="文章列表,API商业系统,技术文章">
    <meta name="description" content="API商业系统文章列表，提供最新技术资讯和API使用教程">
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/site.css"/>
    <style>
        .article-list-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .article-filter {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .article-item {
            display: flex;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
        }
        .article-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .article-cover {
            width: 220px;
            height: 150px;
            object-fit: cover;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        .article-info {
            flex: 1;
            padding: 15px;
            position: relative;
        }
        .article-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }
        .article-title a {
            color: #333;
            text-decoration: none;
        }
        .article-title a:hover {
            color: #009688;
        }
        .article-summary {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .article-meta {
            color: #999;
            font-size: 12px;
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
        }
        .article-meta span {
            margin-right: 15px;
        }
        .article-category {
            display: inline-block;
            padding: 2px 8px;
            background-color: #f2f2f2;
            color: #666;
            border-radius: 2px;
        }
        .article-empty {
            text-align: center;
            padding: 50px 0;
            color: #999;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .article-pagination {
            text-align: center;
            margin-top: 20px;
        }
        @media screen and (max-width: 768px) {
            .article-item {
                flex-direction: column;
            }
            .article-cover {
                width: 100%;
                height: 180px;
                border-radius: 4px 4px 0 0;
            }
            .article-meta {
                position: static;
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>
<!-- 引入网站头部 -->
<div id="header"></div>

<div class="layui-container article-list-container">
    <div class="article-filter">
        <form class="layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <input type="text" name="keyword" placeholder="搜索文章" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <select name="category_id" lay-filter="category">
                            <option value="">所有分类</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="searchForm">
                        <i class="layui-icon">&#xe615;</i> 搜索
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <div id="articleList">
        <!-- 文章列表将通过JS动态加载 -->
        <div class="article-empty">
            <i class="layui-icon" style="font-size: 50px;">&#xe69c;</i>
            <p>加载中...</p>
        </div>
    </div>
    
    <div id="pagination" class="article-pagination"></div>
</div>

<!-- 引入网站底部 -->
<div id="footer"></div>

<!-- 文章项模板 -->
<script type="text/html" id="articleItemTpl">
{{#  layui.each(d.list, function(index, item){ }}
<div class="article-item">
    {{#  if(item.cover){ }}
    <img src="{{ item.cover }}" alt="{{ item.title }}" class="article-cover">
    {{#  } else { }}
    <img src="/assets/images/default-article-cover.jpg" alt="{{ item.title }}" class="article-cover">
    {{#  } }}
    <div class="article-info">
        <h3 class="article-title">
            <a href="/article/detail?id={{ item.id }}">{{ item.title }}</a>
        </h3>
        <div class="article-summary">
            {{#  if(item.summary){ }}
            {{ item.summary }}
            {{#  } else { }}
            暂无摘要
            {{#  } }}
        </div>
        <div class="article-meta">
            <span class="article-category">{{ item.category_name || '未分类' }}</span>
            <span>作者：{{ item.author || '佚名' }}</span>
            <span>发布时间：{{ formatDate(item.create_time * 1000) }}</span>
            <span>浏览量：{{ item.view_count }}</span>
        </div>
    </div>
</div>
{{#  }); }}

{{#  if(d.list.length === 0){ }}
<div class="article-empty">
    <i class="layui-icon" style="font-size: 50px;">&#xe69c;</i>
    <p>暂无文章</p>
</div>
{{#  } }}
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['layer', 'form', 'laypage', 'laytpl', 'jquery'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var laypage = layui.laypage;
    var laytpl = layui.laytpl;
    
    // 加载头部和底部
    $('#header').load('/common/header.html');
    $('#footer').load('/common/footer.html');
    
    // 当前页码和每页数量
    var currentPage = 1;
    var pageSize = 10;
    var searchParams = {
        page: currentPage,
        limit: pageSize,
        keyword: '',
        category_id: ''
    };
    
    // 加载文章分类
    $.get('/article/category/list', function(res) {
        if (res.code === 0) {
            var options = '<option value="">所有分类</option>';
            for (var i = 0; i < res.data.length; i++) {
                options += '<option value="' + res.data[i].id + '">' + res.data[i].name + '</option>';
            }
            $('select[name="category_id"]').html(options);
            form.render('select');
        }
    }, 'json');
    
    // 加载文章列表
    function loadArticleList() {
        layer.load(2);
        $.get('/article/list', searchParams, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                // 渲染文章列表
                var getTpl = document.getElementById('articleItemTpl').innerHTML;
                laytpl(getTpl).render(res.data, function(html){
                    document.getElementById('articleList').innerHTML = html;
                });
                
                // 渲染分页
                laypage.render({
                    elem: 'pagination',
                    count: res.data.count,
                    limit: pageSize,
                    curr: currentPage,
                    layout: ['prev', 'page', 'next', 'skip', 'count', 'limit'],
                    limits: [10, 20, 30, 50],
                    jump: function(obj, first) {
                        if (!first) {
                            currentPage = obj.curr;
                            pageSize = obj.limit;
                            searchParams.page = currentPage;
                            searchParams.limit = pageSize;
                            loadArticleList();
                        }
                    }
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 初始加载
    loadArticleList();
    
    // 搜索表单提交
    form.on('submit(searchForm)', function(data) {
        searchParams.keyword = data.field.keyword;
        searchParams.category_id = data.field.category_id;
        searchParams.page = 1;
        currentPage = 1;
        loadArticleList();
        return false;
    });
    
    // 分类切换
    form.on('select(category)', function(data) {
        searchParams.category_id = data.value;
        searchParams.page = 1;
        currentPage = 1;
        loadArticleList();
    });
    
    // 格式化日期
    window.formatDate = function(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        return year + '-' + month + '-' + day;
    };
});
</script>
</body>
</html>