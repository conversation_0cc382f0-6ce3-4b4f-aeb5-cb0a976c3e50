<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title id="pageTitle">文章详情</title>
    <meta name="keywords" content="" id="pageKeywords">
    <meta name="description" content="" id="pageDescription">
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/site.css"/>
    <style>
        .article-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }
        .article-header {
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .article-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .article-meta {
            color: #999;
            font-size: 14px;
        }
        .article-meta span {
            margin-right: 15px;
        }
        .article-cover {
            width: 100%;
            max-height: 400px;
            object-fit: cover;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .article-content {
            line-height: 1.8;
            font-size: 16px;
            color: #333;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
        }
        .article-summary {
            background-color: #f8f8f8;
            padding: 15px;
            border-left: 4px solid #009688;
            margin-bottom: 20px;
            color: #666;
            font-size: 14px;
        }
        .article-tags {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }
        .article-tag {
            display: inline-block;
            padding: 4px 10px;
            background-color: #f2f2f2;
            color: #666;
            border-radius: 2px;
            margin-right: 10px;
            font-size: 12px;
        }
        .article-related {
            margin-top: 30px;
        }
        .article-related h3 {
            font-size: 18px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .article-related ul {
            padding-left: 20px;
        }
        .article-related li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
<!-- 引入网站头部 -->
<div id="header"></div>

<div class="layui-container">
    <div class="article-container">
        <div class="article-header">
            <h1 class="article-title" id="articleTitle">加载中...</h1>
            <div class="article-meta">
                <span id="articleCategory">分类：加载中...</span>
                <span id="articleAuthor">作者：加载中...</span>
                <span id="articleTime">发布时间：加载中...</span>
                <span id="articleViews">浏览量：0</span>
            </div>
        </div>
        
        <div id="articleSummary" class="article-summary layui-hide">
            摘要：加载中...
        </div>
        
        <img id="articleCover" class="article-cover layui-hide" src="" alt="文章封面">
        
        <div class="article-content" id="articleContent">
            加载中...
        </div>
        
        <div class="article-tags layui-hide" id="articleTags">
            <span>标签：</span>
        </div>
        
        <div class="article-related layui-hide" id="articleRelated">
            <h3>相关文章</h3>
            <ul id="relatedList"></ul>
        </div>
    </div>
</div>

<!-- 引入网站底部 -->
<div id="footer"></div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['layer', 'jquery'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    
    // 加载头部和底部
    $('#header').load('/common/header.html');
    $('#footer').load('/common/footer.html');
    
    // 获取URL参数
    var id = layui.url().search.id;
    if (!id) {
        layer.msg('文章ID不能为空', {icon: 2}, function() {
            location.href = '/article/list';
        });
        return;
    }
    
    // 加载文章数据
    layer.load(2);
    $.get('/article/detail', {
        id: id
    }, function(res) {
        layer.closeAll('loading');
        if (res.code === 0) {
            var data = res.data;
            
            // 设置页面标题和元信息
            $('#pageTitle').text(data.title);
            $('#pageKeywords').attr('content', data.keywords || data.title);
            $('#pageDescription').attr('content', data.summary || '');
            
            // 填充文章内容
            $('#articleTitle').text(data.title);
            $('#articleCategory').text('分类：' + data.category_name);
            $('#articleAuthor').text('作者：' + (data.author || '佚名'));
            $('#articleTime').text('发布时间：' + formatDate(data.create_time * 1000));
            $('#articleViews').text('浏览量：' + (data.view_count + 1)); // 浏览量+1
            
            // 显示摘要
            if (data.summary) {
                $('#articleSummary').removeClass('layui-hide').text('摘要：' + data.summary);
            }
            
            // 显示封面
            if (data.cover) {
                $('#articleCover').removeClass('layui-hide').attr('src', data.cover);
            }
            
            // 显示内容
            $('#articleContent').html(data.content);
            
            // 显示标签
            if (data.keywords) {
                var tags = data.keywords.split(',');
                var tagsHtml = '<span>标签：</span>';
                for (var i = 0; i < tags.length; i++) {
                    if (tags[i].trim()) {
                        tagsHtml += '<span class="article-tag">' + tags[i].trim() + '</span>';
                    }
                }
                $('#articleTags').removeClass('layui-hide').html(tagsHtml);
            }
            
            // 更新浏览量
            $.post('/article/view', {id: id});
            
            // 加载相关文章
            loadRelatedArticles(data.category_id, data.id);
        } else {
            layer.msg(res.msg, {icon: 2}, function() {
                location.href = '/article/list';
            });
        }
    }, 'json');
    
    // 加载相关文章
    function loadRelatedArticles(categoryId, currentId) {
        $.get('/article/related', {
            category_id: categoryId,
            current_id: currentId,
            limit: 5
        }, function(res) {
            if (res.code === 0 && res.data.length > 0) {
                var html = '';
                for (var i = 0; i < res.data.length; i++) {
                    html += '<li><a href="/article/detail?id=' + res.data[i].id + '">' + res.data[i].title + '</a></li>';
                }
                $('#relatedList').html(html);
                $('#articleRelated').removeClass('layui-hide');
            }
        }, 'json');
    }
    
    // 格式化日期
    function formatDate(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
    }
});
</script>
</body>
</html>