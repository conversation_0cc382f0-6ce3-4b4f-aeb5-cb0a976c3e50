<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">API管理</h2>
            <div class="layui-btn-group">
                <button class="layui-btn" id="btnAdd"><i class="layui-icon layui-icon-add-1"></i>添加API</button>
                <button class="layui-btn" id="btnGenerateDoc"><i class="layui-icon layui-icon-template"></i>生成文档</button>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 搜索表单 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">API名称:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="keyword" placeholder="API名称" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">分类:</label>
                        <div class="layui-input-inline">
                            <select name="category_id" id="categorySelect">
                                <option value="">全部分类</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="searchBtn" lay-submit>
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- 数据表格 -->
            <table id="apiTable" lay-filter="apiTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs" lay-event="debug">调试</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="statusSwitch" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status==1?'checked':''}} />
</script>

<!-- 表格免费列 -->
<script type="text/html" id="freeTpl">
    {{#  if(d.is_free == 1){ }}
    <span class="layui-badge layui-bg-green">免费</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-orange">付费</span>
    {{#  } }}
</script>

<!-- 表格请求方法列 -->
<script type="text/html" id="methodTpl">
    {{#  if(d.method == 'GET'){ }}
    <span class="layui-badge layui-bg-blue">GET</span>
    {{#  } else if(d.method == 'POST'){ }}
    <span class="layui-badge layui-bg-green">POST</span>
    {{#  } else if(d.method == 'PUT'){ }}
    <span class="layui-badge layui-bg-orange">PUT</span>
    {{#  } else if(d.method == 'DELETE'){ }}
    <span class="layui-badge layui-bg-red">DELETE</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-gray">{{d.method}}</span>
    {{#  } }}
</script>

<!-- 添加/编辑API弹窗 -->
<script type="text/html" id="apiFormTpl">
    <form class="layui-form" lay-filter="apiForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">API名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入API名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所属分类</label>
            <div class="layui-input-block">
                <select name="category_id" id="formCategorySelect" lay-verify="required">
                    <option value="">请选择分类</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">请求URL</label>
            <div class="layui-input-block">
                <input type="text" name="url" placeholder="请输入请求URL" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">请求方法</label>
            <div class="layui-input-block">
                <select name="method" lay-verify="required">
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">API描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入API描述" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否免费</label>
            <div class="layui-input-block">
                <input type="radio" name="is_free" value="1" title="免费" checked>
                <input type="radio" name="is_free" value="0" title="付费">
            </div>
        </div>
        <div class="layui-form-item" id="priceItem" style="display: none;">
            <label class="layui-form-label">普通价格</label>
            <div class="layui-input-block">
                <input type="text" name="price" placeholder="请输入普通用户价格" class="layui-input" value="0">
            </div>
        </div>
        <div class="layui-form-item" id="merchantPriceItem" style="display: none;">
            <label class="layui-form-label">商家价格</label>
            <div class="layui-input-block">
                <input type="text" name="merchant_price" placeholder="请输入商家价格" class="layui-input" value="0">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort_order" placeholder="请输入排序" class="layui-input" value="0">
            </div>
        </div>
        
        <!-- 参数设置 -->
        <fieldset class="layui-elem-field">
            <legend>参数设置</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <button type="button" class="layui-btn layui-btn-sm" id="addParamBtn">
                            <i class="layui-icon layui-icon-add-1"></i>添加参数
                        </button>
                    </div>
                </div>
                <div id="paramList"></div>
            </div>
        </fieldset>
        
        <!-- 响应示例 -->
        <fieldset class="layui-elem-field">
            <legend>响应示例</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <button type="button" class="layui-btn layui-btn-sm" id="addResponseBtn">
                            <i class="layui-icon layui-icon-add-1"></i>添加响应示例
                        </button>
                    </div>
                </div>
                <div id="responseList"></div>
            </div>
        </fieldset>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-filter="apiSubmit" lay-submit>保存</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<!-- 参数模板 -->
<script type="text/html" id="paramItemTpl">
    <div class="layui-card param-item" data-index="{{d.index}}">
        <div class="layui-card-header">
            参数 {{d.index+1}}
            <a class="layui-btn layui-btn-danger layui-btn-xs param-delete" style="float: right;">删除</a>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">参数名</label>
                <div class="layui-input-block">
                    <input type="text" name="params[{{d.index}}][name]" placeholder="请输入参数名" class="layui-input" value="{{d.name||''}}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">参数类型</label>
                <div class="layui-input-block">
                    <select name="params[{{d.index}}][type]">
                        <option value="string" {{d.type=='string'?'selected':''}}>字符串</option>
                        <option value="number" {{d.type=='number'?'selected':''}}>数字</option>
                        <option value="boolean" {{d.type=='boolean'?'selected':''}}>布尔值</option>
                        <option value="array" {{d.type=='array'?'selected':''}}>数组</option>
                        <option value="object" {{d.type=='object'?'selected':''}}>对象</option>
                        <option value="file" {{d.type=='file'?'selected':''}}>文件</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">参数描述</label>
                <div class="layui-input-block">
                    <input type="text" name="params[{{d.index}}][description]" placeholder="请输入参数描述" class="layui-input" value="{{d.description||''}}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">是否必填</label>
                <div class="layui-input-block">
                    <input type="radio" name="params[{{d.index}}][required]" value="1" title="是" {{d.required==1?'checked':''}}>
                    <input type="radio" name="params[{{d.index}}][required]" value="0" title="否" {{d.required!=1?'checked':''}}>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">默认值</label>
                <div class="layui-input-block">
                    <input type="text" name="params[{{d.index}}][default_value]" placeholder="请输入默认值" class="layui-input" value="{{d.default_value||''}}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="params[{{d.index}}][sort_order]" placeholder="请输入排序" class="layui-input" value="{{d.sort_order||0}}">
                </div>
            </div>
        </div>
    </div>
</script>

<!-- 响应示例模板 -->
<script type="text/html" id="responseItemTpl">
    <div class="layui-card response-item" data-index="{{d.index}}">
        <div class="layui-card-header">
            响应示例 {{d.index+1}}
            <a class="layui-btn layui-btn-danger layui-btn-xs response-delete" style="float: right;">删除</a>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block">
                    <input type="text" name="responses[{{d.index}}][name]" placeholder="请输入名称" class="layui-input" value="{{d.name||''}}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">类型</label>
                <div class="layui-input-block">
                    <select name="responses[{{d.index}}][type]">
                        <option value="success" {{d.type=='success'?'selected':''}}>成功响应</option>
                        <option value="error" {{d.type=='error'?'selected':''}}>错误响应</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">内容</label>
                <div class="layui-input-block">
                    <textarea name="responses[{{d.index}}][content]" placeholder="请输入响应内容" class="layui-textarea" rows="6">{{d.content||''}}</textarea>
                </div>
            </div>
        </div>
    </div>
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../../Easyweb/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script>
layui.use(['table', 'form', 'layer', 'laytpl', 'util'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var util = layui.util;
    var $ = layui.jquery;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#apiTable',
        url: '../controllers/ApiController.php?action=getApiList',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'name', title: 'API名称', sort: true},
            {field: 'category_name', title: '分类', sort: true},
            {field: 'url', title: '请求URL', width: 250},
            {field: 'method', title: '请求方法', templet: '#methodTpl', width: 100, sort: true},
            {field: 'is_free', title: '是否免费', templet: '#freeTpl', width: 100, sort: true},
            {field: 'price', title: '普通价格', width: 100, sort: true},
            {field: 'merchant_price', title: '商家价格', width: 100, sort: true},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100, sort: true},
            {field: 'created_at', title: '创建时间', sort: true},
            {title: '操作', toolbar: '#tableBar', width: 180, fixed: 'right'}
        ]]
    });
    
    // 加载分类
    loadCategories();
    
    // 表格工具条点击事件
    table.on('tool(apiTable)', function(obj){
        var data = obj.data;
        var layEvent = obj.event;
        
        if(layEvent === 'edit'){ // 修改
            showEditForm(data);
        } else if(layEvent === 'del'){ // 删除
            layer.confirm('确定要删除该API吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(i){
                layer.close(i);
                
                $.get('../controllers/ApiController.php?action=delete&id=' + data.id, function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        } else if(layEvent === 'debug'){ // 调试
            window.open('debug.html?id=' + data.id);
        }
    });
    
    // 表单提交事件
    form.on('submit(apiSubmit)', function(data){
        var loadIndex = layer.load(2);
        
        $.ajax({
            url: '../controllers/ApiController.php?action=' + (data.field.id ? 'edit' : 'add'),
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function(res){
                layer.close(loadIndex);
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                    layer.closeAll('page');
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(){
                layer.close(loadIndex);
                layer.msg('服务器错误', {icon: 2});
            }
        });
        
        return false;
    });
    
    // 搜索按钮点击事件
    form.on('submit(searchBtn)', function(data){
        insTb.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 添加按钮点击事件
    $('#btnAdd').click(function(){
        showEditForm();
    });
    
    // 生成文档按钮点击事件
    $('#btnGenerateDoc').click(function(){
        var loadIndex = layer.load(2);
        
        $.get('../controllers/ApiController.php?action=generateDoc', function(res){
            layer.close(loadIndex);
            if(res.code === 0){
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    });
    
    // 状态开关点击事件
    form.on('switch(statusSwitch)', function(obj){
        var loadIndex = layer.load(2);
        
        $.get('../controllers/ApiController.php?action=changeStatus&id=' + obj.value + '&status=' + (obj.elem.checked ? 1 : 0), function(res){
            layer.close(loadIndex);
            if(res.code !== 0){
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });
    
    // 是否免费单选按钮点击事件
    form.on('radio(is_free)', function(obj){
        if(obj.value === '1'){
            $('#priceItem, #merchantPriceItem').hide();
        } else {
            $('#priceItem, #merchantPriceItem').show();
        }
    });
    
    // 加载分类
    function loadCategories(){
        $.get('../controllers/ApiController.php?action=getCategories', function(res){
            if(res.code === 0){
                var options = '<option value="">全部分类</option>';
                var formOptions = '<option value="">请选择分类</option>';
                
                $.each(res.data, function(i, item){
                    options += '<option value="' + item.id + '">' + item.name + '</option>';
                    formOptions += '<option value="' + item.id + '">' + item.name + '</option>';
                });
                
                $('#categorySelect').html(options);
                $('#formCategorySelect').html(formOptions);
                form.render('select');
            }
        }, 'json');
    }
    
    // 显示编辑表单
    function showEditForm(data){
        var title = data ? '编辑API' : '添加API';
        
        layer.open({
            type: 1,
            title: title,
            area: ['800px', '600px'],
            content: laytpl($('#apiFormTpl').html()).render({}),
            success: function(layero, index){
                form.render();
                
                // 绑定添加参数按钮点击事件
                $('#addParamBtn').click(function(){
                    addParam();
                });
                
                // 绑定添加响应示例按钮点击事件
                $('#addResponseBtn').click(function(){
                    addResponse();
                });
                
                // 绑定删除参数按钮点击事件
                $(document).on('click', '.param-delete', function(){
                    $(this).closest('.param-item').remove();
                });
                
                // 绑定删除响应示例按钮点击事件
                $(document).on('click', '.response-delete', function(){
                    $(this).closest('.response-item').remove();
                });
                
                // 绑定是否免费单选按钮点击事件
                form.on('radio', function(obj){
                    if(obj.elem.name === 'is_free'){
                        if(obj.value === '1'){
                            $('#priceItem, #merchantPriceItem').hide();
                        } else {
                            $('#priceItem, #merchantPriceItem').show();
                        }
                    }
                });
                
                // 如果是编辑，加载数据
                if(data){
                    // 加载基本信息
                    form.val('apiForm', data);
                    
                    // 显示/隐藏价格输入框
                    if(data.is_free == 1){
                        $('#priceItem, #merchantPriceItem').hide();
                    } else {
                        $('#priceItem, #merchantPriceItem').show();
                    }
                    
                    // 加载参数
                    if(data.params && data.params.length > 0){
                        $.each(data.params, function(i, param){
                            addParam(param);
                        });
                    }
                    
                    // 加载响应示例
                    if(data.responses && data.responses.length > 0){
                        $.each(data.responses, function(i, response){
                            addResponse(response);
                        });
                    }
                }
            }
        });
    }
    
    // 添加参数
    function addParam(param){
        var index = $('.param-item').length;
        var data = $.extend({index: index}, param || {});
        
        var html = laytpl($('#paramItemTpl').html()).render(data);
        $('#paramList').append(html);
        form.render();
    }
    
    // 添加响应示例
    function addResponse(response){
        var index = $('.response-item').length;
        var data = $.extend({index: index}, response || {});
        
        var html = laytpl($('#responseItemTpl').html()).render(data);
        $('#responseList').append(html);
        form.render();
    }
});
</script>
</body>
</html>