<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>轮播图管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">轮播图管理</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <input id="searchKeyword" class="layui-input" type="text" placeholder="轮播图标题"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <select id="searchStatus">
                            <option value="">所有状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <select id="searchPosition">
                            <option value="">所有位置</option>
                            <option value="home">首页</option>
                            <option value="api">API页面</option>
                            <option value="doc">文档页面</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <button id="searchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                        <button id="addBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                    </div>
                </div>
            </div>

            <table class="layui-table" id="bannerTable" lay-filter="bannerTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 图片预览 -->
<script type="text/html" id="imageTpl">
    <img src="{{d.image_url}}" style="height: 40px;" class="layui-circle" lay-event="preview">
</script>

<!-- 状态 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="statusSwitch" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status==1?'checked':''}} />
</script>

<!-- 添加/修改轮播图弹窗 -->
<script type="text/html" id="bannerEditDialog">
    <form id="bannerEditForm" lay-filter="bannerEditForm" class="layui-form model-form">
        <input name="id" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label">轮播图标题</label>
            <div class="layui-input-block">
                <input name="title" placeholder="请输入轮播图标题" type="text" class="layui-input" maxlength="100" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">轮播图片</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn" id="imageUpload">上传图片</button>
                    <div class="layui-upload-list">
                        <img class="layui-upload-img" id="imagePreview" style="max-height: 100px;">
                        <p id="imageText"></p>
                    </div>
                    <input type="hidden" name="image_url" id="imageUrl" lay-verify="required" required/>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">链接地址</label>
            <div class="layui-input-block">
                <input name="link_url" placeholder="请输入链接地址" type="text" class="layui-input"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">显示位置</label>
            <div class="layui-input-block">
                <select name="position" lay-verify="required">
                    <option value="home">首页</option>
                    <option value="api">API页面</option>
                    <option value="doc">文档页面</option>
                    <option value="other">其他</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序号</label>
            <div class="layui-input-block">
                <input name="sort" placeholder="请输入排序号" type="number" class="layui-input" value="0"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked/>
                <input type="radio" name="status" value="0" title="禁用"/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="bannerEditSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'upload'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var upload = layui.upload;

    // 渲染表格
    var insTb = table.render({
        elem: '#bannerTable',
        url: '/admin/banner/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'title', title: '轮播图标题'},
            {field: 'image_url', title: '轮播图片', templet: '#imageTpl'},
            {field: 'link_url', title: '链接地址'},
            {field: 'position', title: '显示位置', templet: function (d) {
                var positions = {
                    'home': '首页',
                    'api': 'API页面',
                    'doc': '文档页面',
                    'other': '其他'
                };
                return positions[d.position] || d.position;
            }},
            {field: 'sort', title: '排序号', width: 80},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100},
            {field: 'create_time', title: '创建时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }},
            {title: '操作', toolbar: '#tableBar', width: 120}
        ]]
    });

    // 添加按钮点击事件
    $('#addBtn').click(function () {
        showEditDialog();
    });

    // 搜索按钮点击事件
    $('#searchBtn').click(function () {
        insTb.reload({
            where: {
                keyword: $('#searchKeyword').val(),
                status: $('#searchStatus').val(),
                position: $('#searchPosition').val()
            },
            page: {curr: 1}
        });
    });

    // 工具条点击事件
    table.on('tool(bannerTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'edit') { // 修改
            showEditDialog(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该轮播图吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/banner/delete', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        } else if (layEvent === 'preview') { // 预览图片
            layer.photos({
                photos: {
                    data: [{
                        src: data.image_url
                    }]
                },
                shade: .1,
                closeBtn: true
            });
        }
    });

    // 监听状态开关
    form.on('switch(statusSwitch)', function (obj) {
        layer.load(2);
        $.post('/admin/banner/status', {
            id: obj.value,
            status: obj.elem.checked ? 1 : 0
        }, function (res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });

    // 显示编辑弹窗
    function showEditDialog(data) {
        admin.open({
            type: 1,
            title: (data ? '修改' : '添加') + '轮播图',
            content: $('#bannerEditDialog').html(),
            success: function (layero, dIndex) {
                // 回显表单数据
                if (data) {
                    form.val('bannerEditForm', data);
                    $('#imagePreview').attr('src', data.image_url);
                }
                
                // 表单提交事件
                form.on('submit(bannerEditSubmit)', function (data) {
                    layer.load(2);
                    $.post('/admin/banner/save', data.field, function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            insTb.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    return false;
                });
                
                // 渲染上传组件
                upload.render({
                    elem: '#imageUpload',
                    url: '/admin/upload/image',
                    accept: 'images',
                    acceptMime: 'image/*',
                    done: function(res) {
                        if (res.code === 0) {
                            $('#imagePreview').attr('src', res.data.url);
                            $('#imageUrl').val(res.data.url);
                            $('#imageText').html('');
                        } else {
                            $('#imageText').html('上传失败：' + res.msg);
                        }
                    },
                    error: function() {
                        $('#imageText').html('上传失败，请重试');
                    }
                });
            }
        });
    }
});
</script>
</body>
</html>