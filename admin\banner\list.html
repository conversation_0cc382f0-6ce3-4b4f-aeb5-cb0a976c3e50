<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>轮播图管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">轮播图管理</h2>
        </div>
        <div class="layui-card-body">
            <!-- 工具栏 -->
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <button class="layui-btn" id="add-banner">
                            <i class="layui-icon layui-icon-add-1"></i>添加轮播图
                        </button>
                        <button class="layui-btn layui-btn-danger" id="batch-delete">
                            <i class="layui-icon layui-icon-delete"></i>批量删除
                        </button>
                    </div>
                    <div class="layui-inline" style="float: right;">
                        <input type="text" name="title" placeholder="请输入标题搜索" class="layui-input" style="width: 200px; display: inline-block;">
                        <button class="layui-btn" id="search-btn">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="banner-table" lay-filter="banner-table"></table>
        </div>
    </div>
</div>

<!-- 添加/编辑轮播图弹窗 -->
<div id="banner-form" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="banner-form">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">标题</label>
            <div class="layui-input-block">
                <input type="text" name="title" placeholder="请输入轮播图标题" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">轮播图片</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn" id="upload-banner">上传图片</button>
                    <div class="layui-upload-list">
                        <img class="layui-upload-img" id="banner-preview" style="width: 200px; height: 100px; display: none;">
                        <input type="hidden" name="image_url" lay-verify="required">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">链接地址</label>
            <div class="layui-input-block">
                <input type="url" name="link_url" placeholder="请输入跳转链接" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort_order" placeholder="数字越小越靠前" class="layui-input" value="0">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入描述信息" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="save-banner">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" id="cancel-banner">取消</button>
            </div>
        </div>
    </form>
</div>

<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="edit">编辑</button>
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</button>
    </div>
</script>

<script type="text/html" id="status-tpl">
    {{# if(d.status == 1){ }}
        <span class="layui-badge layui-bg-green">启用</span>
    {{# } else { }}
        <span class="layui-badge">禁用</span>
    {{# } }}
</script>

<script type="text/html" id="image-tpl">
    <img src="{{d.image_url}}" style="width: 80px; height: 40px; object-fit: cover;">
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['table', 'form', 'layer', 'upload'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var upload = layui.upload;
    var $ = layui.$;

    // 渲染表格
    table.render({
        elem: '#banner-table',
        url: '../controllers/BannerController.php?action=getList',
        toolbar: '#toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', width: 50},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'title', title: '标题', width: 200},
            {field: 'image_url', title: '图片', width: 120, templet: '#image-tpl'},
            {field: 'link_url', title: '链接地址', width: 250},
            {field: 'sort_order', title: '排序', width: 100, sort: true},
            {field: 'status', title: '状态', width: 100, templet: '#status-tpl'},
            {field: 'created_at', title: '创建时间', width: 180, sort: true},
            {title: '操作', width: 150, align: 'center', toolbar: '#toolbar'}
        ]],
        page: true,
        height: 'full-220'
    });

    // 添加轮播图
    $('#add-banner').click(function(){
        layer.open({
            type: 1,
            title: '添加轮播图',
            content: $('#banner-form'),
            area: ['600px', '500px'],
            success: function(){
                form.val('banner-form', {});
                $('#banner-preview').hide();
            }
        });
    });

    // 图片上传
    upload.render({
        elem: '#upload-banner',
        url: '../controllers/UploadController.php',
        accept: 'images',
        before: function(obj){
            obj.preview(function(index, file, result){
                $('#banner-preview').attr('src', result).show();
            });
        },
        done: function(res){
            if(res.code === 200){
                $('input[name="image_url"]').val(res.data.url);
                layer.msg('上传成功');
            } else {
                layer.msg('上传失败: ' + res.msg);
            }
        }
    });

    // 表格工具栏事件
    table.on('tool(banner-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'edit'){
            layer.open({
                type: 1,
                title: '编辑轮播图',
                content: $('#banner-form'),
                area: ['600px', '500px'],
                success: function(){
                    form.val('banner-form', data);
                    if(data.image_url){
                        $('#banner-preview').attr('src', data.image_url).show();
                    }
                }
            });
        } else if(obj.event === 'del'){
            layer.confirm('确定删除这个轮播图吗？', function(index){
                $.post('../controllers/BannerController.php?action=delete', {id: data.id}, function(res){
                    if(res.code === 200){
                        layer.msg('删除成功');
                        table.reload('banner-table');
                    } else {
                        layer.msg('删除失败: ' + res.msg);
                    }
                }, 'json');
                layer.close(index);
            });
        }
    });

    // 保存轮播图
    form.on('submit(save-banner)', function(data){
        var url = data.field.id ? '../controllers/BannerController.php?action=update' : '../controllers/BannerController.php?action=add';
        $.post(url, data.field, function(res){
            if(res.code === 200){
                layer.msg('保存成功');
                layer.closeAll();
                table.reload('banner-table');
            } else {
                layer.msg('保存失败: ' + res.msg);
            }
        }, 'json');
        return false;
    });

    // 取消按钮
    $('#cancel-banner').click(function(){
        layer.closeAll();
    });

    // 批量删除
    $('#batch-delete').click(function(){
        var checkStatus = table.checkStatus('banner-table');
        if(checkStatus.data.length === 0){
            layer.msg('请选择要删除的数据');
            return;
        }
        layer.confirm('确定删除选中的轮播图吗？', function(index){
            var ids = checkStatus.data.map(function(item){
                return item.id;
            });
            $.post('../controllers/BannerController.php?action=batchDelete', {ids: ids}, function(res){
                if(res.code === 200){
                    layer.msg('删除成功');
                    table.reload('banner-table');
                } else {
                    layer.msg('删除失败: ' + res.msg);
                }
            }, 'json');
            layer.close(index);
        });
    });

    // 搜索
    $('#search-btn').click(function(){
        var title = $('input[name="title"]').val();
        table.reload('banner-table', {
            where: {title: title},
            page: {curr: 1}
        });
    });
});
</script>
</body>
</html>