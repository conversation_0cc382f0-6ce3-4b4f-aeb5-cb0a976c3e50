<?php
/**
 * 文件管理模型
 * API管理系统 - 文件上传与管理
 */

require_once __DIR__ . '/../core/Model.php';
require_once __DIR__ . '/SystemConfig.php';

class FileManager extends Model {
    protected $table = 'files';
    protected $fillable = [
        'original_name', 'file_name', 'file_path', 'file_size', 'file_type', 
        'mime_type', 'user_id', 'category', 'description', 'download_count'
    ];
    
    private $systemConfig;
    
    public function __construct() {
        parent::__construct();
        $this->systemConfig = new SystemConfig();
    }
    
    // 文件分类常量
    const CATEGORY_IMAGE = 'image';
    const CATEGORY_DOCUMENT = 'document';
    const CATEGORY_VIDEO = 'video';
    const CATEGORY_AUDIO = 'audio';
    const CATEGORY_OTHER = 'other';
    
    /**
     * 上传文件
     */
    public function uploadFile($file, $category = self::CATEGORY_OTHER, $userId = null, $description = '') {
        // 验证文件
        $this->validateFile($file);
        
        // 生成文件名和路径
        $fileName = $this->generateFileName($file['name']);
        $filePath = $this->generateFilePath($category, $fileName);
        $fullPath = $this->getUploadDir() . '/' . $filePath;
        
        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // 移动文件
        if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
            throw new Exception('文件上传失败');
        }
        
        // 保存文件信息到数据库
        $fileData = [
            'original_name' => $file['name'],
            'file_name' => $fileName,
            'file_path' => $filePath,
            'file_size' => $file['size'],
            'file_type' => pathinfo($file['name'], PATHINFO_EXTENSION),
            'mime_type' => $file['type'],
            'user_id' => $userId,
            'category' => $category,
            'description' => $description,
            'download_count' => 0
        ];
        
        $fileRecord = $this->create($fileData);
        
        return [
            'id' => $fileRecord['id'],
            'file_name' => $fileName,
            'file_path' => $filePath,
            'file_url' => $this->getFileUrl($filePath),
            'file_size' => $file['size'],
            'file_type' => pathinfo($file['name'], PATHINFO_EXTENSION)
        ];
    }
    
    /**
     * 验证文件
     */
    private function validateFile($file) {
        // 检查上传错误
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('文件上传错误: ' . $this->getUploadErrorMessage($file['error']));
        }
        
        // 检查文件大小
        $maxSize = $this->systemConfig->getConfig('upload_max_size', 10) * 1024 * 1024; // MB转字节
        if ($file['size'] > $maxSize) {
            throw new Exception('文件大小超过限制');
        }
        
        // 检查文件类型
        $allowedTypes = $this->systemConfig->getConfig('allowed_file_types', ['jpg', 'jpeg', 'png', 'gif', 'pdf']);
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($fileExtension, $allowedTypes)) {
            throw new Exception('不支持的文件类型');
        }
        
        // 检查MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        if (isset($allowedMimes[$fileExtension]) && $mimeType !== $allowedMimes[$fileExtension]) {
            throw new Exception('文件类型验证失败');
        }
    }
    
    /**
     * 生成文件名
     */
    private function generateFileName($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        return date('YmdHis') . '_' . uniqid() . '.' . $extension;
    }
    
    /**
     * 生成文件路径
     */
    private function generateFilePath($category, $fileName) {
        return $category . '/' . date('Y/m/d') . '/' . $fileName;
    }
    
    /**
     * 获取上传目录
     */
    private function getUploadDir() {
        return __DIR__ . '/../public/uploads';
    }
    
    /**
     * 获取文件URL
     */
    private function getFileUrl($filePath) {
        return '/uploads/' . $filePath;
    }
    
    /**
     * 获取上传错误信息
     */
    private function getUploadErrorMessage($errorCode) {
        $errors = [
            UPLOAD_ERR_INI_SIZE => '文件大小超过php.ini中upload_max_filesize的值',
            UPLOAD_ERR_FORM_SIZE => '文件大小超过表单中MAX_FILE_SIZE的值',
            UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
            UPLOAD_ERR_NO_FILE => '没有文件被上传',
            UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
            UPLOAD_ERR_CANT_WRITE => '文件写入失败',
            UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
        ];
        
        return $errors[$errorCode] ?? '未知错误';
    }
    
    /**
     * 获取文件列表
     */
    public function getFileList($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        $whereClause = [];
        $params = [];
        
        // 构建查询条件
        if (!empty($conditions['category'])) {
            $whereClause[] = "category = :category";
            $params['category'] = $conditions['category'];
        }
        
        if (!empty($conditions['file_type'])) {
            $whereClause[] = "file_type = :file_type";
            $params['file_type'] = $conditions['file_type'];
        }
        
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['keyword'])) {
            $whereClause[] = "(original_name LIKE :keyword OR description LIKE :keyword)";
            $params['keyword'] = '%' . $conditions['keyword'] . '%';
        }
        
        $whereSQL = empty($whereClause) ? '' : 'WHERE ' . implode(' AND ', $whereClause);
        
        // 查询总数
        $countSQL = "SELECT COUNT(*) as total FROM {$this->getTableName()} {$whereSQL}";
        $totalResult = $this->db->fetchOne($countSQL, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSQL = "SELECT f.*, u.username 
                    FROM {$this->getTableName()} f 
                    LEFT JOIN users u ON f.user_id = u.id 
                    {$whereSQL}
                    ORDER BY f.id DESC 
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSQL, $params);
        
        // 添加文件URL
        foreach ($data as &$file) {
            $file['file_url'] = $this->getFileUrl($file['file_path']);
            $file['file_size_formatted'] = $this->formatFileSize($file['file_size']);
        }
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 删除文件
     */
    public function deleteFile($id) {
        $file = $this->find($id);
        if (!$file) {
            throw new Exception('文件不存在');
        }
        
        // 删除物理文件
        $fullPath = $this->getUploadDir() . '/' . $file['file_path'];
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
        
        // 删除数据库记录
        return $this->delete($id);
    }
    
    /**
     * 批量删除文件
     */
    public function batchDeleteFiles($ids) {
        if (empty($ids) || !is_array($ids)) {
            throw new Exception('ID列表不能为空');
        }
        
        try {
            $this->db->getPdo()->beginTransaction();
            
            foreach ($ids as $id) {
                $this->deleteFile($id);
            }
            
            $this->db->getPdo()->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->getPdo()->rollBack();
            throw $e;
        }
    }
    
    /**
     * 记录下载
     */
    public function recordDownload($id) {
        $sql = "UPDATE {$this->getTableName()} SET download_count = download_count + 1 WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * 获取文件统计
     */
    public function getFileStats() {
        $sql = "SELECT 
                    category,
                    COUNT(*) as file_count,
                    SUM(file_size) as total_size,
                    SUM(download_count) as total_downloads
                FROM {$this->getTableName()} 
                GROUP BY category";
        
        $stats = $this->db->fetchAll($sql);
        
        $result = [];
        foreach ($stats as $stat) {
            $result[$stat['category']] = [
                'count' => $stat['file_count'],
                'size' => $stat['total_size'],
                'size_formatted' => $this->formatFileSize($stat['total_size']),
                'downloads' => $stat['total_downloads']
            ];
        }
        
        return $result;
    }
    
    /**
     * 清理无效文件
     */
    public function cleanInvalidFiles() {
        $sql = "SELECT * FROM {$this->getTableName()}";
        $files = $this->db->fetchAll($sql);
        
        $deletedCount = 0;
        foreach ($files as $file) {
            $fullPath = $this->getUploadDir() . '/' . $file['file_path'];
            if (!file_exists($fullPath)) {
                $this->delete($file['id']);
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * 获取磁盘使用情况
     */
    public function getDiskUsage() {
        $uploadDir = $this->getUploadDir();
        
        // 计算总大小
        $totalSize = 0;
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($uploadDir));
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $totalSize += $file->getSize();
            }
        }
        
        // 获取磁盘空间
        $freeSpace = disk_free_space($uploadDir);
        $totalSpace = disk_total_space($uploadDir);
        $usedSpace = $totalSpace - $freeSpace;
        
        return [
            'upload_size' => $totalSize,
            'upload_size_formatted' => $this->formatFileSize($totalSize),
            'free_space' => $freeSpace,
            'free_space_formatted' => $this->formatFileSize($freeSpace),
            'total_space' => $totalSpace,
            'total_space_formatted' => $this->formatFileSize($totalSpace),
            'used_space' => $usedSpace,
            'used_space_formatted' => $this->formatFileSize($usedSpace),
            'usage_percent' => round(($usedSpace / $totalSpace) * 100, 2)
        ];
    }
}