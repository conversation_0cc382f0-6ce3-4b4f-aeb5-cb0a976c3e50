<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>用户注册 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/style.css"/>
    <style>
        .register-wrapper {
            max-width: 420px;
            margin: 50px auto;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background-color: #fff;
        }
        .register-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .register-header img {
            max-height: 60px;
            margin-bottom: 10px;
        }
        .register-form {
            padding: 20px 0;
        }
        .register-links {
            margin-top: 20px;
            text-align: right;
        }
        .captcha-img {
            cursor: pointer;
            height: 38px;
        }
    </style>
</head>
<body>
    <div class="layui-container">
        <div class="register-wrapper">
            <div class="register-header">
                <?php if (!empty($siteConfig['site_logo'])): ?>
                <a href="/"><img src="<?php echo $siteConfig['site_logo']; ?>" alt="<?php echo $siteConfig['site_name']; ?>"></a>
                <?php else: ?>
                <h2><?php echo $siteConfig['site_name']; ?></h2>
                <?php endif; ?>
                <p>用户注册</p>
            </div>
            
            <?php if (!empty($error)): ?>
            <div class="layui-bg-red" style="padding: 10px; margin-bottom: 15px; border-radius: 2px;">
                <?php echo $error; ?>
            </div>
            <?php endif; ?>
            
            <form class="layui-form register-form" action="/user/register" method="post">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="text" name="username" required lay-verify="required" placeholder="用户名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="email" name="email" required lay-verify="required|email" placeholder="邮箱" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="password" name="password" required lay-verify="required|password" placeholder="密码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="password" name="confirm_password" required lay-verify="required|confirmPassword" placeholder="确认密码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <?php if ($siteConfig['enable_captcha']): ?>
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0; display: flex;">
                        <input type="text" name="captcha" required lay-verify="required" placeholder="验证码" autocomplete="off" class="layui-input" style="flex: 1; margin-right: 10px;">
                        <img src="/captcha.php" class="captcha-img" onclick="this.src='/captcha.php?t='+Math.random();" title="点击刷新">
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if ($siteConfig['enable_invite_code']): ?>
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="text" name="invite_code" <?php echo $siteConfig['require_invite_code'] ? 'required lay-verify="required"' : ''; ?> placeholder="邀请码<?php echo $siteConfig['require_invite_code'] ? '（必填）' : '（选填）'; ?>" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="checkbox" name="agree" value="1" title="我已阅读并同意" lay-skin="primary" required lay-verify="required">
                        <a href="/page/terms" target="_blank" style="color: #1E9FFF;">《服务条款》</a>和
                        <a href="/page/privacy" target="_blank" style="color: #1E9FFF;">《隐私政策》</a>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="registerForm">注 册</button>
                    </div>
                </div>
                
                <div class="register-links">
                    <span>已有账号？</span>
                    <a href="/user/login">立即登录</a>
                </div>
            </form>
        </div>
    </div>
    
    <script src="/Easyweb/assets/libs/layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function() {
        var form = layui.form;
        var layer = layui.layer;
        
        // 自定义验证规则
        form.verify({
            password: [
                /^[\S]{6,16}$/,
                '密码必须6到16位，且不能出现空格'
            ],
            confirmPassword: function(value) {
                var password = $('input[name=password]').val();
                if (value !== password) {
                    return '两次密码输入不一致';
                }
            }
        });
        
        // 表单提交
        form.on('submit(registerForm)', function(data) {
            // 表单数据会自动提交，这里可以添加额外的验证逻辑
            return true;
        });
    });
    </script>
</body>
</html>