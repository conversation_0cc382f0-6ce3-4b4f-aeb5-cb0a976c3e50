<?php
// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /user/login');
    exit;
}

// 获取API ID
$api_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($api_id <= 0) {
    header('Location: /api/market');
    exit;
}

// 获取API详情
$stmt = $pdo->prepare("
    SELECT a.*, c.name as category_name 
    FROM api_apis a 
    LEFT JOIN api_categories c ON a.category_id = c.id 
    WHERE a.id = ? AND a.status = 1
");
$stmt->execute([$api_id]);
$api = $stmt->fetch();

if (!$api) {
    header('Location: /api/market');
    exit;
}

// 检查用户是否已购买（如果是付费API）
if ($api['is_free'] == 0) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM api_purchases 
        WHERE user_id = ? AND api_id = ? AND status = 1
    ");
    $stmt->execute([$_SESSION['user_id'], $api_id]);
    $hasPurchased = ($stmt->fetchColumn() > 0);
    
    if (!$hasPurchased) {
        header('Location: /api/detail?id=' . $api_id);
        exit;
    }
}

// 获取API参数
$stmt = $pdo->prepare("SELECT * FROM api_parameters WHERE api_id = ? ORDER BY sort_order ASC");
$stmt->execute([$api_id]);
$parameters = $stmt->fetchAll();

// 获取用户API密钥
$stmt = $pdo->prepare("SELECT api_key FROM api_users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$api_key = $stmt->fetchColumn();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $api['name']; ?> - API在线调试 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="/public/css/font-awesome.min.css">
    <link rel="stylesheet" href="/public/css/prism.css">
    <link rel="stylesheet" href="/public/css/style.css">
    <style>
        .debug-container {
            display: flex;
            height: calc(100vh - 250px);
            min-height: 500px;
        }
        .param-panel {
            width: 40%;
            padding-right: 20px;
            overflow-y: auto;
        }
        .result-panel {
            width: 60%;
            border-left: 1px solid #dee2e6;
            padding-left: 20px;
            overflow-y: auto;
        }
        .response-container {
            background-color: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            overflow-x: auto;
            min-height: 300px;
        }
        .response-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
        }
        .param-table .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <?php include '../templates/common/header.php'; ?>
    
    <div class="container py-5">
        <div class="card shadow-sm mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><?php echo $api['name']; ?> - API在线调试</h4>
                <a href="/api/detail?id=<?php echo $api_id; ?>" class="btn btn-outline-primary btn-sm">
                    <i class="fa fa-arrow-left mr-1"></i> 返回API详情
                </a>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>接口地址：</strong></p>
                            <div class="input-group">
                                <input type="text" class="form-control" value="<?php echo $api['url']; ?>" readonly id="apiUrl">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('apiUrl')">复制</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <p class="mb-1"><strong>请求方式：</strong></p>
                            <input type="text" class="form-control" value="<?php echo $api['method']; ?>" readonly>
                        </div>
                        <div class="col-md-3">
                            <p class="mb-1"><strong>您的API密钥：</strong></p>
                            <div class="input-group">
                                <input type="text" class="form-control" value="<?php echo $api_key; ?>" readonly id="apiKey">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('apiKey')">复制</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="debug-container">
                    <!-- 参数面板 -->
                    <div class="param-panel">
                        <h5 class="mb-3">请求参数</h5>
                        <form id="apiForm">
                            <input type="hidden" name="api_key" value="<?php echo $api_key; ?>">
                            
                            <?php if (!empty($parameters)): ?>
                            <table class="table table-bordered param-table">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>值</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($parameters as $param): ?>
                                    <tr>
                                        <td>
                                            <?php echo $param['name']; ?>
                                            <div class="small text-muted"><?php echo $param['description']; ?></div>
                                        </td>
                                        <td><?php echo $param['type']; ?></td>
                                        <td>
                                            <?php if ($param['required'] == 1): ?>
                                            <span class="required">是</span>
                                            <?php else: ?>
                                            否
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($param['type'] === 'string' || $param['type'] === 'number'): ?>
                                            <input type="text" class="form-control form-control-sm" name="<?php echo $param['name']; ?>" value="<?php echo $param['default_value']; ?>" <?php echo $param['required'] == 1 ? 'required' : ''; ?>>
                                            <?php elseif ($param['type'] === 'boolean'): ?>
                                            <select class="form-control form-control-sm" name="<?php echo $param['name']; ?>" <?php echo $param['required'] == 1 ? 'required' : ''; ?>>
                                                <option value="">请选择</option>
                                                <option value="true" <?php echo $param['default_value'] === 'true' ? 'selected' : ''; ?>>true</option>
                                                <option value="false" <?php echo $param['default_value'] === 'false' ? 'selected' : ''; ?>>false</option>
                                            </select>
                                            <?php elseif ($param['type'] === 'array' || $param['type'] === 'object'): ?>
                                            <textarea class="form-control form-control-sm" name="<?php echo $param['name']; ?>" rows="3" <?php echo $param['required'] == 1 ? 'required' : ''; ?>><?php echo $param['default_value']; ?></textarea>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle mr-2"></i> 该API没有需要设置的参数
                            </div>
                            <?php endif; ?>
                            
                            <div class="form-group">
                                <label>请求头</label>
                                <textarea class="form-control" id="requestHeaders" rows="3">{
  "Content-Type": "application/json"
}</textarea>
                                <small class="form-text text-muted">请求头使用JSON格式</small>
                            </div>
                            
                            <button type="button" class="btn btn-primary" id="sendRequest">
                                <i class="fa fa-paper-plane mr-1"></i> 发送请求
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fa fa-refresh mr-1"></i> 重置
                            </button>
                        </form>
                    </div>
                    
                    <!-- 结果面板 -->
                    <div class="result-panel">
                        <h5 class="mb-3">响应结果</h5>
                        
                        <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="response-tab" data-toggle="tab" href="#response" role="tab">响应内容</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="headers-tab" data-toggle="tab" href="#headers" role="tab">响应头</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="request-tab" data-toggle="tab" href="#request" role="tab">请求详情</a>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-3" id="resultTabsContent">
                            <div class="tab-pane fade show active" id="response" role="tabpanel">
                                <div class="response-header">
                                    <div>
                                        <span>状态码: </span>
                                        <span id="responseStatus" class="font-weight-bold">-</span>
                                    </div>
                                    <div>
                                        <span>耗时: </span>
                                        <span id="responseTime">-</span>
                                    </div>
                                </div>
                                <div class="response-container" id="responseBody">
                                    <div class="text-center text-muted py-5">
                                        <i class="fa fa-code fa-3x mb-3"></i>
                                        <p>请点击"发送请求"按钮开始调试</p>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="headers" role="tabpanel">
                                <div class="response-container" id="responseHeaders">
                                    <div class="text-center text-muted py-5">
                                        <i class="fa fa-list fa-3x mb-3"></i>
                                        <p>请点击"发送请求"按钮开始调试</p>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="request" role="tabpanel">
                                <div class="response-container" id="requestDetails">
                                    <div class="text-center text-muted py-5">
                                        <i class="fa fa-paper-plane fa-3x mb-3"></i>
                                        <p>请点击"发送请求"按钮开始调试</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <?php include '../templates/common/footer.php'; ?>
    
    <script src="/public/js/jquery-3.5.1.min.js"></script>
    <script src="/public/js/bootstrap.bundle.min.js"></script>
    <script src="/public/js/prism.js"></script>
    <script src="/public/js/main.js"></script>
    <script>
    function copyToClipboard(elementId) {
        var element = document.getElementById(elementId);
        element.select();
        document.execCommand('copy');
        alert('已复制到剪贴板');
    }
    
    $(document).ready(function() {
        $('#sendRequest').click(function() {
            // 显示加载状态
            $('#responseBody').html('<div class="text-center py-5"><i class="fa fa-spinner fa-spin fa-3x"></i><p class="mt-3">请求处理中...</p></div>');
            $('#responseHeaders').html('');
            $('#requestDetails').html('');
            $('#responseStatus').text('-');
            $('#responseTime').text('-');
            
            // 获取表单数据
            var formData = {};
            var formElements = $('#apiForm').serializeArray();
            $.each(formElements, function(i, field) {
                if (field.value) {
                    // 尝试解析JSON
                    if (field.value.startsWith('[') || field.value.startsWith('{')) {
                        try {
                            formData[field.name] = JSON.parse(field.value);
                        } catch (e) {
                            formData[field.name] = field.value;
                        }
                    } else {
                        formData[field.name] = field.value;
                    }
                }
            });
            
            // 解析请求头
            var headers = {};
            try {
                headers = JSON.parse($('#requestHeaders').val());
            } catch (e) {
                alert('请求头格式错误，请检查JSON格式');
                return;
            }
            
            // 记录开始时间
            var startTime = new Date().getTime();
            
            // 构建请求URL
            var apiUrl = '<?php echo $api['url']; ?>';
            var method = '<?php echo $api['method']; ?>';
            
            // 如果是GET请求，将参数添加到URL
            var requestData = formData;
            if (method === 'GET') {
                var queryParams = [];
                for (var key in formData) {
                    if (formData.hasOwnProperty(key)) {
                        queryParams.push(encodeURIComponent(key) + '=' + encodeURIComponent(
                            typeof formData[key] === 'object' ? JSON.stringify(formData[key]) : formData[key]
                        ));
                    }
                }
                if (queryParams.length > 0) {
                    apiUrl += (apiUrl.indexOf('?') > -1 ? '&' : '?') + queryParams.join('&');
                }
                requestData = null;
            }
            
            // 记录请求详情
            var requestInfo = 'URL: ' + apiUrl + '\n';
            requestInfo += 'Method: ' + method + '\n';
            requestInfo += 'Headers: ' + JSON.stringify(headers, null, 2) + '\n';
            if (method !== 'GET' && requestData) {
                requestInfo += 'Body: ' + JSON.stringify(requestData, null, 2);
            }
            $('#requestDetails').text(requestInfo);
            
            // 发送请求
            $.ajax({
                url: apiUrl,
                type: method,
                data: method === 'GET' ? null : JSON.stringify(requestData),
                headers: headers,
                contentType: 'application/json',
                dataType: 'json',
                success: function(data, textStatus, xhr) {
                    // 计算请求耗时
                    var endTime = new Date().getTime();
                    var duration = endTime - startTime;
                    
                    // 显示响应状态
                    $('#responseStatus').text(xhr.status + ' ' + xhr.statusText).css('color', '#28a745');
                    $('#responseTime').text(duration + 'ms');
                    
                    // 显示响应内容
                    $('#responseBody').text(JSON.stringify(data, null, 2));
                    Prism.highlightElement($('#responseBody')[0]);
                    
                    // 显示响应头
                    var headersText = '';
                    var headersList = xhr.getAllResponseHeaders().split('\r\n');
                    for (var i = 0; i < headersList.length; i++) {
                        if (headersList[i]) {
                            headersText += headersList[i] + '\n';
                        }
                    }
                    $('#responseHeaders').text(headersText);
                    
                    // 记录API调用
                    $.post('/api/log', {
                        api_id: <?php echo $api_id; ?>,
                        status: xhr.status,
                        response_time: duration
                    });
                },
                error: function(xhr, textStatus, errorThrown) {
                    // 计算请求耗时
                    var endTime = new Date().getTime();
                    var duration = endTime - startTime;
                    
                    // 显示响应状态
                    $('#responseStatus').text(xhr.status + ' ' + xhr.statusText).css('color', '#dc3545');
                    $('#responseTime').text(duration + 'ms');
                    
                    // 显示响应内容
                    try {
                        var errorData = JSON.parse(xhr.responseText);
                        $('#responseBody').text(JSON.stringify(errorData, null, 2));
                    } catch (e) {
                        $('#responseBody').text(xhr.responseText || errorThrown);
                    }
                    Prism.highlightElement($('#responseBody')[0]);
                    
                    // 显示响应头
                    var headersText = '';
                    var headersList = xhr.getAllResponseHeaders().split('\r\n');
                    for (var i = 0; i < headersList.length; i++) {
                        if (headersList[i]) {
                            headersText += headersList[i] + '\n';
                        }
                    }
                    $('#responseHeaders').text(headersText);
                    
                    // 记录API调用
                    $.post('/api/log', {
                        api_id: <?php echo $api_id; ?>,
                        status: xhr.status,
                        response_time: duration
                    });
                }
            });
        });
    });
    </script>
</body>
</html>