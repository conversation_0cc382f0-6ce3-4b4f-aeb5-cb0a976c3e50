<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>部门管理</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /* 左树 */
        #organizationTreeBar {
            padding: 10px 15px;
            border: 1px solid #e6e6e6;
            background-color: #f2f2f2;
        }

        #organizationTree {
            border: 1px solid #e6e6e6;
            border-top: none;
            padding: 10px 5px;
            overflow: auto;
            height: -webkit-calc(100vh - 125px);
            height: -moz-calc(100vh - 125px);
            height: calc(100vh - 125px);
        }

        .layui-tree-entry .layui-tree-txt {
            padding: 0 5px;
            border: 1px transparent solid;
            text-decoration: none !important;
        }

        .layui-tree-entry.ew-tree-click .layui-tree-txt {
            background-color: #fff3e0;
            border: 1px #FFE6B0 solid;
        }

        /* 右表搜索表单 */
        #organizationUserTbSearchForm .layui-form-label {
            box-sizing: border-box !important;
            width: 90px !important;
        }

        #organizationUserTbSearchForm .layui-input-block {
            margin-left: 90px !important;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid" style="padding-bottom: 0;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body" style="padding: 10px;">
                    <!-- 树工具栏 -->
                    <div class="layui-form toolbar" id="organizationTreeBar">
                        <button id="organizationAddBtn" class="layui-btn layui-btn-sm icon-btn">
                            <i class="layui-icon">&#xe654;</i>添加
                        </button>&nbsp;
                        <button id="organizationEditBtn" class="layui-btn layui-btn-sm layui-btn-warm icon-btn">
                            <i class="layui-icon">&#xe642;</i>修改
                        </button>&nbsp;
                        <button id="organizationDelBtn"
                                class="layui-btn layui-btn-sm layui-btn-danger icon-btn">
                            <i class="layui-icon">&#xe640;</i>删除
                        </button>
                    </div>
                    <!-- 左树 -->
                    <div id="organizationTree"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md9">
            <div class="layui-card">
                <div class="layui-card-body" style="padding: 10px;">
                    <!-- 数据表格 -->
                    <table id="organizationUserTable" lay-filter="organizationUserTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表单弹窗1 -->
<script type="text/html" id="organizationEditDialog">
    <form id="organizationEditForm" lay-filter="organizationEditForm" class="layui-form model-form"
          style="padding-right: 20px;">
        <input name="organizationId" type="hidden"/>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">上级机构:</label>
                    <div class="layui-input-block">
                        <div id="organizationEditParentSel" class="ew-xmselect-tree"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">机构名称:</label>
                    <div class="layui-input-block">
                        <input name="organizationName" placeholder="请输入机构名称" class="layui-input"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">机构全称:</label>
                    <div class="layui-input-block">
                        <input name="organizationFullName" placeholder="请输入机构全称" class="layui-input"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">机构类型:</label>
                    <div class="layui-input-block">
                        <select name="organizationType" lay-verType="tips" lay-verify="required" required>
                            <option value="">请选择机构类型</option>
                            <option value="1">公司</option>
                            <option value="2">子公司</option>
                            <option value="3">部门</option>
                            <option value="4">小组</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">排序号:</label>
                    <div class="layui-input-block">
                        <input name="sortNumber" placeholder="请输入排序号" class="layui-input" type="number"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备注:</label>
                    <div class="layui-input-block">
                        <textarea name="comments" placeholder="请输入备注" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="organizationEditSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>
<!-- 表格操作列 -->
<script type="text/html" id="organizationUserTbBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<!-- 表格工具列 -->
<script type="text/html" id="organizationUserTbToolBar">
    <button lay-event="add" class="layui-btn layui-btn-sm icon-btn">
        <i class="layui-icon">&#xe654;</i>添加
    </button>&nbsp;
    <button lay-event="del" class="layui-btn layui-btn-sm layui-btn-danger icon-btn">
        <i class="layui-icon">&#xe640;</i>删除
    </button>&nbsp;
    <div class="dropdown-menu">
        <button class="layui-btn layui-btn-sm layui-btn-warm icon-btn">
            <i class="layui-icon">&#xe615;</i>搜索
        </button>
        <div class="dropdown-menu-nav dropdown-bottom-right"
             style="width: 300px;padding: 15px 15px 0 0;">
            <div class="dropdown-anchor"></div>
            <div class="layui-form" id="organizationUserTbSearchForm">
                <div class="layui-form-item">
                    <label class="layui-form-label">账&emsp;号:</label>
                    <div class="layui-input-block">
                        <input name="username" class="layui-input" placeholder="输入账号"/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">用户名:</label>
                    <div class="layui-input-block">
                        <input name="nickName" class="layui-input" placeholder="输入用户名"/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">性&emsp;别:</label>
                    <div class="layui-input-block">
                        <select name="sex">
                            <option value="">选择性别</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item text-right">
                    <button class="layui-btn icon-btn" lay-filter="organizationUserTbSearch" lay-submit>
                        <i class="layui-icon">&#xe615;</i>搜索
                    </button>
                </div>
            </div>
        </div>
    </div>
</script>
<!-- 表单弹窗 -->
<script type="text/html" id="organizationUserEditDialog">
    <form id="organizationUserEditForm" lay-filter="organizationUserEditForm" class="layui-form model-form">
        <input name="userId" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">账号:</label>
            <div class="layui-input-block">
                <input name="username" placeholder="请输入账号" class="layui-input"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">用户名:</label>
            <div class="layui-input-block">
                <input name="nickName" placeholder="请输入用户名" class="layui-input"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">性别:</label>
            <div class="layui-input-block">
                <input type="radio" name="sex" value="男" title="男" checked/>
                <input type="radio" name="sex" value="女" title="女"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">角色:</label>
            <div class="layui-input-block">
                <select name="roleId" lay-verType="tips" lay-verify="required">
                    <option value="">请选择角色</option>
                    <option value="1">管理员</option>
                    <option value="2">普通用户</option>
                    <option value="3">游客</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="organizationUserEditSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'admin', 'tree', 'dropdown', 'xmSelect', 'treeTable'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;
        var tree = layui.tree;
        var xmSelect = layui.xmSelect;
        var selObj, treeData;  // 左树选中数据

        /* 渲染树形 */
        function renderTree() {
            $.get('../../json/organization.json', function (res) {
                for (var i = 0; i < res.data.length; i++) {
                    res.data[i].title = res.data[i].organizationName;
                    res.data[i].id = res.data[i].organizationId;
                    res.data[i].spread = true;
                }
                treeData = layui.treeTable.pidToChildren(res.data, 'organizationId', 'parentId');
                tree.render({
                    elem: '#organizationTree',
                    onlyIconControl: true,
                    data: treeData,
                    click: function (obj) {
                        selObj = obj;
                        $('#organizationTree').find('.ew-tree-click').removeClass('ew-tree-click');
                        $(obj.elem).children('.layui-tree-entry').addClass('ew-tree-click');
                        insTb.reload({
                            where: {organizationId: obj.data.organizationId},
                            page: {curr: 1},
                            url: '../../json/user.json'
                        });
                    }
                });
                $('#organizationTree').find('.layui-tree-entry:first>.layui-tree-main>.layui-tree-txt').trigger('click');
            });
        }

        renderTree();

        /* 添加 */
        $('#organizationAddBtn').click(function () {
            showEditModel(null, selObj ? selObj.data.parentId : null);
        });

        /* 修改 */
        $('#organizationEditBtn').click(function () {
            if (!selObj) return layer.msg('未选择机构', {icon: 2});
            showEditModel(selObj.data);
        });

        /* 删除 */
        $('#organizationDelBtn').click(function () {
            if (!selObj) return layer.msg('未选择机构', {icon: 2});
            doDel(selObj);
        });

        /* 显示表单弹窗 */
        function showEditModel(mData, pid) {
            admin.open({
                type: 1,
                area: '600px',
                title: (mData ? '修改' : '添加') + '机构',
                content: $('#organizationEditDialog').html(),
                success: function (layero, dIndex) {
                    // 回显表单数据
                    form.val('organizationEditForm', mData);
                    // 表单提交事件
                    form.on('submit(organizationEditSubmit)', function (data) {
                        data.field.parentId = insXmSel.getValue('valueStr');
                        var loadIndex = layer.load(2);
                        $.get(mData ? '../../json/ok.json' : '../../json/ok.json', data.field, function (res) {
                            layer.close(loadIndex);
                            if (200 === res.code) {
                                layer.close(dIndex);
                                layer.msg(res.msg, {icon: 1});
                                renderTree();
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');
                        return false;
                    });
                    // 渲染下拉树
                    var insXmSel = xmSelect.render({
                        el: '#organizationEditParentSel',
                        height: '250px',
                        data: treeData,
                        initValue: mData ? [mData.parentId] : (pid ? [pid] : []),
                        model: {label: {type: 'text'}},
                        prop: {
                            name: 'organizationName',
                            value: 'organizationId'
                        },
                        radio: true,
                        clickClose: true,
                        tree: {
                            show: true,
                            indent: 15,
                            strict: false,
                            expandedKeys: true
                        }
                    });
                    // 禁止弹窗出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                }
            });
        }

        /* 删除 */
        function doDel(obj) {
            layer.confirm('确定要删除此机构吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                var loadIndex = layer.load(2);
                $.get('../../json/ok.json', {
                    id: obj.data.organizationId,
                }, function (res) {
                    layer.close(loadIndex);
                    if (200 === res.code) {
                        layer.msg(res.msg, {icon: 1});
                        renderTree();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }

        /* 渲染表格 */
        var insTb = table.render({
            elem: '#organizationUserTable',
            data: [],
            height: 'full-52',
            page: true,
            toolbar: '#organizationUserTbToolBar',
            cellMinWidth: 100,
            cols: [[
                {type: 'checkbox'},
                {type: 'numbers'},
                {field: 'username', title: '账号', sort: true},
                {field: 'nickName', title: '用户名', sort: true},
                {field: 'sex', title: '性别', sort: true},
                {field: 'phone', title: '手机号', sort: true},
                {field: 'roleName', title: '角色', sort: true},
                {
                    field: 'createTime', title: '创建时间', templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, sort: true
                },
                {title: '操作', toolbar: '#organizationUserTbBar', align: 'center', width: 120, minWidth: 120}
            ]],
            done: function () {
                // 表格搜索
                form.on('submit(organizationUserTbSearch)', function (data) {
                    insTb.reload({where: data.field, page: {curr: 1}});
                    return false;
                });
            }
        });

        /* 表格工具条点击事件 */
        table.on('tool(organizationUserTable)', function (obj) {
            if (obj.event === 'edit') { // 修改
                showEditModel2(obj.data);
            } else if (obj.event === 'del') { // 删除
                doDel2(obj);
            }
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(organizationUserTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showEditModel2();
            } else if (obj.event === 'del') { // 删除
                var checkRows = table.checkStatus('organizationUserTable');
                if (checkRows.data.length === 0) {
                    layer.msg('请选择要删除的数据', {icon: 2});
                    return;
                }
                var ids = checkRows.data.map(function (d) {
                    return d.userId;
                });
                doDel2({ids: ids});
            }
        });

        /* 显示表单弹窗2 */
        function showEditModel2(mData) {
            admin.open({
                type: 1,
                title: (mData ? '修改' : '添加') + '用户',
                content: $('#organizationUserEditDialog').html(),
                success: function (layero, dIndex) {
                    // 回显表单数据
                    form.val('organizationUserEditForm', mData);
                    // 表单提交事件
                    form.on('submit(organizationUserEditSubmit)', function (data) {
                        data.field.organizationId = selObj ? selObj.data.organizationId : undefined;
                        var loadIndex = layer.load(2);
                        $.get(mData ? '../../json/ok.json' : '../../json/ok.json', data.field, function (res) {
                            layer.close(loadIndex);
                            if (200 === res.code) {
                                layer.close(dIndex);
                                layer.msg(res.msg, {icon: 1});
                                insTb.reload({page: {curr: 1}});
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');
                        return false;
                    });
                    // 禁止弹窗出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                }
            });
        }

        /* 删除2 */
        function doDel2(obj) {
            layer.confirm('确定要删除选中用户吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                var loadIndex = layer.load(2);
                $.get('../../json/ok.json', {
                    id: obj.data ? obj.data.userId : '',
                    ids: obj.ids ? obj.ids.join(',') : ''
                }, function (res) {
                    layer.close(loadIndex);
                    if (200 === res.code) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload({page: {curr: 1}});
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }

    });
</script>
</body>
</html>