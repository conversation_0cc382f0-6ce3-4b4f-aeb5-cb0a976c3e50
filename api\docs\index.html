<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - API商业系统</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <style>
        body {
            padding: 15px;
            background-color: #f2f2f2;
        }
        .api-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .api-header {
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        .api-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .api-description {
            color: #666;
            margin-bottom: 20px;
        }
        .api-category {
            margin-bottom: 30px;
        }
        .api-category-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #1E9FFF;
        }
        .api-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .api-item:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            cursor: pointer;
        }
        .api-item-name {
            font-weight: bold;
            font-size: 16px;
        }
        .api-method {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: #fff;
        }
        .api-method-get {
            background-color: #5FB878;
        }
        .api-method-post {
            background-color: #1E9FFF;
        }
        .api-method-put {
            background-color: #FFB800;
        }
        .api-method-delete {
            background-color: #FF5722;
        }
        .api-item-content {
            display: none;
            padding-top: 10px;
            border-top: 1px dashed #eee;
        }
        .api-item-desc {
            margin-bottom: 15px;
            color: #666;
        }
        .api-param-title, .api-response-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .api-example {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .api-test-btn {
            margin-top: 10px;
        }
        .api-test-panel {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="api-container">
        <div class="api-header">
            <div class="api-title">API接口文档</div>
            <div class="api-description">
                欢迎使用API商业系统接口文档。本文档提供了API的详细说明和调用示例，您可以根据文档进行接口调用和测试。
            </div>
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">API密钥</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <input type="text" id="api-key" placeholder="请输入您的API密钥" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="save-key">保存密钥</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="api-content" id="api-content">
            <!-- API内容将通过AJAX加载 -->
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-progress layui-progress-big" lay-showpercent="true" lay-filter="loading">
                        <div class="layui-progress-bar" lay-percent="0%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">正在加载API文档...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
    layui.use(['jquery', 'element', 'layer', 'form'], function(){
        var $ = layui.jquery;
        var element = layui.element;
        var layer = layui.layer;
        var form = layui.form;
        
        // 加载API文档
        loadApiDocs();
        
        // 保存API密钥
        $('#save-key').click(function(){
            var apiKey = $('#api-key').val();
            if (apiKey) {
                localStorage.setItem('api_key', apiKey);
                layer.msg('API密钥已保存');
            } else {
                layer.msg('请输入API密钥');
            }
        });
        
        // 从本地存储加载API密钥
        var savedKey = localStorage.getItem('api_key');
        if (savedKey) {
            $('#api-key').val(savedKey);
        }
        
        // 加载API文档
        function loadApiDocs() {
            $.ajax({
                url: '../api.php?action=getApiDocs',
                type: 'GET',
                dataType: 'json',
                success: function(res) {
                    if (res.code === 200) {
                        renderApiDocs(res.data);
                    } else {
                        $('#api-content').html('<div class="layui-card"><div class="layui-card-body">加载失败：' + (res.msg || '未知错误') + '</div></div>');
                    }
                },
                error: function() {
                    $('#api-content').html('<div class="layui-card"><div class="layui-card-body">服务器错误，请稍后再试</div></div>');
                }
            });
        }
        
        // 渲染API文档
        function renderApiDocs(data) {
            var html = '';
            
            if (data.categories && data.categories.length > 0) {
                data.categories.forEach(function(category) {
                    html += '<div class="api-category">';
                    html += '<div class="api-category-title">' + category.name + '</div>';
                    
                    if (category.apis && category.apis.length > 0) {
                        category.apis.forEach(function(api) {
                            html += renderApiItem(api);
                        });
                    } else {
                        html += '<div class="layui-card"><div class="layui-card-body">该分类下暂无API</div></div>';
                    }
                    
                    html += '</div>';
                });
            } else {
                html = '<div class="layui-card"><div class="layui-card-body">暂无API数据</div></div>';
            }
            
            $('#api-content').html(html);
            
            // 绑定API项点击事件
            $('.api-item-header').click(function() {
                $(this).next('.api-item-content').slideToggle();
            });
            
            // 绑定测试按钮点击事件
            $('.api-test-btn').click(function(e) {
                e.stopPropagation();
                var testPanel = $(this).closest('.api-item-content').find('.api-test-panel');
                testPanel.slideToggle();
            });
            
            // 绑定发送测试请求按钮
            $('.send-test-btn').click(function() {
                var apiId = $(this).data('id');
                var method = $(this).data('method');
                var url = $(this).data('url');
                var params = {};
                
                // 获取参数
                $('#test-form-' + apiId + ' .param-input').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if (value) {
                        params[name] = value;
                    }
                });
                
                // 获取API密钥
                var apiKey = $('#api-key').val();
                if (!apiKey) {
                    layer.msg('请先设置API密钥');
                    return;
                }
                
                // 发送测试请求
                var loadIndex = layer.load(2);
                $.ajax({
                    url: url,
                    type: method,
                    data: params,
                    headers: {
                        'X-API-KEY': apiKey
                    },
                    dataType: 'json',
                    success: function(res) {
                        layer.close(loadIndex);
                        $('#response-' + apiId).text(JSON.stringify(res, null, 2));
                    },
                    error: function(xhr) {
                        layer.close(loadIndex);
                        $('#response-' + apiId).text('请求错误：' + xhr.status + ' ' + xhr.statusText);
                    }
                });
            });
        }
        
        // 渲染单个API项
        function renderApiItem(api) {
            var methodClass = 'api-method-' + api.method.toLowerCase();
            
            var html = '<div class="api-item">';
            html += '<div class="api-item-header">';
            html += '<div class="api-item-name">' + api.name + '</div>';
            html += '<div class="api-method ' + methodClass + '">' + api.method.toUpperCase() + '</div>';
            html += '</div>';
            
            html += '<div class="api-item-content">';
            html += '<div class="api-item-desc">' + api.description + '</div>';
            
            html += '<div class="api-url"><strong>接口地址：</strong>' + api.url + '</div>';
            
            // 参数部分
            html += '<div class="api-params">';
            html += '<div class="api-param-title">请求参数</div>';
            html += '<table class="layui-table">';
            html += '<thead><tr><th>参数名</th><th>类型</th><th>是否必须</th><th>描述</th></tr></thead>';
            html += '<tbody>';
            
            if (api.params && api.params.length > 0) {
                api.params.forEach(function(param) {
                    html += '<tr>';
                    html += '<td>' + param.name + '</td>';
                    html += '<td>' + param.type + '</td>';
                    html += '<td>' + (param.required ? '是' : '否') + '</td>';
                    html += '<td>' + param.description + '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="4">无参数</td></tr>';
            }
            
            html += '</tbody></table>';
            html += '</div>';
            
            // 响应部分
            html += '<div class="api-response">';
            html += '<div class="api-response-title">响应说明</div>';
            html += '<table class="layui-table">';
            html += '<thead><tr><th>字段</th><th>类型</th><th>描述</th></tr></thead>';
            html += '<tbody>';
            
            if (api.response && api.response.length > 0) {
                api.response.forEach(function(field) {
                    html += '<tr>';
                    html += '<td>' + field.name + '</td>';
                    html += '<td>' + field.type + '</td>';
                    html += '<td>' + field.description + '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3">无响应字段说明</td></tr>';
            }
            
            html += '</tbody></table>';
            html += '</div>';
            
            // 示例部分
            if (api.example) {
                html += '<div class="api-example-section">';
                html += '<div class="api-param-title">请求示例</div>';
                html += '<div class="api-example">' + api.example.request + '</div>';
                html += '<div class="api-param-title">响应示例</div>';
                html += '<div class="api-example">' + api.example.response + '</div>';
                html += '</div>';
            }
            
            // 测试按钮
            html += '<button class="layui-btn layui-btn-sm api-test-btn">在线测试</button>';
            
            // 测试面板
            html += '<div class="api-test-panel">';
            html += '<form class="layui-form" id="test-form-' + api.id + '">';
            
            if (api.params && api.params.length > 0) {
                api.params.forEach(function(param) {
                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">' + param.name + '</label>';
                    html += '<div class="layui-input-block">';
                    html += '<input type="text" name="' + param.name + '" placeholder="' + param.description + '" class="layui-input param-input">';
                    html += '</div></div>';
                });
            } else {
                html += '<div class="layui-form-item"><div class="layui-form-mid">无需参数</div></div>';
            }
            
            html += '<div class="layui-form-item">';
            html += '<div class="layui-input-block">';
            html += '<button type="button" class="layui-btn send-test-btn" data-id="' + api.id + '" data-method="' + api.method + '" data-url="' + api.url + '">发送请求</button>';
            html += '</div></div>';
            html += '</form>';
            
            html += '<div class="api-param-title">响应结果</div>';
            html += '<pre class="api-example" id="response-' + api.id + '">请点击"发送请求"按钮测试接口</pre>';
            html += '</div>';
            
            html += '</div>'; // end of api-item-content
            html += '</div>'; // end of api-item
            
            return html;
        }
    });
    </script>
</body>
</html>