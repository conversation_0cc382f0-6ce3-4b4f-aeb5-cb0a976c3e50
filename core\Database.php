<?php
/**
 * 数据库操作类
 */
class Database
{
    private static $instance = null;
    private $pdo;
    private $statement;
    
    /**
     * 构造函数，初始化PDO连接
     */
    private function __construct()
    {
        $config = require_once __DIR__ . '/../config/database.php';
        
        try {
            $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
            $this->pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取数据库实例（单例模式）
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 准备SQL语句
     */
    public function query($sql)
    {
        $this->statement = $this->pdo->prepare($sql);
        return $this;
    }
    
    /**
     * 绑定参数
     */
    public function bind($param, $value, $type = null)
    {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        
        $this->statement->bindValue($param, $value, $type);
        return $this;
    }
    
    /**
     * 执行SQL语句
     */
    public function execute()
    {
        return $this->statement->execute();
    }
    
    /**
     * 获取所有结果
     */
    public function fetchAll()
    {
        $this->execute();
        return $this->statement->fetchAll();
    }
    
    /**
     * 获取单条结果
     */
    public function fetch()
    {
        $this->execute();
        return $this->statement->fetch();
    }
    
    /**
     * 获取结果数量
     */
    public function rowCount()
    {
        return $this->statement->rowCount();
    }
    
    /**
     * 获取最后插入的ID
     */
    public function lastInsertId()
    {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction()
    {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit()
    {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollBack()
    {
        return $this->pdo->rollBack();
    }
    
    /**
     * 执行简单查询
     */
    public function select($table, $columns = '*', $where = '', $params = [], $orderBy = '', $limit = '')
    {
        $sql = "SELECT {$columns} FROM {$table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        if (!empty($orderBy)) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if (!empty($limit)) {
            $sql .= " LIMIT {$limit}";
        }
        
        $this->query($sql);
        
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $this->bind($key, $value);
            }
        }
        
        return $this->fetchAll();
    }
    
    /**
     * 执行简单插入
     */
    public function insert($table, $data)
    {
        $keys = array_keys($data);
        $fields = '`' . implode('`, `', $keys) . '`';
        $placeholders = ':' . implode(', :', $keys);
        
        $sql = "INSERT INTO {$table} ({$fields}) VALUES ({$placeholders})";
        
        $this->query($sql);
        
        foreach ($data as $key => $value) {
            $this->bind(':' . $key, $value);
        }
        
        $this->execute();
        return $this->lastInsertId();
    }
    
    /**
     * 执行简单更新
     */
    public function update($table, $data, $where, $params = [])
    {
        $sets = [];
        foreach ($data as $key => $value) {
            $sets[] = "`{$key}` = :{$key}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $sets) . " WHERE {$where}";
        
        $this->query($sql);
        
        foreach ($data as $key => $value) {
            $this->bind(':' . $key, $value);
        }
        
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $this->bind($key, $value);
            }
        }
        
        return $this->execute();
    }
    
    /**
     * 执行简单删除
     */
    public function delete($table, $where, $params = [])
    {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        $this->query($sql);
        
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $this->bind($key, $value);
            }
        }
        
        return $this->execute();
    }
    
    /**
     * 获取单个值
     */
    public function getValue($sql, $params = [])
    {
        $this->query($sql);
        
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $this->bind($key, $value);
            }
        }
        
        $this->execute();
        return $this->statement->fetchColumn();
    }
    
    /**
     * 执行自定义SQL
     */
    public function raw($sql, $params = [])
    {
        $this->query($sql);
        
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $this->bind($key, $value);
            }
        }
        
        return $this->execute();
    }
}