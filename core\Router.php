<?php
/**
 * 路由类
 */
class Router
{
    private $routes = [];
    private $notFoundCallback;
    private $baseRoute = '';
    private $requestMethod = '';
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->requestMethod = $_SERVER['REQUEST_METHOD'];
    }
    
    /**
     * 添加路由
     * 
     * @param string $methods 请求方法（GET, POST等，多个用|分隔）
     * @param string $pattern 路由模式
     * @param callable $callback 回调函数
     */
    public function addRoute($methods, $pattern, $callback)
    {
        $pattern = $this->baseRoute . '/' . trim($pattern, '/');
        $pattern = $this->baseRoute ? rtrim($pattern, '/') : $pattern;
        
        foreach (explode('|', $methods) as $method) {
            $this->routes[$method][] = [
                'pattern' => $pattern,
                'callback' => $callback
            ];
        }
    }
    
    /**
     * 添加GET路由
     */
    public function get($pattern, $callback)
    {
        $this->addRoute('GET', $pattern, $callback);
    }
    
    /**
     * 添加POST路由
     */
    public function post($pattern, $callback)
    {
        $this->addRoute('POST', $pattern, $callback);
    }
    
    /**
     * 添加PUT路由
     */
    public function put($pattern, $callback)
    {
        $this->addRoute('PUT', $pattern, $callback);
    }
    
    /**
     * 添加DELETE路由
     */
    public function delete($pattern, $callback)
    {
        $this->addRoute('DELETE', $pattern, $callback);
    }
    
    /**
     * 添加任意请求方法的路由
     */
    public function any($pattern, $callback)
    {
        $this->addRoute('GET|POST|PUT|DELETE|OPTIONS|PATCH|HEAD', $pattern, $callback);
    }
    
    /**
     * 设置路由组
     * 
     * @param string $baseRoute 基础路由
     * @param callable $callback 回调函数
     */
    public function group($baseRoute, $callback)
    {
        $curBaseRoute = $this->baseRoute;
        $this->baseRoute .= '/' . trim($baseRoute, '/');
        call_user_func($callback);
        $this->baseRoute = $curBaseRoute;
    }
    
    /**
     * 设置404回调
     * 
     * @param callable $callback 回调函数
     */
    public function setNotFound($callback)
    {
        $this->notFoundCallback = $callback;
    }
    
    /**
     * 运行路由
     */
    public function run()
    {
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $method = $this->requestMethod;
        
        $routeFound = false;
        
        if (isset($this->routes[$method])) {
            foreach ($this->routes[$method] as $route) {
                // 将路由模式转换为正则表达式
                $pattern = preg_replace('/\/{([\w-]+)}/', '/([^/]+)', $route['pattern']);
                $pattern = '#^' . $pattern . '$#';
                
                if (preg_match($pattern, $uri, $matches)) {
                    // 提取参数
                    array_shift($matches); // 移除完整匹配
                    
                    // 提取命名参数
                    $params = [];
                    preg_match_all('/\/{([\w-]+)}/', $route['pattern'], $paramNames);
                    $paramNames = isset($paramNames[1]) ? $paramNames[1] : [];
                    
                    foreach ($paramNames as $index => $value) {
                        if (isset($matches[$index])) {
                            $params[$value] = $matches[$index];
                        }
                    }
                    
                    // 调用回调函数
                    call_user_func_array($route['callback'], [$params]);
                    $routeFound = true;
                    break;
                }
            }
        }
        
        // 如果没有找到匹配的路由，调用404回调
        if (!$routeFound && $this->notFoundCallback) {
            call_user_func($this->notFoundCallback);
        }
    }
    
    /**
     * 重定向
     * 
     * @param string $url 目标URL
     * @param int $code HTTP状态码
     */
    public static function redirect($url, $code = 302)
    {
        header("Location: {$url}", true, $code);
        exit;
    }
}