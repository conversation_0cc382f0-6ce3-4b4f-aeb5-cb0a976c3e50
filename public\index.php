<?php
/**
 * API管理系统入口文件
 * 系统主入口，处理所有HTTP请求
 */

/**
 * 检查系统是否已安装
 */
function checkSystemInstalled() {
    // 检查安装标记文件
    $installLockFile = __DIR__ . '/../config/install.lock';
    if (!file_exists($installLockFile)) {
        return false;
    }
    
    // 检查数据库配置文件
    $dbConfigFile = __DIR__ . '/../config/database.php';
    if (!file_exists($dbConfigFile)) {
        return false;
    }
    
    // 检查数据库连接
    try {
        require_once $dbConfigFile;
        $config = include $dbConfigFile;
        
        if (!isset($config['host']) || !isset($config['database']) || !isset($config['username'])) {
            return false;
        }
        
        // 尝试连接数据库
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        
        // 检查关键表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() == 0) {
            return false;
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 跳转到安装向导
 */
function redirectToInstaller() {
    // 检查安装向导文件是否存在
    $installerFile = __DIR__ . '/install.php';
    if (!file_exists($installerFile)) {
        // 如果安装向导不存在，创建一个简单的安装页面
        createInstaller();
    }
    
    header('Location: install.php');
    exit;
}

/**
 * 创建安装向导页面
 */
function createInstaller() {
    $installerContent = '<?php
/**
 * API管理系统安装向导
 */

// 处理安装请求
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $dbHost = $_POST["db_host"] ?? "localhost";
    $dbPort = $_POST["db_port"] ?? "3306";
    $dbName = $_POST["db_name"] ?? "api_system";
    $dbUser = $_POST["db_user"] ?? "root";
    $dbPass = $_POST["db_pass"] ?? "";
    $adminUser = $_POST["admin_user"] ?? "admin";
    $adminPass = $_POST["admin_pass"] ?? "admin123";
    
    try {
        // 测试数据库连接
        $dsn = "mysql:host=$dbHost;port=$dbPort;charset=utf8mb4";
        $pdo = new PDO($dsn, $dbUser, $dbPass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 创建数据库
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbName`");
        
        // 执行数据库结构文件
        $sqlFile = __DIR__ . "/../config/database.sql";
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            $pdo->exec($sql);
        }
        
        // 创建数据库配置文件
        $configContent = "<?php
return [
    \"host\" => \"$dbHost\",
    \"port\" => \"$dbPort\",
    \"database\" => \"$dbName\",
    \"username\" => \"$dbUser\",
    \"password\" => \"$dbPass\",
    \"charset\" => \"utf8mb4\",
    \"options\" => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];";
        
        file_put_contents(__DIR__ . "/../config/database.php", $configContent);
        
        // 创建默认管理员账号
        $hashedPassword = password_hash($adminPass, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admins (username, password, email, role, status, created_at) VALUES (?, ?, ?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE password = VALUES(password)");
        $stmt->execute([$adminUser, $hashedPassword, "<EMAIL>", "super_admin", "active"]);
        
        // 创建安装锁定文件
        file_put_contents(__DIR__ . "/../config/install.lock", date("Y-m-d H:i:s"));
        
        $success = true;
        $message = "系统安装成功！";
        
    } catch (Exception $e) {
        $success = false;
        $message = "安装失败：" . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API管理系统 - 安装向导</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .install-container { background: white; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.1); padding: 40px; width: 600px; max-width: 90vw; }
        .install-header { text-align: center; margin-bottom: 30px; }
        .install-header h1 { color: #333; margin-bottom: 10px; }
        .install-header p { color: #666; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 500; color: #333; }
        .form-input { width: 100%; padding: 12px 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; transition: border-color 0.3s; }
        .form-input:focus { outline: none; border-color: #667eea; }
        .form-row { display: flex; gap: 15px; }
        .form-row .form-group { flex: 1; }
        .install-btn { width: 100%; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: transform 0.3s; }
        .install-btn:hover { transform: translateY(-2px); }
        .message { padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .success-actions { text-align: center; margin-top: 20px; }
        .success-actions a { display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 0 10px; }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>API管理系统安装向导</h1>
            <p>欢迎使用API管理系统，请填写以下信息完成安装</p>
        </div>
        
        <?php if (isset($success)): ?>
            <?php if ($success): ?>
                <div class="message success"><?php echo $message; ?></div>
                <div class="success-actions">
                    <a href="index.php">访问首页</a>
                    <a href="admin/">管理后台</a>
                </div>
            <?php else: ?>
                <div class="message error"><?php echo $message; ?></div>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php if (!isset($success) || !$success): ?>
        <form method="POST">
            <h3 style="margin-bottom: 20px; color: #333;">数据库配置</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label>数据库主机</label>
                    <input type="text" name="db_host" class="form-input" value="localhost" required>
                </div>
                <div class="form-group">
                    <label>端口</label>
                    <input type="text" name="db_port" class="form-input" value="3306" required>
                </div>
            </div>
            
            <div class="form-group">
                <label>数据库名称</label>
                <input type="text" name="db_name" class="form-input" value="api_system" required>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>数据库用户名</label>
                    <input type="text" name="db_user" class="form-input" value="root" required>
                </div>
                <div class="form-group">
                    <label>数据库密码</label>
                    <input type="password" name="db_pass" class="form-input">
                </div>
            </div>
            
            <h3 style="margin: 30px 0 20px; color: #333;">管理员账号</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label>管理员用户名</label>
                    <input type="text" name="admin_user" class="form-input" value="admin" required>
                </div>
                <div class="form-group">
                    <label>管理员密码</label>
                    <input type="password" name="admin_pass" class="form-input" value="admin123" required>
                </div>
            </div>
            
            <button type="submit" class="install-btn">开始安装</button>
        </form>
        <?php endif; ?>
    </div>
</body>
</html>';
    
    file_put_contents(__DIR__ . '/install.php', $installerContent);
}

// 检查系统是否已安装
if (!checkSystemInstalled()) {
    redirectToInstaller();
}

// 系统已安装，继续正常流程
try {
    // 引入核心文件
    require_once __DIR__ . '/../core/Database.php';
    require_once __DIR__ . '/../core/System.php';

    // 初始化系统
    $system = System::getInstance();
} catch (Exception $e) {
    // 如果核心文件加载失败，也跳转到安装向导
    redirectToInstaller();
}

// 获取请求信息
$requestMethod = $_SERVER['REQUEST_METHOD'];
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$requestUri = str_replace('/api-system/public', '', $requestUri);

// 简单的路由处理
switch ($requestUri) {
    case '/':
    case '/index':
        // 显示首页
        include __DIR__ . '/index.html';
        exit;
        
    case '/api/test':
        // API测试接口
        System::jsonResponse(['message' => 'API系统运行正常', 'version' => System::getConfig('app.version')]);
        break;
        
    case '/api/status':
        // 系统状态检查
        try {
            $db = Database::getInstance();
            $status = [
                'database' => 'connected',
                'system' => 'running',
                'timestamp' => date('Y-m-d H:i:s')
            ];
            System::jsonResponse($status);
        } catch (Exception $e) {
            System::jsonResponse(['error' => $e->getMessage()], 500, 'error');
        }
        break;
        
    default:
        // 404处理
        http_response_code(404);
        echo json_encode(['code' => 404, 'message' => '接口不存在']);
        break;
}