<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API管理 - API管理系统</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
    <link rel="icon" href="../../Easyweb/assets/images/favicon.ico">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            line-height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 220px;
            background: #2f3349;
            overflow-y: auto;
            z-index: 999;
        }
        
        .admin-main {
            margin-left: 220px;
            margin-top: 60px;
            padding: 20px;
            min-height: calc(100vh - 60px);
            background: #f5f5f5;
        }
        
        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-menu li {
            border-bottom: 1px solid #3a3f5c;
        }
        
        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: #b8c5d6;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        
        .nav-menu i {
            margin-right: 10px;
            width: 16px;
        }
        
        .user-info {
            float: right;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            vertical-align: middle;
            margin-right: 10px;
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .status-active {
            background: #52c41a;
        }
        
        .status-inactive {
            background: #f5222d;
        }
        
        .method-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        
        .method-get { background: #52c41a; }
        .method-post { background: #1890ff; }
        .method-put { background: #fa8c16; }
        .method-delete { background: #f5222d; }
        
        .api-path {
            font-family: 'Courier New', monospace;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="admin-header">
        <div style="float: left;">
            <h2 style="margin: 0; font-size: 18px;">
                <i class="layui-icon layui-icon-api"></i>
                API管理
            </h2>
        </div>
        <div class="user-info">
            <img src="../../Easyweb/assets/images/head.jpg" alt="头像" class="user-avatar">
            <span id="adminName">管理员</span>
            <a href="javascript:;" onclick="logout()" style="color: white; margin-left: 15px;">
                <i class="layui-icon layui-icon-logout"></i> 退出
            </a>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="admin-sidebar">
        <ul class="nav-menu">
            <li>
                <a href="dashboard.html">
                    <i class="layui-icon layui-icon-home"></i>
                    仪表板
                </a>
            </li>
            <li>
                <a href="api-list.html" class="active">
                    <i class="layui-icon layui-icon-api"></i>
                    API管理
                </a>
            </li>
            <li>
                <a href="user-list.html">
                    <i class="layui-icon layui-icon-user"></i>
                    用户管理
                </a>
            </li>
            <li>
                <a href="merchant-list.html">
                    <i class="layui-icon layui-icon-shop"></i>
                    商家管理
                </a>
            </li>
            <li>
                <a href="order-list.html">
                    <i class="layui-icon layui-icon-dollar"></i>
                    订单管理
                </a>
            </li>
            <li>
                <a href="finance.html">
                    <i class="layui-icon layui-icon-rmb"></i>
                    财务管理
                </a>
            </li>
            <li>
                <a href="admin-list.html">
                    <i class="layui-icon layui-icon-username"></i>
                    管理员
                </a>
            </li>
            <li>
                <a href="role-list.html">
                    <i class="layui-icon layui-icon-group"></i>
                    角色权限
                </a>
            </li>
            <li>
                <a href="system-config.html">
                    <i class="layui-icon layui-icon-set"></i>
                    系统配置
                </a>
            </li>
            <li>
                <a href="logs.html">
                    <i class="layui-icon layui-icon-file"></i>
                    系统日志
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="admin-main">
        <!-- 搜索表单 -->
        <div class="search-form">
            <form class="layui-form" lay-filter="searchForm">
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-md3">
                        <input type="text" name="name" placeholder="API名称" class="layui-input">
                    </div>
                    <div class="layui-col-md2">
                        <select name="category_id">
                            <option value="">选择分类</option>
                            <option value="1">工具类</option>
                            <option value="2">数据类</option>
                            <option value="3">娱乐类</option>
                        </select>
                    </div>
                    <div class="layui-col-md2">
                        <select name="method">
                            <option value="">请求方法</option>
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                        </select>
                    </div>
                    <div class="layui-col-md2">
                        <select name="status">
                            <option value="">状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="layui-col-md3">
                        <button type="submit" class="layui-btn" lay-submit lay-filter="search">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="showAddForm()">
                            <i class="layui-icon layui-icon-add-1"></i> 新增API
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- API列表 -->
        <div class="content-card">
            <div class="layui-row" style="margin-bottom: 15px;">
                <div class="layui-col-md6">
                    <h3 style="margin: 0;">API接口列表</h3>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <button class="layui-btn layui-btn-sm" onclick="batchOperation('enable')">批量启用</button>
                    <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="batchOperation('disable')">批量禁用</button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="batchOperation('delete')">批量删除</button>
                </div>
            </div>
            
            <table class="layui-table" lay-filter="apiTable">
                <thead>
                    <tr>
                        <th lay-data="{type:'checkbox', fixed: 'left'}"></th>
                        <th lay-data="{field:'id', width:80, sort: true}">ID</th>
                        <th lay-data="{field:'name', width:200}">API名称</th>
                        <th lay-data="{field:'path', width:250}">接口路径</th>
                        <th lay-data="{field:'method', width:80, align:'center'}">方法</th>
                        <th lay-data="{field:'category_name', width:120}">分类</th>
                        <th lay-data="{field:'price', width:100, align:'right'}">价格</th>
                        <th lay-data="{field:'call_count', width:100, align:'right', sort: true}">调用次数</th>
                        <th lay-data="{field:'status', width:80, align:'center'}">状态</th>
                        <th lay-data="{field:'created_at', width:160, sort: true}">创建时间</th>
                        <th lay-data="{fixed: 'right', width:200, align:'center'}">操作</th>
                    </tr>
                </thead>
                <tbody id="apiTableBody">
                    <tr>
                        <td colspan="11" style="text-align: center; padding: 50px; color: #999;">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                            <br>加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div id="pagination"></div>
        </div>
    </div>

    <!-- 添加/编辑API弹窗 -->
    <div id="apiFormModal" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="apiForm">
            <input type="hidden" name="id" id="apiId">
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">API名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" required lay-verify="required" placeholder="请输入API名称" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">接口路径</label>
                        <div class="layui-input-block">
                            <input type="text" name="path" required lay-verify="required" placeholder="/api/example" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">请求方法</label>
                        <div class="layui-input-block">
                            <select name="method" lay-verify="required">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">分类</label>
                        <div class="layui-input-block">
                            <select name="category_id">
                                <option value="">选择分类</option>
                                <option value="1">工具类</option>
                                <option value="2">数据类</option>
                                <option value="3">娱乐类</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">价格</label>
                        <div class="layui-input-block">
                            <input type="number" name="price" step="0.01" min="0" value="0" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">接口描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入接口描述" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">超时时间</label>
                        <div class="layui-input-block">
                            <input type="number" name="timeout" min="1" max="300" value="30" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">重试次数</label>
                        <div class="layui-input-block">
                            <input type="number" name="retry_count" min="0" max="5" value="0" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="layui-form-item">
                        <label class="layui-form-label">频率限制</label>
                        <div class="layui-input-block">
                            <input type="number" name="rate_limit" min="1" value="60" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">响应示例</label>
                <div class="layui-input-block">
                    <textarea name="response_example" placeholder="请输入响应示例（JSON格式）" class="layui-textarea" style="height: 120px;"></textarea>
                </div>
            </div>
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="启用" checked>
                            <input type="radio" name="status" value="0" title="禁用">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">公开状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="is_public" value="1" title="公开" checked>
                            <input type="radio" name="is_public" value="0" title="私有">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
                <button type="submit" class="layui-btn" lay-submit lay-filter="submitApi">确定</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </form>
    </div>

    <!-- API调试弹窗 -->
    <div id="debugModal" style="display: none; padding: 20px;">
        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">请求参数</li>
                <li>请求头</li>
                <li>响应结果</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <form class="layui-form" lay-filter="debugForm">
                        <div id="debugParams"></div>
                        <div class="layui-form-item" style="margin-top: 20px;">
                            <button type="button" class="layui-btn" onclick="executeDebug()">
                                <i class="layui-icon layui-icon-play"></i> 执行调试
                            </button>
                        </div>
                    </form>
                </div>
                <div class="layui-tab-item">
                    <textarea id="debugHeaders" placeholder="请输入请求头（JSON格式）" class="layui-textarea" style="height: 200px;">{}</textarea>
                </div>
                <div class="layui-tab-item">
                    <div id="debugResult" style="min-height: 300px;">
                        <div style="text-align: center; padding: 50px; color: #999;">
                            点击"执行调试"按钮开始测试API
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'laypage', 'element'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var laypage = layui.laypage;
            var element = layui.element;
            
            var currentPage = 1;
            var pageSize = 20;
            var currentApiId = null;
            
            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                loadApiList();
            });
            
            // 加载API列表
            function loadApiList(page = 1) {
                currentPage = page;
                
                // 模拟数据
                var mockData = {
                    data: [
                        {
                            id: 1,
                            name: '天气查询API',
                            path: '/api/weather',
                            method: 'GET',
                            category_name: '工具类',
                            price: '0.01',
                            call_count: 1234,
                            status: 1,
                            created_at: '2024-01-01 10:00:00'
                        },
                        {
                            id: 2,
                            name: '身份证查询API',
                            path: '/api/idcard',
                            method: 'POST',
                            category_name: '数据类',
                            price: '0.05',
                            call_count: 856,
                            status: 1,
                            created_at: '2024-01-02 14:30:00'
                        },
                        {
                            id: 3,
                            name: '手机归属地API',
                            path: '/api/mobile',
                            method: 'GET',
                            category_name: '工具类',
                            price: '0.02',
                            call_count: 567,
                            status: 0,
                            created_at: '2024-01-03 09:15:00'
                        }
                    ],
                    total: 3,
                    page: page,
                    per_page: pageSize,
                    total_pages: 1
                };
                
                renderApiTable(mockData.data);
                renderPagination(mockData);
            }
            
            // 渲染API表格
            function renderApiTable(data) {
                var html = '';
                
                if (data.length === 0) {
                    html = '<tr><td colspan="11" style="text-align: center; padding: 50px; color: #999;">暂无数据</td></tr>';
                } else {
                    data.forEach(function(item) {
                        var statusBadge = item.status == 1 ? 
                            '<span class="status-badge status-active">启用</span>' : 
                            '<span class="status-badge status-inactive">禁用</span>';
                        
                        var methodBadge = '<span class="method-badge method-' + item.method.toLowerCase() + '">' + item.method + '</span>';
                        
                        html += '<tr>';
                        html += '<td><input type="checkbox" name="ids" value="' + item.id + '" lay-skin="primary"></td>';
                        html += '<td>' + item.id + '</td>';
                        html += '<td>' + item.name + '</td>';
                        html += '<td><code class="api-path">' + item.path + '</code></td>';
                        html += '<td>' + methodBadge + '</td>';
                        html += '<td>' + (item.category_name || '-') + '</td>';
                        html += '<td>¥' + item.price + '</td>';
                        html += '<td>' + item.call_count + '</td>';
                        html += '<td>' + statusBadge + '</td>';
                        html += '<td>' + item.created_at + '</td>';
                        html += '<td>';
                        html += '<button class="layui-btn layui-btn-xs" onclick="showEditForm(' + item.id + ')">编辑</button>';
                        html += '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="debugApi(' + item.id + ')">调试</button>';
                        html += '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="copyApi(' + item.id + ')">复制</button>';
                        html += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteApi(' + item.id + ')">删除</button>';
                        html += '</td>';
                        html += '</tr>';
                    });
                }
                
                document.getElementById('apiTableBody').innerHTML = html;
                form.render('checkbox');
            }
            
            // 渲染分页
            function renderPagination(data) {
                laypage.render({
                    elem: 'pagination',
                    count: data.total,
                    curr: data.page,
                    limit: data.per_page,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function(obj, first) {
                        if (!first) {
                            loadApiList(obj.curr);
                        }
                    }
                });
            }
            
            // 搜索表单提交
            form.on('submit(search)', function(data) {
                loadApiList(1);
                return false;
            });
            
            // 显示添加表单
            window.showAddForm = function() {
                document.getElementById('apiId').value = '';
                document.getElementById('apiFormModal').querySelector('form').reset();
                
                layer.open({
                    type: 1,
                    title: '添加API',
                    content: document.getElementById('apiFormModal'),
                    area: ['800px', '600px'],
                    success: function() {
                        form.render();
                    }
                });
            };
            
            // 显示编辑表单
            window.showEditForm = function(id) {
                // 模拟获取API详情
                var apiData = {
                    id: id,
                    name: '天气查询API',
                    path: '/api/weather',
                    method: 'GET',
                    category_id: 1,
                    price: 0.01,
                    description: '获取指定城市的天气信息',
                    timeout: 30,
                    retry_count: 0,
                    rate_limit: 60,
                    status: 1,
                    is_public: 1,
                    response_example: '{"code": 200, "data": {"city": "北京", "weather": "晴"}}'
                };
                
                // 填充表单
                document.getElementById('apiId').value = apiData.id;
                var form_elem = document.getElementById('apiFormModal').querySelector('form');
                Object.keys(apiData).forEach(function(key) {
                    var input = form_elem.querySelector('[name="' + key + '"]');
                    if (input) {
                        if (input.type === 'radio') {
                            var radio = form_elem.querySelector('[name="' + key + '"][value="' + apiData[key] + '"]');
                            if (radio) radio.checked = true;
                        } else {
                            input.value = apiData[key];
                        }
                    }
                });
                
                layer.open({
                    type: 1,
                    title: '编辑API',
                    content: document.getElementById('apiFormModal'),
                    area: ['800px', '600px'],
                    success: function() {
                        form.render();
                    }
                });
            };
            
            // API表单提交
            form.on('submit(submitApi)', function(data) {
                var loadIndex = layer.load(2);
                
                // 模拟提交
                setTimeout(function() {
                    layer.close(loadIndex);
                    layer.msg('操作成功', {icon: 1}, function() {
                        layer.closeAll();
                        loadApiList(currentPage);
                    });
                }, 1000);
                
                return false;
            });
            
            // API调试
            window.debugApi = function(id) {
                currentApiId = id;
                
                // 模拟API参数
                var params = [
                    {name: 'city', type: 'string', required: true, description: '城市名称'},
                    {name: 'format', type: 'string', required: false, description: '返回格式'}
                ];
                
                var paramsHtml = '';
                params.forEach(function(param) {
                    var required = param.required ? '<span style="color: red;">*</span>' : '';
                    paramsHtml += '<div class="layui-form-item">';
                    paramsHtml += '<label class="layui-form-label">' + param.name + required + '</label>';
                    paramsHtml += '<div class="layui-input-block">';
                    paramsHtml += '<input type="text" name="' + param.name + '" placeholder="' + param.description + '" class="layui-input">';
                    paramsHtml += '</div>';
                    paramsHtml += '</div>';
                });
                
                document.getElementById('debugParams').innerHTML = paramsHtml;
                
                layer.open({
                    type: 1,
                    title: 'API调试 - ' + id,
                    content: document.getElementById('debugModal'),
                    area: ['800px', '500px'],
                    success: function() {
                        form.render();
                    }
                });
            };
            
            // 执行调试
            window.executeDebug = function() {
                var loadIndex = layer.load(2);
                
                // 模拟调试结果
                setTimeout(function() {
                    layer.close(loadIndex);
                    
                    var result = {
                        response_code: 200,
                        response_data: {
                            code: 200,
                            message: 'success',
                            data: {
                                city: '北京',
                                weather: '晴',
                                temperature: '25°C'
                            }
                        },
                        response_time: 156,
                        success: true
                    };
                    
                    var resultHtml = '<div style="margin-bottom: 15px;">';
                    resultHtml += '<strong>响应状态:</strong> <span style="color: ' + (result.success ? 'green' : 'red') + '">' + result.response_code + '</span>';
                    resultHtml += '<span style="margin-left: 20px;"><strong>响应时间:</strong> ' + result.response_time + 'ms</span>';
                    resultHtml += '</div>';
                    resultHtml += '<div><strong>响应数据:</strong></div>';
                    resultHtml += '<pre style="background: #f5f5f5; padding: 15px; border-radius: 4px; max-height: 300px; overflow-y: auto;">';
                    resultHtml += JSON.stringify(result.response_data, null, 2);
                    resultHtml += '</pre>';
                    
                    document.getElementById('debugResult').innerHTML = resultHtml;
                    
                    // 切换到响应结果标签页
                    element.tabChange('debugTab', '2');
                }, 2000);
            };
            
            // 复制API
            window.copyApi = function(id) {
                layer.confirm('确定要复制这个API吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('API复制成功', {icon: 1});
                        loadApiList(currentPage);
                    }, 1000);
                });
            };
            
            // 删除API
            window.deleteApi = function(id) {
                layer.confirm('确定要删除这个API吗？删除后不可恢复！', {
                    icon: 3,
                    title: '警告'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('API删除成功', {icon: 1});
                        loadApiList(currentPage);
                    }, 1000);
                });
            };
            
            // 批量操作
            window.batchOperation = function(action) {
                var checkboxes = document.querySelectorAll('input[name="ids"]:checked');
                if (checkboxes.length === 0) {
                    layer.msg('请选择要操作的API', {icon: 2});
                    return;
                }
                
                var ids = [];
                checkboxes.forEach(function(checkbox) {
                    ids.push(checkbox.value);
                });
                
                var actionText = {
                    'enable': '启用',
                    'disable': '禁用',
                    'delete': '删除'
                };
                
                layer.confirm('确定要批量' + actionText[action] + '选中的API吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('批量操作成功', {icon: 1});
                        loadApiList(currentPage);
                    }, 1000);
                });
            };
            
            // 退出登录
            window.logout = function() {
                layer.confirm('确定要退出登录吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    layer.msg('退出成功', {icon: 1}, function() {
                        location.href = 'index.html';
                    });
                });
            };
        });
    </script>
</body>
</html>
