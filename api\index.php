<?php
/**
 * API系统主入口文件
 */
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入核心文件
require_once '../core/Database.php';
require_once '../core/Auth.php';
require_once '../core/ApiManager.php';
require_once '../core/UserManager.php';
require_once '../core/PaymentManager.php';

// 错误处理
set_error_handler(function($severity, $message, $file, $line) {
    throw new ErrorException($message, 0, $severity, $file, $line);
});

set_exception_handler(function($exception) {
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'message' => '系统错误: ' . $exception->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
});

class ApiRouter {
    private $auth;
    private $apiManager;
    private $userManager;
    private $paymentManager;
    private $config;
    
    public function __construct() {
        $this->auth = new Auth();
        $this->apiManager = new ApiManager();
        $this->userManager = new UserManager();
        $this->paymentManager = new PaymentManager();
        $this->config = require_once '../config/app.php';
    }
    
    /**
     * 路由处理
     */
    public function route() {
        $requestUri = $_SERVER['REQUEST_URI'];
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        
        // 移除查询参数
        $path = parse_url($requestUri, PHP_URL_PATH);
        $path = str_replace('/api', '', $path);
        
        // 路由匹配
        switch (true) {
            // 用户认证相关
            case $path === '/auth/login' && $requestMethod === 'POST':
                $this->login();
                break;
            case $path === '/auth/register' && $requestMethod === 'POST':
                $this->register();
                break;
            case $path === '/auth/refresh' && $requestMethod === 'POST':
                $this->refreshToken();
                break;
                
            // 用户信息相关
            case $path === '/user/profile' && $requestMethod === 'GET':
                $this->getUserProfile();
                break;
            case $path === '/user/profile' && $requestMethod === 'PUT':
                $this->updateUserProfile();
                break;
            case $path === '/user/balance' && $requestMethod === 'GET':
                $this->getUserBalance();
                break;
            case $path === '/user/api-key/reset' && $requestMethod === 'POST':
                $this->resetUserApiKey();
                break;
                
            // 支付相关
            case $path === '/payment/create' && $requestMethod === 'POST':
                $this->createPaymentOrder();
                break;
            case preg_match('/^\/payment\/(\w+)\/notify$/', $path, $matches):
                $this->handlePaymentNotify($matches[1]);
                break;
                
            // API调用
            case preg_match('/^\/call\/(.+)$/', $path, $matches):
                $this->callApi($matches[1]);
                break;
                
            // API文档
            case $path === '/docs' && $requestMethod === 'GET':
                $this->getApiDocs();
                break;
            case preg_match('/^\/docs\/(.+)$/', $path, $matches):
                $this->getApiDoc($matches[1]);
                break;
                
            // 系统信息
            case $path === '/system/info' && $requestMethod === 'GET':
                $this->getSystemInfo();
                break;
                
            default:
                $this->notFound();
        }
    }
    
    /**
     * 用户登录
     */
    private function login() {
        $input = $this->getJsonInput();
        
        if (empty($input['username']) || empty($input['password'])) {
            $this->errorResponse('用户名和密码不能为空', 400);
        }
        
        // 检查IP黑名单
        if ($this->isIpBlocked()) {
            $this->errorResponse('IP已被禁止访问', 403);
        }
        
        $result = $this->auth->login($input['username'], $input['password']);
        
        if ($result['success']) {
            $this->successResponse($result);
        } else {
            $this->errorResponse($result['message'], 401);
        }
    }
    
    /**
     * 用户注册
     */
    private function register() {
        $input = $this->getJsonInput();
        
        // 检查是否允许注册
        $registerEnabled = $this->getSystemConfig('register_enabled', '1');
        if ($registerEnabled !== '1') {
            $this->errorResponse('系统暂不开放注册', 403);
        }
        
        $required = ['username', 'email', 'password'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                $this->errorResponse("字段 {$field} 不能为空", 400);
            }
        }
        
        // 验证邮箱格式
        if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
            $this->errorResponse('邮箱格式不正确', 400);
        }
        
        // 验证密码强度
        if (strlen($input['password']) < 6) {
            $this->errorResponse('密码长度不能少于6位', 400);
        }
        
        $result = $this->auth->register($input);
        
        if ($result['success']) {
            $this->successResponse(['message' => $result['message']]);
        } else {
            $this->errorResponse($result['message'], 400);
        }
    }
    
    /**
     * 刷新Token
     */
    private function refreshToken() {
        $user = $this->getCurrentUser();
        
        // 生成新的Token
        $newToken = $this->auth->generateJWT($user);
        
        $this->successResponse(['token' => $newToken]);
    }
    
    /**
     * 获取用户资料
     */
    private function getUserProfile() {
        $user = $this->getCurrentUser();
        
        // 移除敏感信息
        unset($user['password']);
        
        $this->successResponse($user);
    }
    
    /**
     * 更新用户资料
     */
    private function updateUserProfile() {
        $user = $this->getCurrentUser();
        $input = $this->getJsonInput();
        
        $allowedFields = ['email'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateData[$field] = $input[$field];
            }
        }
        
        if (empty($updateData)) {
            $this->errorResponse('没有可更新的数据', 400);
        }
        
        $result = $this->userManager->updateUser($user['id'], $updateData);
        
        if ($result['success']) {
            $this->successResponse(['message' => $result['message']]);
        } else {
            $this->errorResponse($result['message'], 400);
        }
    }
    
    /**
     * 获取用户余额
     */
    private function getUserBalance() {
        $user = $this->getCurrentUser();
        
        $this->successResponse([
            'balance' => $user['balance'],
            'level' => $user['level']
        ]);
    }
    
    /**
     * 重置用户API密钥
     */
    private function resetUserApiKey() {
        $user = $this->getCurrentUser();
        
        $result = $this->userManager->resetApiKey($user['id']);
        
        if ($result['success']) {
            $this->successResponse(['api_key' => $result['api_key']]);
        } else {
            $this->errorResponse($result['message'], 400);
        }
    }
    
    /**
     * 创建支付订单
     */
    private function createPaymentOrder() {
        $user = $this->getCurrentUser();
        $input = $this->getJsonInput();
        
        if (empty($input['amount']) || empty($input['payment_method'])) {
            $this->errorResponse('参数不完整', 400);
        }
        
        $amount = floatval($input['amount']);
        if ($amount <= 0) {
            $this->errorResponse('金额必须大于0', 400);
        }
        
        $result = $this->paymentManager->createOrder(
            $user['id'],
            $amount,
            $input['payment_method'],
            $input['description'] ?? '账户充值'
        );
        
        if ($result['success']) {
            $this->successResponse($result);
        } else {
            $this->errorResponse($result['message'], 400);
        }
    }
    
    /**
     * 处理支付回调
     */
    private function handlePaymentNotify($paymentMethod) {
        $input = $_POST;
        
        $result = $this->paymentManager->handleCallback($paymentMethod, $input);
        
        if ($result) {
            echo 'success';
        } else {
            echo 'fail';
        }
        exit;
    }
    
    /**
     * 调用API
     */
    private function callApi($apiPath) {
        // 获取用户信息（通过API Key或Token）
        $user = $this->getCurrentUserByApiKey();
        
        if (!$user) {
            $this->errorResponse('认证失败', 401);
        }
        
        // 检查QPS限制
        if (!$this->checkQpsLimit($user['id'])) {
            $this->errorResponse('请求过于频繁', 429);
        }
        
        // 获取请求参数
        $params = $_REQUEST;
        unset($params['api_key']); // 移除API密钥参数
        
        $result = $this->apiManager->handleRequest($apiPath, $params, $user['id']);
        
        $this->jsonResponse($result);
    }
    
    /**
     * 获取API文档列表
     */
    private function getApiDocs() {
        $db = Database::getInstance();
        
        $apis = $db->fetchAll(
            "SELECT a.id, a.name, a.path, a.description, a.method, a.price, c.name as category_name
             FROM apis a 
             LEFT JOIN api_categories c ON a.category_id = c.id 
             WHERE a.status = 1 
             ORDER BY c.sort_order, a.sort_order"
        );
        
        $this->successResponse($apis);
    }
    
    /**
     * 获取单个API文档
     */
    private function getApiDoc($apiPath) {
        $db = Database::getInstance();
        
        $api = $db->fetchOne(
            "SELECT a.*, c.name as category_name 
             FROM apis a 
             LEFT JOIN api_categories c ON a.category_id = c.id 
             WHERE a.path = ? AND a.status = 1",
            [$apiPath]
        );
        
        if (!$api) {
            $this->errorResponse('API不存在', 404);
        }
        
        $this->successResponse($api);
    }
    
    /**
     * 获取系统信息
     */
    private function getSystemInfo() {
        $config = require_once '../config/app.php';
        
        $info = [
            'app_name' => $config['app_name'],
            'app_version' => $config['app_version'],
            'server_time' => date('Y-m-d H:i:s'),
            'timezone' => $config['timezone']
        ];
        
        $this->successResponse($info);
    }
    
    /**
     * 获取当前用户（通过Token）
     */
    private function getCurrentUser() {
        $token = $this->getBearerToken();
        
        if (!$token) {
            $this->errorResponse('请提供访问令牌', 401);
        }
        
        $payload = $this->auth->verifyToken($token);
        
        if (!$payload) {
            $this->errorResponse('访问令牌无效或已过期', 401);
        }
        
        $db = Database::getInstance();
        $user = $db->fetchOne(
            "SELECT * FROM users WHERE id = ? AND status = 1",
            [$payload['user_id']]
        );
        
        if (!$user) {
            $this->errorResponse('用户不存在或已被禁用', 401);
        }
        
        return $user;
    }
    
    /**
     * 获取当前用户（通过API Key）
     */
    private function getCurrentUserByApiKey() {
        $apiKey = $_REQUEST['api_key'] ?? $_SERVER['HTTP_X_API_KEY'] ?? '';
        
        if (!$apiKey) {
            return null;
        }
        
        $db = Database::getInstance();
        $user = $db->fetchOne(
            "SELECT * FROM users WHERE api_key = ? AND status = 1",
            [$apiKey]
        );
        
        return $user;
    }
    
    /**
     * 检查QPS限制
     */
    private function checkQpsLimit($userId) {
        $db = Database::getInstance();
        
        $count = $db->fetchOne(
            "SELECT COUNT(*) as count FROM request_logs 
             WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 SECOND)",
            [$userId]
        )['count'];
        
        return $count < $this->config['api']['qps_limit'];
    }
    
    /**
     * 检查IP是否被禁止
     */
    private function isIpBlocked() {
        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
        
        if (empty($ip)) {
            return false;
        }
        
        $db = Database::getInstance();
        
        // 检查黑名单
        $blocked = $db->fetchOne(
            "SELECT id FROM ip_rules WHERE ip_address = ? AND type = 'blacklist' AND status = 1",
            [$ip]
        );
        
        return $blocked !== false;
    }
    
    /**
     * 获取系统配置
     */
    private function getSystemConfig($key, $default = '') {
        $db = Database::getInstance();
        
        $config = $db->fetchOne(
            "SELECT value FROM system_configs WHERE `key` = ?",
            [$key]
        );
        
        return $config ? $config['value'] : $default;
    }
    
    /**
     * 获取Bearer Token
     */
    private function getBearerToken() {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
    
    /**
     * 获取JSON输入
     */
    private function getJsonInput() {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?: [];
    }
    
    /**
     * 成功响应
     */
    private function successResponse($data = null) {
        $this->jsonResponse([
            'code' => 200,
            'message' => '成功',
            'data' => $data
        ]);
    }
    
    /**
     * 错误响应
     */
    private function errorResponse($message, $code = 400) {
        http_response_code($code);
        $this->jsonResponse([
            'code' => $code,
            'message' => $message,
            'data' => null
        ]);
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data) {
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 404响应
     */
    private function notFound() {
        http_response_code(404);
        $this->jsonResponse([
            'code' => 404,
            'message' => '接口不存在',
            'data' => null
        ]);
    }
}

// 启动路由
$router = new ApiRouter();
$router->route();