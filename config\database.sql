-- API管理系统数据库结构
-- 创建时间: 2025-01-03
-- 版本: 1.0.0

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `api_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `api_system`;

-- ----------------------------
-- 1. 管理员用户表
-- ----------------------------
DROP TABLE IF EXISTS `api_admins`;
CREATE TABLE `api_admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `role_id` int(11) DEFAULT 1 COMMENT '角色ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

-- 插入默认管理员
INSERT INTO `api_admins` (`username`, `password`, `nickname`, `email`, `role_id`) VALUES 
('admin', 'e10adc3949ba59abbe56e057f20f883e', '超级管理员', '<EMAIL>', 1);

-- ----------------------------
-- 2. 角色权限表
-- ----------------------------
DROP TABLE IF EXISTS `api_roles`;
CREATE TABLE `api_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `permissions` text COMMENT '权限列表(JSON格式)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限表';

-- 插入默认角色
INSERT INTO `api_roles` (`name`, `description`, `permissions`) VALUES 
('超级管理员', '拥有所有权限', '["*"]'),
('普通管理员', '基础管理权限', '["api.view", "user.view", "merchant.view"]');

-- ----------------------------
-- 3. 用户表
-- ----------------------------
DROP TABLE IF EXISTS `api_users`;
CREATE TABLE `api_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `level` int(11) DEFAULT 1 COMMENT '会员等级',
  `balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `secret_key` varchar(64) NOT NULL COMMENT '密钥',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1正常，0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `api_key` (`api_key`),
  KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 4. 会员等级表
-- ----------------------------
DROP TABLE IF EXISTS `api_user_levels`;
CREATE TABLE `api_user_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `level` int(11) NOT NULL COMMENT '等级数值',
  `description` varchar(255) DEFAULT NULL COMMENT '等级描述',
  `daily_limit` int(11) DEFAULT 1000 COMMENT '每日调用限制',
  `monthly_limit` int(11) DEFAULT 30000 COMMENT '每月调用限制',
  `price_discount` decimal(3,2) DEFAULT 1.00 COMMENT '价格折扣',
  `features` text COMMENT '特权功能(JSON格式)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员等级表';

-- 插入默认等级
INSERT INTO `api_user_levels` (`name`, `level`, `description`, `daily_limit`, `monthly_limit`, `price_discount`) VALUES 
('普通会员', 1, '基础会员等级', 1000, 30000, 1.00),
('VIP会员', 2, 'VIP会员等级', 5000, 150000, 0.90),
('SVIP会员', 3, '超级VIP会员', 20000, 600000, 0.80);

-- ----------------------------
-- 5. API接口表
-- ----------------------------
DROP TABLE IF EXISTS `api_interfaces`;
CREATE TABLE `api_interfaces` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '接口ID',
  `name` varchar(100) NOT NULL COMMENT '接口名称',
  `path` varchar(255) NOT NULL COMMENT '接口路径',
  `method` varchar(10) DEFAULT 'GET' COMMENT '请求方法',
  `description` text COMMENT '接口描述',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `price` decimal(10,4) DEFAULT 0.0000 COMMENT '调用价格',
  `request_params` text COMMENT '请求参数(JSON格式)',
  `response_example` text COMMENT '响应示例',
  `request_headers` text COMMENT '请求头配置',
  `timeout` int(11) DEFAULT 30 COMMENT '超时时间(秒)',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `rate_limit` int(11) DEFAULT 60 COMMENT '频率限制(次/分钟)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `is_public` tinyint(1) DEFAULT 1 COMMENT '是否公开：1公开，0私有',
  `call_count` int(11) DEFAULT 0 COMMENT '调用次数',
  `success_count` int(11) DEFAULT 0 COMMENT '成功次数',
  `error_count` int(11) DEFAULT 0 COMMENT '错误次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `path` (`path`),
  KEY `category_id` (`category_id`),
  KEY `merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API接口表';

-- ----------------------------
-- 6. API分类表
-- ----------------------------
DROP TABLE IF EXISTS `api_categories`;
CREATE TABLE `api_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API分类表';

-- 插入默认分类
INSERT INTO `api_categories` (`name`, `description`, `icon`) VALUES 
('工具类', '实用工具接口', 'layui-icon-util'),
('数据类', '数据查询接口', 'layui-icon-chart'),
('娱乐类', '娱乐休闲接口', 'layui-icon-face-smile');

-- ----------------------------
-- 7. 商家表
-- ----------------------------
DROP TABLE IF EXISTS `api_merchants`;
CREATE TABLE `api_merchants` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商家ID',
  `username` varchar(50) NOT NULL COMMENT '商家用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `company_name` varchar(100) DEFAULT NULL COMMENT '公司名称',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `level` int(11) DEFAULT 1 COMMENT '商家等级',
  `commission_rate` decimal(5,4) DEFAULT 0.1000 COMMENT '佣金比例',
  `balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额',
  `api_count` int(11) DEFAULT 0 COMMENT 'API数量',
  `total_calls` int(11) DEFAULT 0 COMMENT '总调用次数',
  `total_income` decimal(10,2) DEFAULT 0.00 COMMENT '总收入',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：1审核通过，0待审核，-1拒绝',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家表';

-- ----------------------------
-- 8. 商家等级表
-- ----------------------------
DROP TABLE IF EXISTS `api_merchant_levels`;
CREATE TABLE `api_merchant_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `level` int(11) NOT NULL COMMENT '等级数值',
  `description` varchar(255) DEFAULT NULL COMMENT '等级描述',
  `api_limit` int(11) DEFAULT 10 COMMENT 'API数量限制',
  `commission_rate` decimal(5,4) DEFAULT 0.1000 COMMENT '佣金比例',
  `features` text COMMENT '特权功能(JSON格式)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家等级表';

-- 插入默认商家等级
INSERT INTO `api_merchant_levels` (`name`, `level`, `description`, `api_limit`, `commission_rate`) VALUES 
('初级商家', 1, '初级商家等级', 10, 0.1000),
('中级商家', 2, '中级商家等级', 50, 0.0800),
('高级商家', 3, '高级商家等级', 200, 0.0600);

-- ----------------------------
-- 9. API调用日志表
-- ----------------------------
DROP TABLE IF EXISTS `api_call_logs`;
CREATE TABLE `api_call_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `api_id` int(11) NOT NULL COMMENT '接口ID',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `request_params` text COMMENT '请求参数',
  `request_headers` text COMMENT '请求头',
  `response_code` int(11) DEFAULT NULL COMMENT '响应状态码',
  `response_data` text COMMENT '响应数据',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `cost` decimal(10,4) DEFAULT 0.0000 COMMENT '调用费用',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1成功，0失败',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `api_id` (`api_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API调用日志表';

-- ----------------------------
-- 10. 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `api_configs`;
CREATE TABLE `api_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `group` varchar(50) DEFAULT 'system' COMMENT '配置分组',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO `api_configs` (`key`, `value`, `description`, `type`, `group`) VALUES 
('site_name', 'API管理系统', '网站名称', 'string', 'basic'),
('site_description', '企业级API接口管理平台', '网站描述', 'string', 'basic'),
('site_keywords', 'API,接口,管理,系统', '网站关键词', 'string', 'basic'),
('default_api_price', '0.01', '默认API价格', 'decimal', 'api'),
('max_api_calls_per_day', '10000', '每日最大调用次数', 'integer', 'api');

SET FOREIGN_KEY_CHECKS = 1;