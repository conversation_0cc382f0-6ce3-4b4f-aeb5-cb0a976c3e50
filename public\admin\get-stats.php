<?php
/**
 * 获取统计数据
 */
session_start();

header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['code' => 401, 'msg' => '未登录']);
    exit;
}

try {
    // 连接数据库
    $config = require_once '../../config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    // 获取用户总数
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'user'");
    $stmt->execute();
    $userCount = $stmt->fetch()['count'] ?? 0;
    
    // 获取API总数
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM apis WHERE status = 1");
    $stmt->execute();
    $apiCount = $stmt->fetch()['count'] ?? 0;
    
    // 获取今日收入
    $stmt = $pdo->prepare("SELECT SUM(amount) as total FROM payment_orders WHERE status = 'completed' AND DATE(created_at) = CURDATE()");
    $stmt->execute();
    $todayIncome = $stmt->fetch()['total'] ?? 0;
    
    // 获取今日订单数
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_orders WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $todayOrders = $stmt->fetch()['count'] ?? 0;
    
    // 获取最近订单
    $stmt = $pdo->prepare("SELECT o.order_no, o.amount, o.status, o.created_at, u.username 
                          FROM payment_orders o 
                          LEFT JOIN users u ON o.user_id = u.id 
                          ORDER BY o.id DESC LIMIT 5");
    $stmt->execute();
    $recentOrders = $stmt->fetchAll();
    
    // 获取最近API调用
    $stmt = $pdo->prepare("SELECT r.status, r.created_at, u.username, a.name as api_name 
                          FROM request_logs r 
                          LEFT JOIN users u ON r.user_id = u.id 
                          LEFT JOIN apis a ON r.api_id = a.id 
                          ORDER BY r.id DESC LIMIT 5");
    $stmt->execute();
    $recentApiCalls = $stmt->fetchAll();
    
    // 获取系统信息
    $systemInfo = [
        'serverOS' => php_uname('s') . ' ' . php_uname('r'),
        'phpVersion' => PHP_VERSION,
        'mysqlVersion' => $pdo->getAttribute(PDO::ATTR_SERVER_VERSION),
        'uptime' => getServerUptime(),
        'memoryUsage' => getMemoryUsage()
    ];
    
    echo json_encode([
        'code' => 200,
        'msg' => '获取成功',
        'data' => [
            'userCount' => $userCount,
            'apiCount' => $apiCount,
            'todayIncome' => number_format($todayIncome, 2),
            'todayOrders' => $todayOrders,
            'recentOrders' => $recentOrders,
            'recentApiCalls' => $recentApiCalls,
            'systemInfo' => $systemInfo
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
}

/**
 * 获取服务器运行时间
 */
function getServerUptime() {
    if (function_exists('sys_getloadavg')) {
        $uptime = shell_exec('uptime');
        if ($uptime) {
            return trim($uptime);
        }
    }
    return '未知';
}

/**
 * 获取内存使用情况
 */
function getMemoryUsage() {
    $memory = memory_get_usage(true);
    $peak = memory_get_peak_usage(true);
    
    return formatBytes($memory) . ' / ' . formatBytes($peak) . ' (峰值)';
}

/**
 * 格式化字节数
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>
