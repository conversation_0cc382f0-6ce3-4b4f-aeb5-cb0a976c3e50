<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>用户登录 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/style.css"/>
    <style>
        .login-wrapper {
            max-width: 420px;
            margin: 50px auto;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            background-color: #fff;
        }
        .login-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .login-header img {
            max-height: 60px;
            margin-bottom: 10px;
        }
        .login-form {
            padding: 20px 0;
        }
        .login-other {
            text-align: center;
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .login-other .layui-icon {
            font-size: 26px;
            margin: 0 10px;
            cursor: pointer;
        }
        .login-other .layui-icon-login-qq {
            color: #3492ED;
        }
        .login-other .layui-icon-login-wechat {
            color: #4DAF29;
        }
        .login-other .layui-icon-login-weibo {
            color: #CF1900;
        }
        .login-links {
            margin-top: 20px;
            text-align: right;
        }
        .login-links a {
            margin-left: 10px;
        }
        .captcha-img {
            cursor: pointer;
            height: 38px;
        }
    </style>
</head>
<body>
    <div class="layui-container">
        <div class="login-wrapper">
            <div class="login-header">
                <?php if (!empty($siteConfig['site_logo'])): ?>
                <a href="/"><img src="<?php echo $siteConfig['site_logo']; ?>" alt="<?php echo $siteConfig['site_name']; ?>"></a>
                <?php else: ?>
                <h2><?php echo $siteConfig['site_name']; ?></h2>
                <?php endif; ?>
                <p>用户登录</p>
            </div>
            
            <?php if (!empty($error)): ?>
            <div class="layui-bg-red" style="padding: 10px; margin-bottom: 15px; border-radius: 2px;">
                <?php echo $error; ?>
            </div>
            <?php endif; ?>
            
            <form class="layui-form login-form" action="/user/login" method="post">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="text" name="username" required lay-verify="required" placeholder="用户名/邮箱" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <input type="password" name="password" required lay-verify="required" placeholder="密码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <?php if ($siteConfig['enable_captcha']): ?>
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0; display: flex;">
                        <input type="text" name="captcha" required lay-verify="required" placeholder="验证码" autocomplete="off" class="layui-input" style="flex: 1; margin-right: 10px;">
                        <img src="/captcha.php" class="captcha-img" onclick="this.src='/captcha.php?t='+Math.random();" title="点击刷新">
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0; display: flex; align-items: center;">
                        <input type="checkbox" name="remember" value="1" title="记住我" lay-skin="primary">
                        <div class="login-links">
                            <a href="/user/forgot_password">忘记密码?</a>
                            <a href="/user/register">注册账号</a>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="loginForm">登 录</button>
                    </div>
                </div>
                
                <?php if ($siteConfig['enable_social_login']): ?>
                <div class="login-other">
                    <label>社交账号登录</label>
                    <?php if ($siteConfig['enable_qq_login']): ?>
                    <a href="/user/oauth?type=qq"><i class="layui-icon layui-icon-login-qq"></i></a>
                    <?php endif; ?>
                    <?php if ($siteConfig['enable_wechat_login']): ?>
                    <a href="/user/oauth?type=wechat"><i class="layui-icon layui-icon-login-wechat"></i></a>
                    <?php endif; ?>
                    <?php if ($siteConfig['enable_weibo_login']): ?>
                    <a href="/user/oauth?type=weibo"><i class="layui-icon layui-icon-login-weibo"></i></a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
    
    <script src="/Easyweb/assets/libs/layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function() {
        var form = layui.form;
        var layer = layui.layer;
        
        // 表单提交
        form.on('submit(loginForm)', function(data) {
            // 表单数据会自动提交，这里可以添加额外的验证逻辑
            return true;
        });
    });
    </script>
</body>
</html>