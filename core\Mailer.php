<?php
/**
 * 邮件发送类
 */
class Mailer
{
    private $config;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->config = require_once __DIR__ . '/../config/app.php';
    }
    
    /**
     * 发送邮件
     * 
     * @param string $to 收件人邮箱
     * @param string $toName 收件人姓名
     * @param string $subject 邮件主题
     * @param string $body 邮件内容
     * @param array $attachments 附件数组
     * @return bool 是否发送成功
     */
    public function send($to, $toName, $subject, $body, $attachments = [])
    {
        // 检查邮件配置
        if (empty($this->config['mail']['smtp_host']) || 
            empty($this->config['mail']['smtp_username']) || 
            empty($this->config['mail']['smtp_password'])) {
            return false;
        }
        
        // 引入PHPMailer
        require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/Exception.php';
        require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/PHPMailer.php';
        require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/SMTP.php';
        
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
        
        try {
            // 服务器设置
            $mail->SMTPDebug = 0; // 调试模式输出
            $mail->isSMTP(); // 使用SMTP
            $mail->Host = $this->config['mail']['smtp_host']; // SMTP服务器
            $mail->SMTPAuth = true; // 允许SMTP认证
            $mail->Username = $this->config['mail']['smtp_username']; // SMTP用户名
            $mail->Password = $this->config['mail']['smtp_password']; // SMTP密码
            $mail->SMTPSecure = 'tls'; // 允许TLS或ssl协议
            $mail->Port = $this->config['mail']['smtp_port']; // TCP端口
            
            // 发件人
            $mail->setFrom($this->config['mail']['from_email'], $this->config['mail']['from_name']);
            
            // 收件人
            $mail->addAddress($to, $toName);
            
            // 内容
            $mail->isHTML(true); // 设置邮件格式为HTML
            $mail->Subject = $subject;
            $mail->Body = $body;
            $mail->CharSet = 'UTF-8';
            
            // 添加附件
            if (!empty($attachments)) {
                foreach ($attachments as $attachment) {
                    if (file_exists($attachment['path'])) {
                        $mail->addAttachment($attachment['path'], $attachment['name'] ?? '');
                    }
                }
            }
            
            // 发送
            return $mail->send();
        } catch (\PHPMailer\PHPMailer\Exception $e) {
            // 记录错误日志
            error_log('邮件发送失败: ' . $mail->ErrorInfo);
            return false;
        }
    }
    
    /**
     * 发送系统通知邮件
     * 
     * @param string $to 收件人邮箱
     * @param string $toName 收件人姓名
     * @param string $subject 邮件主题
     * @param string $body 邮件内容
     * @return bool 是否发送成功
     */
    public function sendSystemNotification($to, $toName, $subject, $body)
    {
        // 获取网站配置
        $db = Database::getInstance();
        $siteConfig = $db->query("SELECT * FROM site_config WHERE id = 1")->fetch();
        
        if (!$siteConfig || $siteConfig['email_notify'] != 1) {
            return false;
        }
        
        // 构建邮件模板
        $template = file_get_contents(__DIR__ . '/../templates/email/notification.html');
        
        if ($template) {
            $siteName = $siteConfig['site_name'] ?? 'API商业系统';
            $siteUrl = $siteConfig['site_url'] ?? '';
            $currentYear = date('Y');
            
            $template = str_replace('{SITE_NAME}', $siteName, $template);
            $template = str_replace('{SITE_URL}', $siteUrl, $template);
            $template = str_replace('{SUBJECT}', $subject, $template);
            $template = str_replace('{CONTENT}', $body, $template);
            $template = str_replace('{YEAR}', $currentYear, $template);
            
            $body = $template;
        }
        
        return $this->send($to, $toName, $subject, $body);
    }
    
    /**
     * 发送验证码邮件
     * 
     * @param string $to 收件人邮箱
     * @param string $toName 收件人姓名
     * @param string $code 验证码
     * @param int $expireMinutes 过期时间（分钟）
     * @return bool 是否发送成功
     */
    public function sendVerificationCode($to, $toName, $code, $expireMinutes = 10)
    {
        // 获取网站配置
        $db = Database::getInstance();
        $siteConfig = $db->query("SELECT * FROM site_config WHERE id = 1")->fetch();
        
        $siteName = $siteConfig['site_name'] ?? 'API商业系统';
        
        $subject = "{$siteName} - 验证码";
        $body = "尊敬的 {$toName}，<br><br>您的验证码是：<b>{$code}</b><br><br>此验证码将在 {$expireMinutes} 分钟后过期。<br><br>如果您没有请求此验证码，请忽略此邮件。";
        
        return $this->sendSystemNotification($to, $toName, $subject, $body);
    }
    
    /**
     * 发送密码重置邮件
     * 
     * @param string $to 收件人邮箱
     * @param string $toName 收件人姓名
     * @param string $resetUrl 重置链接
     * @return bool 是否发送成功
     */
    public function sendPasswordReset($to, $toName, $resetUrl)
    {
        // 获取网站配置
        $db = Database::getInstance();
        $siteConfig = $db->query("SELECT * FROM site_config WHERE id = 1")->fetch();
        
        $siteName = $siteConfig['site_name'] ?? 'API商业系统';
        
        $subject = "{$siteName} - 密码重置";
        $body = "尊敬的 {$toName}，<br><br>您收到此邮件是因为您请求重置密码。<br><br>请点击以下链接重置密码：<br><a href='{$resetUrl}'>{$resetUrl}</a><br><br>此链接将在1小时后失效。<br><br>如果您没有请求重置密码，请忽略此邮件。";
        
        return $this->sendSystemNotification($to, $toName, $subject, $body);
    }
    
    /**
     * 发送订单通知邮件
     * 
     * @param string $to 收件人邮箱
     * @param string $toName 收件人姓名
     * @param array $order 订单信息
     * @return bool 是否发送成功
     */
    public function sendOrderNotification($to, $toName, $order)
    {
        // 获取网站配置
        $db = Database::getInstance();
        $siteConfig = $db->query("SELECT * FROM site_config WHERE id = 1")->fetch();
        
        $siteName = $siteConfig['site_name'] ?? 'API商业系统';
        
        $subject = "{$siteName} - 订单通知 #{$order['order_no']}";
        
        $statusText = '';
        switch ($order['status']) {
            case 0:
                $statusText = '待支付';
                break;
            case 1:
                $statusText = '已支付';
                break;
            case 2:
                $statusText = '已取消';
                break;
            case 3:
                $statusText = '已退款';
                break;
        }
        
        $body = "尊敬的 {$toName}，<br><br>您的订单状态已更新：<br><br>";
        $body .= "订单编号：{$order['order_no']}<br>";
        $body .= "订单金额：{$order['amount']} 元<br>";
        $body .= "订单状态：{$statusText}<br>";
        $body .= "创建时间：{$order['created_at']}<br>";
        
        if ($order['status'] == 1) {
            $body .= "支付时间：{$order['paid_at']}<br>";
        }
        
        $body .= "<br>感谢您的使用！";
        
        return $this->sendSystemNotification($to, $toName, $subject, $body);
    }
}