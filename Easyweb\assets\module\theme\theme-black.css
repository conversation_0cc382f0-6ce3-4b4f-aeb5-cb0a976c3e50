/** 透明侧边栏导航 */
.layui-layout-admin .layui-side .layui-nav {
    background-color: transparent;
}

.layui-layout-admin .layui-side .layui-nav .layui-nav-item > a:hover {
    background: rgba(255, 255, 255, .03);
}

/** logo部分样式 */
.layui-layout-admin .layui-header .layui-logo {
    background-color: #3A3D49;
    color: #eee;
    box-shadow: none !important;
}

/** header样式 */
.layui-layout-admin .layui-header {
    background-color: #3A3D49;
}

.layui-layout-admin .layui-header a {
    color: #eee;
}

.layui-layout-admin .layui-header a:hover {
    color: #eee;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-more {
    border-color: #eee transparent transparent;
}

.layui-layout-admin .layui-header .layui-nav .layui-nav-mored {
    border-color: transparent transparent #eee;
}

/** 导航栏下面的线条 */
.layui-layout-admin .layui-header .layui-nav .layui-this:after, .layui-layout-admin .layui-header .layui-nav-bar {
    background-color: #eee;
}

/** 侧边栏样式 */
.layui-layout-admin .layui-side {
    background-color: #24262F;
}

.layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a, .layui-nav-tree .layui-this > a:hover {
    background-color: #009688;
}

.layui-nav-tree .layui-nav-bar {
    background-color: #009688;
}

/** 侧边栏文字颜色 */
.layui-side .layui-nav .layui-nav-item a {
    color: rgba(238, 238, 238, .7);
}

.layui-side .layui-nav-itemed > a, .layui-nav-tree .layui-nav-title a, .layui-nav-tree .layui-nav-title a:hover {
    color: #eee !important;
}

/** tab下划线 */
.layui-layout-admin .layui-body > .layui-tab > .layui-tab-title li.layui-this:after {
    background-color: #292B34;
    top: 38px;
}