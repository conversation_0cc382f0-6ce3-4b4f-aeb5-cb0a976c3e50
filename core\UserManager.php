<?php
/**
 * 用户管理类
 */
class UserManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取用户列表
     */
    public function getUserList($page = 1, $limit = 20, $filters = []) {
        $offset = ($page - 1) * $limit;
        $where = "1=1";
        $params = [];
        
        if (!empty($filters['username'])) {
            $where .= " AND username LIKE ?";
            $params[] = '%' . $filters['username'] . '%';
        }
        
        if (!empty($filters['email'])) {
            $where .= " AND email LIKE ?";
            $params[] = '%' . $filters['email'] . '%';
        }
        
        if (!empty($filters['role'])) {
            $where .= " AND role = ?";
            $params[] = $filters['role'];
        }
        
        if (!empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        // 获取总数
        $total = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM users WHERE {$where}",
            $params
        )['count'];
        
        // 获取用户列表
        $users = $this->db->fetchAll(
            "SELECT id, username, email, role, level, balance, status, 
                    created_at, last_login, login_count 
             FROM users 
             WHERE {$where} 
             ORDER BY created_at DESC 
             LIMIT {$limit} OFFSET {$offset}",
            $params
        );
        
        return [
            'total' => $total,
            'data' => $users,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 获取用户详情
     */
    public function getUserById($id) {
        return $this->db->fetchOne(
            "SELECT * FROM users WHERE id = ?",
            [$id]
        );
    }
    
    /**
     * 创建用户
     */
    public function createUser($data) {
        // 检查用户名和邮箱是否已存在
        $existUser = $this->db->fetchOne(
            "SELECT id FROM users WHERE username = ? OR email = ?",
            [$data['username'], $data['email']]
        );
        
        if ($existUser) {
            return ['success' => false, 'message' => '用户名或邮箱已存在'];
        }
        
        $userData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'role' => $data['role'] ?? 'user',
            'level' => $data['level'] ?? 1,
            'balance' => $data['balance'] ?? 0,
            'status' => $data['status'] ?? 1,
            'api_key' => $this->generateApiKey(),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->db->insert('users', $userData)) {
            return ['success' => true, 'message' => '用户创建成功'];
        }
        
        return ['success' => false, 'message' => '用户创建失败'];
    }
    
    /**
     * 更新用户
     */
    public function updateUser($id, $data) {
        $updateData = [];
        
        if (isset($data['username'])) {
            $updateData['username'] = $data['username'];
        }
        
        if (isset($data['email'])) {
            $updateData['email'] = $data['email'];
        }
        
        if (isset($data['password']) && !empty($data['password'])) {
            $updateData['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        if (isset($data['role'])) {
            $updateData['role'] = $data['role'];
        }
        
        if (isset($data['level'])) {
            $updateData['level'] = $data['level'];
        }
        
        if (isset($data['balance'])) {
            $updateData['balance'] = $data['balance'];
        }
        
        if (isset($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        
        $updateData['updated_at'] = date('Y-m-d H:i:s');
        
        if ($this->db->update('users', $updateData, 'id = ?', [$id])) {
            return ['success' => true, 'message' => '用户更新成功'];
        }
        
        return ['success' => false, 'message' => '用户更新失败'];
    }
    
    /**
     * 删除用户
     */
    public function deleteUser($id) {
        if ($this->db->delete('users', 'id = ?', [$id])) {
            return ['success' => true, 'message' => '用户删除成功'];
        }
        
        return ['success' => false, 'message' => '用户删除失败'];
    }
    
    /**
     * 充值余额
     */
    public function rechargeBalance($userId, $amount, $description = '管理员充值') {
        // 更新用户余额
        $this->db->update('users', [
            'balance' => new PDO_Expression('balance + ' . $amount)
        ], 'id = ?', [$userId]);
        
        // 记录余额变动
        $this->db->insert('balance_logs', [
            'user_id' => $userId,
            'type' => 'recharge',
            'amount' => $amount,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        return ['success' => true, 'message' => '充值成功'];
    }
    
    /**
     * 获取用户余额记录
     */
    public function getBalanceLogs($userId, $page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $total = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM balance_logs WHERE user_id = ?",
            [$userId]
        )['count'];
        
        $logs = $this->db->fetchAll(
            "SELECT * FROM balance_logs 
             WHERE user_id = ? 
             ORDER BY created_at DESC 
             LIMIT {$limit} OFFSET {$offset}",
            [$userId]
        );
        
        return [
            'total' => $total,
            'data' => $logs,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 重置API密钥
     */
    public function resetApiKey($userId) {
        $newApiKey = $this->generateApiKey();
        
        if ($this->db->update('users', ['api_key' => $newApiKey], 'id = ?', [$userId])) {
            return ['success' => true, 'api_key' => $newApiKey];
        }
        
        return ['success' => false, 'message' => 'API密钥重置失败'];
    }
    
    /**
     * 生成API密钥
     */
    private function generateApiKey() {
        return 'ak_' . bin2hex(random_bytes(16));
    }
    
    /**
     * 获取用户统计信息
     */
    public function getUserStats() {
        $stats = [];
        
        // 总用户数
        $stats['total_users'] = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM users"
        )['count'];
        
        // 活跃用户数（最近30天有登录）
        $stats['active_users'] = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM users 
             WHERE last_login > DATE_SUB(NOW(), INTERVAL 30 DAY)"
        )['count'];
        
        // 今日新增用户
        $stats['today_new_users'] = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM users 
             WHERE DATE(created_at) = CURDATE()"
        )['count'];
        
        // 各角色用户数量
        $roleStats = $this->db->fetchAll(
            "SELECT role, COUNT(*) as count FROM users GROUP BY role"
        );
        $stats['role_stats'] = $roleStats;
        
        return $stats;
    }
}