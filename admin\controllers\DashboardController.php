<?php
/**
 * 控制台控制器
 */
require_once '../../classes/Database.php';
require_once '../../core/AdminAuth.php';

class DashboardController {
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new AdminAuth();

        // 检查管理员权限
        $this->checkAdminAuth();
    }
    
    /**
     * 获取统计数据
     */
    public function getStats() {
        $data = [
            'api_count' => $this->getApiCount(),
            'user_count' => $this->getUserCount(),
            'order_count' => $this->getOrderCount(),
            'ticket_count' => $this->getTicketCount(),
            'income_data' => $this->getIncomeData(),
            'recent_api_calls' => $this->getRecentApiCalls(),
            'recent_orders' => $this->getRecentOrders(),
            'system_info' => $this->getSystemInfo()
        ];
        
        $this->jsonResponse(['code' => 200, 'data' => $data]);
    }
    
    /**
     * 获取API数量
     */
    private function getApiCount() {
        $result = $this->db->query("SELECT COUNT(*) as count FROM apis", [], true);
        return $result['count'] ?? 0;
    }

    /**
     * 获取用户数量
     */
    private function getUserCount() {
        $result = $this->db->query("SELECT COUNT(*) as count FROM users WHERE role = 'user'", [], true);
        return $result['count'] ?? 0;
    }

    /**
     * 获取订单数量
     */
    private function getOrderCount() {
        $result = $this->db->query("SELECT COUNT(*) as count FROM payment_orders", [], true);
        return $result['count'] ?? 0;
    }

    /**
     * 获取工单数量
     */
    private function getTicketCount() {
        $result = $this->db->query("SELECT COUNT(*) as count FROM tickets WHERE status = 'pending'", [], true);
        return $result['count'] ?? 0;
    }
    
    /**
     * 获取收入数据（最近7天）
     */
    private function getIncomeData() {
        $dates = [];
        $amounts = [];
        $counts = [];
        
        // 获取最近7天的日期
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dates[] = $date;
            
            // 查询当天收入
            $result = $this->db->query(
                "SELECT SUM(amount) as total FROM payment_orders WHERE status = 'completed' AND DATE(created_at) = ?",
                [$date], true
            );
            $amount = $result['total'] ?? 0;
            $amounts[] = floatval($amount);

            // 查询当天订单数
            $result = $this->db->query(
                "SELECT COUNT(*) as count FROM payment_orders WHERE status = 'completed' AND DATE(created_at) = ?",
                [$date], true
            );
            $count = $result['count'] ?? 0;
            $counts[] = intval($count);
        }
        
        return [
            'dates' => $dates,
            'amounts' => $amounts,
            'counts' => $counts
        ];
    }
    
    /**
     * 获取最近API调用
     */
    private function getRecentApiCalls() {
        return $this->db->query(
            "SELECT r.id, r.status, r.created_at, u.username, a.name as api_name
             FROM request_logs r
             LEFT JOIN users u ON r.user_id = u.id
             LEFT JOIN apis a ON r.api_id = a.id
             ORDER BY r.id DESC LIMIT 10"
        );
    }

    /**
     * 获取最近订单
     */
    private function getRecentOrders() {
        return $this->db->query(
            "SELECT o.order_no, o.amount, o.status, o.product_info, u.username
             FROM payment_orders o
             LEFT JOIN users u ON o.user_id = u.id
             ORDER BY o.id DESC LIMIT 10"
        );
    }
    
    /**
     * 获取系统信息
     */
    private function getSystemInfo() {
        // 获取服务器信息
        $server = $_SERVER['SERVER_SOFTWARE'] ?? '';
        
        // 获取PHP版本
        $phpVersion = PHP_VERSION;
        
        // 获取MySQL版本
        $mysqlVersion = '';
        try {
            $result = $this->db->query("SELECT VERSION() as version", [], true);
            $mysqlVersion = $result['version'] ?? '';
        } catch (Exception $e) {
            $mysqlVersion = '未知';
        }
        
        // 获取上传限制
        $uploadLimit = ini_get('upload_max_filesize');
        
        return [
            'server' => $server,
            'php_version' => $phpVersion,
            'mysql_version' => $mysqlVersion,
            'upload_limit' => $uploadLimit
        ];
    }
    
    /**
     * 检查管理员权限
     */
    private function checkAdminAuth() {
        $token = $_COOKIE['admin_token'] ?? '';

        if (empty($token)) {
            $this->jsonResponse(['code' => 401, 'msg' => '请先登录']);
        }

        $payload = $this->auth->verifyToken($token);

        if (!$payload || $payload['role'] !== 'admin') {
            $this->jsonResponse(['code' => 403, 'msg' => '权限不足']);
        }
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// 处理请求
$action = $_GET['action'] ?? '';
$controller = new DashboardController();

switch ($action) {
    case 'getStats':
        $controller->getStats();
        break;
    default:
        header('HTTP/1.1 404 Not Found');
        echo json_encode(['code' => 404, 'msg' => '接口不存在']);
        break;
}