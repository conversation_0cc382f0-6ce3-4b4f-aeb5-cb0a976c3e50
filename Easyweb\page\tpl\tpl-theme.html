
<div class="layui-card-header">主题设置</div>
<div class="more-theme-list">
    <div class="more-theme-item active">
        <img src="../EasyWeb/assets/module/img/theme-admin.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-cyan">
        <img src="../EasyWeb/assets/module/img/theme-cyan.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-white">
        <img src="../EasyWeb/assets/module/img/theme-white.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-pink">
        <img src="../EasyWeb/assets/module/img/theme-pink.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-colorful">
        <img src="../EasyWeb/assets/module/img/theme-colorful.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-blue">
        <img src="../EasyWeb/assets/module/img/theme-blue.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-green">
        <img src="../EasyWeb/assets/module/img/theme-green.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-purple">
        <img src="../EasyWeb/assets/module/img/theme-purple.png"/>
    </div>
    <div class="more-theme-item" data-theme="theme-red">
        <img src="../EasyWeb/assets/module/img/theme-red.png"/>
    </div>
</div>
<!-- 导航 -->
<div class="more-menu-list">
<!--     <a class="more-menu-item" href="https://easyweb.vip/doc/" target="_blank">
        <i class="layui-icon layui-icon-read" style="font-size: 19px;"></i> 开发文档
    </a>
    <a class="more-menu-item" href="https://demo.easyweb.vip/spa" target="_blank">
        <i class="layui-icon layui-icon-tabs" style="font-size: 16px;"></i> &nbsp;spa版本
    </a>
    <a class="more-menu-item" href="https://demo.easyweb.vip/theme" target="_blank">
        <i class="layui-icon layui-icon-theme"></i> 主题生成器
    </a> -->
</div>
<!-- 控制开关 -->
<div class="layui-form" style="margin: 25px 0;" lay-filter="more-set-form">
    <div class="layui-form-item">
        <label class="set-item-label">页&emsp;脚：</label>
        <div class="set-item-ctrl">
            <input id="setFooter" lay-filter="setFooter" type="checkbox" lay-skin="switch" lay-text="开启|关闭">
        </div>
        <label class="set-item-label"> Tab&nbsp;记忆：</label>
        <div class="set-item-ctrl">
            <input id="setTab" lay-filter="setTab" type="checkbox" lay-skin="switch" lay-text="开启|关闭">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="set-item-label">多标签：</label>
        <div class="set-item-ctrl">
            <input id="setMoreTab" lay-filter="setMoreTab" type="checkbox" lay-skin="switch" lay-text="开启|关闭">
        </div>
        <label class="set-item-label">切换刷新：</label>
        <div class="set-item-ctrl">
            <input id="setRefresh" lay-filter="setRefresh" type="checkbox" lay-skin="switch" lay-text="开启|关闭">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="set-item-label">导航箭头：</label>
        <div class="set-item-ctrl">
            <input lay-filter="navArrow" type="radio" value="" title="默认" name="navArrow">
            <input lay-filter="navArrow" type="radio" value="arrow2" title="箭头" name="navArrow">
            <input lay-filter="navArrow" type="radio" value="arrow3" title="加号" name="navArrow">
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'admin'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var admin = layui.admin;
        var setter = admin.setter;
        var $body = $('body');

        // 切换主题
        var $themItem = $('.more-theme-item');
        $themItem.click(function () {
            $themItem.removeClass('active');
            $(this).addClass('active');
            admin.changeTheme($(this).data('theme'));
        });
        var theme = $body.data('theme');
        if (theme) {
            $themItem.removeClass('active');
            $themItem.filter('[data-theme="' + theme + '"]').addClass('active');
        }

        // 关闭/开启页脚
        form.on('switch(setFooter)', function (data) {
            var checked = data.elem.checked;
            admin.putSetting('closeFooter', !checked);
            checked ? $body.removeClass('close-footer') : $body.addClass('close-footer');
        });
        $('#setFooter').prop('checked', !$body.hasClass('close-footer'));

        // 关闭/开启Tab记忆功能
        form.on('switch(setTab)', function (data) {
            layui.index.setTabCache(data.elem.checked);
        });
        $('#setTab').prop('checked', setter.cacheTab);

        // 关闭/开启多标签
        form.on('switch(setMoreTab)', function (data) {
            var checked = data.elem.checked;
            admin.putSetting('pageTabs', checked);
            admin.putTempData('indexTabs', undefined);
            location.reload();
        });
        $('#setMoreTab').prop('checked', setter.pageTabs);

        // 切换Tab自动刷新
        var $mainTab = $('.layui-body>.layui-tab[lay-filter="admin-pagetabs"]');
        form.on('switch(setRefresh)', function (data) {
            var checked = data.elem.checked;
            admin.putSetting('tabAutoRefresh', checked);
            checked ? $mainTab.attr('lay-autoRefresh', 'true') : $mainTab.removeAttr('lay-autoRefresh');
        });
        $('#setRefresh').prop('checked', setter.tabAutoRefresh === true);

        // 导航小三角
        var $leftNav = $('.layui-layout-admin>.layui-side>.layui-side-scroll>.layui-nav');
        form.on('radio(navArrow)', function (data) {
            $leftNav.removeClass('arrow2 arrow3');
            data.value && $leftNav.addClass(data.value);
            admin.putSetting('navArrow', data.value);
        });
        var navArrow = $leftNav.hasClass('arrow2') ? 'arrow2' : $leftNav.hasClass('arrow3') ? 'arrow3' : '';
        $('[name="navArrow"][value="' + navArrow + '"]').prop('checked', true);

        form.render('radio', 'more-set-form');
        form.render('checkbox', 'more-set-form');
    });
</script>

<style>
    /* theme */
    .more-theme-list {
        padding-left: 15px;
        padding-top: 20px;
        margin-bottom: 10px;
    }

    .more-theme-item {
        padding: 4px;
        margin: 0 6px 15px 0;
        display: inline-block;
        border: 1px solid transparent;
    }

    .more-theme-item img {
        width: 80px;
        height: 50px;
        background: #f5f7f9;
        box-sizing: border-box;
        border: 1px solid #f5f7f9;
        cursor: pointer;
    }

    .more-theme-item:hover, .more-theme-item.active {
        border-color: #5FB878;
    }

    .more-menu-item {
        color: #595959;
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        padding: 0 25px;
        border-bottom: 1px solid #e8e8e8;
        font-style: normal;
        display: block;
    }

    /* menu */
    .more-menu-item:first-child {
        border-top: 1px solid #e8e8e8;
    }

    .more-menu-item:hover {
        color: #595959;
        background: #f6f6f6;
    }

    .more-menu-item .layui-icon {
        font-size: 18px;
        padding-right: 10px;
    }

    .more-menu-item:after {
        color: #8c8c8c;
        right: 16px;
        content: "\e602";
        position: absolute;
        font-family: layui-icon !important;
    }

    .more-menu-item.no-icon:after {
        display: none;
    }

    /* setting from */
    .set-item-label {
        height: 38px;
        line-height: 38px;
        padding-left: 20px;
        display: inline-block;
    }

    .set-item-ctrl {
        height: 38px;
        line-height: 38px;
        display: inline-block;
    }

    .set-item-ctrl > * {
        margin: 0 !important;
    }
</style>