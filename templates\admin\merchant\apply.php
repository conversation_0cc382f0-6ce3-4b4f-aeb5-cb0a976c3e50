<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>商家入驻申请 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
</head>
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-user"></i> 商家入驻申请
        </div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">申请状态:</label>
                        <div class="layui-input-inline">
                            <select id="status">
                                <option value="">所有状态</option>
                                <option value="0">待审核</option>
                                <option value="1">已通过</option>
                                <option value="2">已拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">商家名称:</label>
                        <div class="layui-input-inline">
                            <input id="name" class="layui-input" placeholder="输入商家名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" id="searchBtn">
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                    </div>
                </div>
            </div>
            <table id="applyTable" lay-filter="applyTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
    {{# if(d.status === 0){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="pass">通过</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="reject">拒绝</a>
    {{# } }}
</script>

<!-- 状态列 -->
<script type="text/html" id="statusTpl">
    {{#  if(d.status === 0){ }}
    <span class="layui-badge layui-bg-orange">待审核</span>
    {{#  } else if(d.status === 1){ }}
    <span class="layui-badge layui-bg-green">已通过</span>
    {{#  } else if(d.status === 2){ }}
    <span class="layui-badge">已拒绝</span>
    {{#  } }}
</script>

<!-- 查看申请弹窗 -->
<script type="text/html" id="viewDialog">
    <div style="padding: 20px;">
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">商家名称</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.name}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系人</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.contact}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系电话</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.phone}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系邮箱</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.email}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">联系地址</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{d.address}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">营业执照</label>
                <div class="layui-input-block">
                    <img src="{{d.business_license}}" style="max-width: 100%; max-height: 300px;">
                </div>
            </div>
            {{# if(d.id_card_front){ }}
            <div class="layui-form-item">
                <label class="layui-form-label">身份证正面</label>
                <div class="layui-input-block">
                    <img src="{{d.id_card_front}}" style="max-width: 100%; max-height: 300px;">
                </div>
            </div>
            {{# } }}
            {{# if(d.id_card_back){ }}
            <div class="layui-form-item">
                <label class="layui-form-label">身份证背面</label>
                <div class="layui-input-block">
                    <img src="{{d.id_card_back}}" style="max-width: 100%; max-height: 300px;">
                </div>
            </div>
            {{# } }}
            <div class="layui-form-item">
                <label class="layui-form-label">申请描述</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" readonly>{{d.description}}</textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">申请时间</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{layui.util.toDateString(d.create_time * 1000)}}" readonly>
                </div>
            </div>
            {{# if(d.status !== 0){ }}
            <div class="layui-form-item">
                <label class="layui-form-label">审核时间</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" value="{{layui.util.toDateString(d.audit_time * 1000)}}" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">审核备注</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" readonly>{{d.audit_remark}}</textarea>
                </div>
            </div>
            {{# } }}
        </div>
    </div>
</script>

<!-- 审核弹窗 -->
<script type="text/html" id="auditDialog">
    <form id="auditForm" lay-filter="auditForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id}}"/>
        <input name="status" type="hidden" value="{{d.status}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">商家名称</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.name}}" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系人</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{{d.contact}}" readonly>
            </div>
        </div>
        {{# if(d.status === 1){ }}
        <div class="layui-form-item">
            <label class="layui-form-label">商家等级</label>
            <div class="layui-input-block">
                <select name="level" lay-verify="required">
                    <option value="">请选择商家等级</option>
                </select>
            </div>
        </div>
        {{# } }}
        <div class="layui-form-item">
            <label class="layui-form-label">审核备注</label>
            <div class="layui-input-block">
                <textarea name="audit_remark" placeholder="请输入审核备注" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="auditSubmit" lay-submit>确定</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script type="text/javascript" src="/Easyweb/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="/Easyweb/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'admin', 'laytpl'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;
        var laytpl = layui.laytpl;

        // 渲染表格
        var insTb = table.render({
            elem: '#applyTable',
            url: '/admin/merchant/apply_list',
            page: true,
            toolbar: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'id', title: 'ID', sort: true},
                {field: 'name', title: '商家名称'},
                {field: 'contact', title: '联系人'},
                {field: 'phone', title: '联系电话'},
                {field: 'email', title: '联系邮箱'},
                {field: 'status', title: '状态', templet: '#statusTpl'},
                {field: 'create_time', title: '申请时间', templet: function(d) {
                    return util.toDateString(d.create_time * 1000);
                }},
                {title: '操作', toolbar: '#tableBar', align: 'center', width: 180}
            ]]
        });

        // 搜索
        $('#searchBtn').click(function () {
            insTb.reload({
                where: {
                    status: $('#status').val(),
                    name: $('#name').val()
                },
                page: {curr: 1}
            });
        });

        // 工具条点击事件
        table.on('tool(applyTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            
            if (layEvent === 'view') { // 查看
                viewApply(data);
            } else if (layEvent === 'pass') { // 通过
                auditApply(data, 1);
            } else if (layEvent === 'reject') { // 拒绝
                auditApply(data, 2);
            }
        });

        // 查看申请
        function viewApply(data) {
            laytpl($('#viewDialog').html()).render(data, function(html) {
                admin.open({
                    type: 1,
                    title: '查看申请',
                    area: ['600px', '80%'],
                    content: html
                });
            });
        }

        // 审核申请
        function auditApply(data, status) {
            if (data.status !== 0) {
                layer.msg('该申请已审核', {icon: 2});
                return;
            }
            
            data.status = status;
            
            laytpl($('#auditDialog').html()).render(data, function(html) {
                admin.open({
                    type: 1,
                    title: status === 1 ? '通过申请' : '拒绝申请',
                    area: ['500px', '400px'],
                    content: html,
                    success: function() {
                        // 如果是通过申请，加载商家等级
                        if (status === 1) {
                            // 获取商家等级列表
                            $.get('/admin/merchant/level_list', {page: 1, limit: 100}, function(res) {
                                if (res.code === 0) {
                                    var levels = res.data;
                                    var options = '<option value="">请选择商家等级</option>';
                                    
                                    for (var i = 0; i < levels.length; i++) {
                                        if (levels[i].status === 1) {
                                            options += '<option value="' + levels[i].id + '">' + levels[i].name + '</option>';
                                        }
                                    }
                                    
                                    $('select[name="level"]').html(options);
                                    form.render('select');
                                }
                            }, 'json');
                        }
                    }
                });
            });
        }

        // 审核表单提交
        form.on('submit(auditSubmit)', function(data) {
            var loadIndex = layer.load(2);
            
            $.post('/admin/merchant/audit', data.field, function(res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.closeAll('page');
                    layer.msg(res.msg, {icon: 1});
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
            
            return false;
        });
    });
</script>
</body>
</html>