<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>控制台</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /** 统计快捷方式样式 */
        .console-link-block {
            font-size: 16px;
            padding: 20px 20px;
            border-radius: 4px;
            background-color: #40D4B0;
            color: #FFFFFF !important;
            box-shadow: 0 2px 3px rgba(0, 0, 0, .05);
            position: relative;
            overflow: hidden;
            display: block;
        }

        .console-link-block .console-link-block-num {
            font-size: 40px;
            margin-bottom: 5px;
            opacity: .9;
        }

        .console-link-block .console-link-block-text {
            opacity: .8;
        }

        .console-link-block .console-link-block-icon {
            position: absolute;
            top: 50%;
            right: 20px;
            width: 50px;
            height: 50px;
            font-size: 50px;
            line-height: 50px;
            margin-top: -25px;
            color: #FFFFFF;
            opacity: .8;
        }

        .console-link-block .console-link-block-band {
            color: #fff;
            width: 100px;
            font-size: 12px;
            padding: 2px 0 3px 0;
            background-color: #E32A16;
            line-height: inherit;
            text-align: center;
            position: absolute;
            top: 8px;
            right: -30px;
            transform-origin: center;
            transform: rotate(45deg) scale(.8);
            opacity: .95;
            z-index: 2;
        }

        /** //统计快捷方式样式 */

        /** 设置每个快捷块的颜色 */
        .layui-row > div:nth-child(2) .console-link-block {
            background-color: #55A5EA;
        }

        .layui-row > div:nth-child(3) .console-link-block {
            background-color: #9DAFFF;
        }

        .layui-row > div:nth-child(4) .console-link-block {
            background-color: #F591A2;
        }

        .layui-row > div:nth-child(5) .console-link-block {
            background-color: #FEAA4F;
        }

        .layui-row > div:last-child .console-link-block {
            background-color: #9BC539;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid ew-console-wrapper">
    <!-- 快捷方式 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">15</div>
                <div class="console-link-block-text">外出申请</div>
                <i class="console-link-block-icon layui-icon layui-icon-survey"></i>
                <div class="console-link-block-band">待审批</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">13</div>
                <div class="console-link-block-text">请假审批</div>
                <i class="console-link-block-icon layui-icon layui-icon-print"></i>
                <div class="console-link-block-band">待审批</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">22</div>
                <div class="console-link-block-text">研发周报</div>
                <i class="console-link-block-icon layui-icon layui-icon-list"
                   style="font-size: 62px;padding-right: 5px;"></i>
                <div class="console-link-block-band">去查看</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">18</div>
                <div class="console-link-block-text">研发月报</div>
                <i class="console-link-block-icon layui-icon layui-icon-date"></i>
                <div class="console-link-block-band">去查看</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">11</div>
                <div class="console-link-block-text">拜访记录</div>
                <i class="console-link-block-icon layui-icon layui-icon-service"></i>
                <div class="console-link-block-band">去查看</div>
            </div>
        </div>
        <div class="layui-col-md2 layui-col-sm4 layui-col-xs6">
            <div class="console-link-block">
                <div class="console-link-block-num">26</div>
                <div class="console-link-block-text">项目申报</div>
                <i class="console-link-block-icon layui-icon layui-icon-email"></i>
                <div class="console-link-block-band">待审批</div>
            </div>
        </div>
    </div>
    <!-- 统计图表 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">日统计</div>
                <div class="layui-card-body">
                    <div id="consoleChartsDay" style="height: 300px;"></div>
                    <div style="color: #10B4E8;font-size: 18px;position: absolute;bottom: 85px;left: 0;right:0;text-align: center;cursor: pointer;">
                        签到明细<i class="layui-icon layui-icon-right" style="font-size: 16px;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">周统计</div>
                <div class="layui-card-body">
                    <div id="consoleChartsWeek" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">月统计</div>
                <div class="layui-card-body">
                    <div id="consoleChartsMonth" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4 layui-col-sm6">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">热门搜索</div>
                <div class="layui-card-body">
                    <div id="consoleChartsWord" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md8 layui-col-sm12">
            <div class="layui-card" style="overflow: hidden;">
                <div class="layui-card-header">用户分布</div>
                <div class="layui-card-body">
                    <div class="layui-row">
                        <div class="layui-col-md8 layui-col-sm7">
                            <div id="consoleChartsMap" style="height: 300px;"></div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm5">
                            <table class="layui-table" lay-skin="line" style="margin-top: 15px;">
                                <thead>
                                <tr>
                                    <td>排名</td>
                                    <td>地区</td>
                                    <td>人数</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>浙江</td>
                                    <td>62310</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>上海</td>
                                    <td>59190</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>广东</td>
                                    <td>55891</td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>北京</td>
                                    <td>51919</td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>山东</td>
                                    <td>39231</td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>湖北</td>
                                    <td>37109</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=318"></script>
<script src="../../assets/libs/echarts/echarts.min.js"></script>
<script src="../../assets/libs/echarts/china.js"></script>
<script src="../../assets/libs/echarts/echarts-wordcloud.min.js"></script>
<script>
    layui.use(['layer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;

        /** 渲染日统计图表 */
        var myCharts1 = echarts.init(document.getElementById('consoleChartsDay'));
        var options1 = {
            title: {
                text: '签到人数/应到人数', x: 'center', y: '32%',
                textStyle: {fontSize: 18, color: '#262626', fontWeight: 'normal'},
                subtextStyle: {fontSize: 56, color: '#10B4E8'}, itemGap: 20
            },
            color: ['#10B4E8', '#E0E0E0'],
            tooltip: {trigger: 'item'},
            legend: {
                orient: 'vertical', right: '0px', top: '0px',
                data: ['已签到', '未签到'], textStyle: {color: '#595959'}
            },
            series: [{name: '人数', type: 'pie', radius: ['75%', '80%'], label: {normal: {show: false}}}]
        };
        myCharts1.setOption(options1);
        // 赋值
        myCharts1.setOption({
            title: {subtext: '38/60'}, series: [{data: [{name: '已签到', value: 38}, {name: '未签到', value: 22}]}]
        });

        /** 渲染周统计图表 */
        var myCharts2 = echarts.init(document.getElementById('consoleChartsWeek'));
        var options2 = {
            tooltip: {trigger: 'axis', axisPointer: {lineStyle: {color: '#E0E0E0'}}},
            color: ['#10B4E8', '#FFA800'],
            legend: {
                orient: 'vertical', right: '0px', top: '0px',
                data: ['已签到', '未签到'], textStyle: {color: '#595959'}
            },
            grid: {top: '75px', left: '35px', right: '55px', bottom: '40px'},
            xAxis: {
                name: '星期',
                nameTextStyle: {color: '#595959'},
                type: 'category',
                data: ['周一', '周二', '周三', '周四', '周五'],
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {color: '#8c8c8c'},
                axisTick: {alignWithLabel: true}
            },
            yAxis: {
                name: '人数',
                nameTextStyle: {color: '#595959'},
                type: 'value',
                boundaryGap: ['0', '20%'],
                axisTick: {show: false},
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {color: '#8c8c8c'},
                splitLine: {show: false},
                splitArea: {show: false},
                minInterval: 1
            },
            series: [{
                name: '已签到', type: 'bar', stack: 'one', barMaxWidth: '30px',
                label: {normal: {show: true, position: 'inside'}}
            }, {
                name: '未签到', type: 'bar', stack: 'one', barMaxWidth: '30px',
                label: {normal: {show: true, position: 'inside'}}
            }]
        };
        myCharts2.setOption(options2);
        // 赋值
        myCharts2.setOption({series: [{data: [5, 9, 6, 3, 10]}, {data: [10, 6, 9, 12, 5]}]});

        /** 渲染月统计图表 */
        var myCharts3 = echarts.init(document.getElementById('consoleChartsMonth'));
        var options3 = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {lineStyle: {color: '#E0E0E0'}},
                formatter: '{b}号<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#10B4E8;"></span>{a0}: {c0}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FFA800;"></span>{a1}: {c1}'
            },
            color: ['#10B4E8', '#FFA800'],
            legend: {
                orient: 'vertical', right: '0px', top: '0px',
                data: ['已签到', '未签到'], textStyle: {color: '#595959'}
            },
            grid: {top: '75px', left: '35px', right: '55px', bottom: '40px'},
            xAxis: {
                name: '日期',
                nameTextStyle: {color: '#595959'},
                type: 'category',
                data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31'],
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {
                    color: '#8c8c8c', interval: function (index, value) {
                        return index === 0 || ((index + 1) % 5 === 0);
                    }
                },
                axisTick: {alignWithLabel: true}
            },
            yAxis: {
                name: '人数',
                nameTextStyle: {color: '#595959'},
                type: 'value',
                boundaryGap: ['0', '20%'],
                axisTick: {show: false},
                axisLine: {lineStyle: {color: '#E0E0E0'}, symbol: ['none', 'arrow'], symbolOffset: [0, 10]},
                axisLabel: {color: '#8c8c8c'},
                splitLine: {show: false},
                splitArea: {show: false},
                minInterval: 1
            },
            series: [
                {name: '已签到', type: 'line', smooth: false},
                {name: '未签到', type: 'line', smooth: false}
            ]
        };
        myCharts3.setOption(options3);
        // 赋值
        myCharts3.setOption({
            series: [
                {data: [15, 14, 13, 13, 13, 14, 15, 16, 17, 18, 19, 18, 18, 19, 20, 19, 18, 16, 14, 12, 10, 10, 12, 14, 16, 16, 14, 13, 12, 11, 10]},
                {data: [24, 22, 20, 18, 16, 14, 13, 12, 11, 11, 10, 10, 11, 12, 13, 14, 15, 16, 18, 20, 22, 23, 24, 25, 26, 26, 24, 22, 20, 18, 16]}
            ]
        });

        /** 渲染地图图表 */
        var myCharts4 = echarts.init(document.getElementById('consoleChartsMap'));
        var options4 = {
            tooltip: {trigger: 'item'},
            dataRange: {
                min: 0, max: 6e4, text: ['高', '低'], color: ['#2395FF', '#f2f2f2'], itemHeight: 60, itemWidth: 12
            }, series: [{
                name: '用户数量', type: 'map', mapType: "china",
                itemStyle: {normal: {label: {show: true, color: '#262626'}, borderColor: '#dddddd'}},
                emphasis: {label: {show: true, color: '#fff'}, itemStyle: {areaColor: '#FACF20'}},
                top: '0px', left: '15px', bottom: '0px'
            }]
        };
        myCharts4.setOption(options4);
        // 赋值
        myCharts4.setOption({
            series: [{
                data: [
                    {name: "西藏", value: 60},
                    {name: "青海", value: 167},
                    {name: "宁夏", value: 210},
                    {name: "海南", value: 252},
                    {name: "甘肃", value: 502},
                    {name: "贵州", value: 570},
                    {name: "新疆", value: 661},
                    {name: "云南", value: 8890},
                    {name: "重庆", value: 10010},
                    {name: "吉林", value: 5056},
                    {name: "山西", value: 2123},
                    {name: "天津", value: 9130},
                    {name: "江西", value: 10170},
                    {name: "广西", value: 6172},
                    {name: "陕西", value: 9251},
                    {name: "黑龙江", value: 5125},
                    {name: "内蒙古", value: 1435},
                    {name: "安徽", value: 9530},
                    {name: "北京", value: 51919},
                    {name: "福建", value: 3756},
                    {name: "上海", value: 59190},
                    {name: "湖北", value: 37109},
                    {name: "湖南", value: 8966},
                    {name: "四川", value: 31020},
                    {name: "辽宁", value: 7222},
                    {name: "河北", value: 3451},
                    {name: "河南", value: 9693},
                    {name: "浙江", value: 62310},
                    {name: "山东", value: 39231},
                    {name: "江苏", value: 35911},
                    {name: "广东", value: 55891}
                ]
            }]
        });

        /** 渲染词云图表 */
        var myCharts5 = echarts.init(document.getElementById('consoleChartsWord'));
        var options5 = {
            tooltip: {show: true},
            series: [{
                name: "搜索量",
                type: 'wordCloud',
                shape: 'diamond',
                width: '100%',
                height: '100%',
                sizeRange: [12, 23],
                gridSize: 6,
                textStyle: {
                    normal: {
                        color: function () {
                            return 'rgb(' + [
                                Math.round(Math.random() * 160),
                                Math.round(Math.random() * 160),
                                Math.round(Math.random() * 160)
                            ].join(',') + ')';
                        }
                    },
                    emphasis: {shadowBlur: 10, shadowColor: '#666'}
                }, data: []
            }]
        };
        myCharts5.setOption(options5);
        // 赋值
        myCharts5.setOption({
            series: [{
                data: [
                    {name: "软妹子", value: 23},
                    {name: "汪星人", value: 23},
                    {name: "长腿欧巴", value: 23},
                    {name: "萝莉", value: 22},
                    {name: "辣~", value: 22},
                    {name: "K歌", value: 22},
                    {name: "大长腿", value: 21},
                    {name: "川妹子", value: 21},
                    {name: "女神", value: 21},
                    {name: "米粉", value: 20},
                    {name: "专注设计", value: 20},
                    {name: "逛街", value: 20},
                    {name: "黑长直", value: 20},
                    {name: "海纳百川", value: 19},
                    {name: "萌萌哒", value: 19},
                    {name: "坚持", value: 19},
                    {name: "话唠", value: 19},
                    {name: "果粉", value: 18},
                    {name: "喵星人", value: 18},
                    {name: "花粉", value: 18},
                    {name: "衬衫控", value: 18},
                    {name: "宅男", value: 17},
                    {name: "小清新", value: 17},
                    {name: "眼镜男", value: 17},
                    {name: "琼瑶", value: 17},
                    {name: "穷游党", value: 16},
                    {name: "铲屎官", value: 16},
                    {name: "正太", value: 16},
                    {name: "中二病", value: 16},
                    {name: "夜猫子", value: 15},
                    {name: "逗比", value: 15},
                    {name: "腹黑", value: 15},
                    {name: "吃鸡", value: 15},
                    {name: "为了联盟", value: 14},
                    {name: "背包客", value: 14},
                    {name: "民谣", value: 14},
                    {name: "为了部落", value: 14},
                    {name: "懒癌患者", value: 13},
                    {name: "追剧", value: 13},
                    {name: "IT民工", value: 13},
                    {name: "CNB成员", value: 13},
                    {name: "选择困难", value: 12},
                    {name: "锤粉", value: 12},
                    {name: "欧皇", value: 12},
                    {name: "仙气十足", value: 12}
                ]
            }]
        });

        /** 窗口大小改变事件 */
        window.onresize = function () {
            myCharts1.resize();
            myCharts2.resize();
            myCharts3.resize();
            myCharts4.resize();
            myCharts5.resize();
        };

    });
</script>
</body>
</html>