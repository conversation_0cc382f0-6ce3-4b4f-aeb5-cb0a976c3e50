<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>表格缩略图</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        #tbImgTable + .layui-table-view .layui-table-body tbody > tr > td {
            padding: 0;
        }

        #tbImgTable + .layui-table-view .layui-table-body tbody > tr > td > .layui-table-cell {
            height: 48px;
            line-height: 48px;
        }

        .tb-img-circle {
            width: 40px;
            height: 40px;
            cursor: zoom-in;
            border-radius: 50%;
            border: 2px solid #dddddd;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">搜索:</label>
                        <div class="layui-input-inline">
                            <input name="keyword" class="layui-input" placeholder="输入关键字"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="imgTbSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>
                        <button class="layui-btn icon-btn" type="button">
                            <i class="layui-icon">&#xe654;</i>新增
                        </button>
                    </div>
                </div>
            </div>
            <!-- 数据表格 -->
            <table id="tbImgTable" lay-filter="tbImgTable"></table>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'table'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var table = layui.table;

        /* 渲染表格 */
        table.render({
            elem: '#tbImgTable',
            url: '../../../json/user.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {
                    title: '头像', templet: function (d) {
                        var url = d.imgUrl || '../../../assets/images/head.jpg';
                        return '<img data-index="' + (d.LAY_INDEX - 1) + '" src="' + url + '" class="tb-img-circle" tb-img alt=""/>';
                    }, align: 'center', width: 90, unresize: true
                },
                {field: 'username', title: '账号', align: 'center', sort: true},
                {field: 'nickName', title: '用户名', align: 'center', sort: true},
                {field: 'phone', title: '手机号', align: 'center', sort: true},
                {field: 'sex', title: '性别', align: 'center', sort: true},
                {field: 'createTime', title: '创建时间', align: 'center', sort: true}
            ]]
        });

        /* 点击图片放大 */
        $(document).off('click.tbImg').on('click.tbImg', '[tb-img]', function () {
            var imgList = table.cache['tbImgTable'].map(function (d) {
                return {
                    alt: d.nickName,
                    src: d.imgUrl || '../../../assets/images/head.jpg'
                }
            });
            layer.photos({photos: {data: imgList, start: $(this).data('index')}, shade: .1, closeBtn: true});
        });

    });
</script>
</body>
</html>
