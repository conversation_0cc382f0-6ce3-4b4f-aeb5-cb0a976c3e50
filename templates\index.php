<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $siteConfig['site_name'] ?? 'API商业系统'; ?></title>
    <meta name="keywords" content="<?php echo $siteConfig['site_keywords'] ?? 'API,接口,商业化,管理系统'; ?>">
    <meta name="description" content="<?php echo $siteConfig['site_description'] ?? '专业的API接口管理平台，提供完整的API商业化解决方案'; ?>">
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css">
    <link rel="stylesheet" href="/Easyweb/assets/css/theme.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <?php if (!empty($siteConfig['global_css'])): ?>
    <style>
        <?php echo $siteConfig['global_css']; ?>
    </style>
    <?php endif; ?>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="container">
            <div class="header-logo">
                <?php if (!empty($siteConfig['site_logo'])): ?>
                <a href="/"><img src="<?php echo $siteConfig['site_logo']; ?>" alt="<?php echo $siteConfig['site_name']; ?>"></a>
                <?php else: ?>
                <a href="/"><?php echo $siteConfig['site_name'] ?? 'API商业系统'; ?></a>
                <?php endif; ?>
            </div>
            <div class="header-nav">
                <?php
                $navs = get_navigation();
                echo navigation_html($navs);
                ?>
            </div>
            <div class="header-user">
                <?php if (is_logged_in()): ?>
                <?php $user = get_current_user(); ?>
                <div class="user-info">
                    <img src="<?php echo $user['avatar'] ?: ($siteConfig['default_avatar'] ?? '/assets/images/default-avatar.png'); ?>" alt="头像" class="user-avatar">
                    <span class="user-name"><?php echo $user['username']; ?></span>
                    <div class="user-dropdown">
                        <a href="/user/profile">个人中心</a>
                        <a href="/user/apis">我的API</a>
                        <a href="/user/orders">我的订单</a>
                        <?php if ($user['role'] == 'merchant'): ?>
                        <a href="/merchant/dashboard">商家中心</a>
                        <?php endif; ?>
                        <?php if ($user['role'] == 'admin'): ?>
                        <a href="/admin/">管理后台</a>
                        <?php endif; ?>
                        <a href="/user/logout">退出登录</a>
                    </div>
                </div>
                <?php else: ?>
                <div class="user-btns">
                    <a href="/user/login" class="btn btn-login">登录</a>
                    <a href="/user/register" class="btn btn-register">注册</a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 轮播图 -->
    <div class="banner">
        <div class="layui-carousel" id="banner-carousel">
            <div carousel-item>
                <?php
                $banners = get_banners();
                foreach ($banners as $banner):
                ?>
                <div>
                    <a href="<?php echo $banner['link']; ?>" target="<?php echo $banner['target']; ?>">
                        <img src="<?php echo $banner['image']; ?>" alt="<?php echo $banner['title']; ?>">
                    </a>
                </div>
                <?php endforeach; ?>
                <?php if (empty($banners)): ?>
                <div>
                    <img src="/assets/images/default-banner.jpg" alt="默认轮播图">
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 统计数据 -->
    <div class="stats">
        <div class="container">
            <div class="stats-item">
                <div class="stats-icon">
                    <i class="layui-icon layui-icon-app"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-num"><?php echo get_system_stats()['api_count']; ?></div>
                    <div class="stats-text">API总数</div>
                </div>
            </div>
            <div class="stats-item">
                <div class="stats-icon">
                    <i class="layui-icon layui-icon-chart"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-num"><?php echo get_system_stats()['call_count']; ?></div>
                    <div class="stats-text">调用总次数</div>
                </div>
            </div>
            <div class="stats-item">
                <div class="stats-icon">
                    <i class="layui-icon layui-icon-user"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-num"><?php echo get_system_stats()['user_count']; ?></div>
                    <div class="stats-text">用户总数</div>
                </div>
            </div>
            <div class="stats-item">
                <div class="stats-icon">
                    <i class="layui-icon layui-icon-cart"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-num"><?php echo get_system_stats()['merchant_count']; ?></div>
                    <div class="stats-text">商家总数</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- API分类 -->
    <div class="section">
        <div class="container">
            <div class="section-title">
                <h2>API分类</h2>
                <a href="/api/market" class="more">查看更多 <i class="layui-icon layui-icon-right"></i></a>
            </div>
            <div class="category-list">
                <?php
                $categories = get_api_categories();
                foreach ($categories as $category):
                ?>
                <div class="category-item">
                    <div class="category-icon">
                        <i class="<?php echo $category['icon']; ?>"></i>
                    </div>
                    <div class="category-name"><?php echo $category['name']; ?></div>
                    <div class="category-desc"><?php echo $category['description']; ?></div>
                    <a href="/api/category/<?php echo $category['id']; ?>" class="category-link">查看API</a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- 热门API -->
    <div class="section">
        <div class="container">
            <div class="section-title">
                <h2>热门API</h2>
                <a href="/api/market" class="more">查看更多 <i class="layui-icon layui-icon-right"></i></a>
            </div>
            <div class="api-list">
                <?php
                $hotApis = get_hot_apis(8);
                foreach ($hotApis as $api):
                ?>
                <div class="api-item">
                    <div class="api-name"><?php echo $api['name']; ?></div>
                    <div class="api-desc"><?php echo mb_substr($api['description'], 0, 50) . (mb_strlen($api['description']) > 50 ? '...' : ''); ?></div>
                    <div class="api-info">
                        <span class="api-calls"><i class="layui-icon layui-icon-chart"></i> <?php echo $api['call_count']; ?>次调用</span>
                        <span class="api-price">
                            <?php if ($api['is_free'] == 1): ?>
                            <span class="badge badge-success">免费</span>
                            <?php else: ?>
                            <span class="badge badge-primary"><?php echo format_money($api['price']); ?>元/次</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <a href="/api/detail/<?php echo $api['id']; ?>" class="api-link">查看详情</a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- 最新API -->
    <div class="section">
        <div class="container">
            <div class="section-title">
                <h2>最新API</h2>
                <a href="/api/market" class="more">查看更多 <i class="layui-icon layui-icon-right"></i></a>
            </div>
            <div class="api-list">
                <?php
                $latestApis = get_latest_apis(8);
                foreach ($latestApis as $api):
                ?>
                <div class="api-item">
                    <div class="api-name"><?php echo $api['name']; ?></div>
                    <div class="api-desc"><?php echo mb_substr($api['description'], 0, 50) . (mb_strlen($api['description']) > 50 ? '...' : ''); ?></div>
                    <div class="api-info">
                        <span class="api-date"><i class="layui-icon layui-icon-date"></i> <?php echo format_datetime($api['created_at'], 'Y-m-d'); ?></span>
                        <span class="api-price">
                            <?php if ($api['is_free'] == 1): ?>
                            <span class="badge badge-success">免费</span>
                            <?php else: ?>
                            <span class="badge badge-primary"><?php echo format_money($api['price']); ?>元/次</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <a href="/api/detail/<?php echo $api['id']; ?>" class="api-link">查看详情</a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- VIP会员 -->
    <div class="section section-vip">
        <div class="container">
            <div class="section-title">
                <h2>VIP会员</h2>
                <a href="/user/vip" class="more">查看详情 <i class="layui-icon layui-icon-right"></i></a>
            </div>
            <div class="vip-list">
                <?php
                $vipLevels = get_vip_levels();
                foreach ($vipLevels as $vip):
                ?>
                <div class="vip-item">
                    <div class="vip-header">
                        <div class="vip-name"><?php echo $vip['name']; ?></div>
                        <div class="vip-price"><?php echo format_money($vip['price']); ?><span>元/<?php echo $vip['duration']; ?>天</span></div>
                    </div>
                    <div class="vip-body">
                        <div class="vip-desc"><?php echo $vip['description']; ?></div>
                        <ul class="vip-features">
                            <li><i class="layui-icon layui-icon-ok"></i> 享受<?php echo $vip['discount']; ?>折API调用优惠</li>
                            <li><i class="layui-icon layui-icon-ok"></i> 每日<?php echo $vip['daily_free_calls']; ?>次免费调用</li>
                            <li><i class="layui-icon layui-icon-ok"></i> 专属技术支持</li>
                            <li><i class="layui-icon layui-icon-ok"></i> API文档无限制访问</li>
                        </ul>
                    </div>
                    <div class="vip-footer">
                        <a href="/user/buy_vip/<?php echo $vip['id']; ?>" class="btn btn-primary">立即开通</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- 商家入驻 -->
    <div class="section section-merchant">
        <div class="container">
            <div class="section-title">
                <h2>商家入驻</h2>
                <a href="/merchant/join" class="more">查看详情 <i class="layui-icon layui-icon-right"></i></a>
            </div>
            <div class="merchant-intro">
                <div class="merchant-intro-text">
                    <h3>为什么要成为API商家？</h3>
                    <p>将您的API接入我们的平台，获得更多的曝光和收益。我们提供完整的API商业化解决方案，帮助您轻松实现API变现。</p>
                    <ul class="merchant-features">
                        <li><i class="layui-icon layui-icon-ok"></i> 高达90%的分成比例</li>
                        <li><i class="layui-icon layui-icon-ok"></i> 自动生成API文档</li>
                        <li><i class="layui-icon layui-icon-ok"></i> 完善的计费和结算系统</li>
                        <li><i class="layui-icon layui-icon-ok"></i> 强大的数据分析工具</li>
                        <li><i class="layui-icon layui-icon-ok"></i> 安全的API调用机制</li>
                        <li><i class="layui-icon layui-icon-ok"></i> 专业的技术支持</li>
                    </ul>
                    <a href="/merchant/join" class="btn btn-primary">立即入驻</a>
                </div>
                <div class="merchant-intro-image">
                    <img src="/assets/images/merchant-intro.png" alt="商家入驻">
                </div>
            </div>
            <div class="merchant-levels">
                <h3>商家等级</h3>
                <div class="merchant-level-list">
                    <?php
                    $merchantLevels = get_merchant_levels();
                    foreach ($merchantLevels as $level):
                    ?>
                    <div class="merchant-level-item">
                        <div class="merchant-level-name"><?php echo $level['name']; ?></div>
                        <div class="merchant-level-price"><?php echo format_money($level['price']); ?><span>元/<?php echo $level['duration']; ?>天</span></div>
                        <div class="merchant-level-desc"><?php echo $level['description']; ?></div>
                        <div class="merchant-level-info">
                            <div class="merchant-level-info-item">
                                <span class="label">API数量限制：</span>
                                <span class="value"><?php echo $level['api_limit'] ?: '无限制'; ?></span>
                            </div>
                            <div class="merchant-level-info-item">
                                <span class="label">分成比例：</span>
                                <span class="value"><?php echo $level['commission_rate']; ?>%</span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最新文章 -->
    <div class="section">
        <div class="container">
            <div class="section-title">
                <h2>最新文章</h2>
                <a href="/article/list" class="more">查看更多 <i class="layui-icon layui-icon-right"></i></a>
            </div>
            <div class="article-list">
                <?php
                $latestArticles = get_latest_articles(6);
                foreach ($latestArticles as $article):
                ?>
                <div class="article-item">
                    <?php if ($article['cover']): ?>
                    <div class="article-cover">
                        <a href="/article/detail/<?php echo $article['id']; ?>">
                            <img src="<?php echo $article['cover']; ?>" alt="<?php echo $article['title']; ?>">
                        </a>
                    </div>
                    <?php endif; ?>
                    <div class="article-content">
                        <div class="article-title">
                            <a href="/article/detail/<?php echo $article['id']; ?>"><?php echo $article['title']; ?></a>
                        </div>
                        <div class="article-desc"><?php echo mb_substr(strip_tags($article['content']), 0, 100) . '...'; ?></div>
                        <div class="article-info">
                            <span class="article-date"><i class="layui-icon layui-icon-date"></i> <?php echo format_datetime($article['created_at'], 'Y-m-d'); ?></span>
                            <span class="article-views"><i class="layui-icon layui-icon-eye"></i> <?php echo $article['views']; ?>次阅读</span>
                            <?php if ($article['author']): ?>
                            <span class="article-author"><i class="layui-icon layui-icon-username"></i> <?php echo $article['author']; ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <div class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <?php if (!empty($siteConfig['site_logo'])): ?>
                    <img src="<?php echo $siteConfig['site_logo']; ?>" alt="<?php echo $siteConfig['site_name']; ?>">
                    <?php else: ?>
                    <?php echo $siteConfig['site_name'] ?? 'API商业系统'; ?>
                    <?php endif; ?>
                </div>
                <div class="footer-nav">
                    <div class="footer-nav-item">
                        <div class="footer-nav-title">关于我们</div>
                        <div class="footer-nav-links">
                            <a href="/about">关于我们</a>
                            <a href="/contact">联系我们</a>
                            <a href="/agreement">用户协议</a>