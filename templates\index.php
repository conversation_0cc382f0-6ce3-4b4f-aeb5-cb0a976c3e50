<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title><?php echo $siteConfig['site_name']; ?> - 专业的API接口服务平台</title>
    <meta name="keywords" content="<?php echo $siteConfig['site_keywords']; ?>">
    <meta name="description" content="<?php echo $siteConfig['site_description']; ?>">
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/style.css"/>
    <?php if (!empty($siteConfig['custom_css'])): ?>
    <style>
        <?php echo $siteConfig['custom_css']; ?>
    </style>
    <?php endif; ?>
</head>
<body>
    <!-- 头部导航 -->
    <div class="layui-header header">
        <div class="layui-container">
            <div class="header-logo">
                <a href="/">
                    <?php if (!empty($siteConfig['site_logo'])): ?>
                    <img src="<?php echo $siteConfig['site_logo']; ?>" alt="<?php echo $siteConfig['site_name']; ?>">
                    <?php else: ?>
                    <h1><?php echo $siteConfig['site_name']; ?></h1>
                    <?php endif; ?>
                </a>
            </div>
            <ul class="layui-nav" lay-filter="header-nav">
                <?php foreach ($navMenus['top'] as $menu): ?>
                <li class="layui-nav-item <?php echo $currentPage == $menu['url'] ? 'layui-this' : ''; ?>">
                    <a href="<?php echo $menu['url']; ?>" target="<?php echo $menu['target']; ?>">
                        <?php if (!empty($menu['icon'])): ?>
                        <i class="layui-icon <?php echo $menu['icon']; ?>"></i>
                        <?php endif; ?>
                        <?php echo $menu['name']; ?>
                    </a>
                    <?php if (!empty($menu['children'])): ?>
                    <dl class="layui-nav-child">
                        <?php foreach ($menu['children'] as $child): ?>
                        <dd><a href="<?php echo $child['url']; ?>" target="<?php echo $child['target']; ?>"><?php echo $child['name']; ?></a></dd>
                        <?php endforeach; ?>
                    </dl>
                    <?php endif; ?>
                </li>
                <?php endforeach; ?>
                
                <?php if ($isLoggedIn): ?>
                <li class="layui-nav-item">
                    <a href="javascript:;">
                        <img src="<?php echo $user['avatar'] ?: '/assets/images/default-avatar.png'; ?>" class="layui-nav-img">
                        <?php echo $user['username']; ?>
                    </a>
                    <dl class="layui-nav-child">
                        <dd><a href="/user/dashboard">用户中心</a></dd>
                        <dd><a href="/user/api">我的接口</a></dd>
                        <dd><a href="/user/profile">个人资料</a></dd>
                        <dd><a href="/user/logout">退出登录</a></dd>
                    </dl>
                </li>
                <?php else: ?>
                <li class="layui-nav-item">
                    <a href="/user/login">登录</a>
                </li>
                <li class="layui-nav-item">
                    <a href="/user/register">注册</a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
    
    <!-- 轮播图 -->
    <?php if (!empty($banners)): ?>
    <div class="layui-carousel" id="banner">
        <div carousel-item>
            <?php foreach ($banners as $banner): ?>
            <div>
                <a href="<?php echo $banner['url']; ?>" target="_blank">
                    <img src="<?php echo $banner['image']; ?>" alt="<?php echo $banner['title']; ?>">
                </a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- 主要内容 -->
    <div class="layui-container main-container">
        <!-- 热门API -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>热门API</h2>
                        <a href="/api/market" class="more">查看更多 <i class="layui-icon layui-icon-right"></i></a>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space15">
                            <?php foreach ($hotApis as $api): ?>
                            <div class="layui-col-md3">
                                <div class="api-card">
                                    <div class="api-card-header">
                                        <h3><?php echo $api['name']; ?></h3>
                                        <?php if ($api['is_free']): ?>
                                        <span class="api-tag free">免费</span>
                                        <?php else: ?>
                                        <span class="api-tag paid">付费</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="api-card-body">
                                        <p class="api-desc"><?php echo mb_substr($api['description'], 0, 50); ?>...</p>
                                        <div class="api-meta">
                                            <span><i class="layui-icon layui-icon-star"></i> <?php echo $api['rating']; ?></span>
                                            <span><i class="layui-icon layui-icon-log"></i> <?php echo $api['call_count']; ?>次调用</span>
                                        </div>
                                    </div>
                                    <div class="api-card-footer">
                                        <a href="/api/detail?id=<?php echo $api['id']; ?>" class="layui-btn layui-btn-primary layui-btn-sm">查看详情</a>
                                        <a href="/api/test?id=<?php echo $api['id']; ?>" class="layui-btn layui-btn-sm">在线调试</a>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- API分类 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>API分类</h2>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space15">
                            <?php foreach ($apiCategories as $category): ?>
                            <div class="layui-col-md2">
                                <a href="/api/category?id=<?php echo $category['id']; ?>" class="category-card">
                                    <i class="layui-icon <?php echo $category['icon']; ?>"></i>
                                    <h3><?php echo $category['name']; ?></h3>
                                    <p><?php echo $category['description']; ?></p>
                                </a>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最新文章 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>最新文章</h2>
                        <a href="/article" class="more">查看更多 <i class="layui-icon layui-icon-right"></i></a>
                    </div>
                    <div class="layui-card-body">
                        <ul class="article-list">
                            <?php foreach ($latestArticles as $article): ?>
                            <li>
                                <?php if (!empty($article['thumbnail'])): ?>
                                <div class="article-thumb">
                                    <a href="/article/detail?id=<?php echo $article['id']; ?>">
                                        <img src="<?php echo $article['thumbnail']; ?>" alt="<?php echo $article['title']; ?>">
                                    </a>
                                </div>
                                <?php endif; ?>
                                <div class="article-info">
                                    <h3><a href="/article/detail?id=<?php echo $article['id']; ?>"><?php echo $article['title']; ?></a></h3>
                                    <p class="article-summary"><?php echo $article['summary']; ?></p>
                                    <div class="article-meta">
                                        <span><i class="layui-icon layui-icon-user"></i> <?php echo $article['author']; ?></span>
                                        <span><i class="layui-icon layui-icon-date"></i> <?php echo date('Y-m-d', strtotime($article['created_at'])); ?></span>
                                        <span><i class="layui-icon layui-icon-read"></i> <?php echo $article['views']; ?>次阅读</span>
                                    </div>
                                </div>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 侧边栏 -->
            <div class="layui-col-md4">
                <!-- 会员等级 -->
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>会员等级</h2>
                        <a href="/pricing" class="more">查看详情 <i class="layui-icon layui-icon-right"></i></a>
                    </div>
                    <div class="layui-card-body">
                        <ul class="vip-list">
                            <?php foreach ($vipLevels as $vip): ?>
                            <li>
                                <div class="vip-name">
                                    <?php if (!empty($vip['icon'])): ?>
                                    <img src="<?php echo $vip['icon']; ?>" alt="<?php echo $vip['name']; ?>">
                                    <?php endif; ?>
                                    <h3><?php echo $vip['name']; ?></h3>
                                </div>
                                <div class="vip-price">¥<?php echo $vip['price']; ?>/月</div>
                                <div class="vip-discount"><?php echo $vip['discount']; ?>%折扣</div>
                                <a href="/user/buy_vip?level=<?php echo $vip['level']; ?>" class="layui-btn layui-btn-sm">立即开通</a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
                
                <!-- 商家入驻 -->
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>商家入驻</h2>
                    </div>
                    <div class="layui-card-body">
                        <div class="merchant-entry">
                            <p>成为API提供商，获取额外收益</p>
                            <ul>
                                <li><i class="layui-icon layui-icon-ok-circle"></i> 发布您的API接口</li>
                                <li><i class="layui-icon layui-icon-ok-circle"></i> 获得高达90%的分成</li>
                                <li><i class="layui-icon layui-icon-ok-circle"></i> 专业的API文档生成</li>
                                <li><i class="layui-icon layui-icon-ok-circle"></i> 完善的结算系统</li>
                            </ul>
                            <a href="/merchant/register" class="layui-btn">立即入驻</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <div class="layui-footer footer">
        <div class="layui-container">
            <div class="footer-links">
                <?php foreach ($navMenus['footer'] as $menu): ?>
                <a href="<?php echo $menu['url']; ?>" target="<?php echo $menu['target']; ?>"><?php echo $menu['name']; ?></a>
                <?php endforeach; ?>
            </div>
            <div class="footer-info">
                <p><?php echo $siteConfig['copyright']; ?></p>
                <?php if (!empty($siteConfig['icp_number'])): ?>
                <p><a href="https://beian.miit.gov.cn/" target="_blank"><?php echo $siteConfig['icp_number']; ?></a></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- JS部分 -->
    <script src="/Easyweb/assets/libs/layui/layui.js"></script>
    <script>
    layui.use(['carousel', 'element'], function() {
        var carousel = layui.carousel;
        var element = layui.element;
        
        // 轮播图
        carousel.render({
            elem: '#banner',
            width: '100%',
            height: '400px',
            arrow: 'always',
            anim: 'fade'
        });
    });
    </script>
    <?php if (!empty($siteConfig['custom_js'])): ?>
    <script>
        <?php echo $siteConfig['custom_js']; ?>
    </script>
    <?php endif; ?>
</body>
</html>