<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>编辑文章 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <span id="pageTitle">添加文章</span>
            <div class="layui-btn-group" style="float: right;">
                <button id="backBtn" class="layui-btn layui-btn-primary layui-btn-sm"><i class="layui-icon">&#xe603;</i>返回</button>
            </div>
        </div>
        <div class="layui-card-body">
            <form id="articleForm" lay-filter="articleForm" class="layui-form">
                <input type="hidden" name="id" value="">
                
                <div class="layui-form-item">
                    <label class="layui-form-label">文章标题</label>
                    <div class="layui-input-block">
                        <input name="title" placeholder="请输入文章标题" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">文章分类</label>
                    <div class="layui-input-block">
                        <select name="category_id" lay-verify="required">
                            <option value="">请选择分类</option>
                        </select>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">作者</label>
                    <div class="layui-input-block">
                        <input name="author" placeholder="请输入作者" type="text" class="layui-input"/>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">封面图片</label>
                    <div class="layui-input-block">
                        <div class="layui-upload-drag" id="coverUpload">
                            <i class="layui-icon">&#xe67c;</i>
                            <p>点击上传，或将文件拖拽到此处</p>
                            <div class="layui-hide" id="coverPreview">
                                <hr>
                                <img src="" alt="封面预览" style="max-width: 300px">
                            </div>
                        </div>
                        <input type="hidden" name="cover" id="coverInput">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">摘要</label>
                    <div class="layui-input-block">
                        <textarea name="summary" placeholder="请输入文章摘要" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">文章内容</label>
                    <div class="layui-input-block">
                        <textarea id="content" name="content" style="display: none;"></textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">关键词</label>
                    <div class="layui-input-block">
                        <input name="keywords" placeholder="请输入关键词，多个关键词用逗号分隔" type="text" class="layui-input"/>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">排序号</label>
                    <div class="layui-input-block">
                        <input name="sort" placeholder="请输入排序号，数字越小越靠前" type="number" class="layui-input" value="0"/>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="发布" checked>
                        <input type="radio" name="status" value="0" title="草稿">
                    </div>
                </div>
                
                <div class="layui-form-item text-right">
                    <button class="layui-btn layui-btn-primary" type="button" id="cancelBtn">取消</button>
                    <button class="layui-btn" lay-filter="articleSubmit" lay-submit>保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'upload', 'layedit', 'admin', 'jquery'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var upload = layui.upload;
    var layedit = layui.layedit;
    var admin = layui.admin;
    
    // 获取URL参数
    var id = layui.url().search.id;
    if (id) {
        $('#pageTitle').text('编辑文章');
    }
    
    // 加载文章分类
    $.get('/admin/article/category/list_all', function(res) {
        if (res.code === 0) {
            var options = '<option value="">请选择分类</option>';
            for (var i = 0; i < res.data.length; i++) {
                options += '<option value="' + res.data[i].id + '">' + res.data[i].name + '</option>';
            }
            $('select[name="category_id"]').html(options);
            form.render('select');
            
            // 如果是编辑模式，加载文章数据
            if (id) {
                loadArticleData(id);
            }
        }
    }, 'json');
    
    // 初始化富文本编辑器
    var editIndex = layedit.build('content', {
        height: 500,
        uploadImage: {
            url: '/admin/upload/image',
            type: 'post'
        }
    });
    
    // 封面上传
    upload.render({
        elem: '#coverUpload',
        url: '/admin/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        done: function(res){
            if (res.code === 0) {
                $('#coverPreview').removeClass('layui-hide').find('img').attr('src', res.data.src);
                $('#coverInput').val(res.data.src);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }
    });
    
    // 加载文章数据
    function loadArticleData(id) {
        layer.load(2);
        $.get('/admin/article/detail', {
            id: id
        }, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                form.val('articleForm', res.data);
                
                // 设置富文本内容
                layedit.setContent(editIndex, res.data.content);
                
                // 显示封面预览
                if (res.data.cover) {
                    $('#coverPreview').removeClass('layui-hide').find('img').attr('src', res.data.cover);
                }
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 表单提交事件
    form.on('submit(articleSubmit)', function(data) {
        // 获取富文本内容
        data.field.content = layedit.getContent(editIndex);
        
        layer.load(2);
        $.post('/admin/article/save', data.field, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1}, function() {
                    location.href = '/admin/article/list';
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        
        return false;
    });
    
    // 返回按钮点击事件
    $('#backBtn, #cancelBtn').click(function() {
        location.href = '/admin/article/list';
    });
});
</script>
</body>
</html>