<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>admin风格的弹窗</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /* 表单标题加虚线 */
        .form-item-title {
            text-align: center;
            position: relative;
        }

        .form-item-title:before {
            content: "";
            position: absolute;
            border-top: 1px dashed #ccc;
            left: 40px;
            right: 40px;
            top: 8px;
            z-index: -1;
        }

        .form-item-title > span {
            background-color: white;
            padding: 0 10px;
            font-size: 13px;
            color: #666;
        }
    </style>
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">常见用法</div>
        <div class="layui-card-body layui-text">
            <div class="layui-btn-container" style="margin-top: 15px;">
                <button id="dialogBtn1" class="layui-btn layui-btn-primary">询问弹窗</button>
                <button id="dialogBtn2" class="layui-btn layui-btn-primary">输入弹窗</button>
                <button id="dialogBtn3" class="layui-btn layui-btn-primary">多行输入弹窗</button>
                <button id="dialogBtn4" class="layui-btn layui-btn-primary">基本表单弹窗</button>
                <button id="dialogBtn5" class="layui-btn layui-btn-primary">最大最小化</button>
                <button id="dialogBtn6" class="layui-btn layui-btn-primary">主题风格</button>
                <button id="dialogBtnLoad" class="layui-btn layui-btn-primary">显示loading</button>
            </div>
            <p style="margin: 5px 0 10px 0;">
                更多弹出层的使用示例请查看 <a href="http://layer.layui.com/" target="_blank">layer弹窗组件</a>
            </p>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">进阶用法</div>
        <div class="layui-card-body layui-text">
            <div class="layui-btn-container" style="margin-top: 15px;">
                <button id="dialogBtn7" class="layui-btn">表单底部按钮固定</button>
                <button id="dialogBtn8" class="layui-btn">多排并列表单</button>
                <button ew-event="open" data-type="1" data-url="page/tpl/tpl-password.html" data-window="top"
                        data-title="修改密码2" data-area="360px" class="layui-btn">无js打开弹窗
                </button>
                <button ew-event="popupRight" data-type="1" data-window="top"
                        data-success="onDemoDialog1Success" data-url="page/tpl/tpl-note.html" class="layui-btn">
                    无js右侧弹出
                </button>
            </div>
            <p style="margin: 5px 0 10px 0;">无js打开弹窗真的不需要写js，使用起来不要太爽！！！</p>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">内置功能</div>
        <div class="layui-card-body layui-text">
            <p style="margin: 10px 0 20px 0;">以下弹窗都是easyweb内部已经封装好的功能，使用只需调对应的方法即可。</p>
            <div class="layui-btn-container" style="margin-bottom: 10px;">
                <button id="dialogBtn9" class="layui-btn layui-btn-normal icon-btn">
                    <i class="layui-icon">&#xe715;</i>地图选择位置
                </button>
                <button id="dialogBtn10" class="layui-btn layui-btn-normal icon-btn">
                    <i class="layui-icon">&#xe60d;</i>裁剪图片
                </button>
                <button id="dialogBtn11" class="layui-btn layui-btn-normal icon-btn">
                    <i class="layui-icon">&#xe61d;</i>文件选择(多选)
                </button>
                <button id="dialogBtn12" class="layui-btn layui-btn-normal icon-btn">
                    <i class="layui-icon">&#xe61d;</i>文件选择(单选)
                </button>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">数据传递</div>
        <div class="layui-card-body layui-text">
            <p style="margin: 10px 0 20px 0;">admin.open方法封装了弹窗多页面之间的参数传递，使用起来很方便。</p>
            <div class="layui-btn-container" style="margin-bottom: 10px;">
                <button id="dialogBtn13" class="layui-btn layui-btn-primary">iframe弹窗传值</button>
                <button id="dialogBtn14" class="layui-btn layui-btn-primary">url方式弹窗传值</button>
            </div>
        </div>
    </div>
</div>

<!-- 固定底部按钮 -->
<script type="text/html" id="dialogEditDialog1">
    <form id="dialogEditForm1" lay-filter="dialogEditForm1" class="layui-form model-form no-padding">
        <input name="ptId" type="hidden"/>
        <div class="model-form-body" style="height: 310px;" onscroll="layui.admin.hideFixedEl();">
            <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">实习公司:</label>
                <div class="layui-input-block">
                    <input name="companyName" placeholder="请输入实习公司" class="layui-input"
                           lay-verify="required" lay-verType="tips" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">实习岗位:</label>
                <div class="layui-input-block">
                    <input name="jobName" placeholder="请输入实习岗位" class="layui-input"
                           lay-verify="required" lay-verType="tips" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">实习类型:</label>
                <div class="layui-input-block ew-select-fixed">
                    <select name="ptTypeId" lay-verify="required" lay-verType="tips" required>
                        <option value="">请选择</option>
                        <option value="1">顶岗实习</option>
                        <option value="2">跟岗实习</option>
                        <option value="3">毕业实习</option>
                        <option value="4">XXXX实习</option>
                        <option value="5">XXXX实习</option>
                        <option value="6">XXXX实习</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">公司地址:</label>
                <div class="layui-input-block">
                    <input name="companyAddress" placeholder="请输入公司地址" class="layui-input"
                           lay-verify="required" lay-verType="tips" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label layui-form-required" style="padding-left: 0;width: 95px">公司联系人:</label>
                <div class="layui-input-block">
                    <input name="companyContact" placeholder="请输入公司联系人" class="layui-input"
                           lay-verify="required" lay-verType="tips" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label layui-form-required">起止日期:</label>
                <div class="layui-input-block">
                    <input name="dateRange" placeholder="请选择起止日期" class="layui-input" autocomplete="off"
                           lay-verify="required" lay-verType="tips" required/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">备注:</label>
                <div class="layui-input-block">
                    <textarea name="comments" placeholder="请输入备注" class="layui-textarea" maxlength="500"></textarea>
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right model-form-footer">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
            <button class="layui-btn" lay-filter="dialogEditSubmit1" lay-submit>保存</button>
        </div>
    </form>
</script>
<!-- 双排表单 -->
<script type="text/html" id="dialogEditDialog2">
    <form id="dialogEditForm2" lay-filter="dialogEditForm2" class="layui-form layui-row model-form">
        <input name="authId" type="hidden"/>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">选择公司:</label>
                    <div class="layui-input-block">
                        <select name="companyId" lay-verType="tips" lay-verify="required" required>
                            <option value="">请选择公司</option>
                            <option value="1">公司一</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">应用名称:</label>
                    <div class="layui-input-block">
                        <input name="appName" placeholder="请输入应用名称" class="layui-input"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">价格:</label>
                    <div class="layui-input-block">
                        <input name="price" placeholder="请输入价格" type="number" class="layui-input"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">应用类型:</label>
                    <div class="layui-input-block">
                        <select name="deviceIds" lay-verType="tips" lay-verify="required" required>
                            <option value="1">Android</option>
                            <option value="2">IOS</option>
                            <option value="3">Web</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item form-item-title"><span>Android配置:</span></div>
                <div class="layui-form-item">
                    <label class="layui-form-label">包名:</label>
                    <div class="layui-input-block">
                        <input name="packageName" placeholder="请输入包名" class="layui-input"/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">SHA1:</label>
                    <div class="layui-input-block">
                        <input name="sha1" placeholder="请输入SHA1" class="layui-input"/>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item form-item-title"><span>IOS配置</span></div>
                <div class="layui-form-item">
                    <label class="layui-form-label">Bundle Identifier:</label>
                    <div class="layui-input-block">
                        <textarea name="bundle" placeholder="请输入BundleIdentifier" class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item form-item-title"><span>Web配置</span></div>
                <div class="layui-form-item">
                    <label class="layui-form-label">Referer<br/>白名单:</label>
                    <div class="layui-input-block">
                        <textarea name="referer" placeholder="请输入Referer白名单，用逗号分隔"
                                  class="layui-textarea layui-disabled" disabled></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
            <button class="layui-btn" lay-filter="dialogEditSubmit2" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'admin', 'form', 'laydate', 'fileChoose'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var admin = layui.admin;
        var form = layui.form;
        var laydate = layui.laydate;
        var fileChoose = layui.fileChoose;

        // 询问弹窗
        $('#dialogBtn1').click(function () {
            parent.layui.admin.confirm('确认删除吗？', function (index) {
                parent.layer.close(index);
            });
        });

        // 输入弹窗
        $('#dialogBtn2').click(function () {
            parent.layui.admin.prompt(function (value, index, elem) {
                parent.layer.close(index);
            });
        });

        // 多行输入
        $('#dialogBtn3').click(function () {
            parent.layui.admin.prompt({
                title: '请输入拒绝理由：',
                formType: 2
            }, function (value, index, elem) {
                parent.layer.close(index);
            });
        });

        // 表单弹窗
        $('#dialogBtn4').click(function () {
            parent.layui.admin.open({
                id: 'layer-psw',
                title: '修改密码',
                shade: .1,
                area: '360px',
                url: getProjectUrl() + 'page/tpl/tpl-password.html'
            });
        });

        // 最大化最小化
        $('#dialogBtn5').click(function () {
            parent.layui.admin.open({
                type: 2,
                title: '基础表单',
                shade: 0,
                maxmin: true,
                resize: true,
                area: ['640px', '480px'],
                content: 'https://demo.easyweb.vip/iframe/page/template/form/form-basic.html',
                success: function (layero) {
                    $(layero).attr('minleft', '250px');
                }
            });
        });

        // 主题风格
        $('#dialogBtn6').click(function () {
            $('#dialogBtn4').trigger('click');
            parent.layui.admin.popupRight({
                id: 'layer-theme',
                url: getProjectUrl() + 'page/tpl/tpl-theme.html'
            });
        });

        // 主题风格
        $('#dialogBtnLoad').click(function () {
            var loadIndex = layer.msg('请求中...', {icon: 16, shade: 0.01, time: false});
            setTimeout(function () {
                layer.close(loadIndex);
                layer.msg('请求完成', {icon: 1});
                setTimeout(function () {
                    layer.msg('请求失败', {icon: 5, anim: 6});
                }, 1500);
            }, 2000);
        });

        // 固定底部按钮
        $('#dialogBtn7').click(function () {
            admin.open({
                type: 1,
                title: '添加学生实习信息',
                fixed: true,
                offset: 'auto',
                content: $('#dialogEditDialog1').html(),
                success: function (layero, dIndex) {
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    // 时间范围选择
                    laydate.render({
                        elem: '#dialogEditForm1 input[name="dateRange"]',
                        range: true,
                        trigger: 'click'
                    });
                    form.render('select', 'dialogEditForm1');
                    // 表单提交事件
                    form.on('submit(dialogEditSubmit1)', function (data) {
                        layer.msg(JSON.stringify(data.field), {icon: 1});
                        return false;
                    });

                }
            });
        });

        // 双排表单
        $('#dialogBtn8').click(function () {
            admin.open({
                type: 1,
                title: '添加授权',
                area: '745px',
                content: $('#dialogEditDialog2').html(),
                success: function (layero, dIndex) {
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                    form.render('select', 'dialogEditForm2');
                }
            });
        });

        // 无js打开弹窗成功的回调
        window.onDemoDialog1Success = function () {
            parent.layer.msg('弹窗被成功打开了', {icon: 1});
        };

        // 地图选择位置
        $('#dialogBtn9').click(function () {
            admin.chooseLocation({
                needCity: true,
                onSelect: function (res) {
                    layer.msg(JSON.stringify(res), {icon: 1});
                }
            });
        });

        // 裁剪图片弹窗
        $('#dialogBtn10').click(function () {
            admin.cropImg({
                imgSrc: '../../../assets/images/head.jpg',
                onCrop: function (res) {
                    layer.msg('<img src="' + res + '" width="160px" height="160px" alt=""/>', {offset: '30px'});
                }
            });
        });

        // 文件选择(多选)
        $('#dialogBtn11').click(function () {
            fileChoose.open({
                fileUrl: '',
                listUrl: '../../../json/files.json',
                num: 3,
                dialog: {
                    offset: '60px'
                },
                onChoose: function (urls) {
                    layer.msg('你选择了：' + JSON.stringify(urls), {icon: 1});
                }
            });
        });

        // 文件选择(单选)
        $('#dialogBtn12').click(function () {
            fileChoose.open({
                fileUrl: '',
                listUrl: '../../../json/files.json',
                num: 1,
                dialog: {
                    offset: '60px'
                },
                onChoose: function (urls) {
                    layer.msg('你选择了：' + JSON.stringify(urls), {icon: 1});
                }
            });
        });

        // iframe形式弹窗传值
        $('#dialogBtn13').click(function () {
            admin.open({
                type: 2,
                title: 'iframe弹窗传值',
                content: 'dialog-iframe.html',
                area: ['360px', '230px'],
                data: {
                    name: '李白',
                    sex: '男'
                }
            });
        });

        // url形式弹窗传值
        $('#dialogBtn14').click(function () {
            admin.open({
                title: 'url形式弹窗传值',
                url: 'dialog-url.html',
                area: '360px',
                data: {
                    name: '妲己',
                    sex: '女'
                },
                tpl: true
            });
        });

    });
</script>
</body>
</html>