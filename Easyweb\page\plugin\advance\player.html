<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>视频播放器</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=314"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        .xgplayer-skin-default .xgplayer-start, .xgplayer-skin-default .xgplayer-replay-svg {
            background: rgba(255, 255, 255, .3) !important;
        }

        .xgplayer-skin-default .xgplayer-pip {
            top: 11px !important;
            margin-left: 5px !important;
        }

        .xgplayer-pip-drag {
            outline: none;
        }

        .xgplayer-pip-drag .drag-handle {
            width: 100%;
            color: #fff;
            padding-left: 5px;
        }
    </style>
</head>
<body>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">效果演示</div>
                <div class="layui-card-body" style="padding: 5px;">
                    <div id="demoVideo"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">开启弹幕</div>
                <div class="layui-card-body" style="padding: 5px;">
                    <div id="demoVideo2"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=314"></script>
<script>
    layui.use(['Player'], function () {
        var Player = layui.Player;

        // 视频播放器
        var player = new Player({
            id: "demoVideo",
            url: "//s1.pstatp.com/cdn/expire-1-M/byted-player-videos/1.0.0/xgplayer-demo.mp4",  // 视频地址
            poster: "https://imgcache.qq.com/open_proj/proj_qcloud_v2/gateway/solution/general-video/css/img/scene/1.png",  // 封面
            fluid: true,  // 宽度100%
            playbackRate: [0.5, 1, 1.5, 2],  // 开启倍速播放
            pip: true,  // 开启画中画
            lang: 'zh-cn'
        });

        // 开启弹幕
        var dmStyle = {
            color: '#ffcd08', fontSize: '20px'
        };
        var player = new Player({
            id: "demoVideo2",
            url: "http://demo.htmleaf.com/1704/************/video/2.mp4",  // 视频地址
            autoplay: false,
            fluid: true,  // 宽度100%
            lang: 'zh-cn',
            danmu: {
                comments: [
                    {id: '1', start: 0, txt: '空降', color: true, style: dmStyle, duration: 15000},
                    {id: '2', start: 1500, txt: '前方高能', color: true, style: dmStyle, duration: 15000},
                    {id: '3', start: 3500, txt: '弹幕护体', color: true, style: dmStyle, duration: 15000},
                    {id: '4', start: 4500, txt: '弹幕护体', color: true, style: dmStyle, duration: 15000},
                    {id: '5', start: 6000, txt: '前方高能', color: true, style: dmStyle, duration: 15000},
                    {id: '6', start: 8500, txt: '弹幕护体', color: true, style: dmStyle, duration: 15000},
                    {id: '7', start: 10000, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '8', start: 12500, txt: '前方高能', color: true, style: dmStyle, duration: 15000},
                    {id: '9', start: 15500, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '10', start: 16500, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '11', start: 18000, txt: '关弹幕，保智商', color: true, style: dmStyle, duration: 15000},
                    {id: '12', start: 20500, txt: '关弹幕，保智商', color: true, style: dmStyle, duration: 15000},
                    {id: '13', start: 22000, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '14', start: 25500, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '15', start: 26000, txt: '前方高能', color: true, style: dmStyle, duration: 15000}
                ]
            }
        });

    });
</script>
</body>
</html>