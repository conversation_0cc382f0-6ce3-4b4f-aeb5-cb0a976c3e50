<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - API管理系统</title>
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="icon" href="../Easyweb/assets/images/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 900px;
            max-width: 90vw;
            min-height: 700px;
            display: flex;
        }
        
        .register-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .register-left::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }
        
        .register-left-content {
            position: relative;
            z-index: 1;
        }
        
        .register-left h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .register-left p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        .register-features {
            list-style: none;
        }
        
        .register-features li {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .register-features li i {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        .register-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow-y: auto;
        }
        
        .register-form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .register-form-header h2 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #333;
        }
        
        .register-form-header p {
            color: #666;
            font-size: 1rem;
        }
        
        .register-form {
            max-width: 400px;
            margin: 0 auto;
            width: 100%;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s;
            background: #f8f9fa;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-input.error {
            border-color: #dc3545;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .captcha-input {
            flex: 1;
        }
        
        .captcha-img {
            width: 120px;
            height: 40px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .captcha-img:hover {
            border-color: #667eea;
        }
        
        .agreement {
            margin-bottom: 25px;
        }
        
        .agreement label {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            font-weight: normal;
            cursor: pointer;
        }
        
        .agreement input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin-top: 2px;
        }
        
        .agreement a {
            color: #667eea;
            text-decoration: none;
        }
        
        .agreement a:hover {
            text-decoration: underline;
        }
        
        .register-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .login-link {
            text-align: center;
            color: #666;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        
        .password-strength {
            margin-top: 5px;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .password-strength-bar {
            height: 100%;
            transition: all 0.3s;
            border-radius: 2px;
        }
        
        .strength-weak { background: #dc3545; width: 33%; }
        .strength-medium { background: #ffc107; width: 66%; }
        .strength-strong { background: #28a745; width: 100%; }
        
        .password-strength-text {
            font-size: 0.8rem;
            margin-top: 3px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .register-container {
                flex-direction: column;
                width: 95vw;
                min-height: auto;
            }
            
            .register-left {
                padding: 40px 30px;
                text-align: center;
            }
            
            .register-left h1 {
                font-size: 2rem;
            }
            
            .register-right {
                padding: 40px 30px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <!-- 左侧介绍 -->
        <div class="register-left">
            <div class="register-left-content">
                <h1>加入我们</h1>
                <p>创建您的账户，开始使用强大的API管理平台</p>
                
                <ul class="register-features">
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        免费注册，立即开始
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        丰富的API接口资源
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        专业的技术支持
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        安全可靠的数据保护
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 右侧注册表单 -->
        <div class="register-right">
            <div class="register-form-header">
                <h2>用户注册</h2>
                <p>请填写以下信息创建账户</p>
            </div>
            
            <form class="register-form" id="registerForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" class="form-input" placeholder="请输入用户名" required>
                        <div class="error-message" id="usernameError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">邮箱</label>
                        <input type="email" id="email" name="email" class="form-input" placeholder="请输入邮箱" required>
                        <div class="error-message" id="emailError"></div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">手机号</label>
                        <input type="tel" id="phone" name="phone" class="form-input" placeholder="请输入手机号" required>
                        <div class="error-message" id="phoneError"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="realname">真实姓名</label>
                        <input type="text" id="realname" name="realname" class="form-input" placeholder="请输入真实姓名">
                        <div class="error-message" id="realnameError"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码（6-20位）" required>
                    <div class="password-strength">
                        <div class="password-strength-bar" id="passwordStrengthBar"></div>
                    </div>
                    <div class="password-strength-text" id="passwordStrengthText"></div>
                    <div class="error-message" id="passwordError"></div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" class="form-input" placeholder="请再次输入密码" required>
                    <div class="error-message" id="confirmPasswordError"></div>
                </div>
                
                <div class="form-group">
                    <label for="captcha">验证码</label>
                    <div class="captcha-group">
                        <div class="captcha-input">
                            <input type="text" id="captcha" name="captcha" class="form-input" placeholder="请输入验证码" required>
                            <div class="error-message" id="captchaError"></div>
                        </div>
                        <img src="admin/captcha.php" alt="验证码" class="captcha-img" id="captchaImg" onclick="refreshCaptcha()" title="点击刷新验证码">
                    </div>
                </div>
                
                <div class="agreement">
                    <label>
                        <input type="checkbox" id="agreement" name="agreement" required>
                        <span>我已阅读并同意 <a href="terms.html" target="_blank">用户协议</a> 和 <a href="privacy.html" target="_blank">隐私政策</a></span>
                    </label>
                    <div class="error-message" id="agreementError"></div>
                </div>
                
                <button type="submit" class="register-btn" id="registerBtn">
                    <span id="registerBtnText">立即注册</span>
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" id="registerLoading" style="display: none;"></i>
                </button>
                
                <div class="login-link">
                    已有账户？<a href="login.html">立即登录</a>
                </div>
            </form>
        </div>
    </div>

    <script src="../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        // 表单验证和提交
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // 清除之前的错误信息
            clearErrors();
            
            // 验证表单
            if (!validateForm(data)) {
                return;
            }
            
            // 显示加载状态
            showLoading(true);
            
            // 模拟注册请求
            setTimeout(() => {
                // 模拟注册成功
                alert('注册成功！请登录您的账户。');
                window.location.href = 'login.html';
            }, 2000);
        });
        
        // 表单验证
        function validateForm(data) {
            let hasError = false;
            
            // 用户名验证
            if (!data.username) {
                showError('usernameError', '请输入用户名');
                hasError = true;
            } else if (data.username.length < 3 || data.username.length > 20) {
                showError('usernameError', '用户名长度应为3-20位');
                hasError = true;
            } else if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
                showError('usernameError', '用户名只能包含字母、数字和下划线');
                hasError = true;
            }
            
            // 邮箱验证
            if (!data.email) {
                showError('emailError', '请输入邮箱');
                hasError = true;
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
                showError('emailError', '请输入有效的邮箱地址');
                hasError = true;
            }
            
            // 手机号验证
            if (!data.phone) {
                showError('phoneError', '请输入手机号');
                hasError = true;
            } else if (!/^1[3-9]\d{9}$/.test(data.phone)) {
                showError('phoneError', '请输入有效的手机号');
                hasError = true;
            }
            
            // 密码验证
            if (!data.password) {
                showError('passwordError', '请输入密码');
                hasError = true;
            } else if (data.password.length < 6 || data.password.length > 20) {
                showError('passwordError', '密码长度应为6-20位');
                hasError = true;
            }
            
            // 确认密码验证
            if (!data.confirmPassword) {
                showError('confirmPasswordError', '请确认密码');
                hasError = true;
            } else if (data.password !== data.confirmPassword) {
                showError('confirmPasswordError', '两次输入的密码不一致');
                hasError = true;
            }
            
            // 验证码验证
            if (!data.captcha) {
                showError('captchaError', '请输入验证码');
                hasError = true;
            } else if (data.captcha.length !== 4) {
                showError('captchaError', '验证码长度不正确');
                hasError = true;
            }
            
            // 协议验证
            if (!data.agreement) {
                showError('agreementError', '请阅读并同意用户协议和隐私政策');
                hasError = true;
            }
            
            return !hasError;
        }
        
        // 密码强度检测
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrengthBar');
            const strengthText = document.getElementById('passwordStrengthText');
            
            if (!password) {
                strengthBar.className = 'password-strength-bar';
                strengthText.textContent = '';
                return;
            }
            
            let strength = 0;
            let strengthLabel = '';
            
            // 长度检查
            if (password.length >= 6) strength++;
            if (password.length >= 10) strength++;
            
            // 复杂度检查
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^a-zA-Z0-9]/.test(password)) strength++;
            
            if (strength <= 2) {
                strengthBar.className = 'password-strength-bar strength-weak';
                strengthLabel = '弱';
                strengthText.style.color = '#dc3545';
            } else if (strength <= 4) {
                strengthBar.className = 'password-strength-bar strength-medium';
                strengthLabel = '中';
                strengthText.style.color = '#ffc107';
            } else {
                strengthBar.className = 'password-strength-bar strength-strong';
                strengthLabel = '强';
                strengthText.style.color = '#28a745';
            }
            
            strengthText.textContent = `密码强度：${strengthLabel}`;
        });
        
        // 刷新验证码
        function refreshCaptcha() {
            const captchaImg = document.getElementById('captchaImg');
            captchaImg.src = 'admin/captcha.php?' + new Date().getTime();
        }
        
        // 显示错误信息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            const inputElement = errorElement.previousElementSibling;
            
            errorElement.textContent = message;
            if (inputElement && inputElement.classList.contains('form-input')) {
                inputElement.classList.add('error');
            }
        }
        
        // 清除错误信息
        function clearErrors() {
            const errorElements = document.querySelectorAll('.error-message');
            const inputElements = document.querySelectorAll('.form-input');
            
            errorElements.forEach(element => {
                element.textContent = '';
            });
            
            inputElements.forEach(element => {
                element.classList.remove('error');
            });
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            const registerBtn = document.getElementById('registerBtn');
            const registerBtnText = document.getElementById('registerBtnText');
            const registerLoading = document.getElementById('registerLoading');
            
            if (show) {
                registerBtn.disabled = true;
                registerBtnText.textContent = '注册中...';
                registerLoading.style.display = 'inline-block';
            } else {
                registerBtn.disabled = false;
                registerBtnText.textContent = '立即注册';
                registerLoading.style.display = 'none';
            }
        }
        
        // 输入框焦点事件
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.classList.remove('error');
                const errorElement = this.parentNode.querySelector('.error-message');
                if (errorElement) {
                    errorElement.textContent = '';
                }
            });
        });
    </script>
</body>
</html>