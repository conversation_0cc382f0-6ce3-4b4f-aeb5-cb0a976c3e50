<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家管理 - API管理系统</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
    <link rel="icon" href="../../Easyweb/assets/images/favicon.ico">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            line-height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 220px;
            background: #2f3349;
            overflow-y: auto;
            z-index: 999;
        }
        
        .admin-main {
            margin-left: 220px;
            margin-top: 60px;
            padding: 20px;
            min-height: calc(100vh - 60px);
            background: #f5f5f5;
        }
        
        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-menu li {
            border-bottom: 1px solid #3a3f5c;
        }
        
        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: #b8c5d6;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        
        .nav-menu i {
            margin-right: 10px;
            width: 16px;
        }
        
        .user-info {
            float: right;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            vertical-align: middle;
            margin-right: 10px;
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .status-pending { background: #fa8c16; }
        .status-active { background: #52c41a; }
        .status-suspended { background: #f5222d; }
        .status-rejected { background: #8c8c8c; }
        
        .level-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .level-bronze { background: #cd7f32; }
        .level-silver { background: #c0c0c0; }
        .level-gold { background: #ffd700; }
        .level-diamond { background: #b9f2ff; color: #333; }
        
        .merchant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-item {
            text-align: center;
        }
        
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="admin-header">
        <div style="float: left;">
            <h2 style="margin: 0; font-size: 18px;">
                <i class="layui-icon layui-icon-shop"></i>
                商家管理
            </h2>
        </div>
        <div class="user-info">
            <img src="../../Easyweb/assets/images/head.jpg" alt="头像" class="user-avatar">
            <span id="adminName">管理员</span>
            <a href="javascript:;" onclick="logout()" style="color: white; margin-left: 15px;">
                <i class="layui-icon layui-icon-logout"></i> 退出
            </a>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="admin-sidebar">
        <ul class="nav-menu">
            <li>
                <a href="dashboard.html">
                    <i class="layui-icon layui-icon-home"></i>
                    仪表板
                </a>
            </li>
            <li>
                <a href="api-list.html">
                    <i class="layui-icon layui-icon-api"></i>
                    API管理
                </a>
            </li>
            <li>
                <a href="user-list.html">
                    <i class="layui-icon layui-icon-user"></i>
                    用户管理
                </a>
            </li>
            <li>
                <a href="merchant-list.html" class="active">
                    <i class="layui-icon layui-icon-shop"></i>
                    商家管理
                </a>
            </li>
            <li>
                <a href="order-list.html">
                    <i class="layui-icon layui-icon-dollar"></i>
                    订单管理
                </a>
            </li>
            <li>
                <a href="finance.html">
                    <i class="layui-icon layui-icon-rmb"></i>
                    财务管理
                </a>
            </li>
            <li>
                <a href="admin-list.html">
                    <i class="layui-icon layui-icon-username"></i>
                    管理员
                </a>
            </li>
            <li>
                <a href="role-list.html">
                    <i class="layui-icon layui-icon-group"></i>
                    角色权限
                </a>
            </li>
            <li>
                <a href="system-config.html">
                    <i class="layui-icon layui-icon-set"></i>
                    系统配置
                </a>
            </li>
            <li>
                <a href="logs.html">
                    <i class="layui-icon layui-icon-file"></i>
                    系统日志
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="admin-main">
        <!-- 统计卡片 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="totalMerchants">0</div>
                        <div class="stats-label">总商家数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="activeMerchants">0</div>
                        <div class="stats-label">活跃商家</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="pendingMerchants">0</div>
                        <div class="stats-label">待审核</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="totalRevenue">¥0</div>
                        <div class="stats-label">总收入</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form class="layui-form" lay-filter="searchForm">
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-md3">
                        <input type="text" name="company_name" placeholder="公司名称" class="layui-input">
                    </div>
                    <div class="layui-col-md2">
                        <input type="text" name="contact_name" placeholder="联系人" class="layui-input">
                    </div>
                    <div class="layui-col-md2">
                        <select name="level">
                            <option value="">商家等级</option>
                            <option value="1">铜牌商家</option>
                            <option value="2">银牌商家</option>
                            <option value="3">金牌商家</option>
                            <option value="4">钻石商家</option>
                        </select>
                    </div>
                    <div class="layui-col-md2">
                        <select name="status">
                            <option value="">状态</option>
                            <option value="0">待审核</option>
                            <option value="1">正常</option>
                            <option value="2">暂停</option>
                            <option value="3">拒绝</option>
                        </select>
                    </div>
                    <div class="layui-col-md3">
                        <button type="submit" class="layui-btn" lay-submit lay-filter="search">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="showAddForm()">
                            <i class="layui-icon layui-icon-add-1"></i> 新增商家
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 商家列表 -->
        <div class="content-card">
            <div class="layui-row" style="margin-bottom: 15px;">
                <div class="layui-col-md6">
                    <h3 style="margin: 0;">商家列表</h3>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <button class="layui-btn layui-btn-sm" onclick="batchOperation('activate')">批量激活</button>
                    <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="batchOperation('suspend')">批量暂停</button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="batchOperation('delete')">批量删除</button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="exportMerchants()">导出数据</button>
                </div>
            </div>
            
            <table class="layui-table" lay-filter="merchantTable">
                <thead>
                    <tr>
                        <th lay-data="{type:'checkbox', fixed: 'left'}"></th>
                        <th lay-data="{field:'id', width:80, sort: true}">ID</th>
                        <th lay-data="{field:'company_name', width:200}">公司名称</th>
                        <th lay-data="{field:'contact_name', width:120}">联系人</th>
                        <th lay-data="{field:'contact_phone', width:130}">联系电话</th>
                        <th lay-data="{field:'level', width:100, align:'center'}">等级</th>
                        <th lay-data="{field:'balance', width:120, align:'right'}">余额</th>
                        <th lay-data="{field:'total_revenue', width:120, align:'right'}">总收入</th>
                        <th lay-data="{field:'api_count', width:80, align:'center'}">API数</th>
                        <th lay-data="{field:'status', width:80, align:'center'}">状态</th>
                        <th lay-data="{field:'created_at', width:160, sort: true}">创建时间</th>
                        <th lay-data="{fixed: 'right', width:200, align:'center'}">操作</th>
                    </tr>
                </thead>
                <tbody id="merchantTableBody">
                    <tr>
                        <td colspan="12" style="text-align: center; padding: 50px; color: #999;">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                            <br>加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div id="pagination"></div>
        </div>
    </div>

    <!-- 添加/编辑商家弹窗 -->
    <div id="merchantFormModal" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="merchantForm">
            <input type="hidden" name="id" id="merchantId">
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">公司名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="company_name" required lay-verify="required" placeholder="请输入公司名称" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系人</label>
                        <div class="layui-input-block">
                            <input type="text" name="contact_name" required lay-verify="required" placeholder="请输入联系人姓名" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系电话</label>
                        <div class="layui-input-block">
                            <input type="text" name="contact_phone" required lay-verify="required|phone" placeholder="请输入联系电话" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系邮箱</label>
                        <div class="layui-input-block">
                            <input type="email" name="contact_email" required lay-verify="required|email" placeholder="请输入联系邮箱" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">营业执照</label>
                <div class="layui-input-block">
                    <input type="text" name="business_license" placeholder="请输入营业执照号码" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">公司地址</label>
                <div class="layui-input-block">
                    <textarea name="address" placeholder="请输入公司地址" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">商家等级</label>
                        <div class="layui-input-block">
                            <select name="level">
                                <option value="1">铜牌商家</option>
                                <option value="2">银牌商家</option>
                                <option value="3">金牌商家</option>
                                <option value="4">钻石商家</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">佣金率(%)</label>
                        <div class="layui-input-block">
                            <input type="number" name="commission_rate" step="0.01" min="0" max="100" value="10" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
                <button type="submit" class="layui-btn" lay-submit lay-filter="submitMerchant">确定</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </form>
    </div>

    <!-- 商家详情弹窗 -->
    <div id="merchantDetailModal" style="display: none; padding: 20px;">
        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">基本信息</li>
                <li>API列表</li>
                <li>收入统计</li>
                <li>余额记录</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div id="merchantBasicInfo"></div>
                </div>
                <div class="layui-tab-item">
                    <div id="merchantApiList"></div>
                </div>
                <div class="layui-tab-item">
                    <div id="merchantRevenueStats"></div>
                </div>
                <div class="layui-tab-item">
                    <div id="merchantBalanceHistory"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核商家弹窗 -->
    <div id="reviewModal" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="reviewForm">
            <input type="hidden" name="merchant_id" id="reviewMerchantId">
            
            <div class="layui-form-item">
                <label class="layui-form-label">审核结果</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="通过" checked>
                    <input type="radio" name="status" value="3" title="拒绝">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">审核意见</label>
                <div class="layui-input-block">
                    <textarea name="reason" placeholder="请输入审核意见" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
                <button type="submit" class="layui-btn" lay-submit lay-filter="submitReview">提交审核</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </form>
    </div>

    <script src="../../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'laypage', 'element'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var laypage = layui.laypage;
            var element = layui.element;
            
            var currentPage = 1;
            var pageSize = 20;
            var currentMerchantId = null;
            
            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                loadMerchantList();
                loadStats();
            });
            
            // 加载商家列表
            function loadMerchantList(page = 1) {
                currentPage = page;
                
                // 模拟数据
                var mockData = {
                    data: [
                        {
                            id: 1,
                            company_name: '北京科技有限公司',
                            contact_name: '张三',
                            contact_phone: '***********',
                            level: 3,
                            level_text: '金牌商家',
                            balance: '5680.50',
                            total_revenue: '25680.00',
                            api_count: 15,
                            status: 1,
                            status_text: '正常',
                            created_at: '2024-01-01 10:00:00'
                        },
                        {
                            id: 2,
                            company_name: '上海数据服务公司',
                            contact_name: '李四',
                            contact_phone: '13900139000',
                            level: 2,
                            level_text: '银牌商家',
                            balance: '3250.00',
                            total_revenue: '12500.00',
                            api_count: 8,
                            status: 1,
                            status_text: '正常',
                            created_at: '2024-01-02 14:30:00'
                        },
                        {
                            id: 3,
                            company_name: '深圳创新科技',
                            contact_name: '王五',
                            contact_phone: '13700137000',
                            level: 1,
                            level_text: '铜牌商家',
                            balance: '1200.00',
                            total_revenue: '5600.00',
                            api_count: 3,
                            status: 0,
                            status_text: '待审核',
                            created_at: '2024-01-03 09:15:00'
                        }
                    ],
                    total: 3,
                    page: page,
                    per_page: pageSize,
                    total_pages: 1
                };
                
                renderMerchantTable(mockData.data);
                renderPagination(mockData);
            }
            
            // 渲染商家表格
            function renderMerchantTable(data) {
                var html = '';
                
                if (data.length === 0) {
                    html = '<tr><td colspan="12" style="text-align: center; padding: 50px; color: #999;">暂无数据</td></tr>';
                } else {
                    data.forEach(function(item) {
                        var statusClass = 'status-' + ['pending', 'active', 'suspended', 'rejected'][item.status];
                        var statusBadge = '<span class="status-badge ' + statusClass + '">' + item.status_text + '</span>';
                        
                        var levelClass = 'level-' + ['', 'bronze', 'silver', 'gold', 'diamond'][item.level];
                        var levelBadge = '<span class="level-badge ' + levelClass + '">' + item.level_text + '</span>';
                        
                        html += '<tr>';
                        html += '<td><input type="checkbox" name="ids" value="' + item.id + '" lay-skin="primary"></td>';
                        html += '<td>' + item.id + '</td>';
                        html += '<td>' + item.company_name + '</td>';
                        html += '<td>' + item.contact_name + '</td>';
                        html += '<td>' + item.contact_phone + '</td>';
                        html += '<td>' + levelBadge + '</td>';
                        html += '<td>¥' + item.balance + '</td>';
                        html += '<td>¥' + item.total_revenue + '</td>';
                        html += '<td>' + item.api_count + '</td>';
                        html += '<td>' + statusBadge + '</td>';
                        html += '<td>' + item.created_at + '</td>';
                        html += '<td>';
                        html += '<button class="layui-btn layui-btn-xs" onclick="showMerchantDetail(' + item.id + ')">详情</button>';
                        if (item.status === 0) {
                            html += '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="reviewMerchant(' + item.id + ')">审核</button>';
                        }
                        html += '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="showEditForm(' + item.id + ')">编辑</button>';
                        html += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteMerchant(' + item.id + ')">删除</button>';
                        html += '</td>';
                        html += '</tr>';
                    });
                }
                
                document.getElementById('merchantTableBody').innerHTML = html;
                form.render('checkbox');
            }
            
            // 渲染分页
            function renderPagination(data) {
                laypage.render({
                    elem: 'pagination',
                    count: data.total,
                    curr: data.page,
                    limit: data.per_page,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function(obj, first) {
                        if (!first) {
                            loadMerchantList(obj.curr);
                        }
                    }
                });
            }
            
            // 加载统计数据
            function loadStats() {
                // 模拟统计数据
                document.getElementById('totalMerchants').textContent = '156';
                document.getElementById('activeMerchants').textContent = '128';
                document.getElementById('pendingMerchants').textContent = '12';
                document.getElementById('totalRevenue').textContent = '¥1,256,890';
            }
            
            // 搜索表单提交
            form.on('submit(search)', function(data) {
                loadMerchantList(1);
                return false;
            });
            
            // 显示添加表单
            window.showAddForm = function() {
                document.getElementById('merchantId').value = '';
                document.getElementById('merchantFormModal').querySelector('form').reset();
                
                layer.open({
                    type: 1,
                    title: '添加商家',
                    content: document.getElementById('merchantFormModal'),
                    area: ['700px', '500px'],
                    success: function() {
                        form.render();
                    }
                });
            };
            
            // 显示编辑表单
            window.showEditForm = function(id) {
                // 模拟获取商家详情
                var merchantData = {
                    id: id,
                    company_name: '北京科技有限公司',
                    contact_name: '张三',
                    contact_phone: '***********',
                    contact_email: '<EMAIL>',
                    business_license: '91110000123456789X',
                    address: '北京市朝阳区xxx路xxx号',
                    level: 3,
                    commission_rate: 6
                };
                
                // 填充表单
                document.getElementById('merchantId').value = merchantData.id;
                var form_elem = document.getElementById('merchantFormModal').querySelector('form');
                Object.keys(merchantData).forEach(function(key) {
                    var input = form_elem.querySelector('[name="' + key + '"]');
                    if (input) {
                        input.value = merchantData[key];
                    }
                });
                
                layer.open({
                    type: 1,
                    title: '编辑商家',
                    content: document.getElementById('merchantFormModal'),
                    area: ['700px', '500px'],
                    success: function() {
                        form.render();
                    }
                });
            };
            
            // 商家表单提交
            form.on('submit(submitMerchant)', function(data) {
                var loadIndex = layer.load(2);
                
                // 模拟提交
                setTimeout(function() {
                    layer.close(loadIndex);
                    layer.msg('操作成功', {icon: 1}, function() {
                        layer.closeAll();
                        loadMerchantList(currentPage);
                    });
                }, 1000);
                
                return false;
            });
            
            // 显示商家详情
            window.showMerchantDetail = function(id) {
                currentMerchantId = id;
                
                // 模拟商家详情数据
                var merchantDetail = {
                    id: id,
                    company_name: '北京科技有限公司',
                    contact_name: '张三',
                    contact_phone: '***********',
                    contact_email: '<EMAIL>',
                    level_text: '金牌商家',
                    status_text: '正常',
                    balance: '5680.50',
                    total_revenue: '25680.00',
                    api_count: 15,
                    created_at: '2024-01-01 10:00:00'
                };
                
                var basicInfoHtml = '<div class="layui-row layui-col-space15">';
                basicInfoHtml += '<div class="layui-col-md6">';
                basicInfoHtml += '<p><strong>公司名称：</strong>' + merchantDetail.company_name + '</p>';
                basicInfoHtml += '<p><strong>联系人：</strong>' + merchantDetail.contact_name + '</p>';
                basicInfoHtml += '<p><strong>联系电话：</strong>' + merchantDetail.contact_phone + '</p>';
                basicInfoHtml += '<p><strong>联系邮箱：</strong>' + merchantDetail.contact_email + '</p>';
                basicInfoHtml += '</div>';
                basicInfoHtml += '<div class="layui-col-md6">';
                basicInfoHtml += '<p><strong>商家等级：</strong>' + merchantDetail.level_text + '</p>';
                basicInfoHtml += '<p><strong>状态：</strong>' + merchantDetail.status_text + '</p>';
                basicInfoHtml += '<p><strong>余额：</strong>¥' + merchantDetail.balance + '</p>';
                basicInfoHtml += '<p><strong>总收入：</strong>¥' + merchantDetail.total_revenue + '</p>';
                basicInfoHtml += '</div>';
                basicInfoHtml += '</div>';
                
                document.getElementById('merchantBasicInfo').innerHTML = basicInfoHtml;
                
                layer.open({
                    type: 1,
                    title: '商家详情 - ' + merchantDetail.company_name,
                    content: document.getElementById('merchantDetailModal'),
                    area: ['800px', '600px'],
                    success: function() {
                        element.render();
                    }
                });
            };
            
            // 审核商家
            window.reviewMerchant = function(id) {
                document.getElementById('reviewMerchantId').value = id;
                
                layer.open({
                    type: 1,
                    title: '审核商家',
                    content: document.getElementById('reviewModal'),
                    area: ['500px', '300px'],
                    success: function() {
                        form.render();
                    }
                });
            };
            
            // 审核表单提交
            form.on('submit(submitReview)', function(data) {
                var loadIndex = layer.load(2);
                
                setTimeout(function() {
                    layer.close(loadIndex);
                    layer.msg('审核完成', {icon: 1}, function() {
                        layer.closeAll();
                        loadMerchantList(currentPage);
                    });
                }, 1000);
                
                return false;
            });
            
            // 删除商家
            window.deleteMerchant = function(id) {
                layer.confirm('确定要删除这个商家吗？删除后不可恢复！', {
                    icon: 3,
                    title: '警告'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('商家删除成功', {icon: 1});
                        loadMerchantList(currentPage);
                    }, 1000);
                });
            };
            
            // 批量操作
            window.batchOperation = function(action) {
                var checkboxes = document.querySelectorAll('input[name="ids"]:checked');
                if (checkboxes.length === 0) {
                    layer.msg('请选择要操作的商家', {icon: 2});
                    return;
                }
                
                var ids = [];
                checkboxes.forEach(function(checkbox) {
                    ids.push(checkbox.value);
                });
                
                var actionText = {
                    'activate': '激活',
                    'suspend': '暂停',
                    'delete': '删除'
                };
                
                layer.confirm('确定要批量' + actionText[action] + '选中的商家吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('批量操作成功', {icon: 1});
                        loadMerchantList(currentPage);
                    }, 1000);
                });
            };
            
            // 导出商家数据
            window.exportMerchants = function() {
                layer.msg('正在导出数据...', {icon: 16, time: 2000});
                // 这里可以实现实际的导出功能
            };
            
            // 退出登录
            window.logout = function() {
                layer.confirm('确定要退出登录吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    layer.msg('退出成功', {icon: 1}, function() {
                        location.href = 'index.html';
                    });
                });
            };
        });
    </script>
</body>
</html>
