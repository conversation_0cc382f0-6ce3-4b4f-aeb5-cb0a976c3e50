<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="../../Easyweb/assets/images/favicon.ico" rel="icon">
    <title>API商业系统 - 管理后台</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css?v=318">
    <link rel="stylesheet" href="../../Easyweb/assets/module/icon/iconfont.css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body class="layui-layout-body">

<!-- 整体框架 -->
<div class="layui-layout layui-layout-admin">

    <!-- 头部 -->
    <div class="layui-header">
        <div class="layui-logo">
            <img src="../../Easyweb/assets/images/logo.png" alt="">
            <cite>API商业系统</cite>
        </div>
        <ul class="layui-nav layui-layout-left">
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="flexible" title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="refresh" title="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
            </li>
        </ul>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="message" title="消息">
                    <i class="layui-icon layui-icon-notice"></i>
                    <span class="layui-badge-dot"></span>
                </a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="note" title="便签"><i class="layui-icon layui-icon-note"></i></a>
            </li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect>
                <a ew-event="fullScreen" title="全屏"><i class="layui-icon layui-icon-screen-full"></i></a>
            </li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect>
                <a ew-event="lockScreen" title="锁屏"><i class="layui-icon layui-icon-password"></i></a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a>
                    <img src="../../Easyweb/assets/images/head.jpg" class="layui-nav-img">
                    <cite id="admin-name">管理员</cite>
                </a>
                <dl class="layui-nav-child">
                    <dd lay-unselect><a ew-href="user-info.html">个人中心</a></dd>
                    <dd lay-unselect><a ew-event="psw">修改密码</a></dd>
                    <hr>
                    <dd lay-unselect><a ew-event="logout" data-url="login.html">退出</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="theme" title="主题"><i class="layui-icon layui-icon-more-vertical"></i></a>
            </li>
        </ul>
    </div>

    <!-- 侧边栏 -->
    <div class="layui-side">
        <div class="layui-side-scroll">
            <ul class="layui-nav layui-nav-tree" lay-filter="admin-side-nav" lay-shrink="all">
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-home"></i>&emsp;<cite>控制台</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/console.html">数据统计</a></dd>
                        <dd><a ew-href="pages/system-info.html">系统信息</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-template"></i>&emsp;<cite>API管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/api-list.html">API列表</a></dd>
                        <dd><a ew-href="pages/api-category.html">分类管理</a></dd>
                        <dd><a ew-href="pages/api-logs.html">调用日志</a></dd>
                        <dd><a ew-href="pages/api-debug.html">接口调试</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-user"></i>&emsp;<cite>用户管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/user-list.html">用户列表</a></dd>
                        <dd><a ew-href="pages/user-level.html">会员等级</a></dd>
                        <dd><a ew-href="pages/user-balance.html">余额管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-rmb"></i>&emsp;<cite>订单管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/order-list.html">订单列表</a></dd>
                        <dd><a ew-href="pages/payment-config.html">支付配置</a></dd>
                        <dd><a ew-href="pages/income-stats.html">收入统计</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-cart"></i>&emsp;<cite>商家管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/merchant-list.html">商家列表</a></dd>
                        <dd><a ew-href="pages/merchant-level.html">等级管理</a></dd>
                        <dd><a ew-href="pages/merchant-settlement.html">结算管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-release"></i>&emsp;<cite>内容管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/article-list.html">文章管理</a></dd>
                        <dd><a ew-href="pages/banner-list.html">轮播图管理</a></dd>
                        <dd><a ew-href="pages/navigation-list.html">导航管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-dialogue"></i>&emsp;<cite>工单管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/ticket-list.html">工单列表</a></dd>
                        <dd><a ew-href="pages/ticket-stats.html">工单统计</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-set"></i>&emsp;<cite>系统管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/system-config.html">系统配置</a></dd>
                        <dd><a ew-href="pages/logs.html">系统日志</a></dd>
                        <dd><a ew-href="pages/backup.html">数据备份</a></dd>
                        <dd><a ew-href="pages/template.html">模板管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-auz"></i>&emsp;<cite>安全管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a ew-href="pages/security-ip.html">IP黑白名单</a></dd>
                        <dd><a ew-href="pages/security-qps.html">QPS限制</a></dd>
                        <dd><a ew-href="pages/security-monitor.html">异常监控</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
    </div>

    <!-- 主体部分 -->
    <div class="layui-body"></div>

    <!-- 底部 -->
    <div class="layui-footer">
        Copyright © 2024 API商业系统 All rights reserved.
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../Easyweb/assets/js/common.js?v=318"></script>
<script>
    layui.use(['index'], function () {
        var $ = layui.jquery;
        var index = layui.index;
        
        // 默认加载页面
        index.loadHome({
            menuPath: 'pages/console.html',
            menuName: '<i class="layui-icon layui-icon-home"></i> 控制台'
        });
        
        // 获取管理员信息
        $.ajax({
            url: 'get-admin-info.php',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if (res.code === 200) {
                    $('#admin-name').text(res.data.username);
                }
            }
        });
    });
</script>
</body>
</html>
