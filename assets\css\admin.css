/* 管理后台样式 */
.admin-iframe {
    width: 100%;
    height: calc(100vh - 200px);
    border: none;
}

.page-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ball-loader {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.ball-loader span {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #1890ff;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.ball-loader span:nth-child(1) {
    left: 8px;
    animation: ball1 0.6s infinite;
}

.ball-loader span:nth-child(2) {
    left: 8px;
    animation: ball2 0.6s infinite;
}

.ball-loader span:nth-child(3) {
    left: 32px;
    animation: ball2 0.6s infinite;
}

.ball-loader span:nth-child(4) {
    left: 56px;
    animation: ball3 0.6s infinite;
}

@keyframes ball1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes ball3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}

@keyframes ball2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}
