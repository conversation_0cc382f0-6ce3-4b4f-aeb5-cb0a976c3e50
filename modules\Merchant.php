<?php
/**
 * 商家模型
 * API管理系统 - 商家数据模型
 */

require_once __DIR__ . '/../core/Model.php';

class Merchant extends Model {
    protected $table = 'merchants';
    protected $fillable = [
        'user_id', 'company_name', 'contact_name', 'contact_phone', 'contact_email',
        'business_license', 'address', 'level', 'commission_rate', 'balance',
        'total_revenue', 'api_count', 'call_count', 'status', 'verified_at'
    ];
    
    // 商家等级常量
    const LEVEL_BRONZE = 1;   // 铜牌商家
    const LEVEL_SILVER = 2;   // 银牌商家
    const LEVEL_GOLD = 3;     // 金牌商家
    const LEVEL_DIAMOND = 4;  // 钻石商家
    
    // 商家状态常量
    const STATUS_PENDING = 0;   // 待审核
    const STATUS_ACTIVE = 1;    // 正常
    const STATUS_SUSPENDED = 2; // 暂停
    const STATUS_REJECTED = 3;  // 拒绝
    
    /**
     * 获取商家列表（带用户信息）
     */
    public function getListWithUserInfo($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['company_name'])) {
            $whereClause[] = "m.company_name LIKE :company_name";
            $params['company_name'] = '%' . $conditions['company_name'] . '%';
        }
        
        if (!empty($conditions['contact_name'])) {
            $whereClause[] = "m.contact_name LIKE :contact_name";
            $params['contact_name'] = '%' . $conditions['contact_name'] . '%';
        }
        
        if (!empty($conditions['level'])) {
            $whereClause[] = "m.level = :level";
            $params['level'] = $conditions['level'];
        }
        
        if (!empty($conditions['status'])) {
            $whereClause[] = "m.status = :status";
            $params['status'] = $conditions['status'];
        }
        
        if (!empty($conditions['verified'])) {
            if ($conditions['verified'] == 1) {
                $whereClause[] = "m.verified_at IS NOT NULL";
            } else {
                $whereClause[] = "m.verified_at IS NULL";
            }
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->getTableName()} m {$whereStr}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT m.*, 
                       u.username, u.nickname, u.avatar,
                       COUNT(i.id) as api_count_real
                FROM {$this->getTableName()} m 
                LEFT JOIN {$this->db->getPrefix()}users u ON m.user_id = u.id
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON m.id = i.merchant_id
                {$whereStr}
                GROUP BY m.id
                ORDER BY m.id DESC 
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        // 格式化数据
        foreach ($data as &$item) {
            $item['level_text'] = $this->getLevelText($item['level']);
            $item['status_text'] = $this->getStatusText($item['status']);
            $item['is_verified'] = !empty($item['verified_at']);
        }
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取商家详情（带统计信息）
     */
    public function getDetailWithStats($id) {
        $merchant = $this->find($id);
        if (!$merchant) {
            return null;
        }
        
        // 获取用户信息
        $sql = "SELECT u.username, u.nickname, u.avatar, u.created_at as user_created_at
                FROM {$this->db->getPrefix()}users u 
                WHERE u.id = :user_id";
        $userInfo = $this->db->fetchOne($sql, ['user_id' => $merchant['user_id']]);
        
        if ($userInfo) {
            $merchant = array_merge($merchant, $userInfo);
        }
        
        // 获取API统计
        $sql = "SELECT 
                    COUNT(*) as api_count,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_api_count,
                    SUM(call_count) as total_calls,
                    SUM(success_count) as total_success,
                    AVG(price) as avg_price
                FROM {$this->db->getPrefix()}interfaces 
                WHERE merchant_id = :merchant_id";
        $apiStats = $this->db->fetchOne($sql, ['merchant_id' => $id]);
        
        // 获取收入统计
        $sql = "SELECT 
                    SUM(amount) as total_revenue,
                    COUNT(*) as order_count,
                    SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as confirmed_revenue
                FROM {$this->db->getPrefix()}orders 
                WHERE merchant_id = :merchant_id";
        $revenueStats = $this->db->fetchOne($sql, ['merchant_id' => $id]);
        
        // 获取最近30天调用趋势
        $sql = "SELECT 
                    DATE(cl.created_at) as date,
                    COUNT(*) as calls,
                    SUM(cl.cost) as revenue
                FROM {$this->db->getPrefix()}call_logs cl
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON cl.api_id = i.id
                WHERE i.merchant_id = :merchant_id 
                AND cl.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY DATE(cl.created_at)
                ORDER BY date ASC";
        $callTrend = $this->db->fetchAll($sql, ['merchant_id' => $id]);
        
        $merchant['api_stats'] = $apiStats;
        $merchant['revenue_stats'] = $revenueStats;
        $merchant['call_trend'] = $callTrend;
        $merchant['level_text'] = $this->getLevelText($merchant['level']);
        $merchant['status_text'] = $this->getStatusText($merchant['status']);
        $merchant['is_verified'] = !empty($merchant['verified_at']);
        
        return $merchant;
    }
    
    /**
     * 商家入驻申请
     */
    public function applyMerchant($data) {
        // 检查用户是否已经是商家
        $existing = $this->findByUserId($data['user_id']);
        if ($existing) {
            throw new Exception('该用户已经是商家或已提交申请');
        }
        
        // 设置默认值
        $data['level'] = self::LEVEL_BRONZE;
        $data['commission_rate'] = 0.1; // 默认10%佣金
        $data['balance'] = 0;
        $data['total_revenue'] = 0;
        $data['api_count'] = 0;
        $data['call_count'] = 0;
        $data['status'] = self::STATUS_PENDING;
        
        return $this->create($data);
    }
    
    /**
     * 审核商家申请
     */
    public function reviewApplication($id, $status, $reason = '') {
        $merchant = $this->find($id);
        if (!$merchant) {
            throw new Exception('商家不存在');
        }
        
        if ($merchant['status'] != self::STATUS_PENDING) {
            throw new Exception('该申请已经处理过了');
        }
        
        $updateData = ['status' => $status];
        
        if ($status == self::STATUS_ACTIVE) {
            $updateData['verified_at'] = date('Y-m-d H:i:s');
        }
        
        $result = $this->update($id, $updateData);
        
        // 记录审核日志
        $this->logMerchantAction($id, 'review', [
            'status' => $status,
            'reason' => $reason,
            'reviewer_id' => $_SESSION['admin_id'] ?? null
        ]);
        
        return $result;
    }
    
    /**
     * 升级商家等级
     */
    public function upgradeLevel($id, $newLevel) {
        $merchant = $this->find($id);
        if (!$merchant) {
            throw new Exception('商家不存在');
        }
        
        if ($newLevel <= $merchant['level']) {
            throw new Exception('新等级必须高于当前等级');
        }
        
        if ($newLevel > self::LEVEL_DIAMOND) {
            throw new Exception('等级超出范围');
        }
        
        // 根据等级调整佣金率
        $commissionRates = [
            self::LEVEL_BRONZE => 0.1,   // 10%
            self::LEVEL_SILVER => 0.08,  // 8%
            self::LEVEL_GOLD => 0.06,    // 6%
            self::LEVEL_DIAMOND => 0.05  // 5%
        ];
        
        $updateData = [
            'level' => $newLevel,
            'commission_rate' => $commissionRates[$newLevel]
        ];
        
        $result = $this->update($id, $updateData);
        
        // 记录升级日志
        $this->logMerchantAction($id, 'upgrade_level', [
            'old_level' => $merchant['level'],
            'new_level' => $newLevel,
            'operator_id' => $_SESSION['admin_id'] ?? null
        ]);
        
        return $result;
    }
    
    /**
     * 更新商家余额
     */
    public function updateBalance($id, $amount, $type = 'add', $description = '') {
        $merchant = $this->find($id);
        if (!$merchant) {
            throw new Exception('商家不存在');
        }
        
        $newBalance = $merchant['balance'];
        
        if ($type == 'add') {
            $newBalance += $amount;
        } elseif ($type == 'subtract') {
            if ($merchant['balance'] < $amount) {
                throw new Exception('余额不足');
            }
            $newBalance -= $amount;
        } else {
            throw new Exception('无效的操作类型');
        }
        
        $result = $this->update($id, ['balance' => $newBalance]);
        
        // 记录余额变动
        $this->logBalanceChange($id, $amount, $type, $description, $merchant['balance'], $newBalance);
        
        return $result;
    }
    
    /**
     * 根据用户ID查找商家
     */
    public function findByUserId($userId) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE user_id = :user_id";
        return $this->db->fetchOne($sql, ['user_id' => $userId]);
    }
    
    /**
     * 获取商家统计信息
     */
    public function getMerchantStats() {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = " . self::STATUS_PENDING . " THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = " . self::STATUS_ACTIVE . " THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN status = " . self::STATUS_SUSPENDED . " THEN 1 ELSE 0 END) as suspended,
                    SUM(CASE WHEN verified_at IS NOT NULL THEN 1 ELSE 0 END) as verified,
                    SUM(balance) as total_balance,
                    SUM(total_revenue) as total_revenue,
                    AVG(commission_rate) as avg_commission_rate
                FROM {$this->getTableName()}";
        
        return $this->db->fetchOne($sql);
    }
    
    /**
     * 获取等级分布统计
     */
    public function getLevelDistribution() {
        $sql = "SELECT 
                    level,
                    COUNT(*) as count
                FROM {$this->getTableName()} 
                WHERE status = " . self::STATUS_ACTIVE . "
                GROUP BY level
                ORDER BY level ASC";
        
        $result = $this->db->fetchAll($sql);
        
        // 格式化结果
        $distribution = [];
        foreach ($result as $item) {
            $distribution[] = [
                'level' => $item['level'],
                'level_text' => $this->getLevelText($item['level']),
                'count' => $item['count']
            ];
        }
        
        return $distribution;
    }
    
    /**
     * 获取收入排行榜
     */
    public function getRevenueRanking($limit = 10) {
        $sql = "SELECT 
                    m.*,
                    u.username,
                    u.nickname
                FROM {$this->getTableName()} m
                LEFT JOIN {$this->db->getPrefix()}users u ON m.user_id = u.id
                WHERE m.status = " . self::STATUS_ACTIVE . "
                ORDER BY m.total_revenue DESC
                LIMIT {$limit}";
        
        $result = $this->db->fetchAll($sql);
        
        foreach ($result as &$item) {
            $item['level_text'] = $this->getLevelText($item['level']);
        }
        
        return $result;
    }
    
    /**
     * 获取活跃度排行榜
     */
    public function getActivityRanking($limit = 10) {
        $sql = "SELECT 
                    m.*,
                    u.username,
                    u.nickname,
                    COUNT(cl.id) as recent_calls
                FROM {$this->getTableName()} m
                LEFT JOIN {$this->db->getPrefix()}users u ON m.user_id = u.id
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON m.id = i.merchant_id
                LEFT JOIN {$this->db->getPrefix()}call_logs cl ON i.id = cl.api_id
                WHERE m.status = " . self::STATUS_ACTIVE . "
                AND cl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY m.id
                ORDER BY recent_calls DESC
                LIMIT {$limit}";
        
        $result = $this->db->fetchAll($sql);
        
        foreach ($result as &$item) {
            $item['level_text'] = $this->getLevelText($item['level']);
        }
        
        return $result;
    }
    
    /**
     * 批量更新状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids)) {
            return false;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->getTableName()} SET status = ?, updated_at = ? WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status, date('Y-m-d H:i:s')], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * 获取等级文本
     */
    public function getLevelText($level) {
        $levels = [
            self::LEVEL_BRONZE => '铜牌商家',
            self::LEVEL_SILVER => '银牌商家',
            self::LEVEL_GOLD => '金牌商家',
            self::LEVEL_DIAMOND => '钻石商家'
        ];
        
        return $levels[$level] ?? '未知等级';
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusText($status) {
        $statuses = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_ACTIVE => '正常',
            self::STATUS_SUSPENDED => '暂停',
            self::STATUS_REJECTED => '拒绝'
        ];
        
        return $statuses[$status] ?? '未知状态';
    }
    
    /**
     * 记录商家操作日志
     */
    private function logMerchantAction($merchantId, $action, $data = []) {
        $sql = "INSERT INTO {$this->db->getPrefix()}merchant_logs 
                (merchant_id, action, data, operator_id, created_at) 
                VALUES (?, ?, ?, ?, ?)";
        
        $this->db->query($sql, [
            $merchantId,
            $action,
            json_encode($data, JSON_UNESCAPED_UNICODE),
            $_SESSION['admin_id'] ?? null,
            date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 记录余额变动
     */
    private function logBalanceChange($merchantId, $amount, $type, $description, $oldBalance, $newBalance) {
        $sql = "INSERT INTO {$this->db->getPrefix()}balance_logs 
                (merchant_id, amount, type, description, old_balance, new_balance, operator_id, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $this->db->query($sql, [
            $merchantId,
            $amount,
            $type,
            $description,
            $oldBalance,
            $newBalance,
            $_SESSION['admin_id'] ?? null,
            date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 获取商家余额变动记录
     */
    public function getBalanceHistory($merchantId, $page = 1, $perPage = 20) {
        $offset = ($page - 1) * $perPage;
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->db->getPrefix()}balance_logs WHERE merchant_id = :merchant_id";
        $totalResult = $this->db->fetchOne($countSql, ['merchant_id' => $merchantId]);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT bl.*, a.username as operator_name
                FROM {$this->db->getPrefix()}balance_logs bl
                LEFT JOIN {$this->db->getPrefix()}admins a ON bl.operator_id = a.id
                WHERE bl.merchant_id = :merchant_id
                ORDER BY bl.id DESC
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, ['merchant_id' => $merchantId]);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取商家API列表
     */
    public function getMerchantApis($merchantId, $page = 1, $perPage = 20) {
        $offset = ($page - 1) * $perPage;
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->db->getPrefix()}interfaces WHERE merchant_id = :merchant_id";
        $totalResult = $this->db->fetchOne($countSql, ['merchant_id' => $merchantId]);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT i.*, c.name as category_name
                FROM {$this->db->getPrefix()}interfaces i
                LEFT JOIN {$this->db->getPrefix()}categories c ON i.category_id = c.id
                WHERE i.merchant_id = :merchant_id
                ORDER BY i.id DESC
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, ['merchant_id' => $merchantId]);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 自动升级商家等级
     */
    public function autoUpgradeLevel($merchantId) {
        $merchant = $this->find($merchantId);
        if (!$merchant || $merchant['status'] != self::STATUS_ACTIVE) {
            return false;
        }
        
        // 升级条件
        $upgradeRules = [
            self::LEVEL_SILVER => [
                'min_revenue' => 10000,
                'min_api_count' => 5,
                'min_calls' => 1000
            ],
            self::LEVEL_GOLD => [
                'min_revenue' => 50000,
                'min_api_count' => 15,
                'min_calls' => 10000
            ],
            self::LEVEL_DIAMOND => [
                'min_revenue' => 200000,
                'min_api_count' => 50,
                'min_calls' => 100000
            ]
        ];
        
        $currentLevel = $merchant['level'];
        $newLevel = $currentLevel;
        
        // 检查升级条件
        foreach ($upgradeRules as $level => $rules) {
            if ($level <= $currentLevel) {
                continue;
            }
            
            if ($merchant['total_revenue'] >= $rules['min_revenue'] &&
                $merchant['api_count'] >= $rules['min_api_count'] &&
                $merchant['call_count'] >= $rules['min_calls']) {
                $newLevel = $level;
            } else {
                break;
            }
        }
        
        // 执行升级
        if ($newLevel > $currentLevel) {
            return $this->upgradeLevel($merchantId, $newLevel);
        }
        
        return false;
    }
    
    /**
     * 结算商家收入
     */
    public function settleMerchantRevenue($merchantId, $amount, $orderId = null) {
        $merchant = $this->find($merchantId);
        if (!$merchant) {
            throw new Exception('商家不存在');
        }
        
        // 计算佣金
        $commission = $amount * $merchant['commission_rate'];
        $actualAmount = $amount - $commission;
        
        // 更新商家余额和总收入
        $this->update($merchantId, [
            'balance' => $merchant['balance'] + $actualAmount,
            'total_revenue' => $merchant['total_revenue'] + $amount
        ]);
        
        // 记录结算日志
        $this->logMerchantAction($merchantId, 'settle_revenue', [
            'order_id' => $orderId,
            'amount' => $amount,
            'commission' => $commission,
            'actual_amount' => $actualAmount,
            'commission_rate' => $merchant['commission_rate']
        ]);
        
        // 检查是否可以自动升级
        $this->autoUpgradeLevel($merchantId);
        
        return [
            'amount' => $amount,
            'commission' => $commission,
            'actual_amount' => $actualAmount
        ];
    }
}