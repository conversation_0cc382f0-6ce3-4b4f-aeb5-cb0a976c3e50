<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>分步表单</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        [lay-filter="formStepsStep"] .layui-form-item {
            margin-bottom: 25px;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body" style="padding-top: 40px;">
            <!-- 分布表单开始 -->
            <div class="layui-tab layui-steps layui-steps-readonly" lay-filter="formStepsStep"
                 style="max-width: 650px;">
                <!-- 标题 -->
                <ul class="layui-tab-title">
                    <li class="layui-this">
                        <i class="layui-icon layui-icon-ok">1</i>
                        <span class="layui-steps-title">第一步</span>
                        <span class="layui-steps-content">填写转账信息</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">2</i>
                        <span class="layui-steps-title">第二步</span>
                        <span class="layui-steps-content">确认转账信息</span>
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok">3</i>
                        <span class="layui-steps-title">第三步</span>
                        <span class="layui-steps-content">转账成功</span>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <!-- 表单一 -->
                        <form class="layui-form" style="max-width: 460px;margin: 0 auto;padding: 40px 30px 0 0;">
                            <div class="layui-form-item">
                                <label class="layui-form-label layui-form-required">付款账户:</label>
                                <div class="layui-input-block">
                                    <select name="payAccount" lay-verType="tips" lay-verify="required" required>
                                        <option value="<EMAIL>"><EMAIL></option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label layui-form-required">收款账户:</label>
                                <div class="layui-input-block">
                                    <input name="account" value="<EMAIL>" placeholder="请输入收款账户"
                                           class="layui-input" lay-verType="tips" lay-verify="required" required>
                                </div>
                            </div>
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label layui-form-required">收款人:</label>
                                <div class="layui-input-block">
                                    <input name="trueName" placeholder="请输入收款人姓名" value="Alex" class="layui-input"
                                           lay-verType="tips" lay-verify="required" required/>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label layui-form-required">转账金额:</label>
                                <div class="layui-input-block">
                                    <input name="money" placeholder="请输入金额" value="11" class="layui-input" type="number"
                                           lay-verType="tips" lay-verify="required" required>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-filter="formStepSubmit1" lay-submit>&emsp;下一步&emsp;
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="layui-tab-item">
                        <!-- 表单二 -->
                        <form class="layui-form" style="max-width: 460px;margin: 0 auto;padding: 40px 30px 0 0;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">付款账户:</label>
                                <div class="layui-form-mid layui-word-aux"><EMAIL></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">收款账户:</label>
                                <div class="layui-form-mid layui-word-aux"><EMAIL></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">收款人姓名:</label>
                                <div class="layui-form-mid layui-word-aux">Alex</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">转账金额:</label>
                                <div class="layui-form-mid layui-word-aux">
                                    <span style="font-size: 18px;color: #333;">500</span>（人民币伍佰元整）
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label layui-form-required">支付密码:</label>
                                <div class="layui-input-block">
                                    <input placeholder="请输入支付密码" value="123456" type="password"
                                           class="layui-input" lay-verify="required" required>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-primary" data-steps="prev"> 上 一 步&nbsp;
                                    </button>
                                    <button class="layui-btn" lay-filter="formStepSubmit2" lay-submit>&emsp;提交&emsp;
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="layui-tab-item text-center" style="padding-top: 40px;">
                        <!-- 表单三 -->
                        <i class="layui-icon layui-icon-ok layui-circle"
                           style="background: #52C41A;color: #fff;font-size:30px;font-weight:bold;padding: 20px;line-height: 80px;"></i>
                        <div style="font-size: 24px;color: #333;margin-top: 30px;">操作成功</div>
                        <div style="font-size: 14px;color: #666;margin-top: 20px;">预计两小时到账</div>
                        <div style="text-align: center;margin: 50px 0 25px 0;">
                            <button class="layui-btn" data-steps="next">再转一笔</button>
                            <button class="layui-btn layui-btn-primary">查看账单</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- //分布表单结束 -->
            <hr>
            <div style="padding: 10px 30px 20px 30px;">
                <h3>说明</h3><br>
                <h4>转账到支付宝账户</h4>
                <p class="layui-text">如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。</p>
                <br><h4>转账到银行卡</h4>
                <p class="layui-text">如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。</p>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form', 'steps'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var steps = layui.steps;

        /* 表单一提交事件 */
        form.on('submit(formStepSubmit1)', function (data) {
            steps.next('formStepsStep');
            return false;
        });

        /* 表单二提交事件 */
        form.on('submit(formStepSubmit2)', function (data) {
            steps.next('formStepsStep');
            return false;
        });

    });
</script>
</body>
</html>