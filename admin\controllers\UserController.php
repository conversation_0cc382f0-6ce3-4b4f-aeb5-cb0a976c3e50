<?php
/**
 * 用户管理控制器
 */
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once '../../core/UserManager.php';

class UserController {
    private $db;
    private $auth;
    private $userManager;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new Auth();
        $this->userManager = new UserManager();
        
        // 检查管理员权限
        $this->checkAdminAuth();
    }
    
    /**
     * 获取用户列表
     */
    public function getList() {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        
        $filters = [
            'username' => $_GET['username'] ?? '',
            'email' => $_GET['email'] ?? '',
            'role' => $_GET['role'] ?? '',
            'status' => $_GET['status'] ?? ''
        ];
        
        $result = $this->userManager->getUserList($page, $limit, $filters);
        
        $this->jsonResponse([
            'code' => 0,
            'msg' => '',
            'count' => $result['total'],
            'data' => $result['data']
        ]);
    }
    
    /**
     * 创建用户
     */
    public function create() {
        $data = $_POST;
        
        // 验证必填字段
        if (empty($data['username']) || empty($data['email']) || empty($data['password'])) {
            $this->jsonResponse(['code' => 400, 'msg' => '请填写完整信息']);
        }
        
        // 验证邮箱格式
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->jsonResponse(['code' => 400, 'msg' => '邮箱格式不正确']);
        }
        
        $result = $this->userManager->createUser($data);
        
        if ($result['success']) {
            $this->jsonResponse(['code' => 200, 'msg' => $result['message']]);
        } else {
            $this->jsonResponse(['code' => 400, 'msg' => $result['message']]);
        }
    }
    
    /**
     * 更新用户
     */
    public function update() {
        $id = $_POST['id'] ?? 0;
        $data = $_POST;
        
        if (!$id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 验证邮箱格式
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->jsonResponse(['code' => 400, 'msg' => '邮箱格式不正确']);
        }
        
        $result = $this->userManager->updateUser($id, $data);
        
        if ($result['success']) {
            $this->jsonResponse(['code' => 200, 'msg' => $result['message']]);
        } else {
            $this->jsonResponse(['code' => 400, 'msg' => $result['message']]);
        }
    }
    
    /**
     * 删除用户
     */
    public function delete() {
        $id = $_POST['id'] ?? 0;
        
        if (!$id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 不能删除管理员账户
        $user = $this->userManager->getUserById($id);
        if ($user && $user['role'] === 'admin') {
            $this->jsonResponse(['code' => 400, 'msg' => '不能删除管理员账户']);
        }
        
        $result = $this->userManager->deleteUser($id);
        
        if ($result['success']) {
            $this->jsonResponse(['code' => 200, 'msg' => $result['message']]);
        } else {
            $this->jsonResponse(['code' => 400, 'msg' => $result['message']]);
        }
    }
    
    /**
     * 批量删除用户
     */
    public function batchDelete() {
        $ids = $_POST['ids'] ?? [];
        
        if (empty($ids)) {
            $this->jsonResponse(['code' => 400, 'msg' => '请选择要删除的数据']);
        }
        
        // 检查是否包含管理员账户
        $adminCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM users WHERE id IN (" . str_repeat('?,', count($ids) - 1) . "?) AND role = 'admin'",
            $ids
        )['count'];
        
        if ($adminCount > 0) {
            $this->jsonResponse(['code' => 400, 'msg' => '不能删除管理员账户']);
        }
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        
        if ($this->db->query("DELETE FROM users WHERE id IN ({$placeholders})", $ids)) {
            $this->jsonResponse(['code' => 200, 'msg' => '批量删除成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '批量删除失败']);
        }
    }
    
    /**
     * 更新用户状态
     */
    public function updateStatus() {
        $id = $_POST['id'] ?? 0;
        $status = $_POST['status'] ?? 0;
        
        if (!$id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 不能禁用管理员账户
        $user = $this->userManager->getUserById($id);
        if ($user && $user['role'] === 'admin' && $status == 0) {
            $this->jsonResponse(['code' => 400, 'msg' => '不能禁用管理员账户']);
        }
        
        if ($this->db->update('users', ['status' => $status], 'id = ?', [$id])) {
            $this->jsonResponse(['code' => 200, 'msg' => '状态更新成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '状态更新失败']);
        }
    }
    
    /**
     * 用户充值
     */
    public function recharge() {
        $id = $_POST['id'] ?? 0;
        $amount = $_POST['amount'] ?? 0;
        
        if (!$id || $amount <= 0) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        $result = $this->userManager->rechargeBalance($id, $amount, '管理员充值');
        
        if ($result['success']) {
            $this->jsonResponse(['code' => 200, 'msg' => $result['message']]);
        } else {
            $this->jsonResponse(['code' => 400, 'msg' => $result['message']]);
        }
    }
    
    /**
     * 重置API密钥
     */
    public function resetApiKey() {
        $id = $_POST['id'] ?? 0;
        
        if (!$id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        $result = $this->userManager->resetApiKey($id);
        
        if ($result['success']) {
            $this->jsonResponse(['code' => 200, 'msg' => 'API密钥重置成功', 'api_key' => $result['api_key']]);
        } else {
            $this->jsonResponse(['code' => 400, 'msg' => $result['message']]);
        }
    }
    
    /**
     * 获取用户详情
     */
    public function getDetail() {
        $id = $_GET['id'] ?? 0;
        
        if (!$id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        $user = $this->userManager->getUserById($id);
        
        if (!$user) {
            $this->jsonResponse(['code' => 404, 'msg' => '用户不存在']);
        }
        
        // 移除敏感信息
        unset($user['password']);
        
        $this->jsonResponse(['code' => 200, 'data' => $user]);
    }
    
    /**
     * 获取用户余额记录
     */
    public function getBalanceLogs() {
        $userId = $_GET['user_id'] ?? 0;
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        
        if (!$userId) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        $result = $this->userManager->getBalanceLogs($userId, $page, $limit);
        
        $this->jsonResponse([
            'code' => 0,
            'msg' => '',
            'count' => $result['total'],
            'data' => $result['data']
        ]);
    }
    
    /**
     * 导出用户
     */
    public function export() {
        $users = $this->db->fetchAll(
            "SELECT id, username, email, role, level, balance, status, created_at, last_login, login_count 
             FROM users 
             ORDER BY created_at DESC"
        );
        
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="users_' . date('YmdHis') . '.csv"');
        header('Cache-Control: max-age=0');
        
        $output = fopen('php://output', 'w');
        
        // 写入BOM头，解决中文乱码
        fwrite($output, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($output, ['ID', '用户名', '邮箱', '角色', '等级', '余额', '状态', '注册时间', '最后登录', '登录次数']);
        
        // 写入数据
        foreach ($users as $user) {
            fputcsv($output, [
                $user['id'],
                $user['username'],
                $user['email'],
                $user['role'],
                $user['level'],
                $user['balance'],
                $user['status'] ? '启用' : '禁用',
                $user['created_at'],
                $user['last_login'],
                $user['login_count']
            ]);
        }
        
        fclose($output);
    }
    
    /**
     * 获取用户统计
     */
    public function getStats() {
        $stats = $this->userManager->getUserStats();
        $this->jsonResponse(['code' => 200, 'data' => $stats]);
    }
    
    /**
     * 检查管理员权限
     */
    private function checkAdminAuth() {
        $token = $_SERVER['HTTP_AUTHORIZATION'] ?? $_COOKIE['admin_token'] ?? '';
        
        if (empty($token)) {
            $this->jsonResponse(['code' => 401, 'msg' => '请先登录']);
        }
        
        $payload = $this->auth->verifyToken($token);
        
        if (!$payload || $payload['role'] !== 'admin') {
            $this->jsonResponse(['code' => 403, 'msg' => '权限不足']);
        }
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 路由处理
$action = $_GET['action'] ?? 'list';
$controller = new UserController();

switch ($action) {
    case 'list':
        $controller->getList();
        break;
    case 'create':
        $controller->create();
        break;
    case 'update':
        $controller->update();
        break;
    case 'delete':
        $controller->delete();
        break;
    case 'batch-delete':
        $controller->batchDelete();
        break;
    case 'status':
        $controller->updateStatus();
        break;
    case 'recharge':
        $controller->recharge();
        break;
    case 'reset-key':
        $controller->resetApiKey();
        break;
    case 'detail':
        $controller->getDetail();
        break;
    case 'balance-logs':
        $controller->getBalanceLogs();
        break;
    case 'export':
        $controller->export();
        break;
    case 'stats':
        $controller->getStats();
        break;
    default:
        http_response_code(404);
        echo json_encode(['code' => 404, 'msg' => '接口不存在']);
}