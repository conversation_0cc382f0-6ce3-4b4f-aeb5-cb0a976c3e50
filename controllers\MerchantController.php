<?php
/**
 * 商家控制器
 */
class MerchantController {
    private $db;
    private $config;
    
    public function __construct() {
        global $db, $config;
        $this->db = $db;
        $this->config = $config;
    }
    
    /**
     * 商家入驻申请
     */
    public function apply() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        
        // 检查是否已经是商家
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE user_id = ?");
        $stmt->execute([$userId]);
        if ($stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '您已经是商家，无需重复申请'
            ]);
        }
        
        // 检查是否有待审核的申请
        $stmt = $this->db->prepare("SELECT id FROM merchant_applications WHERE user_id = ? AND status = 0");
        $stmt->execute([$userId]);
        if ($stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '您已提交过申请，请等待审核'
            ]);
        }
        
        // 获取表单数据
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $contact = isset($_POST['contact']) ? trim($_POST['contact']) : '';
        $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $address = isset($_POST['address']) ? trim($_POST['address']) : '';
        $business_license = isset($_POST['business_license']) ? trim($_POST['business_license']) : '';
        $id_card_front = isset($_POST['id_card_front']) ? trim($_POST['id_card_front']) : '';
        $id_card_back = isset($_POST['id_card_back']) ? trim($_POST['id_card_back']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        
        // 验证表单数据
        if (empty($name)) {
            return json([
                'code' => 1,
                'msg' => '商家名称不能为空'
            ]);
        }
        
        if (empty($contact)) {
            return json([
                'code' => 1,
                'msg' => '联系人不能为空'
            ]);
        }
        
        if (empty($phone)) {
            return json([
                'code' => 1,
                'msg' => '联系电话不能为空'
            ]);
        }
        
        if (empty($email)) {
            return json([
                'code' => 1,
                'msg' => '联系邮箱不能为空'
            ]);
        }
        
        if (empty($business_license)) {
            return json([
                'code' => 1,
                'msg' => '请上传营业执照'
            ]);
        }
        
        // 保存申请信息
        $stmt = $this->db->prepare("INSERT INTO merchant_applications (user_id, name, contact, phone, email, address, 
                                   business_license, id_card_front, id_card_back, description, status, create_time) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?)");
        $stmt->execute([
            $userId,
            $name,
            $contact,
            $phone,
            $email,
            $address,
            $business_license,
            $id_card_front,
            $id_card_back,
            $description,
            time()
        ]);
        
        // 发送邮件通知管理员
        $this->sendApplyNotification($name, $contact, $phone, $email);
        
        return json([
            'code' => 0,
            'msg' => '申请提交成功，请等待审核'
        ]);
    }
    
    /**
     * 发送申请通知邮件
     */
    private function sendApplyNotification($name, $contact, $phone, $email) {
        $adminEmail = $this->config['admin_email'];
        
        if (empty($adminEmail)) {
            return;
        }
        
        $subject = '新商家入驻申请通知';
        $message = "您好，管理员：\n\n";
        $message .= "系统收到了一个新的商家入驻申请，详情如下：\n\n";
        $message .= "商家名称：{$name}\n";
        $message .= "联系人：{$contact}\n";
        $message .= "联系电话：{$phone}\n";
        $message .= "联系邮箱：{$email}\n\n";
        $message .= "请登录管理后台进行审核。\n\n";
        $message .= "此邮件由系统自动发送，请勿回复。";
        
        // 发送邮件
        sendMail($adminEmail, $subject, $message);
    }
    
    /**
     * 获取商家申请状态
     */
    public function getApplyStatus() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        
        // 检查是否已经是商家
        $stmt = $this->db->prepare("SELECT id, level, status FROM merchants WHERE user_id = ?");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($merchant) {
            // 获取商家等级信息
            $stmt = $this->db->prepare("SELECT name FROM merchant_levels WHERE id = ?");
            $stmt->execute([$merchant['level']]);
            $level = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'is_merchant' => true,
                    'status' => $merchant['status'],
                    'level' => $merchant['level'],
                    'level_name' => $level ? $level['name'] : '未知等级'
                ]
            ]);
        }
        
        // 检查是否有申请记录
        $stmt = $this->db->prepare("SELECT id, status, create_time, audit_time, audit_remark FROM merchant_applications 
                                   WHERE user_id = ? 
                                   ORDER BY create_time DESC 
                                   LIMIT 1");
        $stmt->execute([$userId]);
        $application = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($application) {
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'is_merchant' => false,
                    'has_application' => true,
                    'application' => $application
                ]
            ]);
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'is_merchant' => false,
                'has_application' => false
            ]
        ]);
    }
    
    /**
     * 获取商家信息
     */
    public function getInfo() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT m.*, l.name as level_name, l.discount_rate, l.max_api_count, l.features 
                                   FROM merchants m 
                                   LEFT JOIN merchant_levels l ON m.level = l.id 
                                   WHERE m.user_id = ? AND m.status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 获取商家API数量
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_apis WHERE merchant_id = ?");
        $stmt->execute([$merchant['id']]);
        $apiCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取商家收入统计
        $stmt = $this->db->prepare("SELECT SUM(amount) as total_income FROM merchant_incomes WHERE merchant_id = ?");
        $stmt->execute([$merchant['id']]);
        $totalIncome = $stmt->fetch(PDO::FETCH_ASSOC)['total_income'] ?: 0;
        
        // 获取今日收入
        $todayStart = strtotime(date('Y-m-d'));
        $stmt = $this->db->prepare("SELECT SUM(amount) as today_income FROM merchant_incomes 
                                   WHERE merchant_id = ? AND create_time >= ?");
        $stmt->execute([$merchant['id'], $todayStart]);
        $todayIncome = $stmt->fetch(PDO::FETCH_ASSOC)['today_income'] ?: 0;
        
        // 获取本月收入
        $monthStart = strtotime(date('Y-m-01'));
        $stmt = $this->db->prepare("SELECT SUM(amount) as month_income FROM merchant_incomes 
                                   WHERE merchant_id = ? AND create_time >= ?");
        $stmt->execute([$merchant['id'], $monthStart]);
        $monthIncome = $stmt->fetch(PDO::FETCH_ASSOC)['month_income'] ?: 0;
        
        // 解析商家等级特权
        $features = json_decode($merchant['features'], true) ?: [];
        
        $merchant['api_count'] = $apiCount;
        $merchant['total_income'] = $totalIncome;
        $merchant['today_income'] = $todayIncome;
        $merchant['month_income'] = $monthIncome;
        $merchant['features'] = $features;
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $merchant
        ]);
    }
    
    /**
     * 更新商家信息
     */
    public function updateInfo() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 获取表单数据
        $contact = isset($_POST['contact']) ? trim($_POST['contact']) : '';
        $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $address = isset($_POST['address']) ? trim($_POST['address']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $logo = isset($_POST['logo']) ? trim($_POST['logo']) : '';
        
        // 验证表单数据
        if (empty($contact)) {
            return json([
                'code' => 1,
                'msg' => '联系人不能为空'
            ]);
        }
        
        if (empty($phone)) {
            return json([
                'code' => 1,
                'msg' => '联系电话不能为空'
            ]);
        }
        
        if (empty($email)) {
            return json([
                'code' => 1,
                'msg' => '联系邮箱不能为空'
            ]);
        }
        
        // 更新商家信息
        $stmt = $this->db->prepare("UPDATE merchants SET contact = ?, phone = ?, email = ?, address = ?, 
                                   description = ?, logo = ?, update_time = ? 
                                   WHERE id = ?");
        $stmt->execute([
            $contact,
            $phone,
            $email,
            $address,
            $description,
            $logo,
            time(),
            $merchant['id']
        ]);
        
        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }
    
    /**
     * 获取商家等级列表
     */
    public function getLevelList() {
        $stmt = $this->db->prepare("SELECT id, name, description, price, discount_rate, max_api_count, features 
                                   FROM merchant_levels 
                                   WHERE status = 1 
                                   ORDER BY price ASC");
        $stmt->execute();
        $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 解析特权
        foreach ($levels as &$level) {
            $level['features'] = json_decode($level['features'], true) ?: [];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $levels
        ]);
    }
    
    /**
     * 升级商家等级
     */
    public function upgradeLevel() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $levelId = isset($_POST['level_id']) ? intval($_POST['level_id']) : 0;
        
        if ($levelId <= 0) {
            return json([
                'code' => 1,
                'msg' => '请选择要升级的等级'
            ]);
        }
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id, level FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 检查要升级的等级是否存在
        $stmt = $this->db->prepare("SELECT id, name, price FROM merchant_levels WHERE id = ? AND status = 1");
        $stmt->execute([$levelId]);
        $level = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$level) {
            return json([
                'code' => 1,
                'msg' => '所选等级不存在或已被禁用'
            ]);
        }
        
        // 检查是否是降级操作
        if ($levelId < $merchant['level']) {
            return json([
                'code' => 1,
                'msg' => '不支持降级操作'
            ]);
        }
        
        // 检查是否已经是该等级
        if ($levelId == $merchant['level']) {
            return json([
                'code' => 1,
                'msg' => '您已经是该等级，无需升级'
            ]);
        }
        
        // 创建升级订单
        $orderNo = 'UPG' . date('YmdHis') . rand(1000, 9999);
        $amount = $level['price'];
        
        $stmt = $this->db->prepare("INSERT INTO orders (order_no, user_id, type, title, amount, status, create_time) 
                                   VALUES (?, ?, 'upgrade', ?, ?, 0, ?)");
        $stmt->execute([
            $orderNo,
            $userId,
            '商家等级升级: ' . $level['name'],
            $amount,
            time()
        ]);
        
        $orderId = $this->db->lastInsertId();
        
        // 保存升级信息
        $stmt = $this->db->prepare("INSERT INTO merchant_upgrades (order_id, merchant_id, from_level, to_level, price, status, create_time) 
                                   VALUES (?, ?, ?, ?, ?, 0, ?)");
        $stmt->execute([
            $orderId,
            $merchant['id'],
            $merchant['level'],
            $levelId,
            $amount,
            time()
        ]);
        
        return json([
            'code' => 0,
            'msg' => '创建升级订单成功',
            'data' => [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'amount' => $amount
            ]
        ]);
    }
    
    /**
     * 获取商家API列表
     */
    public function getApiList() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 获取API总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_apis WHERE merchant_id = ?");
        $stmt->execute([$merchant['id']]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取API列表
        $stmt = $this->db->prepare("SELECT ma.*, a.name as api_name, a.method, a.url 
                                   FROM merchant_apis ma 
                                   LEFT JOIN apis a ON ma.api_id = a.id 
                                   WHERE ma.merchant_id = ? 
                                   ORDER BY ma.create_time DESC 
                                   LIMIT $offset, $limit");
        $stmt->execute([$merchant['id']]);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'count' => $count,
                'list' => $list,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }
    
    /**
     * 添加商家API
     */
    public function addApi() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $apiId = isset($_POST['api_id']) ? intval($_POST['api_id']) : 0;
        $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        
        if ($apiId <= 0) {
            return json([
                'code' => 1,
                'msg' => '请选择要添加的API'
            ]);
        }
        
        if ($price < 0) {
            return json([
                'code' => 1,
                'msg' => '价格不能为负数'
            ]);
        }
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT m.id, m.level, l.max_api_count 
                                   FROM merchants m 
                                   LEFT JOIN merchant_levels l ON m.level = l.id 
                                   WHERE m.user_id = ? AND m.status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 检查API是否存在
        $stmt = $this->db->prepare("SELECT id, name FROM apis WHERE id = ? AND status = 1");
        $stmt->execute([$apiId]);
        $api = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$api) {
            return json([
                'code' => 1,
                'msg' => '所选API不存在或已被禁用'
            ]);
        }
        
        // 检查是否已经添加过该API
        $stmt = $this->db->prepare("SELECT id FROM merchant_apis WHERE merchant_id = ? AND api_id = ?");
        $stmt->execute([$merchant['id'], $apiId]);
        if ($stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '您已经添加过该API'
            ]);
        }
        
        // 检查API数量是否超过限制
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_apis WHERE merchant_id = ?");
        $stmt->execute([$merchant['id']]);
        $apiCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($merchant['max_api_count'] > 0 && $apiCount >= $merchant['max_api_count']) {
            return json([
                'code' => 1,
                'msg' => '您的API数量已达到上限，请升级商家等级'
            ]);
        }
        
        // 添加API
        $stmt = $this->db->prepare("INSERT INTO merchant_apis (merchant_id, api_id, price, description, status, create_time) 
                                   VALUES (?, ?, ?, ?, 1, ?)");
        $stmt->execute([
            $merchant['id'],
            $apiId,
            $price,
            $description,
            time()
        ]);
        
        return json([
            'code' => 0,
            'msg' => '添加成功'
        ]);
    }
    
    /**
     * 更新商家API
     */
    public function updateApi() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        if ($price < 0) {
            return json([
                'code' => 1,
                'msg' => '价格不能为负数'
            ]);
        }
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 检查API是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchant_apis WHERE id = ? AND merchant_id = ?");
        $stmt->execute([$id, $merchant['id']]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '所选API不存在或不属于您'
            ]);
        }
        
        // 更新API
        $stmt = $this->db->prepare("UPDATE merchant_apis SET price = ?, description = ?, status = ?, update_time = ? 
                                   WHERE id = ? AND merchant_id = ?");
        $stmt->execute([
            $price,
            $description,
            $status,
            time(),
            $id,
            $merchant['id']
        ]);
        
        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }
    
    /**
     * 删除商家API
     */
    public function deleteApi() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 检查API是否存在
        $stmt = $this->db->prepare("SELECT id FROM merchant_apis WHERE id = ? AND merchant_id = ?");
        $stmt->execute([$id, $merchant['id']]);
        if (!$stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '所选API不存在或不属于您'
            ]);
        }
        
        // 删除API
        $stmt = $this->db->prepare("DELETE FROM merchant_apis WHERE id = ? AND merchant_id = ?");
        $stmt->execute([$id, $merchant['id']]);
        
        return json([
            'code' => 0,
            'msg' => '删除成功'
        ]);
    }
    
    /**
     * 获取商家收入记录
     */
    public function getIncomeList() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 获取收入总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_incomes WHERE merchant_id = ?");
        $stmt->execute([$merchant['id']]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取收入列表
        $stmt = $this->db->prepare("SELECT mi.*, ma.api_id, a.name as api_name, a.method, a.url 
                                   FROM merchant_incomes mi 
                                   LEFT JOIN merchant_apis ma ON mi.merchant_api_id = ma.id 
                                   LEFT JOIN apis a ON ma.api_id = a.id 
                                   WHERE mi.merchant_id = ? 
                                   ORDER BY mi.create_time DESC 
                                   LIMIT $offset, $limit");
        $stmt->execute([$merchant['id']]);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'count' => $count,
                'list' => $list,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }
    
    /**
     * 获取商家收入统计
     */
    public function getIncomeStats() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 获取总收入
        $stmt = $this->db->prepare("SELECT SUM(amount) as total FROM merchant_incomes WHERE merchant_id = ?");
        $stmt->execute([$merchant['id']]);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?: 0;
        
        // 获取今日收入
        $todayStart = strtotime(date('Y-m-d'));
        $stmt = $this->db->prepare("SELECT SUM(amount) as today FROM merchant_incomes 
                                   WHERE merchant_id = ? AND create_time >= ?");
        $stmt->execute([$merchant['id'], $todayStart]);
        $today = $stmt->fetch(PDO::FETCH_ASSOC)['today'] ?: 0;
        
        // 获取本周收入
        $weekStart = strtotime(date('Y-m-d', strtotime('this week')));
        $stmt = $this->db->prepare("SELECT SUM(amount) as week FROM merchant_incomes 
                                   WHERE merchant_id = ? AND create_time >= ?");
        $stmt->execute([$merchant['id'], $weekStart]);
        $week = $stmt->fetch(PDO::FETCH_ASSOC)['week'] ?: 0;
        
        // 获取本月收入
        $monthStart = strtotime(date('Y-m-01'));
        $stmt = $this->db->prepare("SELECT SUM(amount) as month FROM merchant_incomes 
                                   WHERE merchant_id = ? AND create_time >= ?");
        $stmt->execute([$merchant['id'], $monthStart]);
        $month = $stmt->fetch(PDO::FETCH_ASSOC)['month'] ?: 0;
        
        // 获取各API收入占比
        $stmt = $this->db->prepare("SELECT ma.api_id, a.name as api_name, SUM(mi.amount) as amount 
                                   FROM merchant_incomes mi 
                                   LEFT JOIN merchant_apis ma ON mi.merchant_api_id = ma.id 
                                   LEFT JOIN apis a ON ma.api_id = a.id 
                                   WHERE mi.merchant_id = ? 
                                   GROUP BY ma.api_id 
                                   ORDER BY amount DESC 
                                   LIMIT 10");
        $stmt->execute([$merchant['id']]);
        $apiIncomes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取最近7天的收入趋势
        $trends = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $dayStart = strtotime($date);
            $dayEnd = $dayStart + 86400;
            
            $stmt = $this->db->prepare("SELECT SUM(amount) as amount FROM merchant_incomes 
                                       WHERE merchant_id = ? AND create_time >= ? AND create_time < ?");
            $stmt->execute([$merchant['id'], $dayStart, $dayEnd]);
            $amount = $stmt->fetch(PDO::FETCH_ASSOC)['amount'] ?: 0;
            
            $trends[] = [
                'date' => $date,
                'amount' => $amount
            ];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'total' => $total,
                'today' => $today,
                'week' => $week,
                'month' => $month,
                'api_incomes' => $apiIncomes,
                'trends' => $trends
            ]
        ]);
    }
    
    /**
     * 获取商家提现记录
     */
    public function getWithdrawList() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id, balance FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 获取提现总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM merchant_withdraws WHERE merchant_id = ?");
        $stmt->execute([$merchant['id']]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 获取提现列表
        $stmt = $this->db->prepare("SELECT * FROM merchant_withdraws 
                                   WHERE merchant_id = ? 
                                   ORDER BY create_time DESC 
                                   LIMIT $offset, $limit");
        $stmt->execute([$merchant['id']]);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'count' => $count,
                'list' => $list,
                'page' => $page,
                'limit' => $limit,
                'balance' => $merchant['balance']
            ]
        ]);
    }
    
    /**
     * 申请提现
     */
    public function applyWithdraw() {
        // 检查用户登录状态
        checkLogin();
        
        $userId = $_SESSION['user_id'];
        $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
        $account_name = isset($_POST['account_name']) ? trim($_POST['account_name']) : '';
        $account_number = isset($_POST['account_number']) ? trim($_POST['account_number']) : '';
        $bank_name = isset($_POST['bank_name']) ? trim($_POST['bank_name']) : '';
        
        if ($amount <= 0) {
            return json([
                'code' => 1,
                'msg' => '提现金额必须大于0'
            ]);
        }
        
        if (empty($account_name)) {
            return json([
                'code' => 1,
                'msg' => '收款人姓名不能为空'
            ]);
        }
        
        if (empty($account_number)) {
            return json([
                'code' => 1,
                'msg' => '收款账号不能为空'
            ]);
        }
        
        if (empty($bank_name)) {
            return json([
                'code' => 1,
                'msg' => '开户银行不能为空'
            ]);
        }
        
        // 检查是否是商家
        $stmt = $this->db->prepare("SELECT id, balance FROM merchants WHERE user_id = ? AND status = 1");
        $stmt->execute([$userId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return json([
                'code' => 1,
                'msg' => '您不是商家或商家账号已被禁用'
            ]);
        }
        
        // 检查余额是否足够
        if ($merchant['balance'] < $amount) {
            return json([
                'code' => 1,
                'msg' => '余额不足'
            ]);
        }
        
        // 检查是否有未处理的提现申请
        $stmt = $this->db->prepare("SELECT id FROM merchant_withdraws 
                                   WHERE merchant_id = ? AND status = 0");
        $stmt->execute([$merchant['id']]);
        if ($stmt->fetch()) {
            return json([
                'code' => 1,
                'msg' => '您有未处理的提现申请，请等待处理完成后再申请'
            ]);
        }
        
        // 开始事务
        $this->db->beginTransaction();
        
        try {
            // 创建提现记录
            $stmt = $this->db->prepare("INSERT INTO merchant_withdraws (merchant_id, amount, account_name, account_number, 
                                       bank_name, status, create_time) 
                                       VALUES (?, ?, ?, ?, ?, 0, ?)");
            $stmt->execute([
                $merchant['id'],
                $amount,
                $account_name,
                $account_number,
                $bank_name,
                time()
            ]);
            
            // 扣除余额
            $stmt = $this->db->prepare("UPDATE merchants SET balance = balance - ? WHERE id = ?");
            $stmt->execute([$amount, $merchant['id']]);
            
            // 提交事务
            $this->db->commit();
            
            // 发送邮件通知管理员
            $this->sendWithdrawNotification($merchant['id'], $amount, $account_name, $account_number, $bank_name);
            
            return json([
                'code' => 0,
                'msg' => '提现申请提交成功，请等待审核'
            ]);
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            
            return json([
                'code' => 1,
                'msg' => '提现申请失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 发送提现通知邮件
     */
    private function sendWithdrawNotification($merchantId, $amount, $accountName, $accountNumber, $bankName) {
        $adminEmail = $this->config['admin_email'];
        
        if (empty($adminEmail)) {
            return;
        }
        
        // 获取商家信息
        $stmt = $this->db->prepare("SELECT m.name, u.username 
                                   FROM merchants m 
                                   LEFT JOIN users u ON m.user_id = u.id 
                                   WHERE m.id = ?");
        $stmt->execute([$merchantId]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $subject = '新商家提现申请通知';
        $message = "您好，管理员：\n\n";
        $message .= "系统收到了一个新的商家提现申请，详情如下：\n\n";
        $message .= "商家名称：{$merchant['name']}\n";
        $message .= "用户名：{$merchant['username']}\n";
        $message .= "提现金额：{$amount}\n";
        $message .= "收款人：{$accountName}\n";
        $message .= "收款账号：{$accountNumber}\n";
        $message .= "开户银行：{$bankName}\n\n";
        $message .= "请登录管理后台进行审核。\n\n";
        $message .= "此邮件由系统自动发送，请勿回复。";
        
        // 发送邮件
        sendMail($adminEmail, $subject, $message);
    }
}
