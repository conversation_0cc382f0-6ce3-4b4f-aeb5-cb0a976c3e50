<?php
/**
 * 认证控制器
 * API管理系统 - 用户认证与权限管理
 */

require_once __DIR__ . '/Admin.php';
require_once __DIR__ . '/User.php';

class AuthController {
    private $adminModel;
    private $userModel;
    
    public function __construct() {
        $this->adminModel = new Admin();
        $this->userModel = new User();
    }
    
    /**
     * 管理员登录
     */
    public function adminLogin() {
        try {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            $captcha = $_POST['captcha'] ?? '';
            $remember = isset($_POST['remember']);
            
            // 验证输入
            if (empty($username) || empty($password)) {
                throw new Exception('用户名和密码不能为空');
            }
            
            // 验证验证码
            if (!$this->verifyCaptcha($captcha)) {
                throw new Exception('验证码错误');
            }
            
            // 检查登录尝试次数
            $this->checkLoginAttempts($username);
            
            // 验证登录
            $admin = $this->adminModel->login($username, $password);
            if (!$admin) {
                $this->recordLoginAttempt($username, false);
                throw new Exception('用户名或密码错误');
            }
            
            // 清除登录尝试记录
            $this->clearLoginAttempts($username);
            
            // 设置会话
            $this->setAdminSession($admin, $remember);
            
            System::jsonResponse([
                'redirect' => '/api-system/public/admin/dashboard.html'
            ], 200, '登录成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    public function userLogin() {
        try {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                throw new Exception('用户名和密码不能为空');
            }
            
            // 检查登录尝试次数
            $this->checkLoginAttempts($username, 'user');
            
            // 验证登录
            $user = $this->userModel->login($username, $password);
            if (!$user) {
                $this->recordLoginAttempt($username, false, 'user');
                throw new Exception('用户名或密码错误');
            }
            
            // 清除登录尝试记录
            $this->clearLoginAttempts($username, 'user');
            
            // 生成访问令牌
            $token = $this->generateUserToken($user);
            
            System::jsonResponse([
                'token' => $token,
                'user' => $user,
                'expires_in' => System::getConfig('security.token_expire')
            ], 200, '登录成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 管理员注销
     */
    public function adminLogout() {
        session_destroy();
        System::jsonResponse(null, 200, '注销成功');
    }
    
    /**
     * 用户注册
     */
    public function userRegister() {
        try {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            $email = $_POST['email'] ?? '';
            $phone = $_POST['phone'] ?? '';
            
            // 验证输入
            if (empty($username) || empty($password)) {
                throw new Exception('用户名和密码不能为空');
            }
            
            if (strlen($password) < 6) {
                throw new Exception('密码长度不能少于6位');
            }
            
            // 检查用户名是否存在
            if ($this->userModel->usernameExists($username)) {
                throw new Exception('用户名已存在');
            }
            
            // 创建用户
            $userData = [
                'username' => $username,
                'password' => $password,
                'email' => $email,
                'phone' => $phone,
                'nickname' => $username,
                'level' => 1,
                'status' => 1
            ];
            
            $user = $this->userModel->create($userData);
            
            System::jsonResponse($user, 200, '注册成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 验证管理员权限
     */
    public function checkAdminAuth() {
        if (!isset($_SESSION['admin_id'])) {
            System::jsonResponse(null, 401, '请先登录');
        }
        
        $admin = $this->adminModel->find($_SESSION['admin_id']);
        if (!$admin || $admin['status'] != 1) {
            session_destroy();
            System::jsonResponse(null, 401, '账户状态异常，请重新登录');
        }
        
        return $admin;
    }
    
    /**
     * 验证用户Token
     */
    public function checkUserAuth() {
        $token = $this->getBearerToken();
        if (!$token) {
            System::jsonResponse(null, 401, '缺少访问令牌');
        }
        
        $payload = System::verifyToken($token);
        if (!$payload) {
            System::jsonResponse(null, 401, '访问令牌无效或已过期');
        }
        
        $user = $this->userModel->find($payload['user_id']);
        if (!$user || $user['status'] != 1) {
            System::jsonResponse(null, 401, '用户状态异常');
        }
        
        return $user;
    }
    
    /**
     * 验证API Key
     */
    public function checkApiKey() {
        $apiKey = $_GET['api_key'] ?? $_POST['api_key'] ?? $_SERVER['HTTP_X_API_KEY'] ?? '';
        
        if (empty($apiKey)) {
            System::jsonResponse(null, 401, '缺少API密钥');
        }
        
        $user = $this->userModel->findByApiKey($apiKey);
        if (!$user) {
            System::jsonResponse(null, 401, 'API密钥无效');
        }
        
        return $user;
    }
    
    /**
     * 验证验证码
     */
    private function verifyCaptcha($captcha) {
        if (!isset($_SESSION['captcha'])) {
            return false;
        }
        
        $isValid = strtolower($captcha) === strtolower($_SESSION['captcha']);
        unset($_SESSION['captcha']); // 验证后清除
        
        return $isValid;
    }
    
    /**
     * 检查登录尝试次数
     */
    private function checkLoginAttempts($username, $type = 'admin') {
        $key = "login_attempts_{$type}_{$username}";
        $attempts = $_SESSION[$key] ?? 0;
        $maxAttempts = System::getConfig('security.max_login_attempts');
        
        if ($attempts >= $maxAttempts) {
            $lockoutTime = System::getConfig('security.lockout_time');
            throw new Exception("登录尝试次数过多，请{$lockoutTime}秒后再试");
        }
    }
    
    /**
     * 记录登录尝试
     */
    private function recordLoginAttempt($username, $success, $type = 'admin') {
        $key = "login_attempts_{$type}_{$username}";
        
        if ($success) {
            unset($_SESSION[$key]);
        } else {
            $_SESSION[$key] = ($_SESSION[$key] ?? 0) + 1;
        }
    }
    
    /**
     * 清除登录尝试记录
     */
    private function clearLoginAttempts($username, $type = 'admin') {
        $key = "login_attempts_{$type}_{$username}";
        unset($_SESSION[$key]);
    }
    
    /**
     * 设置管理员会话
     */
    private function setAdminSession($admin, $remember = false) {
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_nickname'] = $admin['nickname'];
        $_SESSION['admin_role_id'] = $admin['role_id'];
        $_SESSION['login_time'] = time();
        
        if ($remember) {
            // 设置记住密码Cookie（7天）
            $token = System::generateToken([
                'admin_id' => $admin['id'],
                'exp' => time() + (7 * 24 * 3600)
            ]);
            
            setcookie('remember_token', $token, time() + (7 * 24 * 3600), '/');
        }
    }
    
    /**
     * 生成用户访问令牌
     */
    private function generateUserToken($user) {
        $payload = [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'level' => $user['level'],
            'iat' => time(),
            'exp' => time() + System::getConfig('security.token_expire')
        ];
        
        return System::generateToken($payload);
    }
    
    /**
     * 获取Bearer Token
     */
    private function getBearerToken() {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            $matches = [];
            if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        
        return $_GET['token'] ?? $_POST['token'] ?? '';
    }
    
    /**
     * 修改密码
     */
    public function changePassword() {
        try {
            $admin = $this->checkAdminAuth();
            
            $oldPassword = $_POST['old_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            if (empty($oldPassword) || empty($newPassword)) {
                throw new Exception('密码不能为空');
            }
            
            if ($newPassword !== $confirmPassword) {
                throw new Exception('两次输入的密码不一致');
            }
            
            if (strlen($newPassword) < 6) {
                throw new Exception('新密码长度不能少于6位');
            }
            
            $this->adminModel->changePassword($admin['id'], $oldPassword, $newPassword);
            
            System::jsonResponse(null, 200, '密码修改成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public function getCurrentUser() {
        try {
            if (isset($_SESSION['admin_id'])) {
                $admin = $this->checkAdminAuth();
                System::jsonResponse($admin, 200, '获取成功');
            } else {
                $user = $this->checkUserAuth();
                System::jsonResponse($user, 200, '获取成功');
            }
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 刷新用户Token
     */
    public function refreshToken() {
        try {
            $user = $this->checkUserAuth();
            $newToken = $this->generateUserToken($user);
            
            System::jsonResponse([
                'token' => $newToken,
                'expires_in' => System::getConfig('security.token_expire')
            ], 200, 'Token刷新成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
}