<?php
/**
 * 数据模型基类
 * API管理系统 - 数据模型基础类
 */

abstract class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 查找单条记录
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE {$this->primaryKey} = :id";
        $result = $this->db->fetchOne($sql, ['id' => $id]);
        return $result ? $this->hideFields($result) : null;
    }
    
    /**
     * 查找所有记录
     */
    public function findAll($conditions = [], $orderBy = null, $limit = null) {
        $sql = "SELECT * FROM {$this->getTableName()}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $results = $this->db->fetchAll($sql, $params);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 分页查询
     */
    public function paginate($page = 1, $perPage = 20, $conditions = [], $orderBy = null) {
        $offset = ($page - 1) * $perPage;
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->getTableName()}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
            $countSql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSql = "SELECT * FROM {$this->getTableName()}";
        if (!empty($conditions)) {
            $dataSql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $dataSql .= " ORDER BY {$orderBy}";
        }
        
        $dataSql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($dataSql, $params);
        $data = array_map([$this, 'hideFields'], $data);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 创建记录
     */
    public function create($data) {
        $data = $this->filterFillable($data);
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $id = $this->db->insert($this->table, $data);
        return $this->find($id);
    }
    
    /**
     * 更新记录
     */
    public function update($id, $data) {
        $data = $this->filterFillable($data);
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $affected = $this->db->update($this->table, $data, "{$this->primaryKey} = :id", ['id' => $id]);
        return $affected > 0 ? $this->find($id) : null;
    }
    
    /**
     * 删除记录
     */
    public function delete($id) {
        return $this->db->delete($this->table, "{$this->primaryKey} = :id", ['id' => $id]) > 0;
    }
    
    /**
     * 批量删除
     */
    public function deleteMany($ids) {
        if (empty($ids)) {
            return false;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "DELETE FROM {$this->getTableName()} WHERE {$this->primaryKey} IN ({$placeholders})";
        
        $stmt = $this->db->query($sql, $ids);
        return $stmt->rowCount() > 0;
    }
    
    /**
     * 获取完整表名
     */
    protected function getTableName() {
        return $this->db->getPrefix() . $this->table;
    }
    
    /**
     * 过滤可填充字段
     */
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    /**
     * 隐藏敏感字段
     */
    protected function hideFields($data) {
        if (empty($this->hidden)) {
            return $data;
        }
        
        foreach ($this->hidden as $field) {
            unset($data[$field]);
        }
        
        return $data;
    }
    
    /**
     * 执行原生SQL查询
     */
    public function query($sql, $params = []) {
        return $this->db->query($sql, $params);
    }
    
    /**
     * 获取单条记录（原生SQL）
     */
    public function fetchOne($sql, $params = []) {
        return $this->db->fetchOne($sql, $params);
    }
    
    /**
     * 获取多条记录（原生SQL）
     */
    public function fetchAll($sql, $params = []) {
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->db->rollback();
    }
    
    /**
     * 统计记录数
     */
    public function count($conditions = []) {
        $sql = "SELECT COUNT(*) as total FROM {$this->getTableName()}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['total'];
    }
    
    /**
     * 检查记录是否存在
     */
    public function exists($conditions) {
        return $this->count($conditions) > 0;
    }
    
    /**
     * 获取第一条记录
     */
    public function first($conditions = [], $orderBy = null) {
        $results = $this->findAll($conditions, $orderBy, 1);
        return !empty($results) ? $results[0] : null;
    }
    
    /**
     * 获取最后一条记录
     */
    public function last($conditions = [], $orderBy = null) {
        if (!$orderBy) {
            $orderBy = "{$this->primaryKey} DESC";
        }
        $results = $this->findAll($conditions, $orderBy, 1);
        return !empty($results) ? $results[0] : null;
    }
}