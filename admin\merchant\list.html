<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商家管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">商家管理</h2>
        </div>
        <div class="layui-card-body">
            <!-- 搜索栏 -->
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">商家名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="name" placeholder="请输入商家名称" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">等级</label>
                        <div class="layui-input-inline">
                            <select name="level">
                                <option value="">全部等级</option>
                                <option value="1">普通商家</option>
                                <option value="2">高级商家</option>
                                <option value="3">VIP商家</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="1">正常</option>
                                <option value="0">禁用</option>
                                <option value="2">待审核</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">搜索</button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">重置</button>
                    </div>
                </div>
            </div>

            <!-- 表格工具栏 -->
            <div class="layui-btn-group">
                <button class="layui-btn" id="add-btn"><i class="layui-icon">&#xe654;</i>添加商家</button>
                <button class="layui-btn layui-btn-danger" id="del-btn"><i class="layui-icon">&#xe640;</i>批量删除</button>
                <button class="layui-btn layui-btn-normal" id="export-btn"><i class="layui-icon">&#xe67d;</i>导出数据</button>
            </div>
            
            <!-- 数据表格 -->
            <table class="layui-hide" id="merchant-table" lay-filter="merchant-table"></table>
            
            <!-- 表格操作列 -->
            <script type="text/html" id="table-bar">
                <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">查看</a>
                <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
            </script>
            
            <!-- 表格状态列 -->
            <script type="text/html" id="status-switch">
                <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="正常|禁用" lay-filter="status-switch" {{ d.status == 1 ? 'checked' : '' }}>
            </script>
        </div>
    </div>
</div>

<!-- 添加/编辑商家弹窗 -->
<script type="text/html" id="merchant-form-tpl">
    <form class="layui-form" id="merchant-form" lay-filter="merchant-form" style="padding: 20px;">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">商家名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入商家名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系人</label>
            <div class="layui-input-block">
                <input type="text" name="contact_name" placeholder="请输入联系人姓名" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系电话</label>
            <div class="layui-input-block">
                <input type="text" name="contact_phone" placeholder="请输入联系电话" class="layui-input" lay-verify="required|phone">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系邮箱</label>
            <div class="layui-input-block">
                <input type="text" name="contact_email" placeholder="请输入联系邮箱" class="layui-input" lay-verify="required|email">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">商家等级</label>
            <div class="layui-input-block">
                <select name="level" lay-verify="required">
                    <option value="">请选择商家等级</option>
                    <option value="1">普通商家</option>
                    <option value="2">高级商家</option>
                    <option value="3">VIP商家</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">佣金比例</label>
            <div class="layui-input-block">
                <input type="number" name="commission_rate" placeholder="请输入佣金比例" class="layui-input" lay-verify="required|number" min="0" max="100" step="0.1">
                <div class="layui-form-mid layui-word-aux">百分比，例如：30表示30%</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">结算周期</label>
            <div class="layui-input-block">
                <select name="settlement_cycle" lay-verify="required">
                    <option value="">请选择结算周期</option>
                    <option value="daily">日结</option>
                    <option value="weekly">周结</option>
                    <option value="monthly">月结</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="正常" checked>
                <input type="radio" name="status" value="0" title="禁用">
                <input type="radio" name="status" value="2" title="待审核">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="merchant-form-submit">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<!-- 查看商家详情弹窗 -->
<script type="text/html" id="merchant-detail-tpl">
    <div style="padding: 20px;">
        <table class="layui-table" lay-skin="nob">
            <colgroup>
                <col width="100">
                <col>
            </colgroup>
            <tbody>
                <tr>
                    <td>商家名称</td>
                    <td>{{ d.name }}</td>
                </tr>
                <tr>
                    <td>联系人</td>
                    <td>{{ d.contact_name }}</td>
                </tr>
                <tr>
                    <td>联系电话</td>
                    <td>{{ d.contact_phone }}</td>
                </tr>
                <tr>
                    <td>联系邮箱</td>
                    <td>{{ d.contact_email }}</td>
                </tr>
                <tr>
                    <td>商家等级</td>
                    <td>
                        {{# if(d.level == 1){ }}
                        普通商家
                        {{# }else if(d.level == 2){ }}
                        高级商家
                        {{# }else if(d.level == 3){ }}
                        VIP商家
                        {{# } }}
                    </td>
                </tr>
                <tr>
                    <td>佣金比例</td>
                    <td>{{ d.commission_rate }}%</td>
                </tr>
                <tr>
                    <td>结算周期</td>
                    <td>
                        {{# if(d.settlement_cycle == 'daily'){ }}
                        日结
                        {{# }else if(d.settlement_cycle == 'weekly'){ }}
                        周结
                        {{# }else if(d.settlement_cycle == 'monthly'){ }}
                        月结
                        {{# } }}
                    </td>
                </tr>
                <tr>
                    <td>状态</td>
                    <td>
                        {{# if(d.status == 1){ }}
                        <span class="layui-badge layui-bg-green">正常</span>
                        {{# }else if(d.status == 0){ }}
                        <span class="layui-badge layui-bg-gray">禁用</span>
                        {{# }else if(d.status == 2){ }}
                        <span class="layui-badge layui-bg-orange">待审核</span>
                        {{# } }}
                    </td>
                </tr>
                <tr>
                    <td>注册时间</td>
                    <td>{{ d.created_at }}</td>
                </tr>
                <tr>
                    <td>更新时间</td>
                    <td>{{ d.updated_at }}</td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td>{{ d.remark || '无' }}</td>
                </tr>
            </tbody>
        </table>
        
        <div class="layui-tab" lay-filter="merchant-detail-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">API列表</li>
                <li>收入统计</li>
                <li>结算记录</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="layui-table" id="merchant-api-table"></table>
                </div>
                <div class="layui-tab-item">
                    <div id="merchant-income-chart" style="width: 100%; height: 300px;"></div>
                </div>
                <div class="layui-tab-item">
                    <table class="layui-table" id="merchant-settlement-table"></table>
                </div>
            </div>
        </div>
    </div>
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../../Easyweb/assets/libs/echarts/echarts.min.js"></script>
<script>
layui.use(['table', 'form', 'layer', 'util', 'laytpl', 'element'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var util = layui.util;
    var laytpl = layui.laytpl;
    var element = layui.element;
    var $ = layui.jquery;
    
    // 渲染表格
    table.render({
        elem: '#merchant-table',
        url: '../controllers/MerchantController.php?action=getList',
        page: true,
        toolbar: true,
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'name', title: '商家名称', minWidth: 150},
            {field: 'contact_name', title: '联系人', width: 100},
            {field: 'contact_phone', title: '联系电话', width: 120},
            {field: 'level', title: '等级', width: 100, templet: function(d){
                if(d.level == 1) return '<span class="layui-badge layui-bg-blue">普通商家</span>';
                else if(d.level == 2) return '<span class="layui-badge layui-bg-green">高级商家</span>';
                else if(d.level == 3) return '<span class="layui-badge layui-bg-orange">VIP商家</span>';
                return '未知';
            }},
            {field: 'commission_rate', title: '佣金比例', width: 100, templet: function(d){
                return d.commission_rate + '%';
            }},
            {field: 'settlement_cycle', title: '结算周期', width: 100, templet: function(d){
                if(d.settlement_cycle == 'daily') return '日结';
                else if(d.settlement_cycle == 'weekly') return '周结';
                else if(d.settlement_cycle == 'monthly') return '月结';
                return '未知';
            }},
            {field: 'status', title: '状态', width: 110, templet: '#status-switch'},
            {field: 'created_at', title: '注册时间', width: 160, sort: true},
            {title: '操作', width: 160, align: 'center', toolbar: '#table-bar'}
        ]],
        done: function(){
            // 表格渲染完成后的回调
        }
    });
    
    // 表格工具条事件
    table.on('tool(merchant-table)', function(obj){
        var data = obj.data;
        var event = obj.event;
        
        if(event === 'edit'){
            // 编辑商家
            showEditForm(data);
        } else if(event === 'del'){
            // 删除商家
            layer.confirm('确定要删除该商家吗？', function(index){
                $.ajax({
                    url: '../controllers/MerchantController.php?action=delete',
                    type: 'POST',
                    data: {id: data.id},
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 200){
                            layer.msg('删除成功');
                            obj.del();
                        } else {
                            layer.msg(res.msg || '删除失败');
                        }
                    },
                    error: function(){
                        layer.msg('服务器错误');
                    }
                });
                layer.close(index);
            });
        } else if(event === 'view'){
            // 查看商家详情
            showMerchantDetail(data);
        }
    });
    
    // 添加商家按钮点击事件
    $('#add-btn').click(function(){
        showEditForm();
    });
    
    // 批量删除按钮点击事件
    $('#del-btn').click(function(){
        var checkStatus = table.checkStatus('merchant-table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要删除的商家');
            return;
        }
        
        layer.confirm('确定要删除选中的 ' + data.length + ' 个商家吗？', function(index){
            var ids = data.map(function(item){
                return item.id;
            });
            
            $.ajax({
                url: '../controllers/MerchantController.php?action=batchDelete',
                type: 'POST',
                data: {ids: ids.join(',')},
                dataType: 'json',
                success: function(res){
                    if(res.code === 200){
                        layer.msg('删除成功');
                        table.reload('merchant-table');
                    } else {
                        layer.msg(res.msg || '删除失败');
                    }
                },
                error: function(){
                    layer.msg('服务器错误');
                }
            });
            
            layer.close(index);
        });
    });
    
    // 导出数据按钮点击事件
    $('#export-btn').click(function(){
        table.exportFile('merchant-table', '商家数据.xls');
    });
    
    // 搜索按钮点击事件
    $('#search-btn').click(function(){
        var name = $('input[name="name"]').val();
        var level = $('select[name="level"]').val();
        var status = $('select[name="status"]').val();
        
        table.reload('merchant-table', {
            page: {
                curr: 1
            },
            where: {
                name: name,
                level: level,
                status: status
            }
        });
    });
    
    // 重置按钮点击事件
    $('#reset-btn').click(function(){
        $('input[name="name"]').val('');
        $('select[name="level"]').val('');
        $('select[name="status"]').val('');
        form.render('select');
    });
    
    // 状态开关事件
    form.on('switch(status-switch)', function(obj){
        var id = this.value;
        var status = obj.elem.checked ? 1 : 0;
        
        $.ajax({
            url: '../controllers/MerchantController.php?action=updateStatus',
            type: 'POST',
            data: {
                id: id,
                status: status
            },
            dataType: 'json',
            success: function(res){
                if(res.code !== 200){
                    layer.msg(res.msg || '操作失败');
                    $(obj.elem).prop('checked', !obj.elem.checked);
                    form.render('checkbox');
                }
            },
            error: function(){
                layer.msg('服务器错误');
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        });
    });
    
    // 表单提交事件
    form.on('submit(merchant-form-submit)', function(data){
        var formData = data.field;
        var url = formData.id ? '../controllers/MerchantController.php?action=update' : '../controllers/MerchantController.php?action=add';
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(res){
                if(res.code === 200){
                    layer.msg('保存成功');
                    layer.closeAll('page');
                    table.reload('merchant-table');
                } else {
                    layer.msg(res.msg || '保存失败');
                }
            },
            error: function(){
                layer.msg('服务器错误');
            }
        });
        
        return false;
    });
    
    // 显示编辑表单
    function showEditForm(data){
        var title = data ? '编辑商家' : '添加商家';
        
        layer.open({
            type: 1,
            title: title,
            area: ['600px', '600px'],
            content: $('#merchant-form-tpl').html(),
            success: function(){
                form.render();
                
                if(data){
                    form.val('merchant-form', data);
                }
            }
        });
    }
    
    // 显示商家详情
    function showMerchantDetail(data){
        layer.open({
            type: 1,
            title: '商家详情 - ' + data.name,
            area: ['800px', '600px'],
            content: laytpl($('#merchant-detail-tpl').html()).render(data),
            success: function(){
                element.render('tab');
                
                // 渲染API表格
                table.render({
                    elem: '#merchant-api-table',
                    url: '../controllers/MerchantController.php?action=getApiList&merchant_id=' + data.id,
                    cols: [[
                        {field: 'name', title: 'API名称'},
                        {field: 'category', title: '分类'},
                        {field: 'price', title: '价格'},
                        {field: 'call_count', title: '调用次数'},
                        {field: 'status', title: '状态', templet: function(d){
                            return d.status == 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge layui-bg-gray">禁用</span>';
                        }}
                    ]]
                });
                
                // 渲染结算表格
                table.render({
                    elem: '#merchant-settlement-table',
                    url: '../controllers/MerchantController.php?action=getSettlementList&merchant_id=' + data.id,
                    cols: [[
                        {field: 'id', title: 'ID', width: 80},
                        {field: 'amount', title: '结算金额'},
                        {field: 'period', title: '结算周期'},
                        {field: 'status', title: '状态', templet: function(d){
                            if(d.status == 'pending') return '<span class="layui-badge layui-bg-orange">待处理</span>';
                            else if(d.status == 'completed') return '<span class="layui-badge layui-bg-green">已完成</span>';
                            else if(d.status == 'failed') return '<span class="layui-badge layui-bg-red">失败</span>';
                            return '未知';
                        }},
                        {field: 'created_at', title: '创建时间'}
                    ]]
                });
                
                // 渲染收入图表
                $.ajax({
                    url: '../controllers/MerchantController.php?action=getIncomeData&merchant_id=' + data.id,
                    type: 'GET',
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 200){
                            renderIncomeChart(res.data);
                        }
                    }
                });
            }
        });
    }
    
    // 渲染收入图表
    function renderIncomeChart(data){
        var myChart = echarts.init(document.getElementById('merchant-income-chart'));
        
        var option = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['收入金额', 'API调用次数']
            },
            xAxis: {
                type: 'category',
                data: data.dates
            },
            yAxis: [
                {
                    type: 'value',
                    name: '金额',
                    axisLabel: {
                        formatter: '{value} 元'
                    }
                },
                {
                    type: 'value',
                    name: '调用次数',
                    axisLabel: {
                        formatter: '{value} 次'
                    }
                }
            ],
            series: [
                {
                    name: '收入金额',
                    type: 'bar',
                    data: data.amounts
                },
                {
                    name: 'API调用次数',
                    type: 'line',
                    yAxisIndex: 1,
                    data: data.calls
                }
            ]
        };
        
        myChart.setOption(option);
    }
});
</script>
</body>
</html>