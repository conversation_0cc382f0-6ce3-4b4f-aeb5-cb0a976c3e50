<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>工单管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">工单管理</h2>
        </div>
        <div class="layui-card-body">
            <!-- 工具栏 -->
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="pending">待处理</option>
                                <option value="processing">处理中</option>
                                <option value="resolved">已解决</option>
                                <option value="closed">已关闭</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">优先级</label>
                        <div class="layui-input-inline">
                            <select name="priority">
                                <option value="">全部优先级</option>
                                <option value="low">低</option>
                                <option value="normal">普通</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <input type="text" name="title" placeholder="请输入标题搜索" class="layui-input" style="width: 200px;">
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="search-btn">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="ticket-table" lay-filter="ticket-table"></table>
        </div>
    </div>
</div>

<!-- 工单详情弹窗 -->
<div id="ticket-detail" style="display: none; padding: 20px;">
    <div class="layui-row">
        <div class="layui-col-md8">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3 id="ticket-title"></h3>
                </div>
                <div class="layui-card-body">
                    <div id="ticket-content"></div>
                    
                    <!-- 回复列表 -->
                    <div id="reply-list" style="margin-top: 20px;">
                        <h4>回复记录</h4>
                        <div id="replies"></div>
                    </div>
                    
                    <!-- 回复表单 -->
                    <div style="margin-top: 20px;">
                        <h4>添加回复</h4>
                        <form class="layui-form" lay-filter="reply-form">
                            <input type="hidden" name="ticket_id">
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <textarea name="content" placeholder="请输入回复内容" class="layui-textarea" style="min-height: 100px;"></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="save-reply">提交回复</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>工单信息</h3>
                </div>
                <div class="layui-card-body">
                    <form class="layui-form" lay-filter="ticket-form">
                        <input type="hidden" name="id">
                        <div class="layui-form-item">
                            <label class="layui-form-label">工单编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="ticket_no" class="layui-input" readonly>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">提交用户</label>
                            <div class="layui-input-block">
                                <input type="text" name="username" class="layui-input" readonly>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">提交时间</label>
                            <div class="layui-input-block">
                                <input type="text" name="created_at" class="layui-input" readonly>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <select name="status">
                                    <option value="pending">待处理</option>
                                    <option value="processing">处理中</option>
                                    <option value="resolved">已解决</option>
                                    <option value="closed">已关闭</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">优先级</label>
                            <div class="layui-input-block">
                                <select name="priority">
                                    <option value="low">低</option>
                                    <option value="normal">普通</option>
                                    <option value="high">高</option>
                                    <option value="urgent">紧急</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">处理人员</label>
                            <div class="layui-input-block">
                                <select name="admin_id" lay-search>
                                    <option value="">请选择处理人员</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="save-ticket">保存更改</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="view">查看详情</button>
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</button>
    </div>
</script>

<script type="text/html" id="status-tpl">
    {{# if(d.status == 'pending'){ }}
        <span class="layui-badge">待处理</span>
    {{# } else if(d.status == 'processing'){ }}
        <span class="layui-badge layui-bg-blue">处理中</span>
    {{# } else if(d.status == 'resolved'){ }}
        <span class="layui-badge layui-bg-green">已解决</span>
    {{# } else if(d.status == 'closed'){ }}
        <span class="layui-badge layui-bg-gray">已关闭</span>
    {{# } }}
</script>

<script type="text/html" id="priority-tpl">
    {{# if(d.priority == 'low'){ }}
        <span class="layui-badge layui-bg-gray">低</span>
    {{# } else if(d.priority == 'normal'){ }}
        <span class="layui-badge layui-bg-blue">普通</span>
    {{# } else if(d.priority == 'high'){ }}
        <span class="layui-badge layui-bg-orange">高</span>
    {{# } else if(d.priority == 'urgent'){ }}
        <span class="layui-badge layui-bg-red">紧急</span>
    {{# } }}
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;

    // 加载管理员列表
    loadAdmins();

    // 渲染表格
    table.render({
        elem: '#ticket-table',
        url: '../controllers/TicketController.php?action=getList',
        toolbar: '#toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', width: 50},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'ticket_no', title: '工单编号', width: 150},
            {field: 'title', title: '标题', width: 300},
            {field: 'username', title: '提交用户', width: 120},
            {field: 'priority', title: '优先级', width: 100, templet: '#priority-tpl'},
            {field: 'status', title: '状态', width: 100, templet: '#status-tpl'},
            {field: 'admin_name', title: '处理人员', width: 120},
            {field: 'created_at', title: '创建时间', width: 180, sort: true},
            {title: '操作', width: 150, align: 'center', toolbar: '#toolbar'}
        ]],
        page: true,
        height: 'full-220'
    });

    // 加载管理员列表
    function loadAdmins() {
        $.get('../controllers/TicketController.php?action=getAdmins', function(res){
            if(res.code === 200){
                var html = '<option value="">请选择处理人员</option>';
                res.data.forEach(function(item){
                    html += '<option value="' + item.id + '">' + item.username + '</option>';
                });
                $('select[name="admin_id"]').html(html);
                form.render('select');
            }
        }, 'json');
    }

    // 表格工具栏事件
    table.on('tool(ticket-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'view'){
            viewTicket(data.id);
        } else if(obj.event === 'del'){
            layer.confirm('确定删除这个工单吗？', function(index){
                $.post('../controllers/TicketController.php?action=delete', {id: data.id}, function(res){
                    if(res.code === 200){
                        layer.msg('删除成功');
                        table.reload('ticket-table');
                    } else {
                        layer.msg('删除失败: ' + res.msg);
                    }
                }, 'json');
                layer.close(index);
            });
        }
    });

    // 查看工单详情
    function viewTicket(id) {
        $.get('../controllers/TicketController.php?action=getDetail', {id: id}, function(res){
            if(res.code === 200){
                var ticket = res.data.ticket;
                var replies = res.data.replies;
                
                // 设置工单信息
                $('#ticket-title').text(ticket.title);
                $('#ticket-content').html(ticket.content);
                
                // 设置回复列表
                var replyHtml = '';
                replies.forEach(function(reply){
                    replyHtml += '<div class="layui-card" style="margin-bottom: 10px;">';
                    replyHtml += '<div class="layui-card-header">';
                    replyHtml += reply.is_admin ? '<span class="layui-badge layui-bg-blue">客服</span> ' : '<span class="layui-badge">用户</span> ';
                    replyHtml += reply.username + ' <span style="float: right;">' + reply.created_at + '</span>';
                    replyHtml += '</div>';
                    replyHtml += '<div class="layui-card-body">' + reply.content + '</div>';
                    replyHtml += '</div>';
                });
                $('#replies').html(replyHtml);
                
                // 设置表单值
                form.val('ticket-form', ticket);
                form.val('reply-form', {ticket_id: ticket.id});
                
                // 打开弹窗
                layer.open({
                    type: 1,
                    title: '工单详情',
                    content: $('#ticket-detail'),
                    area: ['900px', '600px'],
                    maxmin: true
                });
            } else {
                layer.msg('获取工单详情失败: ' + res.msg);
            }
        }, 'json');
    }

    // 保存工单信息
    form.on('submit(save-ticket)', function(data){
        $.post('../controllers/TicketController.php?action=updateTicket', data.field, function(res){
            if(res.code === 200){
                layer.msg('保存成功');
                table.reload('ticket-table');
            } else {
                layer.msg('保存失败: ' + res.msg);
            }
        }, 'json');
        return false;
    });

    // 提交回复
    form.on('submit(save-reply)', function(data){
        $.post('../controllers/TicketController.php?action=addReply', data.field, function(res){
            if(res.code === 200){
                layer.msg('回复成功');
                // 重新加载工单详情
                viewTicket(data.field.ticket_id);
            } else {
                layer.msg('回复失败: ' + res.msg);
            }
        }, 'json');
        return false;
    });

    // 搜索
    $('#search-btn').click(function(){
        var title = $('input[name="title"]').val();
        var status = $('select[name="status"]').val();
        var priority = $('select[name="priority"]').val();
        table.reload('ticket-table', {
            where: {title: title, status: status, priority: priority},
            page: {curr: 1}
        });
    });
});
</script>
</body>
</html>