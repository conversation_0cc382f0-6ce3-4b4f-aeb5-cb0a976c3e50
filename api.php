<?php
/**
 * API入口文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('BASE_PATH', __DIR__);
define('BASE_URL', (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST']);

// 自动加载类
spl_autoload_register(function ($class) {
    $file = BASE_PATH . '/core/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 引入辅助函数
require_once BASE_PATH . '/includes/functions.php';

// 处理API请求
header('Content-Type: application/json; charset=utf-8');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 获取API路径
$path = isset($_GET['path']) ? trim($_GET['path'], '/') : '';

if (empty($path)) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少API路径',
        'data' => null
    ]);
    exit;
}

// 合并GET和POST参数
$params = array_merge($_GET, $_POST);

// 处理JSON请求体
$jsonBody = file_get_contents('php://input');
if (!empty($jsonBody)) {
    $jsonParams = json_decode($jsonBody, true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($jsonParams)) {
        $params = array_merge($params, $jsonParams);
    }
}

// 移除path参数
if (isset($params['path'])) {
    unset($params['path']);
}

try {
    // 初始化API处理器
    $apiHandler = new ApiHandler();
    
    // 处理请求
    $result = $apiHandler->handleRequest($path, $params);
    
    // 输出结果
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    // 记录错误日志
    error_log('API错误: ' . $e->getMessage());
    
    // 返回错误信息
    echo json_encode([
        'code' => 500,
        'message' => '服务器内部错误',
        'data' => null
    ]);
}