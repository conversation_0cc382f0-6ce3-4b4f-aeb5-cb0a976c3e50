<?php
/**
 * API商业系统接口入口文件
 */
session_start();

// 加载配置文件
$dbConfig = require 'config/database.php';
$appConfig = require 'config/app.php';

// 加载函数库
require 'includes/functions.php';

// 设置时区
date_default_timezone_set($appConfig['timezone']);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-KEY');

// 连接数据库
try {
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败',
        'error' => $appConfig['debug'] ? $e->getMessage() : null
    ]);
    exit;
}

// 获取请求方法和路径
$method = $_SERVER['REQUEST_METHOD'];
$path = isset($_GET['path']) ? $_GET['path'] : '';
$path = trim($path, '/');
$segments = explode('/', $path);

// 获取API密钥
$api_key = isset($_SERVER['HTTP_X_API_KEY']) ? $_SERVER['HTTP_X_API_KEY'] : '';
if (empty($api_key) && isset($_GET['api_key'])) {
    $api_key = $_GET['api_key'];
}

// 如果没有提供API密钥，返回错误
if (empty($api_key)) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => '未提供API密钥'
    ]);
    exit;
}

// 检查API密钥是否有效
if (!check_api_key($api_key)) {
    http_response_code(401);
    echo json_encode([
        'code' => 401,
        'message' => 'API密钥无效或已被限制'
    ]);
    exit;
}

// 获取用户ID
$stmt = $pdo->prepare("SELECT user_id FROM {$dbConfig['prefix']}api_keys WHERE api_key = :api_key");
$stmt->execute([':api_key' => $api_key]);
$user_id = $stmt->fetchColumn();

// 处理API请求
if (empty($segments[0])) {
    // 如果没有指定API，返回API列表
    $stmt = $pdo->query("SELECT id, name, description, url, method, is_free, price FROM {$dbConfig['prefix']}apis WHERE status = 1");
    $apis = $stmt->fetchAll();
    
    echo json_encode([
        'code' => 0,
        'message' => 'success',
        'data' => [
            'apis' => $apis
        ]
    ]);
    exit;
} else {
    // 根据路径查找API
    $api_path = $segments[0];
    
    $stmt = $pdo->prepare("SELECT * FROM {$dbConfig['prefix']}apis WHERE url LIKE :url AND status = 1");
    $stmt->execute([':url' => "%{$api_path}%"]);
    $api = $stmt->fetch();
    
    if (!$api) {
        http_response_code(404);
        echo json_encode([
            'code' => 404,
            'message' => 'API不存在'
        ]);
        exit;
    }
    
    // 检查请求方法是否匹配
    if ($api['method'] != $method) {
        http_response_code(405);
        echo json_encode([
            'code' => 405,
            'message' => '请求方法不允许，应为 ' . $api['method']
        ]);
        exit;
    }
    
    // 检查用户是否有权限调用该API
    if (!check_api_permission($user_id, $api['id'])) {
        http_response_code(403);
        echo json_encode([
            'code' => 403,
            'message' => '无权限调用该API，请先购买或升级VIP'
        ]);
        exit;
    }
    
    // 记录开始时间，用于计算响应时间
    $start_time = microtime(true);
    
    // 处理API调用
    $api_file = 'api_handlers/' . $api_path . '.php';
    if (file_exists($api_file)) {
        // 如果存在对应的处理文件，则调用它
        require $api_file;
        
        // 调用处理函数
        $function_name = 'handle_' . strtolower($method) . '_' . str_replace('/', '_', $api_path);
        if (function_exists($function_name)) {
            try {
                // 扣除API调用费用
                if (!$api['is_free'] && !deduct_api_fee($user_id, $api['id'])) {
                    http_response_code(402);
                    echo json_encode([
                        'code' => 402,
                        'message' => '余额不足，请先充值'
                    ]);
                    exit;
                }
                
                // 调用API处理函数
                $result = $function_name($_REQUEST);
                
                // 计算响应时间
                $response_time = round((microtime(true) - $start_time) * 1000);
                
                // 记录API调用日志
                log_api_call($api['id'], $api_key, 200, $response_time);
                
                // 更新API调用次数
                $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}apis SET call_count = call_count + 1 WHERE id = :id");
                $stmt->execute([':id' => $api['id']]);
                
                // 返回结果
                echo json_encode([
                    'code' => 0,
                    'message' => 'success',
                    'data' => $result
                ]);
                exit;
            } catch (Exception $e) {
                // 计算响应时间
                $response_time = round((microtime(true) - $start_time) * 1000);
                
                // 记录API调用日志
                log_api_call($api['id'], $api_key, 500, $response_time);
                
                http_response_code(500);
                echo json_encode([
                    'code' => 500,
                    'message' => 'API调用失败',
                    'error' => $appConfig['debug'] ? $e->getMessage() : null
                ]);
                exit;
            }
        } else {
            http_response_code(501);
            echo json_encode([
                'code' => 501,
                'message' => 'API处理函数不存在'
            ]);
            exit;
        }
    } else {
        // 如果是远程API，则转发请求
        if (strpos($api['url'], 'http') === 0) {
            // 构建请求参数
            $params = $_REQUEST;
            unset($params['path']);
            unset($params['api_key']);
            
            // 添加API密钥参数（如果远程API需要）
            if (!empty($api['remote_key'])) {
                $params['key'] = $api['remote_key'];
            }
            
            // 发起远程请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api['url']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            if ($method == 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            } else {
                $query = http_build_query($params);
                curl_setopt($ch, CURLOPT_URL, $api['url'] . '?' . $query);
            }
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // 计算响应时间
            $response_time = round((microtime(true) - $start_time) * 1000);
            
            // 记录API调用日志
            log_api_call($api['id'], $api_key, $http_code, $response_time);
            
            if ($response === false) {
                http_response_code(502);
                echo json_encode([
                    'code' => 502,
                    'message' => '远程API调用失败',
                    'error' => $appConfig['debug'] ? $error : null
                ]);
                exit;
            }
            
            // 更新API调用次数
            $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}apis SET call_count = call_count + 1 WHERE id = :id");
            $stmt->execute([':id' => $api['id']]);
            
            // 返回远程API的响应
            header('Content-Type: application/json; charset=utf-8');
            echo $response;
            exit;
        } else {
            http_response_code(404);
            echo json_encode([
                'code' => 404,
                'message' => 'API处理文件不存在'
            ]);
            exit;
        }
    }
}