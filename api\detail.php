<?php
// 获取API ID
$api_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($api_id <= 0) {
    header('Location: /api/market');
    exit;
}

// 获取API详情
$stmt = $pdo->prepare("
    SELECT a.*, c.name as category_name, u.username as creator_name 
    FROM api_apis a 
    LEFT JOIN api_categories c ON a.category_id = c.id 
    LEFT JOIN api_users u ON a.creator_id = u.id 
    WHERE a.id = ? AND a.status = 1
");
$stmt->execute([$api_id]);
$api = $stmt->fetch();

if (!$api) {
    header('Location: /api/market');
    exit;
}

// 获取API参数
$stmt = $pdo->prepare("SELECT * FROM api_parameters WHERE api_id = ? ORDER BY sort_order ASC");
$stmt->execute([$api_id]);
$parameters = $stmt->fetchAll();

// 获取API响应示例
$stmt = $pdo->prepare("SELECT * FROM api_responses WHERE api_id = ? ORDER BY id ASC LIMIT 1");
$stmt->execute([$api_id]);
$response = $stmt->fetch();

// 获取相关API
$stmt = $pdo->prepare("
    SELECT * FROM api_apis 
    WHERE category_id = ? AND id != ? AND status = 1 
    ORDER BY call_count DESC 
    LIMIT 4
");
$stmt->execute([$api['category_id'], $api_id]);
$relatedApis = $stmt->fetchAll();

// 检查用户是否已购买（如果是付费API）
$hasPurchased = false;
if (isset($_SESSION['user_id']) && $api['is_free'] == 0) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM api_purchases 
        WHERE user_id = ? AND api_id = ? AND status = 1
    ");
    $stmt->execute([$_SESSION['user_id'], $api_id]);
    $hasPurchased = ($stmt->fetchColumn() > 0);
}

// 处理购买请求
$purchaseError = '';
$purchaseSuccess = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['purchase']) && isset($_SESSION['user_id'])) {
    if ($api['is_free'] == 1) {
        $purchaseError = '该API是免费的，无需购买';
    } else {
        // 检查用户余额
        $stmt = $pdo->prepare("SELECT balance FROM api_users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $userBalance = $stmt->fetchColumn();
        
        // 根据用户角色确定价格
        $price = $api['price'];
        if ($_SESSION['user_role'] === 'merchant') {
            $price = $api['merchant_price'];
        }
        
        if ($userBalance < $price) {
            $purchaseError = '余额不足，请先充值';
        } else {
            // 扣除余额
            $stmt = $pdo->prepare("UPDATE api_users SET balance = balance - ? WHERE id = ?");
            $stmt->execute([$price, $_SESSION['user_id']]);
            
            // 记录购买记录
            $stmt = $pdo->prepare("
                INSERT INTO api_purchases (user_id, api_id, price, created_at, status) 
                VALUES (?, ?, ?, NOW(), 1)
            ");
            $stmt->execute([$_SESSION['user_id'], $api_id, $price]);
            
            // 更新商家余额（如果是商家API）
            if ($api['creator_id'] > 0) {
                $commission_rate = 0.8; // 商家分成比例，可以从系统配置中获取
                $merchant_income = $price * $commission_rate;
                
                $stmt = $pdo->prepare("UPDATE api_users SET balance = balance + ? WHERE id = ?");
                $stmt->execute([$merchant_income, $api['creator_id']]);
            }
            
            $purchaseSuccess = '购买成功，现在您可以使用该API了';
            $hasPurchased = true;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $api['name']; ?> - API详情 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="/public/css/font-awesome.min.css">
    <link rel="stylesheet" href="/public/css/prism.css">
    <link rel="stylesheet" href="/public/css/style.css">
</head>
<body>
    <!-- 头部导航 -->
    <?php include '../templates/common/header.php'; ?>
    
    <div class="container py-5">
        <div class="row">
            <!-- 左侧API详情 -->
            <div class="col-md-8">
                <?php if ($purchaseError): ?>
                <div class="alert alert-danger"><?php echo $purchaseError; ?></div>
                <?php endif; ?>
                
                <?php if ($purchaseSuccess): ?>
                <div class="alert alert-success"><?php echo $purchaseSuccess; ?></div>
                <?php endif; ?>
                
                <!-- API基本信息 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h1 class="h3 mb-3"><?php echo $api['name']; ?></h1>
                        <div class="mb-3">
                            <span class="badge badge-primary mr-2"><?php echo $api['category_name']; ?></span>
                            <span class="badge badge-<?php echo $api['method'] === 'GET' ? 'success' : ($api['method'] === 'POST' ? 'warning' : 'info'); ?> mr-2"><?php echo $api['method']; ?></span>
                            <?php if ($api['is_free'] == 1): ?>
                            <span class="badge badge-success">免费</span>
                            <?php else: ?>
                            <span class="badge badge-warning">付费</span>
                            <?php endif; ?>
                        </div>
                        <p class="text-muted">
                            <i class="fa fa-bar-chart mr-1"></i> <?php echo number_format($api['call_count']); ?>次调用
                            <?php if ($api['creator_id'] > 0): ?>
                            <span class="ml-3"><i class="fa fa-user mr-1"></i> 提供者: <?php echo $api['creator_name']; ?></span>
                            <?php endif; ?>
                        </p>
                        <p><?php echo nl2br($api['description']); ?></p>
                        
                        <?php if ($api['is_free'] == 0): ?>
                        <div class="alert alert-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-0">价格信息</h5>
                                    <p class="mb-0">
                                        普通用户: <strong class="text-danger">¥<?php echo number_format($api['price'], 2); ?>/次</strong>
                                        <?php if ($api['merchant_price'] > 0): ?>
                                        <br>商家用户: <strong class="text-danger">¥<?php echo number_format($api['merchant_price'], 2); ?>/次</strong>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <?php if (isset($_SESSION['user_id'])): ?>
                                <?php if ($hasPurchased): ?>
                                <button class="btn btn-success" disabled>已购买</button>
                                <?php else: ?>
                                <form method="post" action="">
                                    <button type="submit" name="purchase" class="btn btn-primary">立即购买</button>
                                </form>
                                <?php endif; ?>
                                <?php else: ?>
                                <a href="/user/login" class="btn btn-primary">登录后购买</a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- API接口信息 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">接口信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>接口地址</label>
                            <div class="input-group">
                                <input type="text" class="form-control" value="<?php echo $api['url']; ?>" readonly>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo $api['url']; ?>')">复制</button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>请求方式</label>
                            <input type="text" class="form-control" value="<?php echo $api['method']; ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label>返回格式</label>
                            <input type="text" class="form-control" value="JSON" readonly>
                        </div>
                        <div class="form-group mb-0">
                            <label>请求示例</label>
                            <div class="bg-light p-3 rounded">
                                <pre><code class="language-bash"># 使用curl请求示例
curl -X <?php echo $api['method']; ?> "<?php echo $api['url']; ?>?api_key=YOUR_API_KEY<?php
if ($api['method'] === 'GET' && !empty($parameters)) {
    foreach ($parameters as $param) {
        if ($param['required'] == 1) {
            echo '&' . $param['name'] . '={' . $param['name'] . '}';
        }
    }
}
?>"<?php if ($api['method'] !== 'GET'): ?> \
  -H "Content-Type: application/json" \
  -d '{<?php
$paramStr = '';
if (!empty($parameters)) {
    foreach ($parameters as $index => $param) {
        if ($param['required'] == 1) {
            $paramStr .= '"' . $param['name'] . '": "' . ($param['default_value'] ?: '{' . $param['name'] . '}') . '"';
            if ($index < count($parameters) - 1) {
                $paramStr .= ', ';
            }
        }
    }
}
echo $paramStr;
?>}'<?php endif; ?></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 请求参数 -->
                <?php if (!empty($parameters)): ?>
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">请求参数</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>参数名</th>
                                        <th>类型</th>
                                        <th>必填</th>
                                        <th>说明</th>
                                        <th>示例值</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>api_key</td>
                                        <td>string</td>
                                        <td><span class="badge badge-danger">是</span></td>
                                        <td>您的API密钥</td>
                                        <td>xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</td>
                                    </tr>
                                    <?php foreach ($parameters as $param): ?>
                                    <tr>
                                        <td><?php echo $param['name']; ?></td>
                                        <td><?php echo $param['type']; ?></td>
                                        <td>
                                            <?php if ($param['required'] == 1): ?>
                                            <span class="badge badge-danger">是</span>
                                            <?php else: ?>
                                            <span class="badge badge-secondary">否</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $param['description']; ?></td>
                                        <td><?php echo $param['default_value']; ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- 返回示例 -->
                <?php if (!empty($response)): ?>
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">返回示例</h5>
                    </div>
                    <div class="card-body">
                        <pre><code class="language-json"><?php echo $response['content']; ?></code></pre>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- 错误码 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">错误码说明</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>错误码</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>200</td>
                                        <td>请求成功</td>
                                    </tr>
                                    <tr>
                                        <td>400</td>
                                        <td>请求参数错误</td>
                                    </tr>
                                    <tr>
                                        <td>401</td>
                                        <td>未授权，API密钥无效</td>
                                    </tr>
                                    <tr>
                                        <td>403</td>
                                        <td>禁止访问，可能是IP被限制或未购买该API</td>
                                    </tr>
                                    <tr>
                                        <td>404</td>
                                        <td>接口不存在</td>
                                    </tr>
                                    <tr>
                                        <td>429</td>
                                        <td>请求过于频繁，超出QPS限制</td>
                                    </tr>
                                    <tr>
                                        <td>500</td>
                                        <td>服务器内部错误</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 在线调试 -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0">在线调试</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!isset($_SESSION['user_id'])): ?>
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-circle mr-2"></i> 请先 <a href="/user/login">登录</a> 后使用在线调试功能
                        </div>
                        <?php elseif ($api['is_free'] == 0 && !$hasPurchased): ?>
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-circle mr-2"></i> 请先购买该API后使用在线调试功能
                        </div>
                        <?php else: ?>
                        <a href="/api/debug?id=<?php echo $api_id; ?>" class="btn btn-primary">
                            <i class="fa fa-code mr-2"></i> 进入在线调试页面
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- 右侧边栏 -->
            <div class="col-md-4">
                <!-- 快速导航 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">快速导航</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="scrollToElement('.card:nth-child(1)'); return false;">API基本信息</a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="scrollToElement('.card:nth-child(2)'); return false;">接口信息</a>
                        <?php if (!empty($parameters)): ?>
                        <a href="#" class="list-group-item list-group-item-action" onclick="scrollToElement('.card:nth-child(3)'); return false;">请求参数</a>
                        <?php endif; ?>
                        <?php if (!empty($response)): ?>
                        <a href="#" class="list-group-item list-group-item-action" onclick="scrollToElement('.card:nth-child(4)'); return false;">返回示例</a>
                        <?php endif; ?>
                        <a href="#" class="list-group-item list-group-item-action" onclick="scrollToElement('.card:nth-child(5)'); return false;">错误码说明</a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="scrollToElement('.card:nth-child(6)'); return false;">在线调试</a>
                    </div>
                </div>
                
                <!-- 相关API -->
                <?php if (!empty($relatedApis)): ?>
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">相关API</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <?php foreach ($relatedApis as $relatedApi): ?>
                        <a href="/api/detail?id=<?php echo $relatedApi['id']; ?>" class="list-group-item list-group-item-action">
                            <?php echo $relatedApi['name']; ?>
                            <?php if ($relatedApi['is_free'] == 1): ?>
                            <span class="badge badge-success float-right">免费</span>
                            <?php else: ?>
                            <span class="badge badge-warning float-right">付费</span>
                            <?php endif; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- 使用说明 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">使用说明</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>第一步：</strong>获取API密钥</p>
                        <p>登录后在 <a href="/user/profile">个人中心</a> 获取您的API密钥</p>
                        
                        <p><strong>第二步：</strong>构建请求</p>
                        <p>按照接口文档构建HTTP请求，确保包含API密钥</p>
                        
                        <p><strong>第三步：</strong>发送请求</p>
                        <p>使用HTTP客户端发送请求并处理响应</p>
                        
                        <p><strong>注意事项：</strong></p>
                        <ul>
                            <li>请勿频繁调用API，遵守QPS限制</li>
                            <li>妥善保管您的API密钥，不要泄露给他人</li>
                            <li>如遇问题，请 <a href="/user/ticket/create">提交工单</a> 联系我们</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 代码示例 -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0">代码示例</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="codeTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="php-tab" data-toggle="tab" href="#php" role="tab">PHP</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="js-tab" data-toggle="tab" href="#js" role="tab">JavaScript</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="python-tab" data-toggle="tab" href="#python" role="tab">Python</a>
                            </li>
                        </ul>
                        <div class="tab-content mt-3" id="codeTabsContent">
                            <div class="tab-pane fade show active" id="php" role="tabpanel">
                                <pre><code class="language-php">&lt;?php
// PHP示例代码
$apiKey = 'YOUR_API_KEY';
$url = '<?php echo $api['url']; ?>?api_key=' . $apiKey;

<?php if ($api['method'] === 'GET'): ?>
// GET请求
<?php if (!empty($parameters)): ?>
$params = [
<?php foreach ($parameters as $param): ?>
    '<?php echo $param['name']; ?>' => '<?php echo $param['default_value'] ?: 'value'; ?>',
<?php endforeach; ?>
];
$url .= '&' . http_build_query($params);
<?php endif; ?>

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

<?php else: ?>
// <?php echo $api['method']; ?> 请求
<?php if (!empty($parameters)): ?>
$data = [
<?php foreach ($parameters as $param): ?>
    '<?php echo $param['name']; ?>' => '<?php echo $param['default_value'] ?: 'value'; ?>',
<?php endforeach; ?>
];
<?php else: ?>
$data = [];
<?php endif; ?>

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, '<?php echo $api['method']; ?>');
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($data))
]);
$response = curl_exec($ch);
curl_close($ch);
<?php endif; ?>

// 处理响应
$result = json_decode($response, true);
print_r($result);
?&gt;</code></pre>
                            </div>
                            <div class="tab-pane fade" id="js" role="tabpanel">
                                <pre><code class="language-javascript">// JavaScript示例代码
const apiKey = 'YOUR_API_KEY';
<?php if ($api['method'] === 'GET'): ?>
// GET请求
<?php if (!empty($parameters)): ?>
const params = {
<?php foreach ($parameters as $param): ?>
    '<?php echo $param['name']; ?>': '<?php echo $param['default_value'] ?: 'value'; ?>',
<?php endforeach; ?>
};
const queryString = new URLSearchParams({...params, api_key: apiKey}).toString();
const url = '<?php echo $api['url']; ?>?' + queryString;

<?php else: ?>
const url = '<?php echo $api['url']; ?>?api_key=' + apiKey;
<?php endif; ?>

fetch(url)
    .then(response => response.json())
    .then(data => {
        console.log(data);
    })
    .catch(error => {
        console.error('Error:', error);
    });
<?php else: ?>
// <?php echo $api['method']; ?> 请求
const url = '<?php echo $api['url']; ?>?api_key=' + apiKey;
<?php if (!empty($parameters)): ?>
const data = {
<?php foreach ($parameters as $param): ?>
    '<?php echo $param['name']; ?>': '<?php echo $param['default_value'] ?: 'value'; ?>',
<?php endforeach; ?>
};
<?php else: ?>
const data = {};
<?php endif; ?>

fetch(url, {
    method: '<?php echo $api['method']; ?>',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
})
    .then(response => response.json())
    .then(data => {
        console.log(data);
    })
    .catch(error => {
        console.error('Error:', error);
    });
<?php endif; ?></code></pre>
                            </div>
                            <div class="tab-pane fade" id="python" role="tabpanel">
                                <pre><code class="language-python"># Python示例代码
import requests
import json

api_key = 'YOUR_API_KEY'
<?php if ($api['method'] === 'GET'): ?>
# GET请求
<?php if (!empty($parameters)): ?>
params = {
    'api_key': api_key,
<?php foreach ($parameters as $param): ?>
    '<?php echo $param['name']; ?>': '<?php echo $param['default_value'] ?: 'value'; ?>',
<?php endforeach; ?>
}
response = requests.get('<?php echo $api['url']; ?>', params=params)
<?php else: ?>
params = {'api_key': api_key}
response = requests.get('<?php echo $api['url']; ?>', params=params)
<?php endif; ?>
<?php else: ?>
# <?php echo $api['method']; ?> 请求
<?php if (!empty($parameters)): ?>
data = {
<?php foreach ($parameters as $param): ?>
    '<?php echo $param['name']; ?>': '<?php echo $param['default_value'] ?: 'value'; ?>',
<?php endforeach; ?>
}
<?php else: ?>
data = {}
<?php endif; ?>
params = {'api_key': api_key}
response = requests.<?php echo strtolower($api['method']); ?>('<?php echo $api['url']; ?>', params=params, json=data)
<?php endif; ?>

# 处理响应
result = response.json()
print(result)</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <?php include '../templates/common/footer.php'; ?>
    
    <script src="/public/js/jquery-3.5.1.min.js"></script>
    <script src="/public/js/bootstrap.bundle.min.js"></script>
    <script src="/public/js/prism.js"></script>
    <script src="/public/js/main.js"></script>
    <script>
    function copyToClipboard(text) {
        var input = document.createElement('textarea');
        input.value = text;
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        alert('已复制到剪贴板');
    }
    
    function scrollToElement(selector) {
        $('html, body').animate({
            scrollTop: $(selector).offset().top - 70
        }, 500);
    }
    </script>
</body>
</html>
