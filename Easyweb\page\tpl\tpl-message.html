<div class="layui-card" style="box-shadow: none;border: none;">
    <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title" style="text-align: center;">
            <li class="layui-this">通知(5)</li>
            <li>私信(12)</li>
            <li>待办(3)</li>
        </ul>
        <div class="layui-tab-content" style="padding: 0;">
            <!-- tab1 -->
            <div class="layui-tab-item layui-show">
                <div class="message-list">
                    <!-- 实际项目请使用后台数据循环出来 -->
                    <a class="message-list-item" href="javascript:;">
                        <i class="layui-icon layui-icon-speaker message-item-icon"></i>
                        <div class="message-item-right">
                            <h2 class="message-item-title">你收到了14份新周报</h2>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                    <a class="message-list-item" href="javascript:;">
                        <i class="layui-icon layui-icon-speaker message-item-icon"></i>
                        <div class="message-item-right">
                            <h2 class="message-item-title">你收到了14份新周报</h2>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                    <a class="message-list-item" href="javascript:;">
                        <i class="layui-icon layui-icon-speaker message-item-icon"></i>
                        <div class="message-item-right">
                            <h2 class="message-item-title">你收到了14份新周报</h2>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                </div>
                <!-- 列表为空 -->
                <div class="message-list-empty">
                    <i class="layui-icon layui-icon-notice"></i>
                    <div>没有通知</div>
                </div>
                <a id="messageClearBtn1" class="message-btn-clear">全部标记已读</a>
            </div>
            <!-- tab2 -->
            <div class="layui-tab-item">
                <div class="message-list">
                    <a class="message-list-item" href="javascript:;">
                        <img class="message-item-icon" src="assets/images/head.jpg" alt="">
                        <div class="message-item-right">
                            <h2 class="message-item-title">xx评论了你</h2>
                            <p class="message-item-text">哈哈哈哈哈哈</p>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                    <a class="message-list-item" href="javascript:;">
                        <img class="message-item-icon" src="assets/images/head.jpg" alt="">
                        <div class="message-item-right">
                            <h2 class="message-item-title">xx评论了你</h2>
                            <p class="message-item-text">哈哈哈哈哈哈</p>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                    <a class="message-list-item" href="javascript:;">
                        <img class="message-item-icon" src="assets/images/head.jpg" alt="">
                        <div class="message-item-right">
                            <h2 class="message-item-title">xx评论了你</h2>
                            <p class="message-item-text">哈哈哈哈哈哈</p>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                    <a id="messageMoreBtn2" class="message-btn-more">加载更多</a>
                </div>
                <!-- 列表为空 -->
                <div class="message-list-empty">
                    <i class="layui-icon layui-icon-dialogue"></i>
                    <div>没有消息</div>
                </div>
                <a id="messageClearBtn2" class="message-btn-clear">清空消息</a>
            </div>
            <!-- tab3 -->
            <div class="layui-tab-item">
                <div class="message-list">
                    <a class="message-list-item" href="javascript:;">
                        <span class="layui-badge layui-badge-yellow">待完成</span>
                        <div class="message-item-right">
                            <h2 class="message-item-title">你收到了14份新周报</h2>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                    <a class="message-list-item" href="javascript:;">
                        <span class="layui-badge layui-badge-green">已完成</span>
                        <div class="message-item-right">
                            <h2 class="message-item-title">你收到了14份新周报</h2>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                    <a class="message-list-item" href="javascript:;">
                        <span class="layui-badge layui-badge-red">未完成</span>
                        <div class="message-item-right">
                            <h2 class="message-item-title">你收到了14份新周报</h2>
                            <p class="message-item-text">10个月前</p>
                        </div>
                    </a>
                </div>
                <!-- 列表为空 -->
                <div class="message-list-empty">
                    <i class="layui-icon layui-icon-flag"></i>
                    <div>没有待办</div>
                </div>
                <a id="messageClearBtn3" class="message-btn-clear">清空待办</a>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['element', 'admin'], function () {
        var $ = layui.jquery;
        var admin = layui.admin;

        /* 加载更多按钮点击事件 */
        $('#messageMoreBtn2').click(function () {
            var $that = $(this);
            admin.btnLoading($that);
            setTimeout(function () {
                admin.btnLoading($that, false);
                $that.before($that.prev()[0].outerHTML);
            }, 300);
        });

        /* 清空消息点击事件 */
        $('#messageClearBtn1,#messageClearBtn2,#messageClearBtn3').click(function () {
            $(this).parents('.layui-tab-item').addClass('show-empty');
        });

    });
</script>

<style>
    /** 消息列表样式 */
    .message-list {
        position: absolute;
        top: 48px;
        left: 0;
        right: 0;
        bottom: 45px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .message-list-item {
        display: block;
        padding: 10px 20px;
        line-height: 24px;
        position: relative;
        border-bottom: 1px solid #e8e8e8;
    }

    .message-list-item:hover, .message-btn-clear:hover, .message-btn-more:hover {
        background: #F2F2F2;
    }

    .message-list-item .message-item-icon {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin-top: -20px;
        border-radius: 50%;
        position: absolute;
        left: 20px;
        top: 50%;
    }

    .message-list-item .message-item-icon.layui-icon {
        color: #fff;
        font-size: 22px;
        text-align: center;
        background-color: #FE5D58;
    }

    .message-list-item .message-item-icon + .message-item-right {
        margin-left: 55px;
    }

    .message-list-item .message-item-title {
        color: #666;
        font-size: 14px;
    }

    .message-list-item .message-item-text {
        color: #999;
        font-size: 12px;
    }

    .message-list-item > .layui-badge {
        position: absolute;
        right: 20px;
        top: 12px;
    }

    .message-list-item > .layui-badge + .message-item-right {
        margin-right: 50px;
    }

    .message-btn-clear, .message-btn-more {
        color: #666;
        display: block;
        padding: 10px 5px;
        line-height: 24px;
        text-align: center;
        cursor: pointer;
    }

    .message-btn-clear {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        border-top: 1px solid #e8e8e8;
    }

    .message-btn-more {
        color: #666;
        font-size: 13px;
    }

    .message-btn-more.ew-btn-loading > .ew-btn-loading-text {
        font-size: 13px !important;
    }

    .message-list-empty {
        color: #999;
        padding: 100px 0;
        text-align: center;
        display: none;
    }

    .message-list-empty > .layui-icon {
        color: #ccc;
        display: block;
        font-size: 45px;
        margin-bottom: 15px;
    }

    .show-empty .message-list-empty {
        display: block;
    }

    .show-empty .message-btn-clear, .show-empty .message-list {
        display: none;
    }

    /** //消息列表样式结束 */
</style>
