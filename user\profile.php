<?php
// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /user/login');
    exit;
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM api_users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user) {
    // 用户不存在，清除会话
    session_destroy();
    header('Location: /user/login');
    exit;
}

// 处理个人资料更新
$success = '';
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $mobile = $_POST['mobile'] ?? '';
    
    // 更新头像
    $avatar = $user['avatar'];
    if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($_FILES['avatar']['type'], $allowed_types)) {
            $error = '头像只能是JPG、PNG或GIF格式';
        } elseif ($_FILES['avatar']['size'] > $max_size) {
            $error = '头像大小不能超过2MB';
        } else {
            $upload_dir = '../uploads/avatars/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $filename = uniqid() . '_' . $_FILES['avatar']['name'];
            $filepath = $upload_dir . $filename;
            
            if (move_uploaded_file($_FILES['avatar']['tmp_name'], $filepath)) {
                $avatar = '/uploads/avatars/' . $filename;
            } else {
                $error = '头像上传失败';
            }
        }
    }
    
    if (empty($error)) {
        // 更新用户资料
        $stmt = $pdo->prepare("UPDATE api_users SET mobile = ?, avatar = ?, updated_at = NOW() WHERE id = ?");
        $result = $stmt->execute([$mobile, $avatar, $_SESSION['user_id']]);
        
        if ($result) {
            $success = '个人资料更新成功';
            $_SESSION['user_avatar'] = $avatar;
            
            // 重新获取用户信息
            $stmt = $pdo->prepare("SELECT * FROM api_users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();
        } else {
            $error = '个人资料更新失败';
        }
    }
}

// 处理密码修改
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = '所有密码字段都是必填的';
    } elseif (!password_verify($current_password, $user['password'])) {
        $error = '当前密码不正确';
    } elseif ($new_password !== $confirm_password) {
        $error = '两次输入的新密码不一致';
    } elseif (strlen($new_password) < 6) {
        $error = '新密码长度不能少于6个字符';
    } else {
        // 更新密码
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE api_users SET password = ?, updated_at = NOW() WHERE id = ?");
        $result = $stmt->execute([$hashed_password, $_SESSION['user_id']]);
        
        if ($result) {
            $success = '密码修改成功';
        } else {
            $error = '密码修改失败';
        }
    }
}

// 处理API密钥重置
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_api_key'])) {
    $new_api_key = bin2hex(random_bytes(32));
    
    $stmt = $pdo->prepare("UPDATE api_users SET api_key = ?, updated_at = NOW() WHERE id = ?");
    $result = $stmt->execute([$new_api_key, $_SESSION['user_id']]);
    
    if ($result) {
        $success = 'API密钥重置成功';
        
        // 重新获取用户信息
        $stmt = $pdo->prepare("SELECT * FROM api_users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
    } else {
        $error = 'API密钥重置失败';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="/public/css/font-awesome.min.css">
    <link rel="stylesheet" href="/public/css/style.css">
</head>
<body class="bg-light">
    <!-- 头部导航 -->
    <?php include '../templates/common/header.php'; ?>
    
    <div class="container py-5">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="card shadow-sm mb-4">
                    <div class="card-body text-center">
                        <img src="<?php echo !empty($user['avatar']) ? $user['avatar'] : '/public/images/default-avatar.png'; ?>" alt="<?php echo $user['username']; ?>" class="rounded-circle img-thumbnail mb-3" style="width: 100px; height: 100px; object-fit: cover;">
                        <h5><?php echo $user['username']; ?></h5>
                        <p class="text-muted">
                            <?php if ($user['role'] === 'admin'): ?>
                            <span class="badge badge-danger">管理员</span>
                            <?php elseif ($user['role'] === 'merchant'): ?>
                            <span class="badge badge-success">商家</span>
                            <?php else: ?>
                            <span class="badge badge-info">用户</span>
                            <?php endif; ?>
                            
                            <?php if ($user['vip_level'] > 0): ?>
                            <span class="badge badge-warning">VIP<?php echo $user['vip_level']; ?></span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="/user/profile" class="list-group-item list-group-item-action active">
                            <i class="fa fa-user mr-2"></i> 个人资料
                        </a>
                        <a href="/user/api" class="list-group-item list-group-item-action">
                            <i class="fa fa-code mr-2"></i> 我的API
                        </a>
                        <a href="/user/order" class="list-group-item list-group-item-action">
                            <i class="fa fa-shopping-cart mr-2"></i> 订单记录
                        </a>
                        <a href="/user/balance" class="list-group-item list-group-item-action">
                            <i class="fa fa-money mr-2"></i> 余额充值
                        </a>
                        <a href="/user/ticket" class="list-group-item list-group-item-action">
                            <i class="fa fa-ticket mr-2"></i> 我的工单
                        </a>
                        <?php if ($user['role'] === 'merchant'): ?>
                        <a href="/merchant/dashboard" class="list-group-item list-group-item-action">
                            <i class="fa fa-dashboard mr-2"></i> 商家中心
                        </a>
                        <?php endif; ?>
                        <a href="/user/logout" class="list-group-item list-group-item-action text-danger">
                            <i class="fa fa-sign-out mr-2"></i> 退出登录
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容 -->
            <div class="col-md-9">
                <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <!-- 个人资料卡片 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">个人资料</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="" enctype="multipart/form-data">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">用户名</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control-plaintext" value="<?php echo $user['username']; ?>" readonly>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">电子邮箱</label>
                                <div class="col-sm-9">
                                    <input type="email" class="form-control-plaintext" value="<?php echo $user['email']; ?>" readonly>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">手机号码</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" name="mobile" value="<?php echo $user['mobile']; ?>" placeholder="请输入手机号码">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">头像</label>
                                <div class="col-sm-9">
                                    <input type="file" class="form-control-file" name="avatar" accept="image/jpeg,image/png,image/gif">
                                    <small class="form-text text-muted">支持JPG、PNG、GIF格式，大小不超过2MB</small>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">注册时间</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control-plaintext" value="<?php echo $user['created_at']; ?>" readonly>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">最后登录</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control-plaintext" value="<?php echo $user['last_login']; ?> (<?php echo $user['last_ip']; ?>)" readonly>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-9 offset-sm-3">
                                    <button type="submit" name="update_profile" class="btn btn-primary">更新资料</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- API密钥卡片 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">API密钥</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>您的API密钥</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="apiKey" value="<?php echo $user['api_key']; ?>" readonly>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">复制</button>
                                </div>
                            </div>
                            <small class="form-text text-muted">API密钥用于调用API接口，请妥善保管，不要泄露给他人</small>
                        </div>
                        <form method="post" action="" onsubmit="return confirm('确定要重置API密钥吗？重置后原密钥将失效，需要更新所有使用该密钥的应用');">
                            <button type="submit" name="reset_api_key" class="btn btn-warning">重置API密钥</button>
                        </form>
                    </div>
                </div>
                
                <!-- 修改密码卡片 -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0">修改密码</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="form-group">
                                <label for="current_password">当前密码</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            <div class="form-group">
                                <label for="new_password">新密码</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <small class="form-text text-muted">密码长度不能少于6个字符</small>
                            </div>
                            <div class="form-group">
                                <label for="confirm_password">确认新密码</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                            <button type="submit" name="change_password" class="btn btn-primary">修改密码</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <?php include '../templates/common/footer.php'; ?>
    
    <script src="/public/js/jquery-3.5.1.min.js"></script>
    <script src="/public/js/bootstrap.bundle.min.js"></script>
    <script src="/public/js/main.js"></script>
    <script>
    function copyApiKey() {
        var apiKeyInput = document.getElementById('apiKey');
        apiKeyInput.select();
        document.execCommand('copy');
        alert('API密钥已复制到剪贴板');
    }
    </script>
</body>
</html>