<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - API管理系统</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
    <link rel="icon" href="../../Easyweb/assets/images/favicon.ico">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            line-height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 220px;
            background: #2f3349;
            overflow-y: auto;
            z-index: 999;
        }
        
        .admin-main {
            margin-left: 220px;
            margin-top: 60px;
            padding: 20px;
            min-height: calc(100vh - 60px);
            background: #f5f5f5;
        }
        
        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-menu li {
            border-bottom: 1px solid #3a3f5c;
        }
        
        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: #b8c5d6;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        
        .nav-menu i {
            margin-right: 10px;
            width: 16px;
        }
        
        .user-info {
            float: right;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            vertical-align: middle;
            margin-right: 10px;
        }
        
        .config-group {
            margin-bottom: 30px;
        }
        
        .config-group-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .config-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        
        .config-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        
        .config-description {
            font-size: 12px;
            color: #999;
            margin-bottom: 10px;
        }
        
        .config-tabs {
            margin-bottom: 20px;
        }
        
        .config-tabs .layui-tab-title li {
            padding: 0 20px;
        }
        
        .save-btn-fixed {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="admin-header">
        <div style="float: left;">
            <h2 style="margin: 0; font-size: 18px;">
                <i class="layui-icon layui-icon-set"></i>
                系统配置
            </h2>
        </div>
        <div class="user-info">
            <img src="../../Easyweb/assets/images/head.jpg" alt="头像" class="user-avatar">
            <span id="adminName">管理员</span>
            <a href="javascript:;" onclick="logout()" style="color: white; margin-left: 15px;">
                <i class="layui-icon layui-icon-logout"></i> 退出
            </a>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="admin-sidebar">
        <ul class="nav-menu">
            <li>
                <a href="dashboard.html">
                    <i class="layui-icon layui-icon-home"></i>
                    仪表板
                </a>
            </li>
            <li>
                <a href="api-list.html">
                    <i class="layui-icon layui-icon-api"></i>
                    API管理
                </a>
            </li>
            <li>
                <a href="user-list.html">
                    <i class="layui-icon layui-icon-user"></i>
                    用户管理
                </a>
            </li>
            <li>
                <a href="merchant-list.html">
                    <i class="layui-icon layui-icon-shop"></i>
                    商家管理
                </a>
            </li>
            <li>
                <a href="order-list.html">
                    <i class="layui-icon layui-icon-dollar"></i>
                    订单管理
                </a>
            </li>
            <li>
                <a href="finance.html">
                    <i class="layui-icon layui-icon-rmb"></i>
                    财务管理
                </a>
            </li>
            <li>
                <a href="admin-list.html">
                    <i class="layui-icon layui-icon-username"></i>
                    管理员
                </a>
            </li>
            <li>
                <a href="role-list.html">
                    <i class="layui-icon layui-icon-group"></i>
                    角色权限
                </a>
            </li>
            <li>
                <a href="system-config.html" class="active">
                    <i class="layui-icon layui-icon-set"></i>
                    系统配置
                </a>
            </li>
            <li>
                <a href="logs.html">
                    <i class="layui-icon layui-icon-file"></i>
                    系统日志
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="admin-main">
        <div class="content-card">
            <div class="layui-tab layui-tab-brief config-tabs" lay-filter="configTabs">
                <ul class="layui-tab-title">
                    <li class="layui-this">网站配置</li>
                    <li>邮件配置</li>
                    <li>支付配置</li>
                    <li>系统配置</li>
                    <li>安全配置</li>
                </ul>
                
                <div class="layui-tab-content">
                    <!-- 网站配置 -->
                    <div class="layui-tab-item layui-show">
                        <form class="layui-form" lay-filter="siteConfigForm">
                            <div class="config-group">
                                <div class="config-group-title">基本信息</div>
                                
                                <div class="config-item">
                                    <div class="config-label">商户号</div>
                                    <div class="config-description">微信支付商户号</div>
                                    <input type="text" name="wechat_mch_id" placeholder="请输入商户号" class="layui-input">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">支付密钥</div>
                                    <div class="config-description">微信支付密钥</div>
                                    <input type="password" name="wechat_key" placeholder="请输入支付密钥" class="layui-input">
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 系统配置 -->
                    <div class="layui-tab-item">
                        <form class="layui-form" lay-filter="systemConfigForm">
                            <div class="config-group">
                                <div class="config-group-title">基本设置</div>
                                
                                <div class="config-item">
                                    <div class="config-label">系统时区</div>
                                    <div class="config-description">系统默认时区设置</div>
                                    <select name="system_timezone">
                                        <option value="Asia/Shanghai" selected>Asia/Shanghai</option>
                                        <option value="UTC">UTC</option>
                                        <option value="America/New_York">America/New_York</option>
                                    </select>
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">系统语言</div>
                                    <div class="config-description">系统默认语言</div>
                                    <select name="system_language">
                                        <option value="zh-CN" selected>简体中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">API调用频率限制</div>
                                    <div class="config-description">每小时API调用次数限制</div>
                                    <input type="number" name="api_rate_limit" placeholder="请输入频率限制" class="layui-input" value="1000">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">文件上传大小限制</div>
                                    <div class="config-description">单个文件上传大小限制（MB）</div>
                                    <input type="number" name="upload_max_size" placeholder="请输入大小限制" class="layui-input" value="10">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">允许上传的文件类型</div>
                                    <div class="config-description">允许上传的文件扩展名，用逗号分隔</div>
                                    <input type="text" name="allowed_file_types" placeholder="如：jpg,png,pdf" class="layui-input" value="jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx">
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 安全配置 -->
                    <div class="layui-tab-item">
                        <form class="layui-form" lay-filter="securityConfigForm">
                            <div class="config-group">
                                <div class="config-group-title">登录安全</div>
                                
                                <div class="config-item">
                                    <div class="config-label">登录最大尝试次数</div>
                                    <div class="config-description">用户登录失败的最大尝试次数</div>
                                    <input type="number" name="login_max_attempts" placeholder="请输入最大尝试次数" class="layui-input" value="5">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">登录锁定时间</div>
                                    <div class="config-description">登录失败后的锁定时间（分钟）</div>
                                    <input type="number" name="login_lockout_time" placeholder="请输入锁定时间" class="layui-input" value="30">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">密码最小长度</div>
                                    <div class="config-description">用户密码的最小长度要求</div>
                                    <input type="number" name="password_min_length" placeholder="请输入最小长度" class="layui-input" value="6">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">启用验证码</div>
                                    <div class="config-description">是否在登录时启用验证码</div>
                                    <input type="radio" name="enable_captcha" value="1" title="启用" checked>
                                    <input type="radio" name="enable_captcha" value="0" title="禁用">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">启用IP白名单</div>
                                    <div class="config-description">是否启用IP白名单功能</div>
                                    <input type="radio" name="enable_ip_whitelist" value="1" title="启用">
                                    <input type="radio" name="enable_ip_whitelist" value="0" title="禁用" checked>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 固定保存按钮 -->
    <div class="save-btn-fixed">
        <button class="layui-btn layui-btn-lg" onclick="saveAllConfigs()">
            <i class="layui-icon layui-icon-ok"></i> 保存所有配置
        </button>
    </div>

    <script src="../../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['element', 'form', 'layer', 'upload'], function(){
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            var upload = layui.upload;
            
            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                loadConfigs();
            });
            
            // 加载配置
            function loadConfigs() {
                // 模拟加载配置数据
                var configs = {
                    site: {
                        site_name: 'API管理系统',
                        site_title: 'API管理系统 - 专业的API接口管理平台',
                        site_keywords: 'API,接口,管理,平台',
                        site_description: '专业的API接口管理平台，提供API发布、管理、调用等服务',
                        site_logo: '/assets/images/logo.png',
                        site_favicon: '/assets/images/favicon.ico'
                    },
                    mail: {
                        mail_host: 'smtp.qq.com',
                        mail_port: 587,
                        mail_username: '',
                        mail_password: '',
                        mail_from_name: 'API管理系统'
                    },
                    payment: {
                        alipay_app_id: '',
                        alipay_private_key: '',
                        alipay_public_key: '',
                        wechat_app_id: '',
                        wechat_mch_id: '',
                        wechat_key: ''
                    },
                    system: {
                        system_timezone: 'Asia/Shanghai',
                        system_language: 'zh-CN',
                        api_rate_limit: 1000,
                        upload_max_size: 10,
                        allowed_file_types: 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx'
                    },
                    security: {
                        login_max_attempts: 5,
                        login_lockout_time: 30,
                        password_min_length: 6,
                        enable_captcha: 1,
                        enable_ip_whitelist: 0
                    }
                };
                
                // 填充表单数据
                Object.keys(configs).forEach(function(group) {
                    Object.keys(configs[group]).forEach(function(key) {
                        var input = document.querySelector('[name="' + key + '"]');
                        if (input) {
                            if (input.type === 'radio') {
                                var radio = document.querySelector('[name="' + key + '"][value="' + configs[group][key] + '"]');
                                if (radio) radio.checked = true;
                            } else {
                                input.value = configs[group][key];
                            }
                        }
                    });
                });
                
                form.render();
            }
            
            // 保存所有配置
            window.saveAllConfigs = function() {
                var loadIndex = layer.load(2, {content: '正在保存配置...'});
                
                // 收集所有表单数据
                var allConfigs = {};
                
                // 网站配置
                var siteForm = document.querySelector('[lay-filter="siteConfigForm"]');
                var siteData = new FormData(siteForm);
                for (var pair of siteData.entries()) {
                    allConfigs[pair[0]] = pair[1];
                }
                
                // 邮件配置
                var mailForm = document.querySelector('[lay-filter="mailConfigForm"]');
                var mailData = new FormData(mailForm);
                for (var pair of mailData.entries()) {
                    allConfigs[pair[0]] = pair[1];
                }
                
                // 支付配置
                var paymentForm = document.querySelector('[lay-filter="paymentConfigForm"]');
                var paymentData = new FormData(paymentForm);
                for (var pair of paymentData.entries()) {
                    allConfigs[pair[0]] = pair[1];
                }
                
                // 系统配置
                var systemForm = document.querySelector('[lay-filter="systemConfigForm"]');
                var systemData = new FormData(systemForm);
                for (var pair of systemData.entries()) {
                    allConfigs[pair[0]] = pair[1];
                }
                
                // 安全配置
                var securityForm = document.querySelector('[lay-filter="securityConfigForm"]');
                var securityData = new FormData(securityForm);
                for (var pair of securityData.entries()) {
                    allConfigs[pair[0]] = pair[1];
                }
                
                // 模拟保存
                setTimeout(function() {
                    layer.close(loadIndex);
                    layer.msg('配置保存成功', {icon: 1});
                }, 1500);
            };
            
            // 测试邮件发送
            window.testMail = function() {
                var loadIndex = layer.load(2, {content: '正在发送测试邮件...'});
                
                // 模拟测试邮件
                setTimeout(function() {
                    layer.close(loadIndex);
                    layer.msg('测试邮件发送成功', {icon: 1});
                }, 2000);
            };
            
            // 上传Logo
            window.uploadLogo = function() {
                upload.render({
                    elem: '#uploadLogo',
                    url: '/admin/upload',
                    accept: 'images',
                    done: function(res) {
                        if (res.code === 0) {
                            document.querySelector('[name="site_logo"]').value = res.data.url;
                            layer.msg('Logo上传成功', {icon: 1});
                        } else {
                            layer.msg('上传失败: ' + res.msg, {icon: 2});
                        }
                    }
                });
            };
            
            // 上传Favicon
            window.uploadFavicon = function() {
                upload.render({
                    elem: '#uploadFavicon',
                    url: '/admin/upload',
                    accept: 'images',
                    done: function(res) {
                        if (res.code === 0) {
                            document.querySelector('[name="site_favicon"]').value = res.data.url;
                            layer.msg('图标上传成功', {icon: 1});
                        } else {
                            layer.msg('上传失败: ' + res.msg, {icon: 2});
                        }
                    }
                });
            };
            
            // 退出登录
            window.logout = function() {
                layer.confirm('确定要退出登录吗？', {icon: 3, title: '提示'}, function(index) {
                    layer.close(index);
                    window.location.href = 'index.html';
                });
            };
        });
    </script>
</body>
</html>
                                
                                <div class="config-item">
                                    <div class="config-label">网站标题</div>
                                    <div class="config-description">用于SEO优化的网站标题</div>
                                    <input type="text" name="site_title" placeholder="请输入网站标题" class="layui-input" value="API管理系统 - 专业的API接口管理平台">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">网站关键词</div>
                                    <div class="config-description">用于SEO优化的关键词，多个关键词用逗号分隔</div>
                                    <input type="text" name="site_keywords" placeholder="请输入网站关键词" class="layui-input" value="API,接口,管理,平台">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">网站描述</div>
                                    <div class="config-description">用于SEO优化的网站描述</div>
                                    <textarea name="site_description" placeholder="请输入网站描述" class="layui-textarea">专业的API接口管理平台，提供API发布、管理、调用等服务</textarea>
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">网站Logo</div>
                                    <div class="config-description">网站Logo图片路径</div>
                                    <div class="layui-input-inline" style="width: 300px;">
                                        <input type="text" name="site_logo" placeholder="请输入Logo路径" class="layui-input" value="/assets/images/logo.png">
                                    </div>
                                    <button type="button" class="layui-btn layui-btn-primary" onclick="uploadLogo()">上传Logo</button>
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">网站图标</div>
                                    <div class="config-description">网站favicon图标路径</div>
                                    <div class="layui-input-inline" style="width: 300px;">
                                        <input type="text" name="site_favicon" placeholder="请输入图标路径" class="layui-input" value="/assets/images/favicon.ico">
                                    </div>
                                    <button type="button" class="layui-btn layui-btn-primary" onclick="uploadFavicon()">上传图标</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 邮件配置 -->
                    <div class="layui-tab-item">
                        <form class="layui-form" lay-filter="mailConfigForm">
                            <div class="config-group">
                                <div class="config-group-title">SMTP配置</div>
                                
                                <div class="config-item">
                                    <div class="config-label">SMTP服务器</div>
                                    <div class="config-description">邮件服务器地址，如：smtp.qq.com</div>
                                    <input type="text" name="mail_host" placeholder="请输入SMTP服务器" class="layui-input" value="smtp.qq.com">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">SMTP端口</div>
                                    <div class="config-description">邮件服务器端口，通常为25、465或587</div>
                                    <input type="number" name="mail_port" placeholder="请输入端口号" class="layui-input" value="587">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">邮箱账号</div>
                                    <div class="config-description">用于发送邮件的邮箱账号</div>
                                    <input type="email" name="mail_username" placeholder="请输入邮箱账号" class="layui-input">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">邮箱密码</div>
                                    <div class="config-description">邮箱密码或授权码</div>
                                    <input type="password" name="mail_password" placeholder="请输入邮箱密码" class="layui-input">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">发件人名称</div>
                                    <div class="config-description">邮件中显示的发件人名称</div>
                                    <input type="text" name="mail_from_name" placeholder="请输入发件人名称" class="layui-input" value="API管理系统">
                                </div>
                                
                                <div class="config-item">
                                    <button type="button" class="layui-btn layui-btn-normal" onclick="testMail()">
                                        <i class="layui-icon layui-icon-email"></i> 测试邮件发送
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 支付配置 -->
                    <div class="layui-tab-item">
                        <form class="layui-form" lay-filter="paymentConfigForm">
                            <div class="config-group">
                                <div class="config-group-title">支付宝配置</div>
                                
                                <div class="config-item">
                                    <div class="config-label">应用ID</div>
                                    <div class="config-description">支付宝开放平台应用ID</div>
                                    <input type="text" name="alipay_app_id" placeholder="请输入应用ID" class="layui-input">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">应用私钥</div>
                                    <div class="config-description">支付宝应用私钥</div>
                                    <textarea name="alipay_private_key" placeholder="请输入应用私钥" class="layui-textarea" style="height: 120px;"></textarea>
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">支付宝公钥</div>
                                    <div class="config-description">支付宝公钥</div>
                                    <textarea name="alipay_public_key" placeholder="请输入支付宝公钥" class="layui-textarea" style="height: 120px;"></textarea>
                                </div>
                            </div>
                            
                            <div class="config-group">
                                <div class="config-group-title">微信支付配置</div>
                                
                                <div class="config-item">
                                    <div class="config-label">应用ID</div>
                                    <div class="config-description">微信支付应用ID</div>
                                    <input type="text" name="wechat_app_id" placeholder="请输入应用ID" class="layui-input">
                                </div>
                                
                                <div class="config-item">
                                    <div class="config-label">商户号</div>
                                    <div class="config-description">微信支付商户号</div>
                                    <input type="text" name="wechat_mch_id" placeholder="请输入商户号" class="layui-input">