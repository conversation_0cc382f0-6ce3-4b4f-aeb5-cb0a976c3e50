<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>鼠标滚轮监听</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /* 横向滚动 */
        #demoMw2 {
            white-space: nowrap;
            overflow: auto;
            padding: 47px 15px;
        }

        /* 垂直翻页滚动 */
        .demoPage {
            padding: 0;
            height: 250px;
            overflow: hidden;
            border-radius: 5px;
        }

        .demoPage > div {
            height: 250px;
            line-height: 250px;
            color: #fff;
            text-align: center;
            background-color: #01AAED;
        }

        .demoPage > div:nth-child(odd) {
            background-color: #5FB878;
        }

        /* 横向翻页滚动 */
        .demoPage2 {
            white-space: nowrap;
            font-size: 0;
        }

        .demoPage2 > div {
            width: 100%;
            display: inline-block;
            font-size: 14px;
        }

        /* 指示器 */
        .demoCourse {
            text-align: center;
            position: absolute;
            width: 100%;
            bottom: 15px;
        }

        .demoCourse > div {
            display: inline-block;
            margin: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, .3);
        }

        .demoCourse > div.active {
            background-color: #fff;
        }

        /* 垂直指示器 */
        .demoCourse2 > div {
            display: block;
            margin: 10px;
        }

        .demoCourse2 {
            width: auto;
            bottom: unset;
            top: 50%;
            transform: translateY(-50%);
            right: 20px;
        }
    </style>
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">对div进行鼠标滚轮监听</div>
                <div class="layui-card-body" id="demoMw1" style="padding: 55px 15px;">
                    请在此区域滚动鼠标滚轮
                </div>
            </div>
        </div>

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">实现横向滚动</div>
                <div class="layui-card-body layui-badge-list" id="demoMw2">
                    <span class="layui-badge layui-bg-gray">很有想法的</span>
                    <span class="layui-badge layui-bg-gray">专注设计</span>
                    <span class="layui-badge layui-bg-gray">辣~</span>
                    <span class="layui-badge layui-bg-gray">大长腿</span>
                    <span class="layui-badge layui-bg-gray">川妹子</span>
                    <span class="layui-badge layui-bg-gray">海纳百川</span>
                    <span class="layui-badge layui-bg-gray">萌萌哒</span>
                    <span class="layui-badge layui-bg-gray">萝莉</span>
                    <span class="layui-badge layui-bg-gray">宅男拯救世界</span>
                    <span class="layui-badge layui-bg-gray">我这么美我不能死</span>
                    <span class="layui-badge layui-bg-gray">小清新</span>
                    <span class="layui-badge layui-bg-gray">萌新求带</span>
                    <span class="layui-badge layui-bg-gray">吃饱了才有力气减肥</span>
                    <span class="layui-badge layui-bg-gray">穷游党</span>
                    <span class="layui-badge layui-bg-gray">夜猫子</span>
                    <span class="layui-badge layui-bg-gray">做一个开心的吃货</span>
                    <span class="layui-badge layui-bg-gray">铲屎官</span>
                    <span class="layui-badge layui-bg-gray">逗比</span>
                    <span class="layui-badge layui-bg-gray">为了联盟</span>
                    <span class="layui-badge layui-bg-gray">选择恐惧症</span>
                    <span class="layui-badge layui-bg-gray">玄不救非，氪不改命</span>
                    <span class="layui-badge layui-bg-gray">背包客</span>
                    <span class="layui-badge layui-bg-gray">追剧是种坚持</span>
                    <span class="layui-badge layui-bg-gray">懒癌患者</span>
                </div>
            </div>
        </div>

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">实现翻页滚动</div>
                <div class="layui-card-body">
                    <div class="demoPage" id="demoMw3">
                        <div>第一页</div>
                        <div>第二页</div>
                        <div>第三页</div>
                        <div>第四页</div>
                        <div>第五页</div>
                    </div>
                    <div class="demoCourse demoCourse2">
                        <div class="active"></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">横向翻页滚动</div>
                <div class="layui-card-body">
                    <div class="demoPage demoPage2" id="demoMw4">
                        <div>第一页</div>
                        <div>第二页</div>
                        <div>第三页</div>
                        <div>第四页</div>
                        <div>第五页</div>
                    </div>
                    <div class="demoCourse">
                        <div class="active"></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'mousewheel'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;

        // 滚动监听
        $('#demoMw1').on('mousewheel', function (event) {
            var delta = '', factor = 0;
            if (event.deltaX > 0) {
                delta = '右 ';
                factor = event.deltaFactor * event.deltaX;
            } else if (event.deltaX < 0) {
                delta = '左 ';
                factor = event.deltaFactor * event.deltaX;
            }
            if (event.deltaY > 0) {
                delta += '上';
                factor = event.deltaFactor * event.deltaY;
            } else if (event.deltaY < 0) {
                delta = '下';
                factor = event.deltaFactor * event.deltaY;
            }
            $(this).html(new Date().getTime() + '&emsp;滚动方向：' + delta + '&emsp;滚动距离：' + factor + 'px')
            event.stopPropagation();
            event.preventDefault();
        });

        // 实现横向滚动
        $('#demoMw2').on('mousewheel', function (event) {
            $(this).stop();
            $(this).animate({'scrollLeft': $(this).scrollLeft() - event.deltaFactor * event.deltaY * 2}, 80);
            event.stopPropagation();
            event.preventDefault();
        });

        // 实现翻页滚动
        var isScroll1 = false;
        $('#demoMw3').on('mousewheel', function (event) {
            if (!isScroll1) {
                if (event.deltaY > 0) {
                    $course = $(this).parent().find('.demoCourse>div.active').prev();
                    $(this).animate({'scrollTop': $(this).scrollTop() - $(this).outerHeight()}, 500);
                } else if (event.deltaY < 0) {
                    $course = $(this).parent().find('.demoCourse>div.active').next();
                    $(this).animate({'scrollTop': $(this).scrollTop() + $(this).outerHeight()}, 500);
                }
                if ($course.length > 0) {
                    $(this).parent().find('.demoCourse>div').removeClass('active');
                    $course.addClass('active');
                }
                isScroll1 = true;
                setTimeout(function () {
                    isScroll1 = false;
                }, 500);
            }
            event.stopPropagation();
            event.preventDefault();
        });

        // 横向翻页滚动
        var isScroll1 = false;
        $('#demoMw4').on('mousewheel', function (event) {
            if (!isScroll1) {
                var $course;
                if (event.deltaY > 0) {
                    $course = $(this).parent().find('.demoCourse>div.active').prev();
                    $(this).animate({'scrollLeft': $(this).scrollLeft() - $(this).outerWidth()}, 500);
                } else if (event.deltaY < 0) {
                    $course = $(this).parent().find('.demoCourse>div.active').next();
                    $(this).animate({'scrollLeft': $(this).scrollLeft() + $(this).outerWidth()}, 500);
                }
                if ($course.length > 0) {
                    $(this).parent().find('.demoCourse>div').removeClass('active');
                    $course.addClass('active');
                }
                isScroll1 = true;
                setTimeout(function () {
                    isScroll1 = false;
                }, 500);
            }
            event.stopPropagation();
            event.preventDefault();
        });

    });
</script>
</body>
</html>