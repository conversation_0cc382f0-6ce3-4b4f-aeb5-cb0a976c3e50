<?php
/**
 * 会员等级管理控制器
 */
class AdminUserLevelController extends BaseController
{
    /**
     * 会员等级管理页面
     */
    public function index()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 渲染页面
        $this->render('admin/user/level');
    }
    
    /**
     * 获取会员等级列表
     */
    public function list()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 获取会员等级列表
        $levels = $this->db->query("SELECT * FROM user_levels ORDER BY sort_order ASC, id ASC");
        
        // 处理特权功能
        foreach ($levels as &$level) {
            if (!empty($level['features'])) {
                $level['features'] = json_decode($level['features'], true);
            } else {
                $level['features'] = [];
            }
        }
        
        // 返回数据
        $this->success($levels);
    }
    
    /**
     * 获取会员等级详情
     */
    public function detail()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 获取参数
        $id = intval($this->getParam('id', 0));
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        // 获取会员等级详情
        $level = $this->db->query("SELECT * FROM user_levels WHERE id = ?", [$id], true);
        if (empty($level)) {
            $this->error('会员等级不存在');
        }
        
        // 处理特权功能
        if (!empty($level['features'])) {
            $level['features'] = json_decode($level['features'], true);
        } else {
            $level['features'] = [];
        }
        
        // 返回数据
        $this->success($level);
    }
    
    /**
     * 保存会员等级
     */
    public function save()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 获取参数
        $id = intval($this->getParam('id', 0));
        $name = trim($this->getParam('name', ''));
        $description = trim($this->getParam('description', ''));
        $icon = trim($this->getParam('icon', ''));
        $price = floatval($this->getParam('price', 0));
        $discount_rate = floatval($this->getParam('api_discount', 100)) / 100;
        $daily_request_limit = intval($this->getParam('daily_calls', 0));
        $features = $this->getParam('features', []);
        $status = intval($this->getParam('status', 1));
        $is_default = intval($this->getParam('is_default', 0));
        
        // 验证参数
        if (empty($name)) {
            $this->error('请输入等级名称');
        }
        
        if ($price < 0) {
            $this->error('价格不能为负数');
        }
        
        if ($discount_rate < 0 || $discount_rate > 1) {
            $this->error('折扣率必须在0-1之间');
        }
        
        // 检查名称是否重复
        $exists = $this->db->query("SELECT id FROM user_levels WHERE name = ? AND id != ?", [$name, $id], true);
        if (!empty($exists)) {
            $this->error('等级名称已存在');
        }
        
        // 处理特权功能
        $featuresJson = json_encode($features);
        
        // 当前时间
        $now = time();
        
        // 开启事务
        $this->db->beginTransaction();
        
        try {
            if ($id > 0) {
                // 更新会员等级
                $this->db->execute(
                    "UPDATE user_levels SET name = ?, description = ?, icon = ?, price = ?, discount_rate = ?, 
                    daily_request_limit = ?, features = ?, status = ?, update_time = ? WHERE id = ?",
                    [$name, $description, $icon, $price, $discount_rate, $daily_request_limit, $featuresJson, $status, $now, $id]
                );
            } else {
                // 获取最大排序值
                $maxSort = $this->db->query("SELECT MAX(sort_order) as max_sort FROM user_levels", [], true);
                $sortOrder = intval($maxSort['max_sort']) + 10;
                
                // 添加会员等级
                $this->db->execute(
                    "INSERT INTO user_levels (name, description, icon, price, discount_rate, daily_request_limit, 
                    features, status, sort_order, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    [$name, $description, $icon, $price, $discount_rate, $daily_request_limit, $featuresJson, $status, $sortOrder, $now, $now]
                );
                
                $id = $this->db->lastInsertId();
            }
            
            // 处理默认等级
            if ($is_default == 1) {
                // 先将所有等级设为非默认
                $this->db->execute("UPDATE user_levels SET is_default = 0 WHERE id != ?", [$id]);
                // 将当前等级设为默认
                $this->db->execute("UPDATE user_levels SET is_default = 1 WHERE id = ?", [$id]);
            }
            
            // 提交事务
            $this->db->commit();
            
            $this->success('保存成功');
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->error('保存失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除会员等级
     */
    public function delete()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 获取参数
        $id = intval($this->getParam('id', 0));
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        // 检查是否为默认等级
        $level = $this->db->query("SELECT is_default FROM user_levels WHERE id = ?", [$id], true);
        if (empty($level)) {
            $this->error('会员等级不存在');
        }
        
        if ($level['is_default'] == 1) {
            $this->error('默认等级不能删除');
        }
        
        // 检查是否有用户使用该等级
        $userCount = $this->db->query("SELECT COUNT(*) as count FROM user_level_relations WHERE level_id = ?", [$id], true);
        if ($userCount['count'] > 0) {
            $this->error('该等级下有用户，不能删除');
        }
        
        // 删除会员等级
        $this->db->execute("DELETE FROM user_levels WHERE id = ?", [$id]);
        
        $this->success('删除成功');
    }
    
    /**
     * 更新会员等级状态
     */
    public function updateStatus()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 获取参数
        $id = intval($this->getParam('id', 0));
        $status = intval($this->getParam('status', 0));
        
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        // 检查是否为默认等级
        $level = $this->db->query("SELECT is_default FROM user_levels WHERE id = ?", [$id], true);
        if (empty($level)) {
            $this->error('会员等级不存在');
        }
        
        if ($level['is_default'] == 1 && $status == 0) {
            $this->error('默认等级不能禁用');
        }
        
        // 更新状态
        $this->db->execute("UPDATE user_levels SET status = ?, update_time = ? WHERE id = ?", [$status, time(), $id]);
        
        $this->success('更新成功');
    }
    
    /**
     * 设置默认等级
     */
    public function setDefault()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 获取参数
        $id = intval($this->getParam('id', 0));
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        // 检查等级是否存在
        $level = $this->db->query("SELECT status FROM user_levels WHERE id = ?", [$id], true);
        if (empty($level)) {
            $this->error('会员等级不存在');
        }
        
        // 检查等级是否启用
        if ($level['status'] != 1) {
            $this->error('禁用的等级不能设为默认');
        }
        
        // 开启事务
        $this->db->beginTransaction();
        
        try {
            // 先将所有等级设为非默认
            $this->db->execute("UPDATE user_levels SET is_default = 0");
            // 将当前等级设为默认
            $this->db->execute("UPDATE user_levels SET is_default = 1 WHERE id = ?", [$id]);
            
            // 提交事务
            $this->db->commit();
            
            $this->success('设置成功');
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->error('设置失败：' . $e->getMessage());
        }
    }
    
    /**
     * 会员等级排序
     */
    public function sort()
    {
        // 检查管理员权限
        $this->checkAdminPermission('user_level');
        
        // 获取参数
        $ids = $this->getParam('ids', '');
        if (empty($ids)) {
            $this->error('参数错误');
        }
        
        // 解析ID列表
        $idArray = explode(',', $ids);
        if (empty($idArray)) {
            $this->error('参数错误');
        }
        
        // 开启事务
        $this->db->beginTransaction();
        
        try {
            // 更新排序
            foreach ($idArray as $index => $id) {
                $sortOrder = ($index + 1) * 10;
                $this->db->execute("UPDATE user_levels SET sort_order = ? WHERE id = ?", [$sortOrder, $id]);
            }
            
            // 提交事务
            $this->db->commit();
            
            $this->success('排序成功');
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->error('排序失败：' . $e->getMessage());
        }
    }
}