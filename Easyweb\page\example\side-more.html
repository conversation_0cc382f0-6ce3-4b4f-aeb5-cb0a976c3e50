<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="../../assets/images/favicon.ico" rel="icon">
    <title>EasyWeb管理系统模板</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!-- 头部 -->
    <div class="layui-header">
        <div class="layui-logo">
            <img src="../../assets/images/logo.png"/>
            <cite>&nbsp;EasyWeb Iframe</cite>
        </div>
        <ul class="layui-nav layui-layout-left">
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="flexible" title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a ew-event="refresh" title="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
            </li>
            <li class="layui-nav-item layui-hide-xs layui-this" lay-unselect><a nav-bind="xt1">系统一</a></li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect><a nav-bind="xt2">系统二</a></li>
            <li class="layui-nav-item layui-hide-xs" lay-unselect><a nav-bind="xt3">系统三</a></li>
            <!-- 小屏幕下变为下拉形式 -->
            <li class="layui-nav-item layui-hide-sm layui-show-xs-inline-block" lay-unselect>
                <a>更多</a>
                <dl class="layui-nav-child">
                    <dd lay-unselect><a nav-bind="xt1">系统一</a></dd>
                    <dd lay-unselect><a nav-bind="xt2">系统二</a></dd>
                    <dd lay-unselect><a nav-bind="xt3">系统二</a></dd>
                </dl>
            </li>
        </ul>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item" lay-unselect>
                <a>
                    <img src="../../assets/images/head.jpg" class="layui-nav-img">
                    <cite>管理员</cite>
                </a>
            </li>
        </ul>
    </div>

    <!-- 侧边栏 -->
    <div class="layui-side">
        <div class="layui-side-scroll">
            <!-- 系统一的菜单 -->
            <ul class="layui-nav layui-nav-tree" nav-id="xt1" lay-filter="admin-side-nav" style="margin: 15px 0;">
                <li class="layui-nav-item">
                    <a><i class="layui-icon layui-icon-home"></i>&emsp;<cite>Dashboard</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a lay-href="../../page/console/console.html">控制台</a></dd>
                        <dd><a lay-href="../../page/console/dashboard.html">分析页</a></dd>
                    </dl>
                </li>
            </ul>

            <!-- 系统二的菜单，加layui-hide隐藏 -->
            <ul class="layui-nav layui-nav-tree layui-hide" nav-id="xt2" lay-filter="admin-side-nav"
                style="margin: 15px 0;">
                <li class="layui-nav-item">
                    <a><i class="layui-icon layui-icon-table"></i>&emsp;<cite>表格页</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a lay-href="../../page/template/table/table-basic.html">数据表格</a></dd>
                        <dd><a lay-href="../../page/template/table/table-advance.html">复杂查询</a></dd>
                    </dl>
                </li>
            </ul>

            <!-- 系统三的菜单，加layui-hide隐藏 -->
            <ul class="layui-nav layui-nav-tree layui-hide" nav-id="xt3" lay-filter="admin-side-nav"
                style="margin: 15px 0;">
                <li class="layui-nav-item">
                    <a><i class="layui-icon layui-icon-form"></i>&emsp;<cite>表单页</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a lay-href="../../page/template/form/form-basic.html">基础表单</a></dd>
                        <dd><a lay-href="../../page/template/form/form-advance.html">复杂表单</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
    </div>

    <!-- 主体部分 -->
    <div class="layui-body"></div>
    <!-- 底部 -->
    <div class="layui-footer">Copyright © 2019 EasyWeb All rights reserved.</div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['index'], function () {
        var $ = layui.jquery;
        var index = layui.index;

        // 默认加载主页
        index.loadHome({
            menuPath: '../../page/console/console.html',
            menuName: '<i class="layui-icon layui-icon-home"></i>',
            loadSetting: false
        });

    });
</script>
</body>
</html>