<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API在线调试 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/site.css"/>
    <style>
        .api-test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .api-test-card {
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .api-test-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-test-title {
            font-size: 18px;
            font-weight: bold;
        }
        .api-test-body {
            padding: 20px;
        }
        .api-test-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            text-align: right;
        }
        .api-result {
            margin-top: 20px;
            background-color: #f8f8f8;
            border-radius: 4px;
            padding: 15px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 500px;
            overflow-y: auto;
        }
        .api-result-success {
            background-color: #f0f9eb;
            border-left: 4px solid #67c23a;
        }
        .api-result-error {
            background-color: #fef0f0;
            border-left: 4px solid #f56c6c;
        }
        .api-param-table {
            margin-bottom: 0;
        }
        .api-param-required {
            color: #f56c6c;
            margin-left: 5px;
        }
        .api-method-select {
            width: 100px;
        }
        .api-url-input {
            width: calc(100% - 110px);
            margin-left: 10px;
        }
        .api-history-item {
            cursor: pointer;
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-history-item:hover {
            background-color: #f8f8f8;
        }
        .api-history-method {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 3px;
            color: #fff;
            font-size: 12px;
            margin-right: 10px;
        }
        .api-history-method-get {
            background-color: #409eff;
        }
        .api-history-method-post {
            background-color: #67c23a;
        }
        .api-history-method-put {
            background-color: #e6a23c;
        }
        .api-history-method-delete {
            background-color: #f56c6c;
        }
        .api-history-time {
            float: right;
            color: #999;
            font-size: 12px;
        }
        .api-history-empty {
            padding: 30px 0;
            text-align: center;
            color: #999;
        }
        .api-key-select {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
<!-- 引入网站头部 -->
<div id="header"></div>

<div class="layui-container api-test-container">
    <div class="layui-row layui-col-space20">
        <div class="layui-col-md8">
            <div class="api-test-card">
                <div class="api-test-header">
                    <div class="api-test-title">API在线调试</div>
                </div>
                <div class="api-test-body">
                    <form class="layui-form" id="apiTestForm">
                        <div class="layui-form-item api-key-select">
                            <label class="layui-form-label">API密钥</label>
                            <div class="layui-input-block">
                                <select name="api_key" lay-filter="apiKey" lay-verify="required">
                                    <option value="">请选择API密钥</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <select name="method" lay-filter="method" class="api-method-select">
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input type="text" name="url" placeholder="请输入API接口URL" class="layui-input api-url-input" lay-verify="required">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-tab layui-tab-brief">
                                <ul class="layui-tab-title">
                                    <li class="layui-this">参数</li>
                                    <li>请求头</li>
                                    <li>请求体</li>
                                </ul>
                                <div class="layui-tab-content">
                                    <div class="layui-tab-item layui-show">
                                        <table class="layui-table api-param-table" lay-skin="line">
                                            <colgroup>
                                                <col width="150">
                                                <col>
                                                <col width="80">
                                            </colgroup>
                                            <thead>
                                                <tr>
                                                    <th>参数名</th>
                                                    <th>参数值</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="paramTable">
                                                <tr>
                                                    <td><input type="text" name="param_name[]" placeholder="参数名" class="layui-input"></td>
                                                    <td><input type="text" name="param_value[]" placeholder="参数值" class="layui-input"></td>
                                                    <td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="3">
                                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" id="addParamBtn"><i class="layui-icon">&#xe654;</i> 添加参数</button>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <div class="layui-tab-item">
                                        <table class="layui-table api-param-table" lay-skin="line">
                                            <colgroup>
                                                <col width="150">
                                                <col>
                                                <col width="80">
                                            </colgroup>
                                            <thead>
                                                <tr>
                                                    <th>Header名</th>
                                                    <th>Header值</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="headerTable">
                                                <tr>
                                                    <td><input type="text" name="header_name[]" placeholder="Header名" class="layui-input"></td>
                                                    <td><input type="text" name="header_value[]" placeholder="Header值" class="layui-input"></td>
                                                    <td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="3">
                                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" id="addHeaderBtn"><i class="layui-icon">&#xe654;</i> 添加Header</button>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <div class="layui-tab-item">
                                        <textarea name="body" placeholder="请输入请求体内容，支持JSON格式" class="layui-textarea" style="min-height: 150px;"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="api-test-footer">
                    <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">重置</button>
                    <button type="button" class="layui-btn" id="sendBtn">发送请求</button>
                </div>
            </div>
            
            <div class="api-test-card" id="resultCard" style="display: none;">
                <div class="api-test-header">
                    <div class="api-test-title">响应结果</div>
                </div>
                <div class="api-test-body">
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态码</label>
                        <div class="layui-input-block">
                            <input type="text" id="statusCode" class="layui-input" readonly>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">响应时间</label>
                        <div class="layui-input-block">
                            <input type="text" id="responseTime" class="layui-input" readonly>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">响应头</label>
                        <div class="layui-input-block">
                            <pre class="api-result" id="responseHeaders"></pre>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">响应体</label>
                        <div class="layui-input-block">
                            <pre class="api-result" id="responseBody"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-col-md4">
            <div class="api-test-card">
                <div class="api-test-header">
                    <div class="api-test-title">API列表</div>
                </div>
                <div class="api-test-body">
                    <div class="layui-form-item">
                        <input type="text" id="apiSearch" placeholder="搜索API" class="layui-input">
                    </div>
                    <div class="layui-collapse" lay-accordion id="apiList">
                        <!-- API列表将通过JS动态加载 -->
                        <div class="layui-colla-item">
                            <h2 class="layui-colla-title">加载中...</h2>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="api-test-card">
                <div class="api-test-header">
                    <div class="api-test-title">历史记录</div>
                </div>
                <div class="api-test-body">
                    <div id="historyList">
                        <!-- 历史记录将通过JS动态加载 -->
                        <div class="api-history-empty">
                            <i class="layui-icon" style="font-size: 30px;">&#xe69c;</i>
                            <p>暂无历史记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入网站底部 -->
<div id="footer"></div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['layer', 'form', 'element', 'jquery'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var element = layui.element;
    
    // 加载头部和底部
    $('#header').load('/common/header.html');
    $('#footer').load('/common/footer.html');
    
    // 加载API密钥
    loadApiKeys();
    
    // 加载API列表
    loadApiList();
    
    // 加载历史记录
    loadHistory();
    
    // 添加参数行
    $('#addParamBtn').click(function() {
        var newRow = '<tr>' +
                     '<td><input type="text" name="param_name[]" placeholder="参数名" class="layui-input"></td>' +
                     '<td><input type="text" name="param_value[]" placeholder="参数值" class="layui-input"></td>' +
                     '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                     '</tr>';
        $('#paramTable').append(newRow);
    });
    
    // 添加Header行
    $('#addHeaderBtn').click(function() {
        var newRow = '<tr>' +
                     '<td><input type="text" name="header_name[]" placeholder="Header名" class="layui-input"></td>' +
                     '<td><input type="text" name="header_value[]" placeholder="Header值" class="layui-input"></td>' +
                     '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                     '</tr>';
        $('#headerTable').append(newRow);
    });
    
    // 删除行
    $(document).on('click', '.delete-row', function() {
        $(this).closest('tr').remove();
    });
    
    // 重置按钮
    $('#resetBtn').click(function() {
        $('#apiTestForm')[0].reset();
        $('#paramTable').html('<tr>' +
                             '<td><input type="text" name="param_name[]" placeholder="参数名" class="layui-input"></td>' +
                             '<td><input type="text" name="param_value[]" placeholder="参数值" class="layui-input"></td>' +
                             '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                             '</tr>');
        $('#headerTable').html('<tr>' +
                              '<td><input type="text" name="header_name[]" placeholder="Header名" class="layui-input"></td>' +
                              '<td><input type="text" name="header_value[]" placeholder="Header值" class="layui-input"></td>' +
                              '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                              '</tr>');
        $('#resultCard').hide();
    });
    
    // 发送请求按钮
    $('#sendBtn').click(function() {
        var form = $('#apiTestForm');
        var method = form.find('select[name="method"]').val();
        var url = form.find('input[name="url"]').val();
        var apiKey = form.find('select[name="api_key"]').val();
        
        if (!url) {
            layer.msg('请输入API接口URL', {icon: 2});
            return;
        }
        
        if (!apiKey) {
            layer.msg('请选择API密钥', {icon: 2});
            return;
        }
        
        // 构建参数
        var params = {};
        form.find('input[name="param_name[]"]').each(function(index) {
            var name = $(this).val();
            var value = form.find('input[name="param_value[]"]').eq(index).val();
            if (name) {
                params[name] = value;
            }
        });
        
        // 添加API密钥
        params.api_key = apiKey;
        
        // 构建请求头
        var headers = {};
        form.find('input[name="header_name[]"]').each(function(index) {
            var name = $(this).val();
            var value = form.find('input[name="header_value[]"]').eq(index).val();
            if (name) {
                headers[name] = value;
            }
        });
        
        // 请求体
        var body = form.find('textarea[name="body"]').val();
        
        // 显示加载层
        var loadIndex = layer.load(2);
        
        // 记录开始时间
        var startTime = new Date().getTime();
        
        // 发送请求
        $.ajax({
            url: '/api/test',
            type: 'POST',
            data: {
                method: method,
                url: url,
                params: JSON.stringify(params),
                headers: JSON.stringify(headers),
                body: body
            },
            dataType: 'json',
            success: function(res) {
                layer.close(loadIndex);
                
                // 计算响应时间
                var endTime = new Date().getTime();
                var responseTime = endTime - startTime;
                
                // 显示响应结果
                $('#statusCode').val(res.status_code);
                $('#responseTime').val(responseTime + ' ms');
                $('#responseHeaders').text(formatJson(res.headers));
                $('#responseBody').text(formatJson(res.body));
                
                // 根据状态码设置样式
                if (res.status_code >= 200 && res.status_code < 300) {
                    $('#responseBody').addClass('api-result-success').removeClass('api-result-error');
                } else {
                    $('#responseBody').addClass('api-result-error').removeClass('api-result-success');
                }
                
                $('#resultCard').show();
                
                // 保存到历史记录
                saveHistory(method, url, params, headers, body, res);
            },
            error: function(xhr) {
                layer.close(loadIndex);
                layer.msg('请求发送失败', {icon: 2});
            }
        });
    });
    
    // 搜索API
    $('#apiSearch').on('input', function() {
        var keyword = $(this).val().toLowerCase();
        $('.api-item').each(function() {
            var name = $(this).data('name').toLowerCase();
            var url = $(this).data('url').toLowerCase();
            if (name.indexOf(keyword) !== -1 || url.indexOf(keyword) !== -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    
    // 加载API密钥
    function loadApiKeys() {
        $.get('/api/keys/list', function(res) {
            if (res.code === 0) {
                var options = '<option value="">请选择API密钥</option>';
                for (var i = 0; i < res.data.length; i++) {
                    options += '<option value="' + res.data[i].key_value + '">' + res.data[i].key_name + '</option>';
                }
                $('select[name="api_key"]').html(options);
                form.render('select');
            }
        }, 'json');
    }
    
    // 加载API列表
    function loadApiList() {
        $.get('/api/list', function(res) {
            if (res.code === 0) {
                var html = '';
                for (var i = 0; i < res.data.categories.length; i++) {
                    var category = res.data.categories[i];
                    html += '<div class="layui-colla-item">' +
                            '<h2 class="layui-colla-title">' + category.name + '</h2>' +
                            '<div class="layui-colla-content">';
                    
                    for (var j = 0; j < category.apis.length; j++) {
                        var api = category.apis[j];
                        html += '<div class="api-item" data-name="' + api.name + '" data-url="' + api.url + '">' +
                                '<p><a href="javascript:;" class="api-link" data-method="' + api.method + '" data-url="' + api.url + '">' + api.name + '</a></p>' +
                                '<p class="layui-text-muted">' + api.method + ' ' + api.url + '</p>' +
                                '</div>';
                    }
                    
                    html += '</div></div>';
                }
                
                $('#apiList').html(html);
                element.render('collapse');
            }
        }, 'json');
    }
    
    // 点击API链接
    $(document).on('click', '.api-link', function() {
        var method = $(this).data('method');
        var url = $(this).data('url');
        
        $('select[name="method"]').val(method);
        $('input[name="url"]').val(url);
        form.render('select');
    });
    
    // 加载历史记录
    function loadHistory() {
        var history = localStorage.getItem('apiTestHistory');
        if (history) {
            history = JSON.parse(history);
            renderHistory(history);
        }
    }
    
    // 保存历史记录
    function saveHistory(method, url, params, headers, body, response) {
        var history = localStorage.getItem('apiTestHistory');
        history = history ? JSON.parse(history) : [];
        
        // 限制历史记录数量
        if (history.length >= 10) {
            history.pop();
        }
        
        // 添加新记录
        history.unshift({
            method: method,
            url: url,
            params: params,
            headers: headers,
            body: body,
            response: response,
            time: new Date().getTime()
        });
        
        localStorage.setItem('apiTestHistory', JSON.stringify(history));
        
        // 重新渲染历史记录
        renderHistory(history);
    }
    
    // 渲染历史记录
    function renderHistory(history) {
        if (history.length === 0) {
            $('#historyList').html('<div class="api-history-empty">' +
                                  '<i class="layui-icon" style="font-size: 30px;">&#xe69c;</i>' +
                                  '<p>暂无历史记录</p>' +
                                  '</div>');
            return;
        }
        
        var html = '';
        for (var i = 0; i < history.length; i++) {
            var item = history[i];
            var methodClass = 'api-history-method-' + item.method.toLowerCase();
            
            html += '<div class="api-history-item" data-index="' + i + '">' +
                    '<span class="api-history-method ' + methodClass + '">' + item.method + '</span>' +
                    '<span class="api-history-url">' + item.url + '</span>' +
                    '<span class="api-history-time">' + formatDate(item.time) + '</span>' +
                    '</div>';
        }
        
        $('#historyList').html(html);
    }
    
    // 点击历史记录
    $(document).on('click', '.api-history-item', function() {
        var index = $(this).data('index');
        var history = JSON.parse(localStorage.getItem('apiTestHistory'));
        var item = history[index];
        
        // 填充表单
        $('select[name="method"]').val(item.method);
        $('input[name="url"]').val(item.url);
        form.render('select');
        
        // 填充参数
        $('#paramTable').empty();
        for (var key in item.params) {
            if (key === 'api_key') continue;
            var newRow = '<tr>' +
                         '<td><input type="text" name="param_name[]" value="' + key + '" class="layui-input"></td>' +
                         '<td><input type="text" name="param_value[]" value="' + item.params[key] + '" class="layui-input"></td>' +
                         '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                         '</tr>';
            $('#paramTable').append(newRow);
        }
        
        // 如果没有参数，添加一个空行
        if ($('#paramTable').children().length === 0) {
            $('#paramTable').html('<tr>' +
                                 '<td><input type="text" name="param_name[]" placeholder="参数名" class="layui-input"></td>' +
                                 '<td><input type="text" name="param_value[]" placeholder="参数值" class="layui-input"></td>' +
                                 '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                                 '</tr>');
        }
        
        // 填充请求头
        $('#headerTable').empty();
        for (var key in item.headers) {
            var newRow = '<tr>' +
                         '<td><input type="text" name="header_name[]" value="' + key + '" class="layui-input"></td>' +
                         '<td><input type="text" name="header_value[]" value="' + item.headers[key] + '" class="layui-input"></td>' +
                         '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                         '</tr>';
            $('#headerTable').append(newRow);
        }
        
        // 如果没有请求头，添加一个空行
        if ($('#headerTable').children().length === 0) {
            $('#headerTable').html('<tr>' +
                                  '<td><input type="text" name="header_name[]" placeholder="Header名" class="layui-input"></td>' +
                                  '<td><input type="text" name="header_value[]" placeholder="Header值" class="layui-input"></td>' +
                                  '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs delete-row"><i class="layui-icon">&#xe640;</i></button></td>' +
                                  '</tr>');
        }
        
        // 填充请求体
        $('textarea[name="body"]').val(item.body);
        
        // 显示响应结果
        $('#statusCode').val(item.response.status_code);
        $('#responseTime').val('(历史记录)');
        $('#responseHeaders').text(formatJson(item.response.headers));
        $('#responseBody').text(formatJson(item.response.body));
        
        // 根据状态码设置样式
        if (item.response.status_code >= 200 && item.response.status_code < 300) {
            $('#responseBody').addClass('api-result-success').removeClass('api-result-error');
        } else {
            $('#responseBody').addClass('api-result-error').removeClass('api-result-success');
        }
        
        $('#resultCard').show();
    });
    
    // 格式化JSON
    function formatJson(json) {
        if (typeof json === 'string') {
            try {
                json = JSON.parse(json);
            } catch (e) {
                return json;
            }
        }
        
        return JSON.stringify(json, null, 2);
    }
    
    // 格式化日期
    function formatDate(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        var seconds = ('0' + date.getSeconds()).slice(-2);
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }
});
</script>
</body>
</html>