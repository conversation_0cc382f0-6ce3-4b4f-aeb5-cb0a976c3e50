<?php
/**
 * 获取管理员信息
 */
session_start();

header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['code' => 401, 'msg' => '未登录']);
    exit;
}

try {
    // 连接数据库
    $config = require_once '../../config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    // 查询管理员信息
    $stmt = $pdo->prepare("SELECT id, username, email, created_at FROM users WHERE id = ? AND role = 'admin'");
    $stmt->execute([$_SESSION['admin_id']]);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo json_encode(['code' => 404, 'msg' => '管理员不存在']);
        exit;
    }
    
    echo json_encode([
        'code' => 200,
        'msg' => '获取成功',
        'data' => [
            'id' => $admin['id'],
            'username' => $admin['username'],
            'email' => $admin['email'],
            'created_at' => $admin['created_at']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
}
?>
