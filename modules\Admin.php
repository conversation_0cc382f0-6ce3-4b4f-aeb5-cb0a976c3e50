<?php
/**
 * 管理员模型
 * API管理系统 - 管理员数据模型
 */

require_once __DIR__ . '/../core/Model.php';

class Admin extends Model {
    protected $table = 'admins';
    protected $fillable = [
        'username', 'password', 'nickname', 'email', 'phone', 
        'avatar', 'role_id', 'status'
    ];
    protected $hidden = ['password'];
    
    /**
     * 根据用户名查找管理员
     */
    public function findByUsername($username) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE username = :username";
        $result = $this->db->fetchOne($sql, ['username' => $username]);
        return $result ?: null;
    }
    
    /**
     * 验证登录
     */
    public function login($username, $password) {
        $admin = $this->findByUsername($username);
        if (!$admin) {
            return false;
        }
        
        if (!System::verifyPassword($password, $admin['password'])) {
            return false;
        }
        
        if ($admin['status'] != 1) {
            throw new Exception('账户已被禁用');
        }
        
        // 更新登录信息
        $this->update($admin['id'], [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => System::getClientIp(),
            'login_count' => $admin['login_count'] + 1
        ]);
        
        return $this->hideFields($admin);
    }
    
    /**
     * 创建管理员
     */
    public function create($data) {
        if (isset($data['password'])) {
            $data['password'] = System::hashPassword($data['password']);
        }
        
        return parent::create($data);
    }
    
    /**
     * 更新管理员
     */
    public function update($id, $data) {
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = System::hashPassword($data['password']);
        } else {
            unset($data['password']);
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * 获取管理员列表（带角色信息）
     */
    public function getListWithRole($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['username'])) {
            $whereClause[] = "a.username LIKE :username";
            $params['username'] = '%' . $conditions['username'] . '%';
        }
        
        if (!empty($conditions['status'])) {
            $whereClause[] = "a.status = :status";
            $params['status'] = $conditions['status'];
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->getTableName()} a {$whereStr}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT a.*, r.name as role_name 
                FROM {$this->getTableName()} a 
                LEFT JOIN {$this->db->getPrefix()}roles r ON a.role_id = r.id 
                {$whereStr}
                ORDER BY a.id DESC 
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        // 隐藏密码字段
        $data = array_map([$this, 'hideFields'], $data);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 检查用户名是否存在
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->getTableName()} WHERE username = :username";
        $params = ['username' => $username];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * 修改密码
     */
    public function changePassword($id, $oldPassword, $newPassword) {
        $admin = $this->find($id);
        if (!$admin) {
            throw new Exception('管理员不存在');
        }
        
        // 验证原密码
        $adminWithPassword = $this->findByUsername($admin['username']);
        if (!System::verifyPassword($oldPassword, $adminWithPassword['password'])) {
            throw new Exception('原密码错误');
        }
        
        // 更新密码
        return $this->update($id, ['password' => $newPassword]);
    }
    
    /**
     * 重置密码
     */
    public function resetPassword($id, $newPassword = null) {
        if (!$newPassword) {
            $newPassword = System::generateRandomString(8);
        }
        
        $result = $this->update($id, ['password' => $newPassword]);
        
        if ($result) {
            return $newPassword;
        }
        
        return false;
    }
    
    /**
     * 获取在线管理员统计
     */
    public function getOnlineStats() {
        $sql = "SELECT COUNT(*) as total,
                       SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
                       SUM(CASE WHEN last_login_time > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as recent_login
                FROM {$this->getTableName()}";
        
        return $this->db->fetchOne($sql);
    }
}