<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统配置</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- 基础配置 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>基础配置</h3>
                </div>
                <div class="layui-card-body">
                    <form class="layui-form" lay-filter="basic-config">
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="site_name" placeholder="请输入网站名称" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站描述</label>
                            <div class="layui-input-block">
                                <textarea name="site_description" placeholder="请输入网站描述" class="layui-textarea"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站关键词</label>
                            <div class="layui-input-block">
                                <input type="text" name="site_keywords" placeholder="请输入网站关键词" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站Logo</label>
                            <div class="layui-input-block">
                                <div class="layui-upload">
                                    <button type="button" class="layui-btn" id="upload-logo">上传Logo</button>
                                    <div class="layui-upload-list">
                                        <img class="layui-upload-img" id="logo-preview" style="width: 100px; height: 100px; display: none;">
                                        <input type="hidden" name="site_logo" id="site_logo">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="save-basic">保存基础配置</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 功能配置 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>功能配置</h3>
                </div>
                <div class="layui-card-body">
                    <form class="layui-form" lay-filter="function-config">
                        <div class="layui-form-item">
                            <label class="layui-form-label">允许注册</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="register_enabled" lay-skin="switch" lay-text="开启|关闭">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">邮箱验证</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="email_verify" lay-skin="switch" lay-text="开启|关闭">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">新用户默认余额</label>
                            <div class="layui-input-block">
                                <input type="number" name="default_balance" placeholder="请输入默认余额" class="layui-input" step="0.01">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">QPS限制</label>
                            <div class="layui-input-block">
                                <input type="number" name="qps_limit" placeholder="每秒请求限制" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">上传文件大小限制(MB)</label>
                            <div class="layui-input-block">
                                <input type="number" name="upload_max_size" placeholder="文件大小限制" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="save-function">保存功能配置</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-row layui-col-space15" style="margin-top: 15px;">
        <!-- 邮件配置 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>邮件配置</h3>
                </div>
                <div class="layui-card-body">
                    <form class="layui-form" lay-filter="email-config">
                        <div class="layui-form-item">
                            <label class="layui-form-label">SMTP服务器</label>
                            <div class="layui-input-block">
                                <input type="text" name="smtp_host" placeholder="请输入SMTP服务器" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">SMTP端口</label>
                            <div class="layui-input-block">
                                <input type="number" name="smtp_port" placeholder="请输入SMTP端口" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">SMTP用户名</label>
                            <div class="layui-input-block">
                                <input type="text" name="smtp_username" placeholder="请输入SMTP用户名" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">SMTP密码</label>
                            <div class="layui-input-block">
                                <input type="password" name="smtp_password" placeholder="请输入SMTP密码" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">发件人邮箱</label>
                            <div class="layui-input-block">
                                <input type="email" name="from_email" placeholder="请输入发件人邮箱" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">发件人名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="from_name" placeholder="请输入发件人名称" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="save-email">保存邮件配置</button>
                                <button type="button" class="layui-btn layui-btn-normal" id="test-email">测试邮件</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 支付配置 -->
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>支付配置</h3>
                </div>
                <div class="layui-card-body">
                    <div class="layui-tab" lay-filter="payment-tab">
                        <ul class="layui-tab-title">
                            <li class="layui-this">支付宝</li>
                            <li>微信支付</li>
                        </ul>
                        <div class="layui-tab-content">
                            <!-- 支付宝配置 -->
                            <div class="layui-tab-item layui-show">
                                <form class="layui-form" lay-filter="alipay-config">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">应用ID</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="alipay_app_id" placeholder="请输入支付宝应用ID" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">应用私钥</label>
                                        <div class="layui-input-block">
                                            <textarea name="alipay_private_key" placeholder="请输入应用私钥" class="layui-textarea"></textarea>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">支付宝公钥</label>
                                        <div class="layui-input-block">
                                            <textarea name="alipay_public_key" placeholder="请输入支付宝公钥" class="layui-textarea"></textarea>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <button class="layui-btn" lay-submit lay-filter="save-alipay">保存支付宝配置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- 微信支付配置 -->
                            <div class="layui-tab-item">
                                <form class="layui-form" lay-filter="wechat-config">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">应用ID</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="wechat_app_id" placeholder="请输入微信应用ID" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商户号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="wechat_mch_id" placeholder="请输入商户号" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商户密钥</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="wechat_key" placeholder="请输入商户密钥" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <button class="layui-btn" lay-submit lay-filter="save-wechat">保存微信配置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['form', 'upload', 'element', 'layer'], function(){
    var form = layui.form;
    var upload = layui.upload;
    var element = layui.element;
    var layer = layui.layer;
    var $ = layui.$;

    // 加载配置数据
    loadConfigs();

    // Logo上传
    upload.render({
        elem: '#upload-logo',
        url: '/admin/upload/image',
        before: function(obj){
            obj.preview(function(index, file, result){
                $('#logo-preview').attr('src', result).show();
            });
        },
        done: function(res){
            if(res.code === 200){
                $('#site_logo').val(res.data.url);
                layer.msg('上传成功', {icon: 1});
            } else {
                layer.msg('上传失败: ' + res.message, {icon: 2});
            }
        }
    });

    // 保存基础配置
    form.on('submit(save-basic)', function(data){
        $.post('/admin/system/config/save', {
            type: 'basic',
            data: data.field
        }, function(res){
            if(res.code === 200){
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg('保存失败: ' + res.message, {icon: 2});
            }
        });
        return false;
    });

    // 保存功能配置
    form.on('submit(save-function)', function(data){
        // 处理开关状态
        data.field.register_enabled = data.field.register_enabled ? '1' : '0';
        data.field.email_verify = data.field.email_verify ? '1' : '0';
        
        $.post('/admin/system/config/save', {
            type: 'function',
            data: data.field
        }, function(res){
            if(res.code === 200){
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg('保存失败: ' + res.message, {icon: 2});
            }
        });
        return false;
    });

    // 保存邮件配置
    form.on('submit(save-email)', function(data){
        $.post('/admin/system/config/save', {
            type: 'email',
            data: data.field
        }, function(res){
            if(res.code === 200){
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg('保存失败: ' + res.message, {icon: 2});
            }
        });
        return false;
    });

    // 保存支付宝配置
    form.on('submit(save-alipay)', function(data){
        $.post('/admin/system/config/save', {
            type: 'alipay',
            data: data.field
        }, function(res){
            if(res.code === 200){
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg('保存失败: ' + res.message, {icon: 2});
            }
        });
        return false;
    });

    // 保存微信配置
    form.on('submit(save-wechat)', function(data){
        $.post('/admin/system/config/save', {
            type: 'wechat',
            data: data.field
        }, function(res){
            if(res.code === 200){
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg('保存失败: ' + res.message, {icon: 2});
            }
        });
        return false;
    });

    // 测试邮件
    $('#test-email').click(function(){
        layer.prompt({
            title: '请输入测试邮箱地址',
            formType: 0
        }, function(value, index){
            if(!value || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)){
                layer.msg('请输入正确的邮箱地址', {icon: 2});
                return;
            }
            
            $.post('/admin/system/config/test-email', {
                email: value
            }, function(res){
                if(res.code === 200){
                    layer.msg('测试邮件发送成功', {icon: 1});
                    layer.close(index);
                } else {
                    layer.msg('测试邮件发送失败: ' + res.message, {icon: 2});
                }
            });
        });
    });

    // 加载配置数据
    function loadConfigs(){
        $.get('/admin/system/config/get', function(res){
            if(res.code === 200){
                var configs = res.data;
                
                // 填充基础配置
                form.val('basic-config', {
                    site_name: configs.site_name || '',
                    site_description: configs.site_description || '',
                    site_keywords: configs.site_keywords || '',
                    site_logo: configs.site_logo || ''
                });
                
                // 显示Logo预览
                if(configs.site_logo){
                    $('#logo-preview').attr('src', configs.site_logo).show();
                    $('#site_logo').val(configs.site_logo);
                }
                
                // 填充功能配置
                form.val('function-config', {
                    register_enabled: configs.register_enabled === '1',
                    email_verify: configs.email_verify === '1',
                    default_balance: configs.default_balance || '0',
                    qps_limit: configs.qps_limit || '10',
                    upload_max_size: configs.upload_max_size ? (configs.upload_max_size / 1024 / 1024) : '10'
                });
                
                // 填充邮件配置
                form.val('email-config', {
                    smtp_host: configs.smtp_host || '',
                    smtp_port: configs.smtp_port || '587',
                    smtp_username: configs.smtp_username || '',
                    smtp_password: configs.smtp_password || '',
                    from_email: configs.from_email || '',
                    from_name: configs.from_name || ''
                });
                
                // 填充支付宝配置
                form.val('alipay-config', {
                    alipay_app_id: configs.alipay_app_id || '',
                    alipay_private_key: configs.alipay_private_key || '',
                    alipay_public_key: configs.alipay_public_key || ''
                });
                
                // 填充微信配置
                form.val('wechat-config', {
                    wechat_app_id: configs.wechat_app_id || '',
                    wechat_mch_id: configs.wechat_mch_id || '',
                    wechat_key: configs.wechat_key || ''
                });
            }
        });
    }
});
</script>
</body>
</html>