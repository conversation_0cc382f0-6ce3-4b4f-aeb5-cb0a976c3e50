<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - API管理系统</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
    <link rel="icon" href="../../Easyweb/assets/images/favicon.ico">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            line-height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 220px;
            background: #2f3349;
            overflow-y: auto;
            z-index: 999;
        }
        
        .admin-main {
            margin-left: 220px;
            margin-top: 60px;
            padding: 20px;
            min-height: calc(100vh - 60px);
            background: #f5f5f5;
        }
        
        .content-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-menu li {
            border-bottom: 1px solid #3a3f5c;
        }
        
        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: #b8c5d6;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        
        .nav-menu i {
            margin-right: 10px;
            width: 16px;
        }
        
        .user-info {
            float: right;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            vertical-align: middle;
            margin-right: 10px;
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .status-pending { background: #fa8c16; }
        .status-paid { background: #52c41a; }
        .status-processing { background: #1890ff; }
        .status-completed { background: #52c41a; }
        .status-cancelled { background: #8c8c8c; }
        .status-refunded { background: #f5222d; }
        
        .payment-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .payment-unpaid { background: #fa8c16; }
        .payment-paid { background: #52c41a; }
        .payment-refunded { background: #f5222d; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-item {
            text-align: center;
        }
        
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .order-detail-table {
            margin-top: 15px;
        }
        
        .order-detail-table th {
            background: #f5f5f5;
            font-weight: bold;
            width: 120px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="admin-header">
        <div style="float: left;">
            <h2 style="margin: 0; font-size: 18px;">
                <i class="layui-icon layui-icon-dollar"></i>
                订单管理
            </h2>
        </div>
        <div class="user-info">
            <img src="../../Easyweb/assets/images/head.jpg" alt="头像" class="user-avatar">
            <span id="adminName">管理员</span>
            <a href="javascript:;" onclick="logout()" style="color: white; margin-left: 15px;">
                <i class="layui-icon layui-icon-logout"></i> 退出
            </a>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="admin-sidebar">
        <ul class="nav-menu">
            <li>
                <a href="dashboard.html">
                    <i class="layui-icon layui-icon-home"></i>
                    仪表板
                </a>
            </li>
            <li>
                <a href="api-list.html">
                    <i class="layui-icon layui-icon-api"></i>
                    API管理
                </a>
            </li>
            <li>
                <a href="user-list.html">
                    <i class="layui-icon layui-icon-user"></i>
                    用户管理
                </a>
            </li>
            <li>
                <a href="merchant-list.html">
                    <i class="layui-icon layui-icon-shop"></i>
                    商家管理
                </a>
            </li>
            <li>
                <a href="order-list.html" class="active">
                    <i class="layui-icon layui-icon-dollar"></i>
                    订单管理
                </a>
            </li>
            <li>
                <a href="finance.html">
                    <i class="layui-icon layui-icon-rmb"></i>
                    财务管理
                </a>
            </li>
            <li>
                <a href="admin-list.html">
                    <i class="layui-icon layui-icon-username"></i>
                    管理员
                </a>
            </li>
            <li>
                <a href="role-list.html">
                    <i class="layui-icon layui-icon-group"></i>
                    角色权限
                </a>
            </li>
            <li>
                <a href="system-config.html">
                    <i class="layui-icon layui-icon-set"></i>
                    系统配置
                </a>
            </li>
            <li>
                <a href="logs.html">
                    <i class="layui-icon layui-icon-file"></i>
                    系统日志
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="admin-main">
        <!-- 统计卡片 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="totalOrders">0</div>
                        <div class="stats-label">总订单数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="paidOrders">0</div>
                        <div class="stats-label">已支付订单</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="totalAmount">¥0</div>
                        <div class="stats-label">总金额</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stats-card">
                    <div class="stats-item">
                        <div class="stats-number" id="refundAmount">¥0</div>
                        <div class="stats-label">退款金额</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form class="layui-form" lay-filter="searchForm">
                <div class="layui-row layui-col-space10">
                    <div class="layui-col-md2">
                        <input type="text" name="order_no" placeholder="订单号" class="layui-input">
                    </div>
                    <div class="layui-col-md2">
                        <input type="text" name="username" placeholder="用户名" class="layui-input">
                    </div>
                    <div class="layui-col-md2">
                        <select name="status">
                            <option value="">订单状态</option>
                            <option value="0">待支付</option>
                            <option value="1">已支付</option>
                            <option value="2">处理中</option>
                            <option value="3">已完成</option>
                            <option value="4">已取消</option>
                            <option value="5">已退款</option>
                        </select>
                    </div>
                    <div class="layui-col-md2">
                        <select name="payment_status">
                            <option value="">支付状态</option>
                            <option value="0">未支付</option>
                            <option value="1">已支付</option>
                            <option value="2">已退款</option>
                        </select>
                    </div>
                    <div class="layui-col-md2">
                        <input type="text" name="start_date" placeholder="开始日期" class="layui-input" id="startDate">
                    </div>
                    <div class="layui-col-md2">
                        <input type="text" name="end_date" placeholder="结束日期" class="layui-input" id="endDate">
                    </div>
                </div>
                <div class="layui-row layui-col-space10" style="margin-top: 10px;">
                    <div class="layui-col-md12">
                        <button type="submit" class="layui-btn" lay-submit lay-filter="search">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="exportOrders()">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </button>
                        <button type="button" class="layui-btn layui-btn-warm" onclick="batchRefund()">
                            <i class="layui-icon layui-icon-return"></i> 批量退款
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 订单列表 -->
        <div class="content-card">
            <div class="layui-row" style="margin-bottom: 15px;">
                <div class="layui-col-md6">
                    <h3 style="margin: 0;">订单列表</h3>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="refreshList()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            
            <table class="layui-table" lay-filter="orderTable">
                <thead>
                    <tr>
                        <th lay-data="{type:'checkbox', fixed: 'left'}"></th>
                        <th lay-data="{field:'id', width:80, sort: true}">ID</th>
                        <th lay-data="{field:'order_no', width:180}">订单号</th>
                        <th lay-data="{field:'username', width:120}">用户</th>
                        <th lay-data="{field:'api_name', width:200}">API名称</th>
                        <th lay-data="{field:'merchant_name', width:150}">商家</th>
                        <th lay-data="{field:'amount', width:100, align:'right'}">金额</th>
                        <th lay-data="{field:'quantity', width:80, align:'center'}">数量</th>
                        <th lay-data="{field:'status', width:80, align:'center'}">状态</th>
                        <th lay-data="{field:'payment_status', width:100, align:'center'}">支付状态</th>
                        <th lay-data="{field:'created_at', width:160, sort: true}">创建时间</th>
                        <th lay-data="{fixed: 'right', width:200, align:'center'}">操作</th>
                    </tr>
                </thead>
                <tbody id="orderTableBody">
                    <tr>
                        <td colspan="12" style="text-align: center; padding: 50px; color: #999;">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                            <br>加载中...
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div id="pagination"></div>
        </div>
    </div>

    <!-- 订单详情弹窗 -->
    <div id="orderDetailModal" style="display: none; padding: 20px;">
        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">基本信息</li>
                <li>支付信息</li>
                <li>操作日志</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <table class="layui-table order-detail-table">
                        <tbody id="orderBasicInfo">
                            <!-- 基本信息内容 -->
                        </tbody>
                    </table>
                </div>
                <div class="layui-tab-item">
                    <table class="layui-table order-detail-table">
                        <tbody id="orderPaymentInfo">
                            <!-- 支付信息内容 -->
                        </tbody>
                    </table>
                </div>
                <div class="layui-tab-item">
                    <div id="orderOperationLogs">
                        <!-- 操作日志内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 退款弹窗 -->
    <div id="refundModal" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="refundForm">
            <input type="hidden" name="order_id" id="refundOrderId">
            
            <div class="layui-form-item">
                <label class="layui-form-label">订单号</label>
                <div class="layui-input-block">
                    <input type="text" id="refundOrderNo" readonly class="layui-input layui-disabled">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">订单金额</label>
                <div class="layui-input-block">
                    <input type="text" id="refundOrderAmount" readonly class="layui-input layui-disabled">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">退款金额</label>
                <div class="layui-input-block">
                    <input type="number" name="refund_amount" step="0.01" min="0" required lay-verify="required" placeholder="请输入退款金额" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">退款原因</label>
                <div class="layui-input-block">
                    <textarea name="refund_reason" placeholder="请输入退款原因" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
                <button type="submit" class="layui-btn layui-btn-danger" lay-submit lay-filter="submitRefund">确认退款</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </form>
    </div>

    <script src="../../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'laypage', 'element', 'laydate'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var laypage = layui.laypage;
            var element = layui.element;
            var laydate = layui.laydate;
            
            var currentPage = 1;
            var pageSize = 20;
            var currentOrderId = null;
            
            // 初始化日期选择器
            laydate.render({
                elem: '#startDate',
                type: 'datetime'
            });
            
            laydate.render({
                elem: '#endDate',
                type: 'datetime'
            });
            
            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                loadOrderList();
                loadStats();
            });
            
            // 加载订单列表
            function loadOrderList(page = 1) {
                currentPage = page;
                
                // 模拟数据
                var mockData = {
                    data: [
                        {
                            id: 1,
                            order_no: 'ORD20240101001',
                            username: '张三',
                            user_email: '<EMAIL>',
                            api_name: '天气查询API',
                            merchant_name: '北京科技有限公司',
                            amount: '0.50',
                            quantity: 50,
                            unit_price: '0.01',
                            status: 3,
                            status_text: '已完成',
                            payment_status: 1,
                            payment_status_text: '已支付',
                            payment_method: 'balance',
                            created_at: '2024-01-01 10:00:00',
                            paid_at: '2024-01-01 10:01:00'
                        },
                        {
                            id: 2,
                            order_no: 'ORD20240101002',
                            username: '李四',
                            user_email: '<EMAIL>',
                            api_name: '身份证查询API',
                            merchant_name: '上海数据服务公司',
                            amount: '2.50',
                            quantity: 50,
                            unit_price: '0.05',
                            status: 1,
                            status_text: '已支付',
                            payment_status: 1,
                            payment_status_text: '已支付',
                            payment_method: 'alipay',
                            created_at: '2024-01-01 14:30:00',
                            paid_at: '2024-01-01 14:32:00'
                        },
                        {
                            id: 3,
                            order_no: 'ORD20240101003',
                            username: '王五',
                            user_email: '<EMAIL>',
                            api_name: '手机归属地API',
                            merchant_name: '深圳创新科技',
                            amount: '1.00',
                            quantity: 50,
                            unit_price: '0.02',
                            status: 0,
                            status_text: '待支付',
                            payment_status: 0,
                            payment_status_text: '未支付',
                            payment_method: '',
                            created_at: '2024-01-01 16:15:00',
                            paid_at: null
                        }
                    ],
                    total: 3,
                    page: page,
                    per_page: pageSize,
                    total_pages: 1
                };
                
                renderOrderTable(mockData.data);
                renderPagination(mockData);
            }
            
            // 渲染订单表格
            function renderOrderTable(data) {
                var html = '';
                
                if (data.length === 0) {
                    html = '<tr><td colspan="12" style="text-align: center; padding: 50px; color: #999;">暂无数据</td></tr>';
                } else {
                    data.forEach(function(item) {
                        var statusClass = 'status-' + ['pending', 'paid', 'processing', 'completed', 'cancelled', 'refunded'][item.status];
                        var statusBadge = '<span class="status-badge ' + statusClass + '">' + item.status_text + '</span>';
                        
                        var paymentClass = 'payment-' + ['unpaid', 'paid', 'refunded'][item.payment_status];
                        var paymentBadge = '<span class="payment-badge ' + paymentClass + '">' + item.payment_status_text + '</span>';
                        
                        html += '<tr>';
                        html += '<td><input type="checkbox" name="ids" value="' + item.id + '" lay-skin="primary"></td>';
                        html += '<td>' + item.id + '</td>';
                        html += '<td>' + item.order_no + '</td>';
                        html += '<td>' + item.username + '</td>';
                        html += '<td>' + item.api_name + '</td>';
                        html += '<td>' + (item.merchant_name || '-') + '</td>';
                        html += '<td>¥' + item.amount + '</td>';
                        html += '<td>' + item.quantity + '</td>';
                        html += '<td>' + statusBadge + '</td>';
                        html += '<td>' + paymentBadge + '</td>';
                        html += '<td>' + item.created_at + '</td>';
                        html += '<td>';
                        html += '<button class="layui-btn layui-btn-xs" onclick="showOrderDetail(' + item.id + ')">详情</button>';
                        if (item.payment_status === 1 && item.status !== 5) {
                            html += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="refundOrder(' + item.id + ')">退款</button>';
                        }
                        if (item.status === 0) {
                            html += '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="cancelOrder(' + item.id + ')">取消</button>';
                        }
                        html += '</td>';
                        html += '</tr>';
                    });
                }
                
                document.getElementById('orderTableBody').innerHTML = html;
                form.render('checkbox');
            }
            
            // 渲染分页
            function renderPagination(data) {
                laypage.render({
                    elem: 'pagination',
                    count: data.total,
                    curr: data.page,
                    limit: data.per_page,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function(obj, first) {
                        if (!first) {
                            loadOrderList(obj.curr);
                        }
                    }
                });
            }
            
            // 加载统计数据
            function loadStats() {
                // 模拟统计数据
                document.getElementById('totalOrders').textContent = '1,256';
                document.getElementById('paidOrders').textContent = '1,089';
                document.getElementById('totalAmount').textContent = '¥125,680';
                document.getElementById('refundAmount').textContent = '¥2,350';
            }
            
            // 搜索表单提交
            form.on('submit(search)', function(data) {
                loadOrderList(1);
                return false;
            });
            
            // 显示订单详情
            window.showOrderDetail = function(id) {
                currentOrderId = id;
                
                // 模拟订单详情数据
                var orderDetail = {
                    id: id,
                    order_no: 'ORD20240101001',
                    username: '张三',
                    user_email: '<EMAIL>',
                    user_phone: '13800138000',
                    api_name: '天气查询API',
                    api_path: '/api/weather',
                    merchant_name: '北京科技有限公司',
                    amount: '0.50',
                    quantity: 50,
                    unit_price: '0.01',
                    status_text: '已完成',
                    payment_status_text: '已支付',
                    payment_method: 'balance',
                    created_at: '2024-01-01 10:00:00',
                    paid_at: '2024-01-01 10:01:00',
                    expired_at: '2024-01-01 10:30:00'
                };
                
                // 基本信息
                var basicInfoHtml = '';
                basicInfoHtml += '<tr><th>订单号</th><td>' + orderDetail.order_no + '</td></tr>';
                basicInfoHtml += '<tr><th>用户</th><td>' + orderDetail.username + ' (' + orderDetail.user_email + ')</td></tr>';
                basicInfoHtml += '<tr><th>API名称</th><td>' + orderDetail.api_name + '</td></tr>';
                basicInfoHtml += '<tr><th>API路径</th><td>' + orderDetail.api_path + '</td></tr>';
                basicInfoHtml += '<tr><th>商家</th><td>' + orderDetail.merchant_name + '</td></tr>';
                basicInfoHtml += '<tr><th>订单金额</th><td>¥' + orderDetail.amount + '</td></tr>';
                basicInfoHtml += '<tr><th>购买数量</th><td>' + orderDetail.quantity + '</td></tr>';
                basicInfoHtml += '<tr><th>单价</th><td>¥' + orderDetail.unit_price + '</td></tr>';
                basicInfoHtml += '<tr><th>订单状态</th><td>' + orderDetail.status_text + '</td></tr>';
                basicInfoHtml += '<tr><th>创建时间</th><td>' + orderDetail.created_at + '</td></tr>';
                basicInfoHtml += '<tr><th>过期时间</th><td>' + orderDetail.expired_at + '</td></tr>';
                
                document.getElementById('orderBasicInfo').innerHTML = basicInfoHtml;
                
                // 支付信息
                var paymentInfoHtml = '';
                paymentInfoHtml += '<tr><th>支付状态</th><td>' + orderDetail.payment_status_text + '</td></tr>';
                paymentInfoHtml += '<tr><th>支付方式</th><td>' + (orderDetail.payment_method || '-') + '</td></tr>';
                paymentInfoHtml += '<tr><th>支付时间</th><td>' + (orderDetail.paid_at || '-') + '</td></tr>';
                
                document.getElementById('orderPaymentInfo').innerHTML = paymentInfoHtml;
                
                layer.open({
                    type: 1,
                    title: '订单详情 - ' + orderDetail.order_no,
                    content: document.getElementById('orderDetailModal'),
                    area: ['800px', '600px'],
                    success: function() {
                        element.render();
                    }
                });
            };
            
            // 退款订单
            window.refundOrder = function(id) {
                // 模拟获取订单信息
                var orderInfo = {
                    id: id,
                    order_no: 'ORD20240101001',
                    amount: '0.50'
                };
                
                document.getElementById('refundOrderId').value = orderInfo.id;
                document.getElementById('refundOrderNo').value = orderInfo.order_no;
                document.getElementById('refundOrderAmount').value = '¥' + orderInfo.amount;
                document.querySelector('[name="refund_amount"]').value = orderInfo.amount;
                
                layer.open({
                    type: 1,
                    title: '订单退款',
                    content: document.getElementById('refundModal'),
                    area: ['500px', '400px'],
                    success: function() {
                        form.render();
                    }
                });
            };
            
            // 退款表单提交
            form.on('submit(submitRefund)', function(data) {
                layer.confirm('确定要退款吗？退款后不可撤销！', {
                    icon: 3,
                    title: '警告'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('退款成功', {icon: 1}, function() {
                            layer.closeAll();
                            loadOrderList(currentPage);
                        });
                    }, 1000);
                });
                
                return false;
            });
            
            // 取消订单
            window.cancelOrder = function(id) {
                layer.confirm('确定要取消这个订单吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('订单取消成功', {icon: 1});
                        loadOrderList(currentPage);
                    }, 1000);
                });
            };
            
            // 批量退款
            window.batchRefund = function() {
                var checkboxes = document.querySelectorAll('input[name="ids"]:checked');
                if (checkboxes.length === 0) {
                    layer.msg('请选择要退款的订单', {icon: 2});
                    return;
                }
                
                var ids = [];
                checkboxes.forEach(function(checkbox) {
                    ids.push(checkbox.value);
                });
                
                layer.confirm('确定要批量退款选中的订单吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(2);
                    
                    setTimeout(function() {
                        layer.close(loadIndex);
                        layer.msg('批量退款成功', {icon: 1});
                        loadOrderList(currentPage);
                    }, 1000);
                });
            };
            
            // 导出订单
            window.exportOrders = function() {
                layer.msg('正在导出订单数据...', {icon: 16, time: 2000});
                // 这里可以实现实际的导出功能
            };
            
            // 刷新列表
            window.refreshList = function() {
                loadOrderList(currentPage);
                loadStats();
                layer.msg('刷新成功', {icon: 1, time: 1000});
            };
            
            // 退出登录
            window.logout = function() {
                layer.confirm('确定要退出登录吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    layer.close(index);
                    layer.msg('退出成功', {icon: 1}, function() {
                        location.href = 'index.html';
                    });
                });
            };
        });
    </script>
</body>
</html>
