<?php
/**
 * 安全管理模块
 * API管理系统 - 安全防护与监控
 */

require_once __DIR__ . '/../core/Model.php';

class Security extends Model {
    
    // 安全事件类型
    const EVENT_LOGIN_SUCCESS = 'login_success';
    const EVENT_LOGIN_FAILED = 'login_failed';
    const EVENT_LOGIN_BLOCKED = 'login_blocked';
    const EVENT_API_ABUSE = 'api_abuse';
    const EVENT_SUSPICIOUS_ACTIVITY = 'suspicious_activity';
    const EVENT_PERMISSION_DENIED = 'permission_denied';
    const EVENT_SQL_INJECTION = 'sql_injection';
    const EVENT_XSS_ATTEMPT = 'xss_attempt';
    const EVENT_CSRF_ATTEMPT = 'csrf_attempt';
    
    /**
     * 记录安全事件
     */
    public function logSecurityEvent($type, $description, $userId = null, $ip = null, $userAgent = null, $data = null) {
        $ip = $ip ?: $this->getClientIP();
        $userAgent = $userAgent ?: $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $logData = [
            'type' => $type,
            'description' => $description,
            'user_id' => $userId,
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'request_data' => $data ? json_encode($data) : null,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $sql = "INSERT INTO security_logs (type, description, user_id, ip_address, user_agent, request_data, created_at) 
                VALUES (:type, :description, :user_id, :ip_address, :user_agent, :request_data, :created_at)";
        
        return $this->db->execute($sql, $logData);
    }
    
    /**
     * 检查IP是否被封禁
     */
    public function isIPBlocked($ip = null) {
        $ip = $ip ?: $this->getClientIP();
        
        $sql = "SELECT * FROM ip_blacklist WHERE ip_address = :ip AND status = 1 AND (expired_at IS NULL OR expired_at > NOW())";
        $result = $this->db->fetchOne($sql, ['ip' => $ip]);
        
        return !empty($result);
    }
    
    /**
     * 封禁IP地址
     */
    public function blockIP($ip, $reason, $duration = null, $adminId = null) {
        if ($this->isIPBlocked($ip)) {
            return false; // 已经被封禁
        }
        
        $expiredAt = null;
        if ($duration) {
            $expiredAt = date('Y-m-d H:i:s', time() + $duration);
        }
        
        $data = [
            'ip_address' => $ip,
            'reason' => $reason,
            'admin_id' => $adminId,
            'expired_at' => $expiredAt,
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $sql = "INSERT INTO ip_blacklist (ip_address, reason, admin_id, expired_at, status, created_at) 
                VALUES (:ip_address, :reason, :admin_id, :expired_at, :status, :created_at)";
        
        $result = $this->db->execute($sql, $data);
        
        if ($result) {
            $this->logSecurityEvent(self::EVENT_LOGIN_BLOCKED, "IP地址 {$ip} 被封禁: {$reason}", null, $ip);
        }
        
        return $result;
    }
    
    /**
     * 检查登录失败次数
     */
    public function checkLoginAttempts($identifier, $ip = null) {
        $ip = $ip ?: $this->getClientIP();
        $timeLimit = date('Y-m-d H:i:s', time() - 3600); // 1小时内
        
        // 检查IP登录失败次数
        $sql = "SELECT COUNT(*) as count FROM security_logs 
                WHERE type = :type AND ip_address = :ip AND created_at > :time_limit";
        $ipAttempts = $this->db->fetchOne($sql, [
            'type' => self::EVENT_LOGIN_FAILED,
            'ip' => $ip,
            'time_limit' => $timeLimit
        ]);
        
        // 检查用户登录失败次数
        $sql = "SELECT COUNT(*) as count FROM security_logs 
                WHERE type = :type AND description LIKE :identifier AND created_at > :time_limit";
        $userAttempts = $this->db->fetchOne($sql, [
            'type' => self::EVENT_LOGIN_FAILED,
            'identifier' => "%{$identifier}%",
            'time_limit' => $timeLimit
        ]);
        
        return [
            'ip_attempts' => $ipAttempts['count'],
            'user_attempts' => $userAttempts['count']
        ];
    }
    
    /**
     * 检查是否需要验证码
     */
    public function needCaptcha($identifier, $ip = null) {
        $attempts = $this->checkLoginAttempts($identifier, $ip);
        return $attempts['ip_attempts'] >= 3 || $attempts['user_attempts'] >= 3;
    }
    
    /**
     * 检查是否应该临时封禁
     */
    public function shouldTempBlock($identifier, $ip = null) {
        $attempts = $this->checkLoginAttempts($identifier, $ip);
        return $attempts['ip_attempts'] >= 10 || $attempts['user_attempts'] >= 5;
    }
    
    /**
     * 检测SQL注入攻击
     */
    public function detectSQLInjection($input) {
        $patterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bor\b.*=.*)/i',
            '/(\band\b.*=.*)/i',
            '/(\'.*or.*\'.*=.*\')/i',
            '/(\".*or.*\".*=.*\")/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检测XSS攻击
     */
    public function detectXSS($input) {
        $patterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<img[^>]*src[^>]*>/i',
            '/<object[^>]*>.*?<\/object>/is',
            '/<embed[^>]*>.*?<\/embed>/is'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 过滤危险输入
     */
    public function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        // 移除危险字符
        $input = strip_tags($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        return $input;
    }
    
    /**
     * 验证CSRF令牌
     */
    public function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * 生成CSRF令牌
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        
        return $token;
    }
    
    /**
     * 检查API调用频率
     */
    public function checkAPIRateLimit($userId, $apiId, $limit = 100, $window = 3600) {
        $timeLimit = date('Y-m-d H:i:s', time() - $window);
        
        $sql = "SELECT COUNT(*) as count FROM api_call_logs 
                WHERE user_id = :user_id AND api_id = :api_id AND created_at > :time_limit";
        
        $result = $this->db->fetchOne($sql, [
            'user_id' => $userId,
            'api_id' => $apiId,
            'time_limit' => $timeLimit
        ]);
        
        return $result['count'] < $limit;
    }
    
    /**
     * 获取客户端真实IP
     */
    public function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 检查密码强度
     */
    public function checkPasswordStrength($password) {
        $score = 0;
        $feedback = [];
        
        // 长度检查
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = '密码长度至少8位';
        }
        
        if (strlen($password) >= 12) {
            $score += 1;
        }
        
        // 复杂度检查
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '包含小写字母';
        }
        
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '包含大写字母';
        }
        
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '包含数字';
        }
        
        if (preg_match('/[^a-zA-Z0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '包含特殊字符';
        }
        
        // 评分
        if ($score >= 5) {
            $strength = 'strong';
        } elseif ($score >= 3) {
            $strength = 'medium';
        } else {
            $strength = 'weak';
        }
        
        return [
            'score' => $score,
            'strength' => $strength,
            'feedback' => $feedback
        ];
    }
    
    /**
     * 获取安全日志
     */
    public function getSecurityLogs($page = 1, $perPage = 50, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['type'])) {
            $whereClause[] = "type = :type";
            $params['type'] = $conditions['type'];
        }
        
        if (!empty($conditions['user_id'])) {
            $whereClause[] = "user_id = :user_id";
            $params['user_id'] = $conditions['user_id'];
        }
        
        if (!empty($conditions['ip_address'])) {
            $whereClause[] = "ip_address = :ip_address";
            $params['ip_address'] = $conditions['ip_address'];
        }
        
        if (!empty($conditions['start_date'])) {
            $whereClause[] = "created_at >= :start_date";
            $params['start_date'] = $conditions['start_date'];
        }
        
        if (!empty($conditions['end_date'])) {
            $whereClause[] = "created_at <= :end_date";
            $params['end_date'] = $conditions['end_date'];
        }
        
        $whereSQL = empty($whereClause) ? '' : 'WHERE ' . implode(' AND ', $whereClause);
        
        // 查询总数
        $countSQL = "SELECT COUNT(*) as total FROM security_logs {$whereSQL}";
        $totalResult = $this->db->fetchOne($countSQL, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSQL = "SELECT sl.*, u.username 
                    FROM security_logs sl 
                    LEFT JOIN users u ON sl.user_id = u.id 
                    {$whereSQL}
                    ORDER BY sl.id DESC 
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSQL, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取IP黑名单
     */
    public function getIPBlacklist($page = 1, $perPage = 50) {
        $offset = ($page - 1) * $perPage;
        
        // 查询总数
        $countSQL = "SELECT COUNT(*) as total FROM ip_blacklist";
        $totalResult = $this->db->fetchOne($countSQL);
        $total = $totalResult['total'];
        
        // 查询数据
        $dataSQL = "SELECT ib.*, a.username as admin_name 
                    FROM ip_blacklist ib 
                    LEFT JOIN admins a ON ib.admin_id = a.id 
                    ORDER BY ib.id DESC 
                    LIMIT {$offset}, {$perPage}";
        
        $data = $this->db->fetchAll($dataSQL);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 解封IP地址
     */
    public function unblockIP($id, $adminId = null) {
        $sql = "UPDATE ip_blacklist SET status = 0, updated_at = NOW() WHERE id = :id";
        $result = $this->db->execute($sql, ['id' => $id]);
        
        if ($result) {
            // 获取IP信息
            $ipInfo = $this->db->fetchOne("SELECT ip_address FROM ip_blacklist WHERE id = :id", ['id' => $id]);
            if ($ipInfo) {
                $this->logSecurityEvent('ip_unblocked', "IP地址 {$ipInfo['ip_address']} 被解封", $adminId);
            }
        }
        
        return $result;
    }
}