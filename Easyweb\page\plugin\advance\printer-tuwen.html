<!DOCTYPE html>
<html lang="en" class="bg-white">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>单据打印</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        .paper-info-group {
            padding: 15px 15px 15px 25px;
        }

        .paper-info-group h3 {
            font-weight: bold;
            color: #000000;
            padding-bottom: 8px;
        }

        .paper-info-group p {
            color: #666;
            padding-bottom: 12px;
            font-size: 16px;
        }

        .paper-info-group .paper-info-group-imgs {
            padding-top: 5px;
        }

        .paper-info-group .paper-info-group-imgs img {
            margin-right: 15px;
            margin-bottom: 15px;
        }

        .paper-info-group .paper-info-group-imgs img:last-child {
            margin-right: 0;
        }
    </style>
</head>
<body class="bg-white">

<!-- 正文开始 -->
<div class="paper-info-group">
    <h3>本周工作总结</h3>
    <p>完成了一个页面</p>
    <h3>下周工作计划</h3>
    <p>完成下一个页面</p>
    <h3>备注</h3>
    <p>无法重现</p>
    <div class="paper-info-group-imgs" id="paper-imgs">
        <img src="https://pic.qqtn.com/up/2018-9/15367146917869444.jpg" width="130px" height="130px">
        <img src="https://pic.qqtn.com/up/2018-9/15367146917869444.jpg" width="130px" height="130px">
        <img src="https://pic.qqtn.com/up/2018-9/15367146917869444.jpg" width="130px" height="130px">
    </div>
    <div class="text-right hide-print">
        <button class="layui-btn layui-btn-primary" ew-event="closeDialog">关闭</button>
        <button class="layui-btn" id="btnPrint">打印</button>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['printer', 'admin'], function () {
        var $ = layui.jquery;
        var printer = layui.printer;
        var admin = layui.admin;

        // 打印当前页面
        $('#btnPrint').click(function () {
            printer.print();
        });

        admin.iframeAuto();

    });
</script>
</body>
</html>