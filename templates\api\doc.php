<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API文档 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/site.css"/>
    <style>
        .api-doc-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .api-doc-card {
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .api-doc-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-doc-title {
            font-size: 18px;
            font-weight: bold;
        }
        .api-doc-body {
            padding: 20px;
        }
        .api-doc-section {
            margin-bottom: 30px;
        }
        .api-doc-section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-doc-table {
            margin-bottom: 0;
        }
        .api-doc-param-required {
            color: #f56c6c;
            margin-left: 5px;
        }
        .api-doc-method {
            display: inline-block;
            padding: 2px 5px;
            border-radius: 3px;
            color: #fff;
            font-size: 12px;
            margin-right: 10px;
        }
        .api-doc-method-get {
            background-color: #409eff;
        }
        .api-doc-method-post {
            background-color: #67c23a;
        }
        .api-doc-method-put {
            background-color: #e6a23c;
        }
        .api-doc-method-delete {
            background-color: #f56c6c;
        }
        .api-doc-url {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f8f8f8;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .api-doc-code {
            background-color: #f8f8f8;
            border-radius: 4px;
            padding: 15px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 300px;
            overflow-y: auto;
        }
        .api-doc-nav {
            position: sticky;
            top: 20px;
        }
        .api-doc-nav-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .api-doc-nav-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
        }
        .api-doc-nav-item:hover {
            color: #409eff;
        }
        .api-doc-nav-item.active {
            color: #409eff;
            font-weight: bold;
        }
        .api-doc-search {
            margin-bottom: 15px;
        }
        .api-doc-category-title {
            font-size: 18px;
            font-weight: bold;
            margin: 30px 0 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        .api-doc-api-title {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px;
        }
        .api-doc-api-desc {
            margin-bottom: 15px;
            color: #666;
        }
        .api-doc-test-btn {
            float: right;
        }
    </style>
</head>
<body>
<!-- 引入网站头部 -->
<div id="header"></div>

<div class="layui-container api-doc-container">
    <div class="layui-row layui-col-space20">
        <div class="layui-col-md3">
            <div class="api-doc-card">
                <div class="api-doc-header">
                    <div class="api-doc-title">API导航</div>
                </div>
                <div class="api-doc-body">
                    <div class="api-doc-search">
                        <input type="text" id="apiSearch" placeholder="搜索API" class="layui-input">
                    </div>
                    <div class="api-doc-nav" id="apiNav">
                        <!-- API导航将通过JS动态加载 -->
                        <div class="api-doc-nav-item">加载中...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-col-md9">
            <div class="api-doc-card">
                <div class="api-doc-header">
                    <div class="api-doc-title">API文档</div>
                </div>
                <div class="api-doc-body" id="apiDoc">
                    <!-- API文档将通过JS动态加载 -->
                    <div class="layui-text">
                        <h3>欢迎使用API文档</h3>
                        <p>请在左侧选择要查看的API接口。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入网站底部 -->
<div id="footer"></div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['layer', 'jquery'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    
    // 加载头部和底部
    $('#header').load('/common/header.html');
    $('#footer').load('/common/footer.html');
    
    // 加载API文档
    loadApiDoc();
    
    // 搜索API
    $('#apiSearch').on('input', function() {
        var keyword = $(this).val().toLowerCase();
        $('.api-doc-nav-item').each(function() {
            var name = $(this).data('name').toLowerCase();
            var url = $(this).data('url').toLowerCase();
            if (name.indexOf(keyword) !== -1 || url.indexOf(keyword) !== -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    
    // 加载API文档
    function loadApiDoc() {
        $.get('/api/doc/list', function(res) {
            if (res.code === 0) {
                renderApiNav(res.data);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 渲染API导航
    function renderApiNav(data) {
        var html = '';
        
        for (var i = 0; i < data.categories.length; i++) {
            var category = data.categories[i];
            html += '<div class="api-doc-nav-title">' + category.name + '</div>';
            
            for (var j = 0; j < category.apis.length; j++) {
                var api = category.apis[j];
                html += '<div class="api-doc-nav-item" data-id="' + api.id + '" data-name="' + api.name + '" data-url="' + api.url + '">' + api.name + '</div>';
            }
        }
        
        $('#apiNav').html(html);
        
        // 点击API导航项
        $('.api-doc-nav-item').click(function() {
            $('.api-doc-nav-item').removeClass('active');
            $(this).addClass('active');
            
            var apiId = $(this).data('id');
            loadApiDetail(apiId);
        });
    }
    
    // 加载API详情
    function loadApiDetail(apiId) {
        $.get('/api/doc/detail', {id: apiId}, function(res) {
            if (res.code === 0) {
                renderApiDetail(res.data);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 渲染API详情
    function renderApiDetail(api) {
        var methodClass = 'api-doc-method-' + api.method.toLowerCase();
        
        var html = '<h2 class="api-doc-api-title">' + api.name + '</h2>';
        html += '<p class="api-doc-api-desc">' + api.description + '</p>';
        
        html += '<div class="api-doc-section">';
        html += '<div class="api-doc-section-title">基本信息</div>';
        html += '<table class="layui-table api-doc-table">';
        html += '<tr><td width="100">接口URL</td><td><span class="api-doc-method ' + methodClass + '">' + api.method + '</span><span class="api-doc-url">' + api.url + '</span></td></tr>';
        html += '<tr><td>认证方式</td><td>API密钥（api_key参数）</td></tr>';
        html += '<tr><td>返回格式</td><td>JSON</td></tr>';
        html += '</table>';
        html += '</div>';
        
        html += '<div class="api-doc-section">';
        html += '<div class="api-doc-section-title">请求参数</div>';
        html += '<table class="layui-table api-doc-table">';
        html += '<thead><tr><th width="150">参数名</th><th width="100">类型</th><th width="80">必填</th><th>说明</th><th width="150">默认值</th></tr></thead>';
        html += '<tbody>';
        
        // 添加API密钥参数
        html += '<tr><td>api_key</td><td>string</td><td><span class="api-doc-param-required">是</span></td><td>API密钥</td><td>-</td></tr>';
        
        // 添加其他参数
        for (var i = 0; i < api.params.length; i++) {
            var param = api.params[i];
            html += '<tr>';
            html += '<td>' + param.name + '</td>';
            html += '<td>' + param.type + '</td>';
            html += '<td>' + (param.required ? '<span class="api-doc-param-required">是</span>' : '否') + '</td>';
            html += '<td>' + param.description + '</td>';
            html += '<td>' + (param.default_value ? param.default_value : '-') + '</td>';
            html += '</tr>';
        }
        
        html += '</tbody></table>';
        html += '</div>';
        
        html += '<div class="api-doc-section">';
        html += '<div class="api-doc-section-title">返回参数</div>';
        html += '<table class="layui-table api-doc-table">';
        html += '<thead><tr><th width="150">参数名</th><th width="100">类型</th><th>说明</th></tr></thead>';
        html += '<tbody>';
        html += '<tr><td>code</td><td>int</td><td>状态码，0表示成功，非0表示失败</td></tr>';
        html += '<tr><td>msg</td><td>string</td><td>状态信息</td></tr>';
        html += '<tr><td>data</td><td>object/array</td><td>返回数据</td></tr>';
        html += '</tbody></table>';
        html += '</div>';
        
        if (api.example_params) {
            html += '<div class="api-doc-section">';
            html += '<div class="api-doc-section-title">请求示例</div>';
            html += '<pre class="api-doc-code">' + formatJson(api.example_params) + '</pre>';
            html += '</div>';
        }
        
        if (api.example_response) {
            html += '<div class="api-doc-section">';
            html += '<div class="api-doc-section-title">返回示例</div>';
            html += '<pre class="api-doc-code">' + formatJson(api.example_response) + '</pre>';
            html += '</div>';
        }
        
        html += '<div class="api-doc-section">';
        html += '<button type="button" class="layui-btn" onclick="window.location.href=\'/api/test?api_id=' + api.id + '\'">在线调试</button>';
        html += '</div>';
        
        $('#apiDoc').html(html);
    }
    
    // 格式化JSON
    function formatJson(json) {
        if (typeof json === 'string') {
            try {
                json = JSON.parse(json);
            } catch (e) {
                return json;
            }
        }
        
        return JSON.stringify(json, null, 2);
    }
});
</script>
</body>
</html>