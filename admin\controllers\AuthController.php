<?php
/**
 * 管理员认证控制器
 */
session_start();

require_once '../../classes/Database.php';
require_once '../../core/AdminAuth.php';

class AuthController {
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new AdminAuth();
    }
    
    /**
     * 管理员登录
     */
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['code' => 405, 'msg' => '请求方法不允许']);
        }
        
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $captcha = $_POST['captcha'] ?? '';
        $remember = isset($_POST['remember']);
        
        // 验证输入
        if (empty($username) || empty($password) || empty($captcha)) {
            $this->jsonResponse(['code' => 400, 'msg' => '请填写完整信息']);
        }
        
        // 验证验证码
        if (!isset($_SESSION['captcha']) || strtolower($captcha) !== strtolower($_SESSION['captcha'])) {
            $this->jsonResponse(['code' => 400, 'msg' => '验证码错误']);
        }
        
        // 查找管理员
        $admin = $this->db->query(
            "SELECT * FROM users WHERE username = ? AND role = 'admin' AND status = 1",
            [$username],
            true
        );
        
        if (!$admin) {
            $this->jsonResponse(['code' => 400, 'msg' => '用户名或密码错误']);
        }
        
        // 验证密码
        if (!password_verify($password, $admin['password'])) {
            $this->jsonResponse(['code' => 400, 'msg' => '用户名或密码错误']);
        }
        
        // 生成JWT令牌
        $payload = [
            'user_id' => $admin['id'],
            'username' => $admin['username'],
            'role' => 'admin',
            'exp' => time() + ($remember ? 30 * 24 * 3600 : 24 * 3600) // 记住密码30天，否则1天
        ];
        
        $token = $this->auth->generateToken($payload);
        
        // 设置Cookie
        $expire = $remember ? time() + 30 * 24 * 3600 : 0;
        setcookie('admin_token', $token, $expire, '/admin/', '', false, true);
        
        // 更新最后登录时间
        $this->db->execute(
            "UPDATE users SET last_login_time = NOW(), last_login_ip = ? WHERE id = ?",
            [$_SERVER['REMOTE_ADDR'], $admin['id']]
        );
        
        // 记录登录日志
        $this->db->execute(
            "INSERT INTO admin_logs (admin_id, action, ip, user_agent, created_at) VALUES (?, ?, ?, ?, NOW())",
            [$admin['id'], 'login', $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'] ?? '']
        );
        
        $this->jsonResponse(['code' => 200, 'msg' => '登录成功', 'data' => ['token' => $token]]);
    }
    
    /**
     * 管理员退出登录
     */
    public function logout() {
        // 清除Cookie
        setcookie('admin_token', '', time() - 3600, '/admin/', '', false, true);
        
        // 清除Session
        session_destroy();
        
        $this->jsonResponse(['code' => 200, 'msg' => '退出成功']);
    }
    
    /**
     * 生成验证码
     */
    public function captcha() {
        // 生成随机验证码
        $code = '';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        for ($i = 0; $i < 4; $i++) {
            $code .= $chars[rand(0, strlen($chars) - 1)];
        }
        
        // 保存到Session
        $_SESSION['captcha'] = $code;
        
        // 创建图像
        $width = 100;
        $height = 40;
        $image = imagecreate($width, $height);
        
        // 设置颜色
        $bg_color = imagecolorallocate($image, 255, 255, 255);
        $text_color = imagecolorallocate($image, 0, 0, 0);
        $line_color = imagecolorallocate($image, 200, 200, 200);
        
        // 添加干扰线
        for ($i = 0; $i < 5; $i++) {
            imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $line_color);
        }
        
        // 添加验证码文字
        $font_size = 16;
        $x = ($width - strlen($code) * $font_size * 0.6) / 2;
        $y = ($height + $font_size) / 2;
        
        imagestring($image, 5, $x, $y - 10, $code, $text_color);
        
        // 输出图像
        header('Content-Type: image/png');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        imagepng($image);
        imagedestroy($image);
        exit;
    }
    
    /**
     * 获取管理员信息
     */
    public function getInfo() {
        $token = $_COOKIE['admin_token'] ?? '';
        
        if (empty($token)) {
            $this->jsonResponse(['code' => 401, 'msg' => '未登录']);
        }
        
        $payload = $this->auth->verifyToken($token);
        
        if (!$payload) {
            $this->jsonResponse(['code' => 401, 'msg' => '登录已过期']);
        }
        
        $admin = $this->db->query(
            "SELECT id, username, email, avatar, created_at FROM users WHERE id = ? AND role = 'admin'",
            [$payload['user_id']],
            true
        );
        
        if (!$admin) {
            $this->jsonResponse(['code' => 404, 'msg' => '用户不存在']);
        }
        
        $this->jsonResponse(['code' => 200, 'data' => $admin]);
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 路由处理
$action = $_GET['action'] ?? '';
$controller = new AuthController();

switch ($action) {
    case 'login':
        $controller->login();
        break;
    case 'logout':
        $controller->logout();
        break;
    case 'captcha':
        $controller->captcha();
        break;
    case 'getInfo':
        $controller->getInfo();
        break;
    default:
        http_response_code(404);
        echo json_encode(['code' => 404, 'msg' => '接口不存在']);
}
?>
