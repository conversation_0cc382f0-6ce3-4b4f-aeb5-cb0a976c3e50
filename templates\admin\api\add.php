<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>添加API接口 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .layui-card-body {
            padding: 15px;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-form-item .layui-form-label {
            width: 120px;
        }
        .layui-form-item .layui-input-block {
            margin-left: 150px;
        }
        .layui-form-item .layui-input-inline {
            width: 300px;
        }
        .layui-form-mid {
            padding: 0 !important;
        }
        .layui-form-mid .layui-word-aux {
            color: #999 !important;
        }
        .price-unit-select {
            width: 100px !important;
        }
        .api-method-select {
            width: 120px !important;
        }
        .api-format-select {
            width: 120px !important;
        }
        .api-cache-select {
            width: 120px !important;
        }
        .api-timeout-input {
            width: 120px !important;
        }
        .api-retry-input {
            width: 120px !important;
        }
        .api-qps-input {
            width: 120px !important;
        }
        .api-sort-input {
            width: 120px !important;
        }
        .api-version-input {
            width: 120px !important;
        }
        .api-example-textarea {
            min-height: 150px;
        }
        .api-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #1E9FFF;
        }
        .api-section-subtitle {
            font-size: 16px;
            font-weight: bold;
            margin: 15px 0;
            color: #333;
        }
        .layui-form-radio {
            margin: 0;
            padding-right: 18px;
        }
        .layui-form-checkbox {
            margin-top: 0;
        }
        .layui-form-checkbox + .layui-form-checkbox {
            margin-left: 10px;
        }
        .layui-form-select dl {
            max-height: 200px;
        }
        .layui-form-select dl dd.layui-this {
            background-color: #1E9FFF;
        }
        .layui-form-onswitch {
            background-color: #1E9FFF;
            border-color: #1E9FFF;
        }
        .layui-form-checked[lay-skin=primary] i {
            border-color: #1E9FFF;
            background-color: #1E9FFF;
        }
        .layui-form-checked span, .layui-form-checked:hover span {
            background-color: #1E9FFF;
        }
        .layui-form-checked i, .layui-form-checked:hover i {
            color: #1E9FFF;
        }
        .layui-form-radio > i:hover, .layui-form-radioed > i {
            color: #1E9FFF;
        }
        .layui-btn-primary:hover {
            border-color: #1E9FFF;
            color: #1E9FFF;
        }
        .layui-tab-brief > .layui-tab-title .layui-this {
            color: #1E9FFF;
        }
        .layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom-color: #1E9FFF;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">添加API接口</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="apiForm">
                <!-- 基本信息 -->
                <div class="api-section">
                    <div class="api-section-title">基本信息</div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">接口名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" placeholder="请输入接口名称" lay-verify="required" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">接口标识</label>
                        <div class="layui-input-block">
                            <input type="text" name="identifier" placeholder="请输入接口标识，英文字母、数字和下划线组成" lay-verify="required" autocomplete="off" class="layui-input">
                            <div class="layui-form-mid layui-word-aux">接口标识用于生成API路径，一旦创建不可修改</div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属分类</label>
                        <div class="layui-input-block">
                            <select name="category_id" lay-verify="required">
                                <option value="">请选择分类</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属商家</label>
                        <div class="layui-input-block">
                            <select name="merchant_id">
                                <option value="0">平台自营</option>
                                <?php foreach ($merchants as $merchant): ?>
                                <option value="<?php echo $merchant['id']; ?>"><?php echo $merchant['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">接口描述</label>
                        <div class="layui-input-block">
                            <textarea name="description" placeholder="请输入接口描述" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- 接口配置 -->
                <div class="api-section">
                    <div class="api-section-title">接口配置</div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">请求方式</label>
                        <div class="layui-input-inline api-method-select">
                            <select name="method" lay-verify="required">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                                <option value="PATCH">PATCH</option>
                                <option value="OPTIONS">OPTIONS</option>
                                <option value="HEAD">HEAD</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">接口类型</label>
                        <div class="layui-input-block">
                            <input type="radio" name="api_type" value="local" title="本地接口" checked>
                            <input type="radio" name="api_type" value="remote" title="远程接口">
                            <div class="layui-form-mid layui-word-aux">本地接口使用系统内部处理，远程接口转发到其他服务</div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item" id="remoteUrlItem" style="display: none;">
                        <label class="layui-form-label">远程地址</label>
                        <div class="layui-input-block">
                            <input type="text" name="remote_url" placeholder="请输入远程接口地址" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item" id="localHandlerItem">
                        <label class="layui-form-label">处理器</label>
                        <div class="layui-input-block">
                            <input type="text" name="handler" placeholder="请输入处理器类名，例如：UserApi" autocomplete="off" class="layui-input">
                            <div class="layui-form-mid layui-word-aux">处理器类需要放在 api_handlers 目录下</div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">返回格式</label>
                        <div class="layui-input-inline api-format-select">
                            <select name="response_format">
                                <option value="json">JSON</option>
                                <option value="xml">XML</option>
                                <option value="jsonp">JSONP</option>
                                <option value="text">纯文本</option>
                                <option value="binary">二进制</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">缓存设置</label>
                        <div class="layui-input-inline api-cache-select">
                            <select name="cache_time">
                                <option value="0">不缓存</option>
                                <option value="60">1分钟</option>
                                <option value="300">5分钟</option>
                                <option value="600">10分钟</option>
                                <option value="1800">30分钟</option>
                                <option value="3600">1小时</option>
                                <option value="86400">1天</option>
                                <option value="604800">1周</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">超时时间</label>
                        <div class="layui-input-inline api-timeout-input">
                            <input type="number" name="timeout" value="30" placeholder="请输入超时时间" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">秒</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">重试次数</label>
                        <div class="layui-input-inline api-retry-input">
                            <input type="number" name="retry_times" value="0" placeholder="请输入重试次数" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">QPS限制</label>
                        <div class="layui-input-inline api-qps-input">
                            <input type="number" name="qps_limit" value="0" placeholder="请输入QPS限制" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">0表示不限制</div>
                    </div>
                </div>
                
                <!-- 计费设置 -->
                <div class="api-section">
                    <div class="api-section-title">计费设置</div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">是否免费</label>
                        <div class="layui-input-block">
                            <input type="radio" name="is_free" value="1" title="免费" checked>
                            <input type="radio" name="is_free" value="0" title="付费">
                        </div>
                    </div>
                    
                    <div class="layui-form-item" id="priceItem" style="display: none;">
                        <label class="layui-form-label">价格</label>
                        <div class="layui-input-inline">
                            <input type="number" name="price" value="0.00" placeholder="请输入价格" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-input-inline price-unit-select">
                            <select name="price_unit">
                                <option value="次">每次</option>
                                <option value="天">每天</option>
                                <option value="月">每月</option>
                                <option value="年">每年</option>
                                <option value="千次">千次</option>
                                <option value="万次">万次</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item" id="chargeTypeItem" style="display: none;">
                        <label class="layui-form-label">计费方式</label>
                        <div class="layui-input-block">
                            <input type="radio" name="charge_type" value="count" title="按次数" checked>
                            <input type="radio" name="charge_type" value="time" title="按时间">
                        </div>
                    </div>
                </div>
                
                <!-- 权限设置 -->
                <div class="api-section">
                    <div class="api-section-title">权限设置</div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">需要登录</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="need_login" lay-skin="switch" lay-text="是|否">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">需要签名</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="need_sign" lay-skin="switch" lay-text="是|否" checked>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">IP白名单</label>
                        <div class="layui-input-block">
                            <textarea name="ip_whitelist" placeholder="请输入IP白名单，多个IP用英文逗号分隔，留空表示不限制" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">允许的域名</label>
                        <div class="layui-input-block">
                            <textarea name="allowed_domains" placeholder="请输入允许的域名，多个域名用英文逗号分隔，留空表示不限制" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">会员等级</label>
                        <div class="layui-input-block">
                            <select name="vip_level">
                                <option value="0">不限制</option>
                                <?php foreach ($vipLevels as $level): ?>
                                <option value="<?php echo $level['id']; ?>"><?php echo $level['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 示例代码 -->
                <div class="api-section">
                    <div class="api-section-title">示例代码</div>
                    
                    <div class="layui-tab" lay-filter="exampleTab">
                        <ul class="layui-tab-title">
                            <li class="layui-this">cURL</li>
                            <li>PHP</li>
                            <li>JavaScript</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <textarea name="example_curl" placeholder="请输入cURL示例代码" class="layui-textarea api-example-textarea"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <textarea name="example_php" placeholder="请输入PHP示例代码" class="layui-textarea api-example-textarea"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item">
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <textarea name="example_javascript" placeholder="请输入JavaScript示例代码" class="layui-textarea api-example-textarea"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 其他设置 -->
                <div class="api-section">
                    <div class="api-section-title">其他设置</div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">版本号</label>
                        <div class="layui-input-inline api-version-input">
                            <input type="text" name="version" value="1.0.0" placeholder="请输入版本号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">排序</label>
                        <div class="layui-input-inline api-sort-input">
                            <input type="number" name="sort" value="100" placeholder="请输入排序" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">数字越小越靠前</div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="上线" checked>
                            <input type="radio" name="status" value="0" title="下线">
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-filter="apiSubmit" lay-submit>保存</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="backBtn">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'element', 'admin'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var element = layui.element;
    var admin = layui.admin;
    
    // 监听接口类型切换
    form.on('radio(api_type)', function(data) {
        if (data.value === 'remote') {
            $('#remoteUrlItem').show();
            $('#localHandlerItem').hide();
        } else {
            $('#remoteUrlItem').hide();
            $('#localHandlerItem').show();
        }
    });
    
    // 监听是否免费切换
    form.on('radio(is_free)', function(data) {
        if (data.value === '0') {
            $('#priceItem').show();
            $('#chargeTypeItem').show();
        } else {
            $('#priceItem').hide();
            $('#chargeTypeItem').hide();
        }
    });
    
    // 表单提交事件
    form.on('submit(apiSubmit)', function(data) {
        var loadIndex = layer.load(2);
        
        $.post('/admin/api/add', data.field, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1, time: 1500}, function() {
                    location.href = '/admin/api/list';
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        
        return false;
    });
    
    // 返回按钮点击事件
    $('#backBtn').click(function() {
        location.href = '/admin/api/list';
    });
    
    // 初始化表单
    form.render();
    
    // 添加表单验证
    form.verify({
        identifier: function(value) {
            if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                return '接口标识只能包含英文字母、数字和下划线';
            }
        }
    });
    
    // 监听接口类型切换
    form.on('radio(api_type)', function(data) {
        if (data.value === 'remote') {
            $('#remoteUrlItem').show();
            $('#localHandlerItem').hide();
        } else {
            $('#remoteUrlItem').hide();
            $('#localHandlerItem').show();
        }
    });
    
    // 监听是否免费切换
    form.on('radio(is_free)', function(data) {
        if (data.value === '0') {
            $('#priceItem').show();
            $('#chargeTypeItem').show();
        } else {
            $('#priceItem').hide();
            $('#chargeTypeItem').hide();
        }
    });
});
</script>
</body>
</html>