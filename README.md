# API管理系统

一个功能完整的API接口管理平台，支持API发布、管理、调用、计费等功能。

## 🚀 系统特性

### 核心功能
- **API管理中心**：完整的API生命周期管理
- **商家管理系统**：多等级商家体系，支持入驻和管理
- **用户权限体系**：基于角色的权限管理系统
- **支付与变现**：支持多种支付方式和收费模式
- **系统配置管理**：灵活的系统配置和参数管理

### 技术特色
- **现代化架构**：基于PHP + MySQL + Easyweb框架
- **安全防护**：SQL注入、XSS攻击、CSRF防护
- **性能优化**：数据库优化、缓存机制、日志管理
- **响应式设计**：完美适配桌面端和移动端
- **API文档**：自动生成API文档和在线调试

## 📋 系统要求

### 服务器环境
- **PHP版本**：7.4 或更高版本
- **MySQL版本**：5.7 或更高版本
- **Web服务器**：Apache 2.4+ 或 Nginx 1.16+

### PHP扩展要求
- pdo
- pdo_mysql
- json
- curl
- openssl
- mbstring
- gd
- zip

### 目录权限
以下目录需要可写权限（755）：
- `logs/` - 日志文件目录
- `uploads/` - 文件上传目录
- `cache/` - 缓存文件目录
- `temp/` - 临时文件目录

## 🛠️ 安装部署

### 1. 下载源码
```bash
git clone https://github.com/your-repo/api-system.git
cd api-system
```

### 2. 配置数据库
复制并编辑数据库配置文件：
```bash
cp config/database.php.example config/database.php
```

编辑 `config/database.php`：
```php
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'api_system');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. 配置系统参数
复制并编辑系统配置文件：
```bash
cp config/config.php.example config/config.php
```

### 4. 运行安装脚本
访问安装页面：
```
http://your-domain.com/config/install.php
```

或者使用命令行安装：
```bash
php config/install.php
```

### 5. 自动化部署
使用部署脚本进行自动化部署：
```bash
php deploy/deploy.php
```

## 🔧 配置说明

### 数据库配置
位置：`config/database.php`
```php
// 数据库连接配置
define('DB_HOST', 'localhost');     // 数据库主机
define('DB_PORT', '3306');          // 数据库端口
define('DB_NAME', 'api_system');    // 数据库名称
define('DB_USER', 'username');      // 数据库用户名
define('DB_PASS', 'password');      // 数据库密码
define('DB_CHARSET', 'utf8mb4');    // 字符集
```

### 系统配置
位置：`config/config.php`
```php
// 基本配置
define('APP_NAME', 'API管理系统');
define('APP_VERSION', '1.0.0');
define('APP_DEBUG', false);

// 安全配置
define('SECURITY_KEY', 'your-secret-key');
define('SESSION_LIFETIME', 7200);

// 文件上传配置
define('UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_ALLOWED_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx');
```

## 📚 使用指南

### 管理员操作

#### 1. 登录管理后台
访问：`http://your-domain.com/admin/`
- 默认管理员账号：admin
- 默认密码：admin123（首次登录后请修改）

#### 2. 系统配置
- **网站配置**：设置网站基本信息
- **邮件配置**：配置SMTP邮件服务
- **支付配置**：配置支付宝、微信支付参数
- **安全配置**：设置安全策略和限制

#### 3. 用户管理
- 查看和管理注册用户
- 设置用户权限和等级
- 处理用户反馈和工单

#### 4. 商家管理
- 审核商家入驻申请
- 管理商家等级和权限
- 查看商家收入和统计

#### 5. API管理
- 审核和发布API接口
- 设置API价格和限制
- 监控API调用情况

### 商家操作

#### 1. 商家入驻
- 填写公司信息和联系方式
- 上传营业执照等资质文件
- 等待管理员审核

#### 2. API发布
- 创建API接口
- 设置接口参数和文档
- 配置价格和调用限制

#### 3. 收入管理
- 查看收入统计和明细
- 申请提现和结算
- 查看佣金和分成

### 用户操作

#### 1. 注册登录
- 用户注册和邮箱验证
- 登录和密码找回
- 个人信息管理

#### 2. API调用
- 浏览和搜索API接口
- 在线测试API功能
- 购买API调用次数

#### 3. 订单管理
- 查看购买记录
- 管理API密钥
- 查看调用统计

## 🔒 安全特性

### 数据安全
- **SQL注入防护**：使用PDO预处理语句
- **XSS防护**：输入过滤和输出转义
- **CSRF防护**：令牌验证机制
- **密码安全**：BCrypt加密存储

### 访问控制
- **权限管理**：基于角色的访问控制
- **IP限制**：支持IP黑白名单
- **频率限制**：API调用频率控制
- **登录保护**：失败次数限制和验证码

### 数据传输
- **HTTPS支持**：强制HTTPS传输
- **API签名**：请求签名验证
- **数据加密**：敏感数据加密存储

## 📊 监控和日志

### 系统日志
- **访问日志**：记录所有HTTP请求
- **错误日志**：记录系统错误和异常
- **安全日志**：记录安全相关事件
- **API日志**：记录API调用详情

### 性能监控
- **响应时间**：监控API响应时间
- **并发数**：监控系统并发访问
- **资源使用**：监控CPU、内存使用情况
- **数据库性能**：监控数据库查询性能

### 日志管理
- **日志分级**：DEBUG、INFO、WARNING、ERROR、CRITICAL
- **日志轮转**：自动清理过期日志
- **日志导出**：支持CSV、JSON格式导出
- **实时监控**：实时查看系统状态

## 🚀 性能优化

### 数据库优化
- **索引优化**：为常用查询字段添加索引
- **查询优化**：优化SQL查询语句
- **连接池**：使用数据库连接池
- **读写分离**：支持主从数据库配置

### 缓存机制
- **页面缓存**：静态页面缓存
- **数据缓存**：热点数据缓存
- **API缓存**：API响应结果缓存
- **配置缓存**：系统配置缓存

### 前端优化
- **资源压缩**：CSS、JS文件压缩
- **图片优化**：图片压缩和格式优化
- **CDN加速**：静态资源CDN分发
- **懒加载**：图片和内容懒加载

## 🔧 开发指南

### 目录结构
```
api-system/
├── config/          # 配置文件
├── core/            # 核心类库
├── modules/         # 功能模块
├── public/          # 公共文件
│   ├── admin/       # 管理后台
│   ├── assets/      # 静态资源
│   └── api/         # API接口
├── tests/           # 测试文件
├── logs/            # 日志文件
├── uploads/         # 上传文件
├── cache/           # 缓存文件
├── temp/            # 临时文件
└── deploy/          # 部署脚本
```

### 开发规范
- **编码规范**：遵循PSR-4自动加载规范
- **命名规范**：使用驼峰命名法
- **注释规范**：完整的PHPDoc注释
- **错误处理**：统一的异常处理机制

### API开发
- **RESTful设计**：遵循REST API设计规范
- **版本控制**：支持API版本管理
- **文档生成**：自动生成API文档
- **测试工具**：内置API测试工具

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
php tests/SystemTest.php

# 运行特定测试
php tests/UserTest.php
php tests/ApiTest.php
```

### 测试覆盖
- **单元测试**：核心功能单元测试
- **集成测试**：模块间集成测试
- **性能测试**：系统性能压力测试
- **安全测试**：安全漏洞扫描测试

## 📞 技术支持

### 常见问题
1. **安装问题**：检查PHP版本和扩展
2. **数据库连接**：检查数据库配置和权限
3. **权限问题**：检查目录权限设置
4. **性能问题**：检查服务器配置和优化

### 联系方式
- **技术支持**：<EMAIL>
- **Bug反馈**：<EMAIL>
- **功能建议**：<EMAIL>

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进项目。

### 贡献指南
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📝 更新日志

### v1.0.0 (2024-01-15)
- 🎉 首次发布
- ✨ 完整的API管理功能
- ✨ 商家管理系统
- ✨ 用户权限体系
- ✨ 支付集成功能
- ✨ 安全防护机制
- ✨ 日志监控系统

---

**API管理系统** - 让API管理更简单、更安全、更高效！