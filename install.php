<?php
/**
 * 系统安装脚本
 * 用于初始化数据库和创建必要的表结构
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 自动加载类
require_once __DIR__ . '/classes/Database.php';

// 显示头部
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API商业系统 - 安装向导</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #5FB878; }
        .step { background-color: #f8f8f8; border: 1px solid #e6e6e6; border-radius: 4px; padding: 15px; margin-bottom: 20px; }
        .step-title { font-weight: bold; margin-bottom: 10px; }
        .success { color: #5FB878; }
        .error { color: #FF5722; }
        .warning { color: #FFB800; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; }
        .btn { display: inline-block; padding: 8px 16px; background-color: #5FB878; color: #fff; text-decoration: none; border-radius: 4px; }
        .btn:hover { background-color: #4aaa67; }
    </style>
</head>
<body>
    <h1>API商业系统 - 安装向导</h1>';

// 检查是否已安装
if (file_exists(__DIR__ . '/install.lock')) {
    echo '<div class="step">
        <div class="step-title warning">系统已安装</div>
        <p>系统已经安装过了，如需重新安装，请删除根目录下的 install.lock 文件。</p>
        <p><a href="/" class="btn">返回首页</a></p>
    </div>';
    echo '</body></html>';
    exit;
}

// 检查配置文件
echo '<div class="step">
    <div class="step-title">步骤1：检查配置文件</div>';

if (!file_exists(__DIR__ . '/config/database.php')) {
    echo '<p class="error">数据库配置文件不存在，请创建 config/database.php 文件。</p>';
    echo '</div></body></html>';
    exit;
}

echo '<p class="success">数据库配置文件存在。</p>
</div>';

// 连接数据库
echo '<div class="step">
    <div class="step-title">步骤2：连接数据库</div>';

try {
    $config = require_once __DIR__ . '/config/database.php';
    $dsn = "mysql:host={$config['host']};charset=utf8mb4";
    $db = new Database($dsn, $config['username'], $config['password']);
    echo '<p class="success">数据库连接成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">数据库连接失败：' . $e->getMessage() . '</p>';
    echo '</div></body></html>';
    exit;
}

// 创建数据库
echo '<div class="step">
    <div class="step-title">步骤3：创建数据库</div>';

try {
    $db->execute("CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo '<p class="success">数据库 ' . $config['database'] . ' 创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">数据库创建失败：' . $e->getMessage() . '</p>';
    echo '</div></body></html>';
    exit;
}

// 选择数据库
try {
    $db->execute("USE `{$config['database']}`");
    echo '<p class="success">数据库 ' . $config['database'] . ' 选择成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">数据库选择失败：' . $e->getMessage() . '</p>';
    echo '</div></body></html>';
    exit;
}

echo '</div>';

// 创建表结构
echo '<div class="step">
    <div class="step-title">步骤4：创建表结构</div>';

// 创建管理员表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `admins` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `role` varchar(20) NOT NULL DEFAULT 'admin',
            `status` tinyint(1) NOT NULL DEFAULT '1',
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            `last_login_time` int(11) DEFAULT NULL,
            `last_login_ip` varchar(50) DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">管理员表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">管理员表创建失败：' . $e->getMessage() . '</p>';
}

// 创建站点配置表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `site_config` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `key` varchar(50) NOT NULL,
            `value` text,
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `key` (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">站点配置表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">站点配置表创建失败：' . $e->getMessage() . '</p>';
}

// 创建用户表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `status` tinyint(1) NOT NULL DEFAULT '1',
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            `last_login_time` int(11) DEFAULT NULL,
            `last_login_ip` varchar(50) DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">用户表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">用户表创建失败：' . $e->getMessage() . '</p>';
}

// 创建API表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `apis` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `description` text,
            `url` varchar(255) NOT NULL,
            `method` varchar(10) NOT NULL DEFAULT 'GET',
            `category_id` int(11) DEFAULT NULL,
            `merchant_id` int(11) DEFAULT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT '0.00',
            `status` tinyint(1) NOT NULL DEFAULT '1',
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">API表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">API表创建失败：' . $e->getMessage() . '</p>';
}

// 创建API分类表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `api_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `description` varchar(255) DEFAULT NULL,
            `sort` int(11) NOT NULL DEFAULT '0',
            `status` tinyint(1) NOT NULL DEFAULT '1',
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">API分类表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">API分类表创建失败：' . $e->getMessage() . '</p>';
}

// 创建商家表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `merchants` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `name` varchar(100) NOT NULL,
            `description` text,
            `contact_name` varchar(50) NOT NULL,
            `contact_phone` varchar(20) NOT NULL,
            `contact_email` varchar(100) NOT NULL,
            `status` tinyint(1) NOT NULL DEFAULT '0',
            `level_id` int(11) DEFAULT NULL,
            `balance` decimal(10,2) NOT NULL DEFAULT '0.00',
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">商家表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">商家表创建失败：' . $e->getMessage() . '</p>';
}

// 创建订单表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `orders` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `order_no` varchar(50) NOT NULL,
            `user_id` int(11) NOT NULL,
            `type` varchar(20) NOT NULL,
            `title` varchar(100) NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `pay_type` varchar(20) NOT NULL,
            `status` tinyint(1) NOT NULL DEFAULT '0',
            `trade_no` varchar(100) DEFAULT NULL,
            `pay_time` int(11) DEFAULT NULL,
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `order_no` (`order_no`),
            KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">订单表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">订单表创建失败：' . $e->getMessage() . '</p>';
}

// 创建API请求记录表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `api_requests` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `api_id` int(11) NOT NULL,
            `user_id` int(11) NOT NULL,
            `ip` varchar(50) NOT NULL,
            `request_data` text,
            `response_data` text,
            `status` tinyint(1) NOT NULL DEFAULT '1',
            `create_time` int(11) NOT NULL,
            PRIMARY KEY (`id`),
            KEY `api_id` (`api_id`),
            KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">API请求记录表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">API请求记录表创建失败：' . $e->getMessage() . '</p>';
}

// 创建工单表
try {
    $db->execute("
        CREATE TABLE IF NOT EXISTS `tickets` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `title` varchar(100) NOT NULL,
            `content` text NOT NULL,
            `status` tinyint(1) NOT NULL DEFAULT '0',
            `create_time` int(11) NOT NULL,
            `update_time` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    echo '<p class="success">工单表创建成功。</p>';
} catch (Exception $e) {
    echo '<p class="error">工单表创建失败：' . $e->getMessage() . '</p>';
}

echo '</div>';

// 创建默认数据
echo '<div class="step">
    <div class="step-title">步骤5：创建默认数据</div>';

// 创建默认管理员
try {
    // 检查是否已存在管理员
    $admin = $db->query("SELECT COUNT(*) as count FROM admins", [], true);
    
    if ($admin['count'] == 0) {
        // 创建默认管理员
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $time = time();
        $db->execute(
            "INSERT INTO `admins` (`username`, `password`, `role`, `status`, `create_time`) VALUES (?, ?, ?, ?, ?)",
            ['admin', $password, 'super_admin', 1, $time]
        );
        echo '<p class="success">默认管理员创建成功。用户名：admin，密码：admin123</p>';
    } else {
        echo '<p class="warning">管理员已存在，跳过创建。</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">默认管理员创建失败：' . $e->getMessage() . '</p>';
}

// 创建默认站点配置
try {
    // 检查是否已存在站点配置
    $config = $db->query("SELECT COUNT(*) as count FROM site_config", [], true);
    
    if ($config['count'] == 0) {
        // 创建默认站点配置
        $time = time();
        $configs = [
            ['site_name', 'API商业系统', $time],
            ['site_keywords', 'API,接口,开发', $time],
            ['site_description', 'API商业系统是一个多功能的API接口管理和销售平台', $time],
            ['site_logo', '/assets/images/logo.png', $time],
            ['site_icp', '', $time],
            ['site_copyright', '© ' . date('Y') . ' API商业系统', $time],
            ['admin_email', '<EMAIL>', $time]
        ];
        
        foreach ($configs as $config) {
            $db->execute(
                "INSERT INTO `site_config` (`key`, `value`, `create_time`) VALUES (?, ?, ?)",
                $config
            );
        }
        echo '<p class="success">默认站点配置创建成功。</p>';
    } else {
        echo '<p class="warning">站点配置已存在，跳过创建。</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">默认站点配置创建失败：' . $e->getMessage() . '</p>';
}

// 创建默认API分类
try {
    // 检查是否已存在API分类
    $category = $db->query("SELECT COUNT(*) as count FROM api_categories", [], true);
    
    if ($category['count'] == 0) {
        // 创建默认API分类
        $time = time();
        $categories = [
            ['工具类', '实用工具API', 0, 1, $time],
            ['娱乐类', '娱乐休闲API', 1, 1, $time],
            ['金融类', '金融服务API', 2, 1, $time],
            ['生活类', '生活服务API', 3, 1, $time],
            ['开发类', '开发辅助API', 4, 1, $time]
        ];
        
        foreach ($categories as $category) {
            $db->execute(
                "INSERT INTO `api_categories` (`name`, `description`, `sort`, `status`, `create_time`) VALUES (?, ?, ?, ?, ?)",
                $category
            );
        }
        echo '<p class="success">默认API分类创建成功。</p>';
    } else {
        echo '<p class="warning">API分类已存在，跳过创建。</p>';
    }
} catch (Exception $e) {
    echo '<p class="error">默认API分类创建失败：' . $e->getMessage() . '</p>';
}

echo '</div>';

// 创建锁定文件
file_put_contents(__DIR__ . '/install.lock', date('Y-m-d H:i:s'));

// 显示完成信息
echo '<div class="step">
    <div class="step-title success">安装完成</div>
    <p>API商业系统安装成功！</p>
    <p>管理员账号：admin</p>
    <p>管理员密码：admin123</p>
    <p>请立即登录后台修改默认密码。</p>
    <p><a href="/admin/login" class="btn">进入后台</a></p>
</div>';

echo '</body></html>';