<!DOCTYPE html>
<html>
<head>
    <script>if (window !== top) top.location.replace(location.href);</script>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="../../Easyweb/assets/images/favicon.ico" rel="icon">
    <title>管理员登录 - API商业系统</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css?v=318">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        body {
            background-image: url("../../Easyweb/assets/images/bg-login.jpg");
            background-repeat: no-repeat;
            background-size: cover;
            min-height: 100vh;
        }

        body:before {
            content: "";
            background-color: rgba(0, 0, 0, .3);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .login-wrapper {
            max-width: 420px;
            padding: 20px;
            margin: 0 auto;
            position: relative;
            box-sizing: border-box;
            z-index: 2;
        }

        .login-wrapper > .layui-form {
            padding: 30px 35px;
            background-color: #fff;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            box-sizing: border-box;
            border-radius: 8px;
        }

        .login-wrapper > .layui-form > h2 {
            color: #333;
            font-size: 20px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .login-wrapper > .layui-form > .layui-form-item {
            margin-bottom: 25px;
            position: relative;
        }

        .login-wrapper > .layui-form > .layui-form-item:last-child {
            margin-bottom: 0;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-input {
            height: 48px;
            line-height: 1;
            border-radius: 4px !important;
            border: 1px solid #e6e6e6;
            font-size: 14px;
        }

        .login-wrapper .layui-input-icon-group > .layui-input {
            padding-left: 48px;
        }

        .login-wrapper .layui-input-icon-group > .layui-icon {
            width: 48px;
            height: 48px;
            line-height: 48px;
            font-size: 18px;
            color: #999;
            position: absolute;
            left: 0;
            top: 0;
            text-align: center;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-captcha-group {
            padding-right: 135px;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-captcha-group > .login-captcha {
            height: 48px;
            width: 120px;
            cursor: pointer;
            box-sizing: border-box;
            border: 1px solid #e6e6e6;
            border-radius: 4px !important;
            position: absolute;
            right: 0;
            top: 0;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-form-checkbox {
            margin: 0 !important;
            padding-left: 25px;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-form-checkbox > .layui-icon {
            width: 15px !important;
            height: 15px !important;
        }

        .login-wrapper > .layui-form .layui-btn-fluid {
            height: 50px;
            line-height: 50px;
            font-size: 16px;
            border-radius: 4px !important;
            background-color: #1890ff;
            border-color: #1890ff;
        }

        .login-wrapper > .layui-form .layui-btn-fluid:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .login-copyright {
            color: #eee;
            padding-bottom: 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        @media screen and (min-height: 550px) {
            .login-wrapper {
                margin: -280px auto 0;
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                width: 100%;
            }

            .login-copyright {
                position: absolute;
                bottom: 0;
                right: 0;
                left: 0;
            }
        }

        .layui-link {
            color: #1890ff !important;
        }

        .layui-link:hover {
            color: #40a9ff !important;
        }

        .login-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .login-header .logo {
            font-size: 24px;
            color: #1890ff;
            margin-bottom: 10px;
        }

        .login-header .subtitle {
            color: #666;
            font-size: 14px;
        }

        .error-msg {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
    </style>
</head>
<body>
<div class="login-wrapper layui-anim layui-anim-scale layui-hide">
    <form class="layui-form" lay-filter="loginForm">
        <div class="login-header">
            <div class="logo">
                <i class="layui-icon layui-icon-website"></i>
                API商业系统
            </div>
            <div class="subtitle">管理员登录</div>
        </div>
        
        <h2>欢迎回来</h2>
        
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-username"></i>
            <input class="layui-input" name="username" placeholder="请输入管理员账号" autocomplete="off"
                   lay-verType="tips" lay-verify="required" required/>
            <div class="error-msg" id="usernameError"></div>
        </div>
        
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-password"></i>
            <input class="layui-input" name="password" placeholder="请输入登录密码" type="password"
                   lay-verType="tips" lay-verify="required" required/>
            <div class="error-msg" id="passwordError"></div>
        </div>
        
        <div class="layui-form-item layui-input-icon-group login-captcha-group">
            <i class="layui-icon layui-icon-auz"></i>
            <input class="layui-input" name="captcha" placeholder="请输入验证码" autocomplete="off"
                   lay-verType="tips" lay-verify="required" required/>
            <img class="login-captcha" alt="验证码" title="点击刷新验证码"/>
            <div class="error-msg" id="captchaError"></div>
        </div>
        
        <div class="layui-form-item">
            <input type="checkbox" name="remember" title="记住密码" lay-skin="primary">
            <a href="javascript:;" class="layui-link pull-right">忘记密码？</a>
        </div>
        
        <div class="layui-form-item">
            <button class="layui-btn layui-btn-fluid" lay-filter="loginSubmit" lay-submit>登录</button>
        </div>
    </form>
</div>
<div class="login-copyright">Copyright © 2024 API商业系统 All Rights Reserved</div>

<!-- js部分 -->
<script type="text/javascript" src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../Easyweb/assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        
        $('.login-wrapper').removeClass('layui-hide');

        /* 表单提交 */
        form.on('submit(loginSubmit)', function (obj) {
            var loadIndex = layer.load(2, {shade: 0.3});
            
            // 清除之前的错误信息
            $('.error-msg').hide();
            
            $.ajax({
                url: 'login.php',
                type: 'POST',
                data: obj.field,
                dataType: 'json',
                success: function(res) {
                    layer.close(loadIndex);
                    if (res.code === 200) {
                        layer.msg('登录成功', {icon: 1, time: 1500}, function () {
                            location.href = 'main.html';
                        });
                    } else {
                        layer.msg(res.msg || '登录失败', {icon: 2, anim: 6});
                        // 刷新验证码
                        refreshCaptcha();
                    }
                },
                error: function() {
                    layer.close(loadIndex);
                    layer.msg('服务器错误，请稍后重试', {icon: 2, anim: 6});
                    refreshCaptcha();
                }
            });
            
            return false;
        });

        /* 图形验证码 */
        function refreshCaptcha() {
            $('.login-captcha')[0].src = 'captcha.php?t=' + (new Date).getTime();
        }
        
        $('.login-captcha').click(refreshCaptcha).trigger('click');

        // 输入框焦点事件，清除错误提示
        $('input[name="username"], input[name="password"], input[name="captcha"]').focus(function() {
            $(this).siblings('.error-msg').hide();
        });
    });
</script>
</body>
</html>
