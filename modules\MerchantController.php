<?php
/**
 * 商家控制器
 * API管理系统 - 商家管理控制器
 */

require_once __DIR__ . '/Merchant.php';
require_once __DIR__ . '/AuthController.php';
require_once __DIR__ . '/Permission.php';

class MerchantController {
    private $merchantModel;
    private $authController;
    private $permission;
    
    public function __construct() {
        $this->merchantModel = new Merchant();
        $this->authController = new AuthController();
        $this->permission = new Permission();
    }
    
    /**
     * 获取商家列表
     */
    public function getMerchantList() {
        try {
            $this->permission->requirePermission('merchant.view');
            
            $page = $_GET['page'] ?? 1;
            $perPage = $_GET['per_page'] ?? 20;
            $conditions = [];
            
            // 搜索条件
            if (!empty($_GET['company_name'])) {
                $conditions['company_name'] = $_GET['company_name'];
            }
            
            if (!empty($_GET['contact_name'])) {
                $conditions['contact_name'] = $_GET['contact_name'];
            }
            
            if (!empty($_GET['level'])) {
                $conditions['level'] = $_GET['level'];
            }
            
            if (!empty($_GET['status'])) {
                $conditions['status'] = $_GET['status'];
            }
            
            if (!empty($_GET['verified'])) {
                $conditions['verified'] = $_GET['verified'];
            }
            
            $result = $this->merchantModel->getListWithUserInfo($page, $perPage, $conditions);
            
            System::jsonResponse($result, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取商家详情
     */
    public function getMerchantDetail() {
        try {
            $this->permission->requirePermission('merchant.view');
            
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception('商家ID不能为空');
            }
            
            $merchant = $this->merchantModel->getDetailWithStats($id);
            if (!$merchant) {
                throw new Exception('商家不存在');
            }
            
            System::jsonResponse($merchant, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 商家入驻申请
     */
    public function applyMerchant() {
        try {
            $data = [
                'user_id' => $_POST['user_id'] ?? 0,
                'company_name' => $_POST['company_name'] ?? '',
                'contact_name' => $_POST['contact_name'] ?? '',
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'business_license' => $_POST['business_license'] ?? '',
                'address' => $_POST['address'] ?? ''
            ];
            
            // 验证必填字段
            $required = ['user_id', 'company_name', 'contact_name', 'contact_phone', 'contact_email'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new Exception("字段 {$field} 不能为空");
                }
            }
            
            // 验证邮箱格式
            if (!filter_var($data['contact_email'], FILTER_VALIDATE_EMAIL)) {
                throw new Exception('邮箱格式不正确');
            }
            
            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $data['contact_phone'])) {
                throw new Exception('手机号格式不正确');
            }
            
            $merchant = $this->merchantModel->applyMerchant($data);
            
            System::jsonResponse($merchant, 200, '申请提交成功，请等待审核');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 审核商家申请
     */
    public function reviewMerchant() {
        try {
            $this->permission->requirePermission('merchant.review');
            
            $id = $_POST['id'] ?? 0;
            $status = $_POST['status'] ?? 0;
            $reason = $_POST['reason'] ?? '';
            
            if (!$id) {
                throw new Exception('商家ID不能为空');
            }
            
            if (!in_array($status, [Merchant::STATUS_ACTIVE, Merchant::STATUS_REJECTED])) {
                throw new Exception('无效的审核状态');
            }
            
            $result = $this->merchantModel->reviewApplication($id, $status, $reason);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'review_merchant',
                "Merchant:{$id}",
                "审核商家申请: " . ($status == Merchant::STATUS_ACTIVE ? '通过' : '拒绝')
            );
            
            System::jsonResponse($result, 200, '审核完成');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 更新商家信息
     */
    public function updateMerchant() {
        try {
            $this->permission->requirePermission('merchant.edit');
            
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception('商家ID不能为空');
            }
            
            $data = [
                'company_name' => $_POST['company_name'] ?? '',
                'contact_name' => $_POST['contact_name'] ?? '',
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'business_license' => $_POST['business_license'] ?? '',
                'address' => $_POST['address'] ?? '',
                'commission_rate' => $_POST['commission_rate'] ?? null
            ];
            
            // 过滤空值
            $data = array_filter($data, function($value) {
                return $value !== '' && $value !== null;
            });
            
            if (empty($data)) {
                throw new Exception('没有要更新的数据');
            }
            
            // 验证邮箱格式
            if (isset($data['contact_email']) && !filter_var($data['contact_email'], FILTER_VALIDATE_EMAIL)) {
                throw new Exception('邮箱格式不正确');
            }
            
            // 验证手机号格式
            if (isset($data['contact_phone']) && !preg_match('/^1[3-9]\d{9}$/', $data['contact_phone'])) {
                throw new Exception('手机号格式不正确');
            }
            
            // 验证佣金率
            if (isset($data['commission_rate'])) {
                $rate = floatval($data['commission_rate']);
                if ($rate < 0 || $rate > 1) {
                    throw new Exception('佣金率必须在0-1之间');
                }
                $data['commission_rate'] = $rate;
            }
            
            $result = $this->merchantModel->update($id, $data);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'update_merchant',
                "Merchant:{$id}",
                "更新商家信息"
            );
            
            System::jsonResponse($result, 200, '更新成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 升级商家等级
     */
    public function upgradeMerchantLevel() {
        try {
            $this->permission->requirePermission('merchant.upgrade');
            
            $id = $_POST['id'] ?? 0;
            $newLevel = $_POST['level'] ?? 0;
            
            if (!$id || !$newLevel) {
                throw new Exception('商家ID和等级不能为空');
            }
            
            $result = $this->merchantModel->upgradeLevel($id, $newLevel);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'upgrade_merchant_level',
                "Merchant:{$id}",
                "升级商家等级到: " . $this->merchantModel->getLevelText($newLevel)
            );
            
            System::jsonResponse($result, 200, '等级升级成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 更新商家余额
     */
    public function updateMerchantBalance() {
        try {
            $this->permission->requirePermission('merchant.balance');
            
            $id = $_POST['id'] ?? 0;
            $amount = $_POST['amount'] ?? 0;
            $type = $_POST['type'] ?? 'add';
            $description = $_POST['description'] ?? '';
            
            if (!$id || !$amount) {
                throw new Exception('商家ID和金额不能为空');
            }
            
            $amount = floatval($amount);
            if ($amount <= 0) {
                throw new Exception('金额必须大于0');
            }
            
            if (!in_array($type, ['add', 'subtract'])) {
                throw new Exception('无效的操作类型');
            }
            
            $result = $this->merchantModel->updateBalance($id, $amount, $type, $description);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'update_merchant_balance',
                "Merchant:{$id}",
                "余额操作: {$type} {$amount}元 - {$description}"
            );
            
            System::jsonResponse($result, 200, '余额更新成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 批量操作商家
     */
    public function batchMerchantOperation() {
        try {
            $this->permission->requirePermission('merchant.edit');
            
            $action = $_POST['action'] ?? '';
            $ids = $_POST['ids'] ?? [];
            
            if (empty($action) || empty($ids)) {
                throw new Exception('操作类型和ID列表不能为空');
            }
            
            $result = false;
            $message = '';
            
            switch ($action) {
                case 'activate':
                    $result = $this->merchantModel->batchUpdateStatus($ids, Merchant::STATUS_ACTIVE);
                    $message = '商家批量激活成功';
                    break;
                    
                case 'suspend':
                    $result = $this->merchantModel->batchUpdateStatus($ids, Merchant::STATUS_SUSPENDED);
                    $message = '商家批量暂停成功';
                    break;
                    
                case 'delete':
                    $this->permission->requirePermission('merchant.delete');
                    $result = $this->merchantModel->deleteMany($ids);
                    $message = '商家批量删除成功';
                    break;
                    
                default:
                    throw new Exception('不支持的操作类型');
            }
            
            if (!$result) {
                throw new Exception('批量操作失败');
            }
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                "batch_{$action}_merchant",
                "Merchants:" . implode(',', $ids),
                "批量{$action}商家"
            );
            
            System::jsonResponse(null, 200, $message);
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取商家统计信息
     */
    public function getMerchantStats() {
        try {
            $this->permission->requirePermission('merchant.stats');
            
            $stats = $this->merchantModel->getMerchantStats();
            $levelDistribution = $this->merchantModel->getLevelDistribution();
            $revenueRanking = $this->merchantModel->getRevenueRanking(10);
            $activityRanking = $this->merchantModel->getActivityRanking(10);
            
            System::jsonResponse([
                'stats' => $stats,
                'level_distribution' => $levelDistribution,
                'revenue_ranking' => $revenueRanking,
                'activity_ranking' => $activityRanking
            ], 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取商家余额变动记录
     */
    public function getMerchantBalanceHistory() {
        try {
            $this->permission->requirePermission('merchant.view');
            
            $merchantId = $_GET['merchant_id'] ?? 0;
            $page = $_GET['page'] ?? 1;
            $perPage = $_GET['per_page'] ?? 20;
            
            if (!$merchantId) {
                throw new Exception('商家ID不能为空');
            }
            
            $result = $this->merchantModel->getBalanceHistory($merchantId, $page, $perPage);
            
            System::jsonResponse($result, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取商家API列表
     */
    public function getMerchantApis() {
        try {
            $this->permission->requirePermission('merchant.view');
            
            $merchantId = $_GET['merchant_id'] ?? 0;
            $page = $_GET['page'] ?? 1;
            $perPage = $_GET['per_page'] ?? 20;
            
            if (!$merchantId) {
                throw new Exception('商家ID不能为空');
            }
            
            $result = $this->merchantModel->getMerchantApis($merchantId, $page, $perPage);
            
            System::jsonResponse($result, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 结算商家收入
     */
    public function settleMerchantRevenue() {
        try {
            $this->permission->requirePermission('merchant.settle');
            
            $merchantId = $_POST['merchant_id'] ?? 0;
            $amount = $_POST['amount'] ?? 0;
            $orderId = $_POST['order_id'] ?? null;
            
            if (!$merchantId || !$amount) {
                throw new Exception('商家ID和金额不能为空');
            }
            
            $amount = floatval($amount);
            if ($amount <= 0) {
                throw new Exception('金额必须大于0');
            }
            
            $result = $this->merchantModel->settleMerchantRevenue($merchantId, $amount, $orderId);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'settle_merchant_revenue',
                "Merchant:{$merchantId}",
                "结算收入: {$amount}元"
            );
            
            System::jsonResponse($result, 200, '结算成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 导出商家数据
     */
    public function exportMerchants() {
        try {
            $this->permission->requirePermission('merchant.export');
            
            $conditions = [];
            
            // 搜索条件
            if (!empty($_GET['level'])) {
                $conditions['level'] = $_GET['level'];
            }
            
            if (!empty($_GET['status'])) {
                $conditions['status'] = $_GET['status'];
            }
            
            // 获取所有符合条件的商家
            $result = $this->merchantModel->getListWithUserInfo(1, 10000, $conditions);
            $merchants = $result['data'];
            
            // 设置CSV头
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="merchants_' . date('Y-m-d') . '.csv"');
            
            // 输出CSV内容
            $output = fopen('php://output', 'w');
            
            // 写入BOM，解决中文乱码
            fwrite($output, "\xEF\xBB\xBF");
            
            // 写入表头
            fputcsv($output, [
                'ID', '公司名称', '联系人', '联系电话', '联系邮箱', 
                '等级', '状态', '余额', '总收入', '佣金率', '创建时间'
            ]);
            
            // 写入数据
            foreach ($merchants as $merchant) {
                fputcsv($output, [
                    $merchant['id'],
                    $merchant['company_name'],
                    $merchant['contact_name'],
                    $merchant['contact_phone'],
                    $merchant['contact_email'],
                    $merchant['level_text'],
                    $merchant['status_text'],
                    $merchant['balance'],
                    $merchant['total_revenue'],
                    $merchant['commission_rate'] * 100 . '%',
                    $merchant['created_at']
                ]);
            }
            
            fclose($output);
            exit;
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取商家申请列表（待审核）
     */
    public function getPendingApplications() {
        try {
            $this->permission->requirePermission('merchant.review');
            
            $page = $_GET['page'] ?? 1;
            $perPage = $_GET['per_page'] ?? 20;
            
            $conditions = ['status' => Merchant::STATUS_PENDING];
            $result = $this->merchantModel->getListWithUserInfo($page, $perPage, $conditions);
            
            System::jsonResponse($result, 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 自动升级商家等级
     */
    public function autoUpgradeMerchantLevel() {
        try {
            $this->permission->requirePermission('merchant.upgrade');
            
            $merchantId = $_POST['merchant_id'] ?? 0;
            
            if (!$merchantId) {
                throw new Exception('商家ID不能为空');
            }
            
            $result = $this->merchantModel->autoUpgradeLevel($merchantId);
            
            if ($result) {
                System::jsonResponse($result, 200, '自动升级成功');
            } else {
                System::jsonResponse(null, 200, '暂不满足升级条件');
            }
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 获取商家收入趋势
     */
    public function getMerchantRevenueTrend() {
        try {
            $this->permission->requirePermission('merchant.stats');
            
            $merchantId = $_GET['merchant_id'] ?? 0;
            $days = $_GET['days'] ?? 30;
            
            if (!$merchantId) {
                throw new Exception('商家ID不能为空');
            }
            
            $merchant = $this->merchantModel->getDetailWithStats($merchantId);
            if (!$merchant) {
                throw new Exception('商家不存在');
            }
            
            System::jsonResponse([
                'trend' => $merchant['call_trend'],
                'stats' => $merchant['revenue_stats']
            ], 200, '获取成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
    
    /**
     * 发送商家通知
     */
    public function sendMerchantNotification() {
        try {
            $this->permission->requirePermission('merchant.notify');
            
            $merchantId = $_POST['merchant_id'] ?? 0;
            $title = $_POST['title'] ?? '';
            $content = $_POST['content'] ?? '';
            $type = $_POST['type'] ?? 'info';
            
            if (!$merchantId || !$title || !$content) {
                throw new Exception('商家ID、标题和内容不能为空');
            }
            
            // 这里可以集成邮件发送、短信发送等功能
            // 暂时记录到数据库
            $sql = "INSERT INTO {$this->merchantModel->db->getPrefix()}merchant_notifications 
                    (merchant_id, title, content, type, sender_id, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $this->merchantModel->db->query($sql, [
                $merchantId,
                $title,
                $content,
                $type,
                $_SESSION['admin_id'],
                date('Y-m-d H:i:s')
            ]);
            
            // 记录操作日志
            $this->permission->logPermissionAction(
                $_SESSION['admin_id'],
                'send_merchant_notification',
                "Merchant:{$merchantId}",
                "发送通知: {$title}"
            );
            
            System::jsonResponse(null, 200, '通知发送成功');
            
        } catch (Exception $e) {
            System::jsonResponse(null, 400, $e->getMessage());
        }
    }
}