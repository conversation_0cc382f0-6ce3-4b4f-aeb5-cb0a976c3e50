<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>QPS限制 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">QPS限制配置</div>
        <div class="layui-card-body">
            <div class="layui-tab" lay-filter="qpsTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">全局设置</li>
                    <li>API设置</li>
                    <li>用户设置</li>
                    <li>IP设置</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 全局设置 -->
                    <div class="layui-tab-item layui-show">
                        <form id="globalForm" lay-filter="globalForm" class="layui-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">启用QPS限制</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="enable_qps_limit" lay-skin="switch" lay-text="启用|禁用">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">默认QPS</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="default_qps" placeholder="请输入默认QPS" class="layui-input" value="10">
                                </div>
                                <div class="layui-form-mid layui-word-aux">每秒请求次数，0表示不限制</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">超限处理</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="limit_action" value="reject" title="直接拒绝" checked>
                                    <input type="radio" name="limit_action" value="queue" title="排队等待">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">超限提示</label>
                                <div class="layui-input-block">
                                    <input type="text" name="limit_message" placeholder="请输入超限提示信息" class="layui-input" value="请求过于频繁，请稍后再试">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">白名单IP</label>
                                <div class="layui-input-block">
                                    <textarea name="whitelist_ips" placeholder="请输入白名单IP，每行一个" class="layui-textarea"></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-filter="globalSubmit" lay-submit>保存设置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- API设置 -->
                    <div class="layui-tab-item">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input id="searchKeyword" class="layui-input" type="text" placeholder="API名称/路径"/>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button id="searchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                    <button id="addApiLimitBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                                </div>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="apiTable" lay-filter="apiTable"></table>
                    </div>
                    
                    <!-- 用户设置 -->
                    <div class="layui-tab-item">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input id="userSearchKeyword" class="layui-input" type="text" placeholder="用户名/邮箱"/>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button id="userSearchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                    <button id="addUserLimitBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                                </div>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="userTable" lay-filter="userTable"></table>
                    </div>
                    
                    <!-- IP设置 -->
                    <div class="layui-tab-item">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input id="ipSearchKeyword" class="layui-input" type="text" placeholder="IP地址"/>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button id="ipSearchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                    <button id="addIpLimitBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                                </div>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="ipTable" lay-filter="ipTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 添加/编辑API限制弹窗 -->
<script type="text/html" id="apiLimitDialog">
    <form id="apiLimitForm" lay-filter="apiLimitForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id || ''}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">API</label>
            <div class="layui-input-block">
                <select name="api_id" lay-verify="required" lay-search>
                    <option value="">请选择API</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">QPS限制</label>
            <div class="layui-input-block">
                <input name="qps_limit" placeholder="请输入QPS限制" type="number" class="layui-input" value="{{d.qps_limit || '10'}}" lay-verify="required|number" required/>
                <div class="layui-form-mid layui-word-aux">每秒请求次数，0表示不限制</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">超限处理</label>
            <div class="layui-input-block">
                <input type="radio" name="limit_action" value="reject" title="直接拒绝" {{d.limit_action === 'queue' ? '' : 'checked'}}>
                <input type="radio" name="limit_action" value="queue" title="排队等待" {{d.limit_action === 'queue' ? 'checked' : ''}}>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">超限提示</label>
            <div class="layui-input-block">
                <input name="limit_message" placeholder="请输入超限提示信息" type="text" class="layui-input" value="{{d.limit_message || '请求过于频繁，请稍后再试'}}"/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="apiLimitSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- 添加/编辑用户限制弹窗 -->
<script type="text/html" id="userLimitDialog">
    <form id="userLimitForm" lay-filter="userLimitForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id || ''}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">用户</label>
            <div class="layui-input-block">
                <select name="user_id" lay-verify="required" lay-search>
                    <option value="">请选择用户</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">QPS限制</label>
            <div class="layui-input-block">
                <input name="qps_limit" placeholder="请输入QPS限制" type="number" class="layui-input" value="{{d.qps_limit || '10'}}" lay-verify="required|number" required/>
                <div class="layui-form-mid layui-word-aux">每秒请求次数，0表示不限制</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">超限处理</label>
            <div class="layui-input-block">
                <input type="radio" name="limit_action" value="reject" title="直接拒绝" {{d.limit_action === 'queue' ? '' : 'checked'}}>
                <input type="radio" name="limit_action" value="queue" title="排队等待" {{d.limit_action === 'queue' ? 'checked' : ''}}>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">超限提示</label>
            <div class="layui-input-block">
                <input name="limit_message" placeholder="请输入超限提示信息" type="text" class="layui-input" value="{{d.limit_message || '请求过于频繁，请稍后再试'}}"/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="userLimitSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- 添加/编辑IP限制弹窗 -->
<script type="text/html" id="ipLimitDialog">
    <form id="ipLimitForm" lay-filter="ipLimitForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id || ''}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">IP地址</label>
            <div class="layui-input-block">
                <input name="ip" placeholder="请输入IP地址" type="text" class="layui-input" value="{{d.ip || ''}}" lay-verify="required" required/>
                <div class="layui-form-mid layui-word-aux">支持单个IP或IP段，如：***********或***********/24</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">QPS限制</label>
            <div class="layui-input-block">
                <input name="qps_limit" placeholder="请输入QPS限制" type="number" class="layui-input" value="{{d.qps_limit || '10'}}" lay-verify="required|number" required/>
                <div class="layui-form-mid layui-word-aux">每秒请求次数，0表示不限制</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">超限处理</label>
            <div class="layui-input-block">
                <input type="radio" name="limit_action" value="reject" title="直接拒绝" {{d.limit_action === 'queue' ? '' : 'checked'}}>
                <input type="radio" name="limit_action" value="queue" title="排队等待" {{d.limit_action === 'queue' ? 'checked' : ''}}>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">超限提示</label>
            <div class="layui-input-block">
                <input name="limit_message" placeholder="请输入超限提示信息" type="text" class="layui-input" value="{{d.limit_message || '请求过于频繁，请稍后再试'}}"/>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="ipLimitSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'element'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var element = layui.element;
    
    // 加载全局设置
    $.get('/admin/security/qps/global', function(res) {
        if (res.code === 0) {
            form.val('globalForm', res.data);
        }
    }, 'json');
    
    // 全局设置表单提交
    form.on('submit(globalSubmit)', function(data) {
        layer.load(2);
        $.post('/admin/security/qps/global/save', data.field, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 渲染API限制表格
    var apiTable = table.render({
        elem: '#apiTable',
        url: '/admin/security/qps/api/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'api_name', title: 'API名称'},
            {field: 'api_path', title: 'API路径'},
            {field: 'qps_limit', title: 'QPS限制', width: 100},
            {field: 'limit_action', title: '超限处理', templet: function(d) {
                return d.limit_action === 'queue' ? '排队等待' : '直接拒绝';
            }, width: 100},
            {field: 'limit_message', title: '超限提示'},
            {field: 'create_time', title: '创建时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 120}
        ]]
    });
    
    // 渲染用户限制表格
    var userTable = table.render({
        elem: '#userTable',
        url: '/admin/security/qps/user/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'username', title: '用户名'},
            {field: 'email', title: '邮箱'},
            {field: 'qps_limit', title: 'QPS限制', width: 100},
            {field: 'limit_action', title: '超限处理', templet: function(d) {
                return d.limit_action === 'queue' ? '排队等待' : '直接拒绝';
            }, width: 100},
            {field: 'limit_message', title: '超限提示'},
            {field: 'create_time', title: '创建时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 120}
        ]]
    });
    
    // 渲染IP限制表格
    var ipTable = table.render({
        elem: '#ipTable',
        url: '/admin/security/qps/ip/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'ip', title: 'IP地址'},
            {field: 'qps_limit', title: 'QPS限制', width: 100},
            {field: 'limit_action', title: '超限处理', templet: function(d) {
                return d.limit_action === 'queue' ? '排队等待' : '直接拒绝';
            }, width: 100},
            {field: 'limit_message', title: '超限提示'},
            {field: 'create_time', title: '创建时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }, width: 160},
            {title: '操作', toolbar: '#tableBar', width: 120}
        ]]
    });
    
    // API搜索按钮点击事件
    $('#searchBtn').click(function () {
        apiTable.reload({
            where: {
                keyword: $('#searchKeyword').val()
            },
            page: {curr: 1}
        });
    });
    
    // 用户搜索按钮点击事件
    $('#userSearchBtn').click(function () {
        userTable.reload({
            where: {
                keyword: $('#userSearchKeyword').val()
            },
            page: {curr: 1}
        });
    });
    
    // IP搜索按钮点击事件
    $('#ipSearchBtn').click(function () {
        ipTable.reload({
            where: {
                keyword: $('#ipSearchKeyword').val()
            },
            page: {curr: 1}
        });
    });
    
    // 添加API限制按钮点击事件
    $('#addApiLimitBtn').click(function() {
        showApiLimitDialog();
    });
    
    // 添加用户限制按钮点击事件
    $('#addUserLimitBtn').click(function() {
        showUserLimitDialog();
    });
    
    // 添加IP限制按钮点击事件
    $('#addIpLimitBtn').click(function() {
        showIpLimitDialog();
    });
    
    // 工具条点击事件
    table.on('tool(apiTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showApiLimitDialog(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该API限制吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/security/qps/api/delete', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        apiTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 工具条点击事件
    table.on('tool(userTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showUserLimitDialog(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该用户限制吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/security/qps/user/delete', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        userTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 工具条点击事件
    table.on('tool(ipTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showIpLimitDialog(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该IP限制吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/security/qps/ip/delete', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        ipTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 显示API限制弹窗
    function showApiLimitDialog(data) {
        admin.open({
            type: 1,
            title: data ? '编辑API限制' : '添加API限制',
            content: laytpl($('#apiLimitDialog').html()).render(data || {}),
            area: ['500px', '400px'],
            success: function (layero, dIndex) {
                // 加载API列表
                $.get('/admin/api/list_all', function(res) {
                    if (res.code === 0) {
                        var options = '<option value="">请选择API</option>';
                        for (var i = 0; i < res.data.length; i++) {
                            var selected = data && data.api_id == res.data[i].id ? 'selected' : '';
                            options += '<option value="' + res.data[i].id + '" ' + selected + '>' + res.data[i].name + ' (' + res.data[i].path + ')</option>';
                        }
                        $('select[name="api_id"]').html(options);
                        form.render('select');
                    }
                }, 'json');
                
                form.render();
                
                // 表单提交事件
                form.on('submit(apiLimitSubmit)', function (data) {
                    layer.load(2);
                    $.post('/admin/security/qps/api/save', data.field, function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            apiTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    return false;
                });
            }
        });
    }
    
    // 显示用户限制弹窗
    function showUserLimitDialog(data) {
        admin.open({
            type: 1,
            title: data ? '编辑用户限制' : '添加用户限制',
            content: laytpl($('#userLimitDialog').html()).render(data || {}),
            area: ['500px', '400px'],
            success: function (layero, dIndex) {
                // 加载用户列表
                $.get('/admin/user/list_all', function(res) {
                    if (res.code === 0) {
                        var options = '<option value="">请选择用户</option>';
                        for (var i = 0; i < res.data.length; i++) {
                            var selected = data && data.user_id == res.data[i].id ? 'selected' : '';
                            options += '<option value="' + res.data[i].id + '" ' + selected + '>' + res.data[i].username + ' (' + res.data[i].email + ')</option>';
                        }
                        $('select[name="user_id"]').html(options);
                        form.render('select');
                    }
                }, 'json');
                
                form.render();
                
                // 表单提交事件
                form.on('submit(userLimitSubmit)', function (data) {
                    layer.load(2);
                    $.post('/admin/security/qps/user/save', data.field, function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            userTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    return false;
                });
            }
        });
    }
    
    // 显示IP限制弹窗
    function showIpLimitDialog(data) {
        admin.open({
            type: 1,
            title: data ? '编辑IP限制' : '添加IP限制',
            content: laytpl($('#ipLimitDialog').html()).render(data || {}),
            area: ['500px', '400px'],
            success: function (layero, dIndex) {
                form.render();
                
                // 表单提交事件
                form.on('submit(ipLimitSubmit)', function (data) {
                    layer.load(2);
                    $.post('/admin/security/qps/ip/save', data.field, function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            ipTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    return false;
                });
            }
        });
    }
});
</script>
</body>
</html>