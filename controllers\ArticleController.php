<?php
/**
 * 文章控制器
 */
class ArticleController {
    private $db;
    
    public function __construct() {
        global $db;
        $this->db = $db;
    }
    
    /**
     * 获取文章列表
     */
    public function getList() {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        
        $where = "WHERE status = 1 ";
        $params = [];
        
        if (!empty($keyword)) {
            $where .= "AND (title LIKE ? OR summary LIKE ? OR content LIKE ?) ";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
        }
        
        if ($category_id > 0) {
            $where .= "AND category_id = ? ";
            $params[] = $category_id;
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM articles $where");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT a.*, c.name as category_name 
                                   FROM articles a 
                                   LEFT JOIN article_categories c ON a.category_id = c.id 
                                   $where 
                                   ORDER BY a.sort ASC, a.create_time DESC 
                                   LIMIT $offset, $limit");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'count' => $count,
                'list' => $list
            ]
        ]);
    }
    
    /**
     * 获取文章详情
     */
    public function getDetail() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '文章ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("SELECT a.*, c.name as category_name 
                                   FROM articles a 
                                   LEFT JOIN article_categories c ON a.category_id = c.id 
                                   WHERE a.id = ? AND a.status = 1");
        $stmt->execute([$id]);
        $article = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$article) {
            return json([
                'code' => 1,
                'msg' => '文章不存在或已被删除'
            ]);
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $article
        ]);
    }
    
    /**
     * 更新文章浏览量
     */
    public function updateView() {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '文章ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("UPDATE articles SET view_count = view_count + 1 WHERE id = ?");
        $stmt->execute([$id]);
        
        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }
    
    /**
     * 获取相关文章
     */
    public function getRelated() {
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        $current_id = isset($_GET['current_id']) ? intval($_GET['current_id']) : 0;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 5;
        
        if ($category_id <= 0) {
            return json([
                'code' => 1,
                'msg' => '分类ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("SELECT id, title, cover, create_time, view_count 
                                   FROM articles 
                                   WHERE category_id = ? AND id != ? AND status = 1 
                                   ORDER BY create_time DESC 
                                   LIMIT ?");
        $stmt->execute([$category_id, $current_id, $limit]);
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $list
        ]);
    }
    
    /**
     * 获取文章分类列表
     */
    public function getCategoryList() {
        $stmt = $this->db->prepare("SELECT id, name, description 
                                   FROM article_categories 
                                   ORDER BY sort ASC");
        $stmt->execute();
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $list
        ]);
    }
}