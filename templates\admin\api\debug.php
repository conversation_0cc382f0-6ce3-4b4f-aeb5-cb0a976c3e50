<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API在线调试 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .api-debug-card {
            margin-bottom: 15px;
        }
        .api-debug-card .layui-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .api-debug-card .layui-card-header .header-right {
            font-weight: normal;
        }
        .api-debug-card .layui-form-item:last-child {
            margin-bottom: 0;
        }
        .api-debug-result {
            background-color: #f8f8f8;
            padding: 10px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 500px;
            overflow-y: auto;
        }
        .api-debug-result.success {
            border-left: 3px solid #5FB878;
        }
        .api-debug-result.error {
            border-left: 3px solid #FF5722;
        }
        .api-debug-history-item {
            cursor: pointer;
            padding: 8px 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .api-debug-history-item:hover {
            background-color: #f8f8f8;
        }
        .api-debug-history-item .api-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .api-debug-history-item .api-time {
            font-size: 12px;
            color: #999;
        }
        .api-debug-history-item .api-status {
            float: right;
            padding: 2px 5px;
            font-size: 12px;
            border-radius: 2px;
        }
        .api-debug-history-item .api-status.success {
            background-color: #5FB878;
            color: #fff;
        }
        .api-debug-history-item .api-status.error {
            background-color: #FF5722;
            color: #fff;
        }
        .layui-tab-content {
            padding: 15px 0;
        }
        .code-format {
            color: #333;
        }
        .code-format .string { color: #008000; }
        .code-format .number { color: #0000ff; }
        .code-format .boolean { color: #b22222; }
        .code-format .null { color: #808080; }
        .code-format .key { color: #a52a2a; }
        .api-debug-info {
            margin-bottom: 10px;
            color: #666;
        }
        .api-debug-info span {
            margin-right: 15px;
        }
        .api-debug-info .layui-badge {
            margin-right: 5px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md9">
            <!-- API选择 -->
            <div class="layui-card api-debug-card">
                <div class="layui-card-header">
                    <span>选择API</span>
                    <div class="header-right">
                        <button id="saveAsBtn" class="layui-btn layui-btn-sm layui-btn-primary">另存为</button>
                        <button id="clearBtn" class="layui-btn layui-btn-sm layui-btn-primary">清空</button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">API分类</label>
                            <div class="layui-input-block">
                                <select id="apiCategory" lay-filter="apiCategory">
                                    <option value="">请选择API分类</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">API接口</label>
                            <div class="layui-input-block">
                                <select id="apiInterface" lay-filter="apiInterface">
                                    <option value="">请选择API接口</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 请求设置 -->
            <div class="layui-card api-debug-card">
                <div class="layui-card-header">请求设置</div>
                <div class="layui-card-body">
                    <div class="layui-form" lay-filter="requestForm">
                        <div class="layui-form-item">
                            <label class="layui-form-label">请求方式</label>
                            <div class="layui-input-block">
                                <input type="radio" name="method" value="GET" title="GET" checked>
                                <input type="radio" name="method" value="POST" title="POST">
                                <input type="radio" name="method" value="PUT" title="PUT">
                                <input type="radio" name="method" value="DELETE" title="DELETE">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">请求地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="url" placeholder="请输入请求地址" autocomplete="off" class="layui-input" readonly>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">API密钥</label>
                            <div class="layui-input-block">
                                <select name="api_key" lay-filter="apiKey">
                                    <option value="">请选择API密钥</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 请求参数 -->
            <div class="layui-card api-debug-card">
                <div class="layui-card-header">请求参数</div>
                <div class="layui-card-body">
                    <div class="layui-tab" lay-filter="paramTab">
                        <ul class="layui-tab-title">
                            <li class="layui-this">参数表单</li>
                            <li>原始数据</li>
                            <li>请求头</li>
                        </ul>
                        <div class="layui-tab-content">
                            <!-- 参数表单 -->
                            <div class="layui-tab-item layui-show">
                                <div id="paramFormContainer">
                                    <div class="layui-form-item">
                                        <div class="layui-form-mid layui-word-aux">暂无参数</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button id="addParamBtn" class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon">&#xe654;</i> 添加参数
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 原始数据 -->
                            <div class="layui-tab-item">
                                <div class="layui-form-item layui-form-text">
                                    <div class="layui-input-block">
                                        <textarea name="raw_data" placeholder="请输入原始数据，如JSON格式" class="layui-textarea" style="min-height: 150px;"></textarea>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">数据格式</label>
                                    <div class="layui-input-block">
                                        <input type="radio" name="content_type" value="application/json" title="JSON" checked>
                                        <input type="radio" name="content_type" value="application/xml" title="XML">
                                        <input type="radio" name="content_type" value="text/plain" title="TEXT">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 请求头 -->
                            <div class="layui-tab-item">
                                <div id="headerContainer">
                                    <div class="layui-form-item">
                                        <div class="layui-form-mid layui-word-aux">暂无自定义请求头</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button id="addHeaderBtn" class="layui-btn layui-btn-sm layui-btn-normal">
                                            <i class="layui-icon">&#xe654;</i> 添加请求头
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 发送请求 -->
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button id="sendBtn" class="layui-btn">发送请求</button>
                    <button id="resetBtn" type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
            
            <!-- 响应结果 -->
            <div class="layui-card api-debug-card" id="resultCard" style="display: none;">
                <div class="layui-card-header">响应结果</div>
                <div class="layui-card-body">
                    <div class="api-debug-info">
                        <span>状态码: <span id="statusCode" class="layui-badge">200</span></span>
                        <span>耗时: <span id="responseTime">0</span> ms</span>
                        <span>大小: <span id="responseSize">0</span> bytes</span>
                    </div>
                    <div class="layui-tab" lay-filter="resultTab">
                        <ul class="layui-tab-title">
                            <li class="layui-this">预览</li>
                            <li>原始数据</li>
                            <li>响应头</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <div id="resultPreview" class="api-debug-result"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="resultRaw" class="api-debug-result"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="resultHeaders" class="api-debug-result"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧面板 -->
        <div class="layui-col-md3">
            <!-- API文档 -->
            <div class="layui-card api-debug-card">
                <div class="layui-card-header">API文档</div>
                <div class="layui-card-body">
                    <div id="apiDocContent">
                        <div class="layui-form-mid layui-word-aux">请先选择API接口</div>
                    </div>
                </div>
            </div>
            
            <!-- 调试历史 -->
            <div class="layui-card api-debug-card">
                <div class="layui-card-header">
                    <span>调试历史</span>
                    <div class="header-right">
                        <button id="clearHistoryBtn" class="layui-btn layui-btn-xs layui-btn-primary">清空历史</button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <div id="historyList" style="max-height: 400px; overflow-y: auto;">
                        <div class="layui-form-mid layui-word-aux">暂无调试历史</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 参数表单项模板 -->
<script type="text/html" id="paramFormItemTpl">
    <div class="layui-form-item param-item">
        <div class="layui-inline" style="width: 30%;">
            <input type="text" name="param_name[]" placeholder="参数名" autocomplete="off" class="layui-input" value="{{d.name || ''}}">
        </div>
        <div class="layui-inline" style="width: 50%;">
            <input type="text" name="param_value[]" placeholder="参数值" autocomplete="off" class="layui-input" value="{{d.value || ''}}">
        </div>
        <div class="layui-inline" style="width: 10%;">
            <button type="button" class="layui-btn layui-btn-danger layui-btn-sm remove-param">
                <i class="layui-icon">&#xe640;</i>
            </button>
        </div>
    </div>
</script>

<!-- 请求头表单项模板 -->
<script type="text/html" id="headerFormItemTpl">
    <div class="layui-form-item header-item">
        <div class="layui-inline" style="width: 30%;">
            <input type="text" name="header_name[]" placeholder="Header名" autocomplete="off" class="layui-input" value="{{d.name || ''}}">
        </div>
        <div class="layui-inline" style="width: 50%;">
            <input type="text" name="header_value[]" placeholder="Header值" autocomplete="off" class="layui-input" value="{{d.value || ''}}">
        </div>
        <div class="layui-inline" style="width: 10%;">
            <button type="button" class="layui-btn layui-btn-danger layui-btn-sm remove-header">
                <i class="layui-icon">&#xe640;</i>
            </button>
        </div>
    </div>
</script>

<!-- 历史记录项模板 -->
<script type="text/html" id="historyItemTpl">
    <div class="api-debug-history-item" data-id="{{d.id}}">
        <div class="api-status {{d.status === 'success' ? 'success' : 'error'}}">{{d.status === 'success' ? '成功' : '失败'}}</div>
        <div class="api-name">{{d.api_name}}</div>
        <div class="api-time">{{d.time}}</div>
    </div>
</script>

<!-- 另存为弹窗 -->
<script type="text/html" id="saveAsDialog">
    <form id="saveAsForm" lay-filter="saveAsForm" class="layui-form model-form">
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-block">
                <input name="name" placeholder="请输入名称" type="text" class="layui-input" maxlength="100" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入描述" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="saveAsSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'laytpl', 'element'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var util = layui.util;
    var admin = layui.admin;
    var laytpl = layui.laytpl;
    var element = layui.element;
    
    // 加载API分类
    $.get('/admin/api/categories/list', function(res) {
        if (res.code === 0) {
            var options = '<option value="">请选择API分类</option>';
            for (var i = 0; i < res.data.length; i++) {
                options += '<option value="' + res.data[i].id + '">' + res.data[i].name + '</option>';
            }
            $('#apiCategory').html(options);
            form.render('select');
        }
    }, 'json');
    
    // 加载API密钥
    $.get('/admin/api/keys', function(res) {
        if (res.code === 0) {
            var options = '<option value="">请选择API密钥</option>';
            for (var i = 0; i < res.data.length; i++) {
                options += '<option value="' + res.data[i].key + '">' + res.data[i].name + '</option>';
            }
            $('select[name="api_key"]').html(options);
            form.render('select');
        }
    }, 'json');
    
    // 监听API分类选择
    form.on('select(apiCategory)', function(data) {
        if (!data.value) {
            $('#apiInterface').html('<option value="">请选择API接口</option>');
            form.render('select');
            return;
        }
        
        // 加载该分类下的API接口
        $.get('/admin/api/list_by_category', {
            category_id: data.value
        }, function(res) {
            if (res.code === 0) {
                var options = '<option value="">请选择API接口</option>';
                for (var i = 0; i < res.data.length; i++) {
                    options += '<option value="' + res.data[i].id + '">' + res.data[i].name + '</option>';
                }
                $('#apiInterface').html(options);
                form.render('select');
            }
        }, 'json');
    });
    
    // 监听API接口选择
    form.on('select(apiInterface)', function(data) {
        if (!data.value) {
            // 清空API文档
            $('#apiDocContent').html('<div class="layui-form-mid layui-word-aux">请先选择API接口</div>');
            // 清空参数表单
            $('#paramFormContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无参数</div></div>');
            return;
        }
        
        // 加载API详情
        $.get('/admin/api/detail', {
            id: data.value
        }, function(res) {
            if (res.code === 0) {
                var api = res.data;
                
                // 设置请求地址
                $('input[name="url"]').val(api.url);
                
                // 设置请求方式
                $('input[name="method"][value="' + api.method + '"]').prop('checked', true);
                form.render('radio');
                
                // 显示API文档
                var docHtml = '<h3>' + api.name + '</h3>';
                docHtml += '<p>' + api.description + '</p>';
                docHtml += '<h4>请求方式</h4>';
                docHtml += '<p>' + api.method + '</p>';
                docHtml += '<h4>请求地址</h4>';
                docHtml += '<p>' + api.url + '</p>';
                
                if (api.params && api.params.length > 0) {
                    docHtml += '<h4>请求参数</h4>';
                    docHtml += '<table class="layui-table">';
                    docHtml += '<thead><tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr></thead>';
                    docHtml += '<tbody>';
                    
                    // 生成参数表单
                    $('#paramFormContainer').empty();
                    
                    for (var i = 0; i < api.params.length; i++) {
                        var param = api.params[i];
                        docHtml += '<tr>';
                        docHtml += '<td>' + param.name + '</td>';
                        docHtml += '<td>' + param.type + '</td>';
                        docHtml += '<td>' + (param.required ? '是' : '否') + '</td>';
                        docHtml += '<td>' + param.description + '</td>';
                        docHtml += '</tr>';
                        
                        // 添加到参数表单
                        laytpl($('#paramFormItemTpl').html()).render({
                            name: param.name,
                            value: param.default_value || ''
                        }, function(html) {
                            $('#paramFormContainer').append(html);
                        });
                    }
                    
                    docHtml += '</tbody></table>';
                } else {
                    $('#paramFormContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无参数</div></div>');
                }
                
                if (api.response_example) {
                    docHtml += '<h4>返回示例</h4>';
                    docHtml += '<pre class="api-debug-result">' + formatJson(api.response_example) + '</pre>';
                }
                
                $('#apiDocContent').html(docHtml);
            }
        }, 'json');
    });
    
    // 添加参数按钮点击事件
    $('#addParamBtn').click(function() {
        laytpl($('#paramFormItemTpl').html()).render({}, function(html) {
            if ($('#paramFormContainer .layui-form-mid').length > 0) {
                $('#paramFormContainer').empty();
            }
            $('#paramFormContainer').append(html);
        });
    });
    
    // 删除参数按钮点击事件
    $(document).on('click', '.remove-param', function() {
        $(this).closest('.param-item').remove();
        if ($('#paramFormContainer .param-item').length === 0) {
            $('#paramFormContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无参数</div></div>');
        }
    });
    
    // 添加请求头按钮点击事件
    $('#addHeaderBtn').click(function() {
        laytpl($('#headerFormItemTpl').html()).render({}, function(html) {
            if ($('#headerContainer .layui-form-mid').length > 0) {
                $('#headerContainer').empty();
            }
            $('#headerContainer').append(html);
        });
    });
    
    // 删除请求头按钮点击事件
    $(document).on('click', '.remove-header', function() {
        $(this).closest('.header-item').remove();
        if ($('#headerContainer .header-item').length === 0) {
            $('#headerContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无自定义请求头</div></div>');
        }
    });
    
    // 发送请求按钮点击事件
    $('#sendBtn').click(function() {
        var apiId = $('#apiInterface').val();
        if (!apiId) {
            layer.msg('请先选择API接口', {icon: 2});
            return;
        }
        
        var apiName = $('#apiInterface option:selected').text();
        var method = $('input[name="method"]:checked').val();
        var url = $('input[name="url"]').val();
        var apiKey = $('select[name="api_key"]').val();
        
        // 收集参数
        var params = {};
        $('#paramFormContainer .param-item').each(function() {
            var name = $(this).find('input[name="param_name[]"]').val();
            var value = $(this).find('input[name="param_value[]"]').val();
            if (name) {
                params[name] = value;
            }
        });
        
        // 收集请求头
        var headers = {};
        $('#headerContainer .header-item').each(function() {
            var name = $(this).find('input[name="header_name[]"]').val();
            var value = $(this).find('input[name="header_value[]"]').val();
            if (name) {
                headers[name] = value;
            }
        });
        
        // 原始数据
        var rawData = $('textarea[name="raw_data"]').val();
        var contentType = $('input[name="content_type"]:checked').val();
        
        // 显示加载层
        var loadIndex = layer.load(2);
        
        // 发送请求
        $.ajax({
            url: '/admin/api/debug',
            type: 'POST',
            dataType: 'json',
            data: {
                api_id: apiId,
                method: method,
                url: url,
                api_key: apiKey,
                params: params,
                headers: headers,
                raw_data: rawData,
                content_type: contentType
            },
            success: function(res) {
                layer.close(loadIndex);
                
                // 显示结果卡片
                $('#resultCard').show();
                
                // 设置响应信息
                $('#statusCode').text(res.status_code).removeClass('layui-bg-green layui-bg-red')
                    .addClass(res.status_code >= 200 && res.status_code < 300 ? 'layui-bg-green' : 'layui-bg-red');
                $('#responseTime').text(res.response_time);
                $('#responseSize').text(res.response_size);
                
                // 设置响应内容
                var formattedResponse = '';
                try {
                    formattedResponse = formatJson(res.response);
                    $('#resultPreview').html(formattedResponse).addClass('success').removeClass('error');
                } catch (e) {
                    $('#resultPreview').text(res.response).addClass('error').removeClass('success');
                }
                
                $('#resultRaw').text(res.response);
                
                // 设置响应头
                var headersHtml = '';
                for (var key in res.headers) {
                    headersHtml += key + ': ' + res.headers[key] + '\n';
                }
                $('#resultHeaders').text(headersHtml);
                
                // 添加到历史记录
                var historyItem = {
                    id: new Date().getTime(),
                    api_name: apiName,
                    time: util.toDateString(new Date(), 'yyyy-MM-dd HH:mm:ss'),
                    status: res.status_code >= 200 && res.status_code < 300 ? 'success' : 'error',
                    request: {
                        method: method,
                        url: url,
                        api_key: apiKey,
                        params: params,
                        headers: headers,
                        raw_data: rawData,
                        content_type: contentType
                    },
                    response: {
                        status_code: res.status_code,
                        response_time: res.response_time,
                        response_size: res.response_size,
                        response: res.response,
                        headers: res.headers
                    }
                };
                
                addToHistory(historyItem);
            },
            error: function(xhr) {
                layer.close(loadIndex);
                layer.msg('请求发送失败', {icon: 2});
                
                // 显示结果卡片
                $('#resultCard').show();
                
                // 设置响应信息
                $('#statusCode').text(xhr.status).removeClass('layui-bg-green').addClass('layui-bg-red');
                $('#responseTime').text('0');
                $('#responseSize').text('0');
                
                // 设置响应内容
                $('#resultPreview').text('请求发送失败：' + xhr.statusText).addClass('error').removeClass('success');
                $('#resultRaw').text('请求发送失败：' + xhr.statusText);
                $('#resultHeaders').text('');
            }
        });
    });
    
        // 重置按钮点击事件
    $('#resetBtn').click(function() {
        // 清空参数表单
        $('#paramFormContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无参数</div></div>');
        
        // 清空请求头表单
        $('#headerContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无自定义请求头</div></div>');
        
        // 清空原始数据
        $('textarea[name="raw_data"]').val('');
        
        // 隐藏结果卡片
        $('#resultCard').hide();
    });
    
    // 清空按钮点击事件
    $('#clearBtn').click(function() {
        // 清空API选择
        $('#apiCategory').val('');
        $('#apiInterface').html('<option value="">请选择API接口</option>');
        form.render('select');
        
        // 清空请求设置
        $('input[name="method"][value="GET"]').prop('checked', true);
        $('input[name="url"]').val('');
        $('select[name="api_key"]').val('');
        form.render();
        
        // 清空参数表单
        $('#paramFormContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无参数</div></div>');
        
        // 清空请求头表单
        $('#headerContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无自定义请求头</div></div>');
        
        // 清空原始数据
        $('textarea[name="raw_data"]').val('');
        
        // 清空API文档
        $('#apiDocContent').html('<div class="layui-form-mid layui-word-aux">请先选择API接口</div>');
        
        // 隐藏结果卡片
        $('#resultCard').hide();
    });
    
    // 另存为按钮点击事件
    $('#saveAsBtn').click(function() {
        var apiId = $('#apiInterface').val();
        if (!apiId) {
            layer.msg('请先选择API接口', {icon: 2});
            return;
        }
        
        admin.open({
            type: 1,
            title: '保存调试配置',
            content: $('#saveAsDialog').html(),
            success: function (layero, dIndex) {
                // 表单提交事件
                form.on('submit(saveAsSubmit)', function (data) {
                    // 收集参数
                    var params = {};
                    $('#paramFormContainer .param-item').each(function() {
                        var name = $(this).find('input[name="param_name[]"]').val();
                        var value = $(this).find('input[name="param_value[]"]').val();
                        if (name) {
                            params[name] = value;
                        }
                    });
                    
                    // 收集请求头
                    var headers = {};
                    $('#headerContainer .header-item').each(function() {
                        var name = $(this).find('input[name="header_name[]"]').val();
                        var value = $(this).find('input[name="header_value[]"]').val();
                        if (name) {
                            headers[name] = value;
                        }
                    });
                    
                    var saveData = {
                        name: data.field.name,
                        description: data.field.description,
                        api_id: apiId,
                        method: $('input[name="method"]:checked').val(),
                        api_key: $('select[name="api_key"]').val(),
                        params: params,
                        headers: headers,
                        raw_data: $('textarea[name="raw_data"]').val(),
                        content_type: $('input[name="content_type"]:checked').val()
                    };
                    
                    layer.load(2);
                    $.post('/admin/api/debug_save', saveData, function(res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
            }
        });
    });
    
    // 清空历史按钮点击事件
    $('#clearHistoryBtn').click(function() {
        layer.confirm('确定要清空调试历史记录吗？', {
            skin: 'layui-layer-admin',
            shade: .1
        }, function(i) {
            layer.close(i);
            localStorage.removeItem('apiDebugHistory');
            $('#historyList').html('<div class="layui-form-mid layui-word-aux">暂无调试历史</div>');
        });
    });
    
    // 添加到历史记录
    function addToHistory(item) {
        var history = localStorage.getItem('apiDebugHistory');
        var historyList = history ? JSON.parse(history) : [];
        
        // 限制历史记录数量
        if (historyList.length >= 20) {
            historyList.pop();
        }
        
        // 添加到开头
        historyList.unshift(item);
        
        // 保存到本地存储
        localStorage.setItem('apiDebugHistory', JSON.stringify(historyList));
        
        // 更新历史记录列表
        updateHistoryList();
    }
    
    // 更新历史记录列表
    function updateHistoryList() {
        var history = localStorage.getItem('apiDebugHistory');
        if (!history) {
            $('#historyList').html('<div class="layui-form-mid layui-word-aux">暂无调试历史</div>');
            return;
        }
        
        var historyList = JSON.parse(history);
        if (historyList.length === 0) {
            $('#historyList').html('<div class="layui-form-mid layui-word-aux">暂无调试历史</div>');
            return;
        }
        
        var html = '';
        for (var i = 0; i < historyList.length; i++) {
            laytpl($('#historyItemTpl').html()).render(historyList[i], function(itemHtml) {
                html += itemHtml;
            });
        }
        
        $('#historyList').html(html);
    }
    
    // 历史记录项点击事件
    $(document).on('click', '.api-debug-history-item', function() {
        var id = $(this).data('id');
        var history = localStorage.getItem('apiDebugHistory');
        if (!history) {
            return;
        }
        
        var historyList = JSON.parse(history);
        var item = null;
        
        for (var i = 0; i < historyList.length; i++) {
            if (historyList[i].id == id) {
                item = historyList[i];
                break;
            }
        }
        
        if (!item) {
            return;
        }
        
        // 恢复请求设置
        $('input[name="method"][value="' + item.request.method + '"]').prop('checked', true);
        $('input[name="url"]').val(item.request.url);
        $('select[name="api_key"]').val(item.request.api_key);
        form.render();
        
        // 恢复参数表单
        $('#paramFormContainer').empty();
        var hasParams = false;
        for (var key in item.request.params) {
            hasParams = true;
            laytpl($('#paramFormItemTpl').html()).render({
                name: key,
                value: item.request.params[key]
            }, function(html) {
                $('#paramFormContainer').append(html);
            });
        }
        
        if (!hasParams) {
            $('#paramFormContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无参数</div></div>');
        }
        
        // 恢复请求头表单
        $('#headerContainer').empty();
        var hasHeaders = false;
        for (var key in item.request.headers) {
            hasHeaders = true;
            laytpl($('#headerFormItemTpl').html()).render({
                name: key,
                value: item.request.headers[key]
            }, function(html) {
                $('#headerContainer').append(html);
            });
        }
        
        if (!hasHeaders) {
            $('#headerContainer').html('<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">暂无自定义请求头</div></div>');
        }
        
        // 恢复原始数据
        $('textarea[name="raw_data"]').val(item.request.raw_data);
        $('input[name="content_type"][value="' + item.request.content_type + '"]').prop('checked', true);
        form.render('radio');
        
        // 显示结果卡片
        $('#resultCard').show();
        
        // 设置响应信息
        $('#statusCode').text(item.response.status_code).removeClass('layui-bg-green layui-bg-red')
            .addClass(item.response.status_code >= 200 && item.response.status_code < 300 ? 'layui-bg-green' : 'layui-bg-red');
        $('#responseTime').text(item.response.response_time);
        $('#responseSize').text(item.response.response_size);
        
        // 设置响应内容
        var formattedResponse = '';
        try {
            formattedResponse = formatJson(item.response.response);
            $('#resultPreview').html(formattedResponse).addClass('success').removeClass('error');
        } catch (e) {
            $('#resultPreview').text(item.response.response).addClass('error').removeClass('success');
        }
        
        $('#resultRaw').text(item.response.response);
        
        // 设置响应头
        var headersHtml = '';
        for (var key in item.response.headers) {
            headersHtml += key + ': ' + item.response.headers[key] + '\n';
        }
        $('#resultHeaders').text(headersHtml);
    });
    
    // 格式化JSON
    function formatJson(json) {
        if (typeof json === 'string') {
            json = JSON.parse(json);
        }
        
        var formatted = JSON.stringify(json, null, 4);
        
        // 添加语法高亮
        formatted = formatted.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function(match) {
            var cls = 'number';
            if (/^"/.test(match)) {
                if (/:$/.test(match)) {
                    cls = 'key';
                } else {
                    cls = 'string';
                }
            } else if (/true|false/.test(match)) {
                cls = 'boolean';
            } else if (/null/.test(match)) {
                cls = 'null';
            }
            return '<span class="' + cls + '">' + match + '</span>';
        });
        
        return '<pre class="code-format">' + formatted + '</pre>';
    }
    
    // 初始化加载历史记录
    updateHistoryList();
});
</script>
</body>
</html>
