-- 继续工单表定义
CREATE TABLE `tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `priority` tinyint(4) NOT NULL DEFAULT '1' COMMENT '优先级：1低，2中，3高',
  `admin_id` int(11) DEFAULT NULL COMMENT '处理管理员ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
  PRIMARY KEY (`id`),
  <PERSON>EY `user_id` (`user_id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单表';

-- 工单回复表
CREATE TABLE `ticket_replies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL COMMENT '工单ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `content` text NOT NULL COMMENT '回复内容',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `ticket_id` (`ticket_id`),
  KEY `user_id` (`user_id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单回复表';

-- IP黑白名单表
CREATE TABLE `ip_lists` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` varchar(50) NOT NULL COMMENT 'IP地址',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '类型：1白名单，2黑名单',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP黑白名单表';

-- 模板表
CREATE TABLE `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '模板名称',
  `directory` varchar(50) NOT NULL COMMENT '模板目录',
  `description` varchar(255) DEFAULT NULL COMMENT '模板描述',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT '缩略图',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `directory` (`directory`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板表';

-- 缓存表
CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL COMMENT '缓存键',
  `value` text NOT NULL COMMENT '缓存值',
  `expiration` int(11) NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存表';

-- 系统日志表
CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` varchar(20) NOT NULL COMMENT '日志级别',
  `message` text NOT NULL COMMENT '日志消息',
  `context` text COMMENT '上下文信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- 插入初始数据

-- 插入管理员
INSERT INTO `admins` (`username`, `password`, `email`, `real_name`, `role`, `status`, `created_at`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '系统管理员', 2, 1, NOW());

-- 插入API分类
INSERT INTO `api_categories` (`name`, `description`, `icon`, `sort`, `status`, `created_at`) VALUES
('工具类', '实用工具API', 'layui-icon-util', 1, 1, NOW()),
('天气类', '天气预报相关API', 'layui-icon-water', 2, 1, NOW()),
('金融类', '金融数据相关API', 'layui-icon-rmb', 3, 1, NOW()),
('地图类', '地图和位置服务API', 'layui-icon-location', 4, 1, NOW()),
('通讯类', '短信和通讯相关API', 'layui-icon-cellphone', 5, 1, NOW());

-- 插入VIP等级
INSERT INTO `vip_levels` (`name`, `level`, `price`, `discount`, `qps_limit`, `description`, `status`, `created_at`) VALUES
('普通会员', 1, 9.90, 95.00, 15, '基础API调用权限，享受95折优惠', 1, NOW()),
('高级会员', 2, 29.90, 90.00, 30, '全站API调用权限，享受9折优惠', 1, NOW()),
('钻石会员', 3, 99.90, 80.00, 50, '全站API无限调用，享受8折优惠', 1, NOW());

-- 插入商家等级
INSERT INTO `merchant_levels` (`name`, `level`, `price`, `commission_rate`, `qps_limit`, `api_limit`, `description`, `status`, `created_at`) VALUES
('普通商家', 1, 0.00, 70.00, 20, 5, '基础API发布权限，70%分成比例', 1, NOW()),
('金牌商家', 2, 99.90, 80.00, 50, 20, '高级API发布权限，80%分成比例', 1, NOW()),
('钻石商家', 3, 299.90, 90.00, 100, 50, '无限API发布权限，90%分成比例', 1, NOW());

-- 插入网站配置
INSERT INTO `site_config` (`site_name`, `site_keywords`, `site_description`, `site_url`, `api_base_url`, `copyright`, `admin_email`, `upload_allowed_types`, `created_at`) VALUES
('API商业系统', 'API,接口,开发者,工具', 'API商业系统是一个多功能的API接口调用和管理平台', 'http://localhost', 'http://localhost/api.php', 'Copyright © 2023 API商业系统 All Rights Reserved', '<EMAIL>', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar', NOW());

-- 插入导航菜单
INSERT INTO `nav_menus` (`name`, `url`, `icon`, `parent_id`, `position`, `sort`, `status`, `created_at`) VALUES
('首页', '/', 'layui-icon-home', 0, 'top', 1, 1, NOW()),
('API市场', '/api/market', 'layui-icon-app', 0, 'top', 2, 1, NOW()),
('开发文档', '/docs', 'layui-icon-read', 0, 'top', 3, 1, NOW()),
('价格', '/pricing', 'layui-icon-rmb', 0, 'top', 4, 1, NOW()),
('关于我们', '/about', 'layui-icon-about', 0, 'footer', 1, 1, NOW()),
('联系我们', '/contact', 'layui-icon-dialogue', 0, 'footer', 2, 1, NOW()),
('服务条款', '/terms', 'layui-icon-file', 0, 'footer', 3, 1, NOW()),
('隐私政策', '/privacy', 'layui-icon-auz', 0, 'footer', 4, 1, NOW());

-- 插入文章分类
INSERT INTO `article_categories` (`name`, `description`, `sort`, `status`, `created_at`) VALUES
('新闻公告', '网站新闻和公告', 1, 1, NOW()),
('使用教程', 'API使用教程', 2, 1, NOW()),
('开发指南', '开发者指南', 3, 1, NOW()),
('常见问题', '常见问题解答', 4, 1, NOW());

-- 插入单页
INSERT INTO `pages` (`title`, `slug`, `content`, `keywords`, `description`, `status`, `created_at`) VALUES
('关于我们', 'about', '<h2>关于API商业系统</h2><p>API商业系统是一个功能强大的API接口调用和管理平台，为开发者和企业提供便捷的API服务。</p>', '关于我们,API系统', '了解API商业系统的详细信息', 1, NOW()),
('联系我们', 'contact', '<h2>联系我们</h2><p>如果您有任何问题或建议，请通过以下方式联系我们：</p><p>邮箱：<EMAIL></p>', '联系我们,客服', '联系API商业系统客服', 1, NOW()),
('服务条款', 'terms', '<h2>服务条款</h2><p>使用本网站服务前，请仔细阅读以下条款...</p>', '服务条款,使用条款', 'API商业系统服务条款', 1, NOW()),
('隐私政策', 'privacy', '<h2>隐私政策</h2><p>我们重视您的隐私，本政策说明了我们如何收集和使用您的个人信息...</p>', '隐私政策,数据保护', 'API商业系统隐私政策', 1, NOW());

-- 插入模板
INSERT INTO `templates` (`name`, `directory`, `description`, `author`, `version`, `is_default`, `status`, `created_at`) VALUES
('默认模板', 'default', '系统默认模板', 'API系统', '1.0.0', 1, 1, NOW()),
('蓝色主题', 'blue', '蓝色主题模板', 'API系统', '1.0.0', 0, 1, NOW()),
('暗黑主题', 'dark', '暗黑主题模板', 'API系统', '1.0.0', 0, 1, NOW());