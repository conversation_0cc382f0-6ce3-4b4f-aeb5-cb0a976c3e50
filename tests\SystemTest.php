<?php
/**
 * 系统测试类
 * API管理系统 - 系统功能测试
 */

require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/System.php';
require_once __DIR__ . '/../modules/User.php';
require_once __DIR__ . '/../modules/ApiInterface.php';
require_once __DIR__ . '/../modules/Order.php';
require_once __DIR__ . '/../modules/Logger.php';

class SystemTest {
    private $db;
    private $logger;
    private $testResults = [];
    
    public function __construct() {
        $this->db = new Database();
        $this->logger = new Logger();
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests() {
        echo "开始系统测试...\n";
        echo "==========================================\n";
        
        $this->testDatabaseConnection();
        $this->testUserModule();
        $this->testApiModule();
        $this->testOrderModule();
        $this->testSecurityModule();
        $this->testPerformance();
        
        $this->generateTestReport();
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection() {
        echo "测试数据库连接...\n";
        
        try {
            $result = $this->db->fetchOne("SELECT 1 as test");
            if ($result && $result['test'] == 1) {
                $this->addTestResult('数据库连接', true, '连接成功');
            } else {
                $this->addTestResult('数据库连接', false, '连接失败');
            }
        } catch (Exception $e) {
            $this->addTestResult('数据库连接', false, $e->getMessage());
        }
    }
    
    /**
     * 测试用户模块
     */
    private function testUserModule() {
        echo "测试用户模块...\n";
        
        $userModel = new User();
        
        // 测试用户注册
        try {
            $testUser = [
                'username' => 'test_user_' . time(),
                'email' => 'test' . time() . '@example.com',
                'password' => 'test123456',
                'phone' => '13800138000'
            ];
            
            $userId = $userModel->register($testUser);
            if ($userId) {
                $this->addTestResult('用户注册', true, '注册成功，用户ID: ' . $userId);
                
                // 测试用户登录
                $loginResult = $userModel->login($testUser['username'], $testUser['password']);
                if ($loginResult) {
                    $this->addTestResult('用户登录', true, '登录成功');
                } else {
                    $this->addTestResult('用户登录', false, '登录失败');
                }
                
                // 清理测试数据
                $userModel->delete($userId);
                
            } else {
                $this->addTestResult('用户注册', false, '注册失败');
            }
        } catch (Exception $e) {
            $this->addTestResult('用户注册', false, $e->getMessage());
        }
    }
    
    /**
     * 测试API模块
     */
    private function testApiModule() {
        echo "测试API模块...\n";
        
        $apiModel = new ApiInterface();
        
        try {
            // 测试API创建
            $testApi = [
                'name' => 'Test API',
                'path' => '/test/api',
                'method' => 'GET',
                'description' => '测试API接口',
                'category_id' => 1,
                'merchant_id' => 1,
                'price' => 0.01,
                'status' => 1
            ];
            
            $apiId = $apiModel->create($testApi);
            if ($apiId) {
                $this->addTestResult('API创建', true, 'API创建成功，ID: ' . $apiId);
                
                // 测试API调用
                $callResult = $apiModel->callApi($apiId, 1, []);
                if ($callResult) {
                    $this->addTestResult('API调用', true, 'API调用成功');
                } else {
                    $this->addTestResult('API调用', false, 'API调用失败');
                }
                
                // 清理测试数据
                $apiModel->delete($apiId);
                
            } else {
                $this->addTestResult('API创建', false, 'API创建失败');
            }
        } catch (Exception $e) {
            $this->addTestResult('API模块', false, $e->getMessage());
        }
    }
    
    /**
     * 测试订单模块
     */
    private function testOrderModule() {
        echo "测试订单模块...\n";
        
        $orderModel = new Order();
        
        try {
            // 测试订单创建
            $testOrder = [
                'user_id' => 1,
                'api_id' => 1,
                'merchant_id' => 1,
                'quantity' => 1,
                'unit_price' => 0.01
            ];
            
            $orderId = $orderModel->createOrder($testOrder);
            if ($orderId) {
                $this->addTestResult('订单创建', true, '订单创建成功，ID: ' . $orderId);
                
                // 测试订单支付
                try {
                    $payResult = $orderModel->payOrder($orderId, 'balance');
                    $this->addTestResult('订单支付', true, '订单支付成功');
                } catch (Exception $e) {
                    $this->addTestResult('订单支付', false, $e->getMessage());
                }
                
            } else {
                $this->addTestResult('订单创建', false, '订单创建失败');
            }
        } catch (Exception $e) {
            $this->addTestResult('订单模块', false, $e->getMessage());
        }
    }
    
    /**
     * 测试安全模块
     */
    private function testSecurityModule() {
        echo "测试安全模块...\n";
        
        try {
            // 测试SQL注入检测
            $sqlInjection = "'; DROP TABLE users; --";
            $isInjection = $this->detectSQLInjection($sqlInjection);
            if ($isInjection) {
                $this->addTestResult('SQL注入检测', true, '成功检测到SQL注入');
            } else {
                $this->addTestResult('SQL注入检测', false, '未能检测到SQL注入');
            }
            
            // 测试XSS检测
            $xssScript = "<script>alert('xss')</script>";
            $isXSS = $this->detectXSS($xssScript);
            if ($isXSS) {
                $this->addTestResult('XSS检测', true, '成功检测到XSS攻击');
            } else {
                $this->addTestResult('XSS检测', false, '未能检测到XSS攻击');
            }
            
        } catch (Exception $e) {
            $this->addTestResult('安全模块', false, $e->getMessage());
        }
    }
    
    /**
     * 测试系统性能
     */
    private function testPerformance() {
        echo "测试系统性能...\n";
        
        // 测试数据库查询性能
        $startTime = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            $this->db->fetchOne("SELECT 1");
        }
        $endTime = microtime(true);
        $dbTime = ($endTime - $startTime) * 1000;
        
        if ($dbTime < 1000) { // 100次查询小于1秒
            $this->addTestResult('数据库性能', true, "100次查询耗时: {$dbTime}ms");
        } else {
            $this->addTestResult('数据库性能', false, "100次查询耗时过长: {$dbTime}ms");
        }
        
        // 测试内存使用
        $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB
        if ($memoryUsage < 50) { // 内存使用小于50MB
            $this->addTestResult('内存使用', true, "内存使用: {$memoryUsage}MB");
        } else {
            $this->addTestResult('内存使用', false, "内存使用过高: {$memoryUsage}MB");
        }
    }
    
    /**
     * SQL注入检测
     */
    private function detectSQLInjection($input) {
        $patterns = [
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
            '/(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i',
            '/[\'";].*(-{2}|\/\*)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * XSS检测
     */
    private function detectXSS($input) {
        $patterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe[^>]*>.*?<\/iframe>/is'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 添加测试结果
     */
    private function addTestResult($testName, $success, $message) {
        $this->testResults[] = [
            'name' => $testName,
            'success' => $success,
            'message' => $message,
            'time' => date('Y-m-d H:i:s')
        ];
        
        $status = $success ? '✓ 通过' : '✗ 失败';
        echo "  {$testName}: {$status} - {$message}\n";
    }
    
    /**
     * 生成测试报告
     */
    private function generateTestReport() {
        echo "\n==========================================\n";
        echo "测试报告\n";
        echo "==========================================\n";
        
        $totalTests = count($this->testResults);
        $passedTests = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passedTests);
        $failedCount = $totalTests - $passedCount;
        $successRate = $totalTests > 0 ? round(($passedCount / $totalTests) * 100, 2) : 0;
        
        echo "总测试数: {$totalTests}\n";
        echo "通过数: {$passedCount}\n";
        echo "失败数: {$failedCount}\n";
        echo "成功率: {$successRate}%\n";
        
        if ($failedCount > 0) {
            echo "\n失败的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "  - {$result['name']}: {$result['message']}\n";
                }
            }
        }
        
        // 保存测试报告到文件
        $this->saveTestReport($totalTests, $passedCount, $failedCount, $successRate);
        
        echo "\n测试完成！\n";
    }
    
    /**
     * 保存测试报告
     */
    private function saveTestReport($total, $passed, $failed, $successRate) {
        $reportData = [
            'test_time' => date('Y-m-d H:i:s'),
            'total_tests' => $total,
            'passed_tests' => $passed,
            'failed_tests' => $failed,
            'success_rate' => $successRate,
            'details' => $this->testResults
        ];
        
        $reportFile = __DIR__ . '/../logs/test_report_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo "测试报告已保存到: {$reportFile}\n";
    }
}

// 如果直接运行此文件，则执行测试
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $test = new SystemTest();
    $test->runAllTests();
}