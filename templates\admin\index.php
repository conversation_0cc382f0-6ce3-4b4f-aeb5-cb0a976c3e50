<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>管理后台 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!-- 头部 -->
    <div class="layui-header">
        <div class="layui-logo">
            <?php if (!empty($siteConfig['admin_logo'])): ?>
            <img src="<?php echo $siteConfig['admin_logo']; ?>" alt="<?php echo $siteConfig['site_name']; ?>">
            <?php else: ?>
            <h1><?php echo $siteConfig['site_name']; ?> 管理后台</h1>
            <?php endif; ?>
        </div>
        <ul class="layui-nav layui-layout-left">
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;" data-refresh="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
            </li>
        </ul>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;" data-clear="清理缓存"><i class="layui-icon layui-icon-delete"></i></a>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;">
                    <img src="<?php echo $admin['avatar'] ?: '/assets/images/default-avatar.png'; ?>" class="layui-nav-img">
                    <?php echo $admin['username']; ?>
                </a>
                <dl class="layui-nav-child">
                    <dd><a href="javascript:;" data-url="/admin/profile">个人信息</a></dd>
                    <dd><a href="javascript:;" data-url="/admin/password">修改密码</a></dd>
                    <hr>
                    <dd><a href="/admin/logout">退出登录</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;" data-toggle="fullscreen"><i class="layui-icon layui-icon-screen-full"></i></a>
            </li>
        </ul>
    </div>
    
    <!-- 侧边栏 -->
    <div class="layui-side">
        <div class="layui-side-scroll">
            <ul class="layui-nav layui-nav-tree" lay-filter="admin-side-nav" lay-shrink="all">
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-home"></i>&emsp;<cite>控制台</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/dashboard">系统概览</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-set"></i>&emsp;<cite>系统设置</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/settings/site">网站设置</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/settings/security">安全设置</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/settings/payment">支付设置</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/settings/email">邮件设置</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/settings/upload">上传设置</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/settings/api">接口设置</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-user"></i>&emsp;<cite>用户管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/users">用户列表</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/users/vip">会员管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/users/apikeys">API密钥管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-component"></i>&emsp;<cite>API管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/api/categories">分类管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/api/list">接口列表</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/api/add">添加接口</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/api/params">参数管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/api/docs">文档管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-cart"></i>&emsp;<cite>商家管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/merchants">商家列表</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/merchants/levels">等级管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/merchants/audit">入驻审核</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/merchants/settlement">结算管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-rmb"></i>&emsp;<cite>交易管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/orders">订单列表</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/transactions">交易记录</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/recharge">充值记录</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/withdraw">提现管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-chart"></i>&emsp;<cite>统计分析</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/stats/api">接口调用统计</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/stats/income">收入统计</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/stats/user">用户统计</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/stats/logs">请求日志分析</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-template"></i>&emsp;<cite>内容管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/content/banners">轮播图管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/content/articles">文章管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/content/categories">文章分类</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/content/pages">单页管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/content/nav">导航管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-file"></i>&emsp;<cite>文件管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/files">文件列表</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/files/upload">上传文件</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/files/categories">分类管理</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-auz"></i>&emsp;<cite>安全管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/security/admins">管理员</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/security/roles">角色管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/security/permissions">权限管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/security/logs">操作日志</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/security/ip">IP黑白名单</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-service"></i>&emsp;<cite>工单系统</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/tickets">工单列表</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/tickets/categories">工单分类</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/tickets/settings">工单设置</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-template-1"></i>&emsp;<cite>模板管理</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/templates">模板列表</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/templates/custom">自定义样式</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;"><i class="layui-icon layui-icon-util"></i>&emsp;<cite>系统工具</cite></a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="/admin/tools/cache">缓存管理</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/tools/backup">数据备份</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/tools/restore">数据恢复</a></dd>
                        <dd><a href="javascript:;" data-url="/admin/tools/optimize">优化表</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- 主体部分 -->
    <div class="layui-body">
        <!-- 页面标签 -->
        <div class="layui-tab" lay-filter="admin-pagetabs" lay-allowClose="true">
            <ul class="layui-tab-title">
                <li class="layui-this" data-url="/admin/dashboard"><i class="layui-icon layui-icon-home"></i> 控制台</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <iframe src="/admin/dashboard" frameborder="0" class="admin-iframe"></iframe>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <div class="layui-footer">
        Copyright © <?php echo date('Y'); ?> <?php echo $siteConfig['site_name']; ?> 版权所有
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['index'], function() {
    var $ = layui.jquery;
    var index = layui.index;
    
    // 默认加载主页
    index.loadHome({
        menuPath: '/admin/dashboard',
        menuName: '<i class="layui-icon layui-icon-home"></i> 控制台'
    });
    
    // 消息通知
    var noticeInterval = setInterval(function() {
        $.get('/admin/notice/check', function(res) {
            if (res.code === 0 && res.data.count > 0) {
                layer.open({
                    type: 1,
                    title: '系统通知',
                    area: ['350px', 'auto'],
                    shade: 0,
                    offset: 'rb',
                    skin: 'notice-layer',
                    btn: ['查看全部', '知道了'],
                    content: '<div class="notice-content">' + res.data.message + '</div>',
                    success: function(layero, index) {
                        var btn = layero.find('.layui-layer-btn0');
                        btn.attr('data-url', '/admin/notice').attr('data-title', '系统通知');
                        btn.click(function() {
                            layer.close(index);
                            layui.index.openTab($(this));
                        });
                    },
                    yes: function(index) {
                        layer.close(index);
                    }
                });
            }
        });
    }, 60000);
    
    // 清除定时器
    window.onbeforeunload = function() {
        clearInterval(noticeInterval);
    };
});
</script>
</body>
</html>