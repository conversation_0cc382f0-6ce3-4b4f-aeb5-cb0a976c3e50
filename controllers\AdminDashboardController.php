<?php
/**
 * 管理员仪表盘控制器
 */
class AdminDashboardController extends BaseController
{
    /**
     * 仪表盘首页
     */
    public function index()
    {
        // 检查管理员登录状态
        $this->checkAdminLogin();
        
        // 获取统计数据
        $stats = $this->getStats();
        
        // 渲染页面
        $this->render('admin/dashboard', [
            'stats' => $stats
        ]);
    }
    
    /**
     * 获取统计数据
     * 
     * @return array
     */
    private function getStats()
    {
        $stats = [];
        
        try {
            // 用户统计
            $userStats = $this->db->query(
                "SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN create_time > ? THEN 1 END) as new_users_today
                FROM users",
                [strtotime('today')],
                true
            );
            
            $stats['users'] = [
                'total' => $userStats['total_users'] ?? 0,
                'today' => $userStats['new_users_today'] ?? 0
            ];
            
            // API统计
            $apiStats = $this->db->query(
                "SELECT 
                    COUNT(*) as total_apis
                FROM apis",
                [],
                true
            );
            
            $stats['apis'] = [
                'total' => $apiStats['total_apis'] ?? 0
            ];
            
            // 商家统计
            $merchantStats = $this->db->query(
                "SELECT 
                    COUNT(*) as total_merchants,
                    COUNT(CASE WHEN status = 0 THEN 1 END) as pending_merchants
                FROM merchants",
                [],
                true
            );
            
            $stats['merchants'] = [
                'total' => $merchantStats['total_merchants'] ?? 0,
                'pending' => $merchantStats['pending_merchants'] ?? 0
            ];
            
            // 订单统计
            $orderStats = $this->db->query(
                "SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN create_time > ? THEN 1 END) as orders_today,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN create_time > ? THEN amount ELSE 0 END) as amount_today
                FROM orders WHERE status = 1",
                [strtotime('today'), strtotime('today')],
                true
            );
            
            $stats['orders'] = [
                'total' => $orderStats['total_orders'] ?? 0,
                'today' => $orderStats['orders_today'] ?? 0,
                'amount_total' => $orderStats['total_amount'] ?? 0,
                'amount_today' => $orderStats['amount_today'] ?? 0
            ];
            
            // API调用统计
            $apiCallStats = $this->db->query(
                "SELECT 
                    COUNT(*) as total_calls,
                    COUNT(CASE WHEN create_time > ? THEN 1 END) as calls_today
                FROM api_requests",
                [strtotime('today')],
                true
            );
            
            $stats['api_calls'] = [
                'total' => $apiCallStats['total_calls'] ?? 0,
                'today' => $apiCallStats['calls_today'] ?? 0
            ];
            
            // 工单统计
            $ticketStats = $this->db->query(
                "SELECT 
                    COUNT(*) as total_tickets,
                    COUNT(CASE WHEN status = 0 THEN 1 END) as pending_tickets
                FROM tickets",
                [],
                true
            );
            
            $stats['tickets'] = [
                'total' => $ticketStats['total_tickets'] ?? 0,
                'pending' => $ticketStats['pending_tickets'] ?? 0
            ];
            
        } catch (Exception $e) {
            // 如果数据库表不存在，使用默认值
            $stats = [
                'users' => ['total' => 0, 'today' => 0],
                'apis' => ['total' => 0],
                'merchants' => ['total' => 0, 'pending' => 0],
                'orders' => ['total' => 0, 'today' => 0, 'amount_total' => 0, 'amount_today' => 0],
                'api_calls' => ['total' => 0, 'today' => 0],
                'tickets' => ['total' => 0, 'pending' => 0]
            ];
        }
        
        return $stats;
    }
}