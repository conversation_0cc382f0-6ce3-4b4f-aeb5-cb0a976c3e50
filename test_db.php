<?php
/**
 * 数据库测试脚本
 */
require_once 'classes/Database.php';

try {
    $db = Database::getInstance();
    echo "数据库连接成功！\n";
    
    // 检查管理员用户
    $admin = $db->query("SELECT * FROM users WHERE role = 'admin'", [], true);
    
    if ($admin) {
        echo "管理员用户存在：\n";
        echo "用户名: " . $admin['username'] . "\n";
        echo "邮箱: " . $admin['email'] . "\n";
        echo "状态: " . ($admin['status'] ? '启用' : '禁用') . "\n";
    } else {
        echo "管理员用户不存在，正在创建...\n";
        
        // 创建管理员用户
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $db->execute(
            "INSERT INTO users (username, email, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, NOW())",
            ['admin', '<EMAIL>', $password, 'admin', 1]
        );
        
        echo "管理员用户创建成功！\n";
        echo "用户名: admin\n";
        echo "密码: admin123\n";
    }
    
    // 检查表结构
    $tables = $db->query("SHOW TABLES");
    echo "\n数据库表列表：\n";
    foreach ($tables as $table) {
        echo "- " . array_values($table)[0] . "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
