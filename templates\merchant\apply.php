<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>商家入驻申请 - API商业系统</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/site.css"/>
</head>
<body>
<!-- 引入网站头部 -->
<div id="header"></div>

<div class="layui-container" style="padding-top: 30px; padding-bottom: 30px;">
    <div class="layui-card">
        <div class="layui-card-header">商家入驻申请</div>
        <div class="layui-card-body">
            <div class="layui-tab">
                <ul class="layui-tab-title">
                    <li class="layui-this">入驻须知</li>
                    <li>申请表单</li>
                    <li>申请状态</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 入驻须知 -->
                    <div class="layui-tab-item layui-show">
                        <div class="layui-text">
                            <h3>商家入驻须知</h3>
                            <p>欢迎申请成为我们的商家合作伙伴！请仔细阅读以下入驻须知：</p>
                            
                            <h4>一、入驻条件</h4>
                            <ol>
                                <li>具有合法的经营资质，能够提供营业执照等相关证件；</li>
                                <li>具有稳定可靠的API服务能力，能够提供高质量的API服务；</li>
                                <li>具有良好的信誉和口碑，无不良记录；</li>
                                <li>同意并遵守平台的各项规则和协议。</li>
                            </ol>
                            
                            <h4>二、入驻流程</h4>
                            <ol>
                                <li>提交入驻申请表单；</li>
                                <li>平台审核申请材料（1-3个工作日）；</li>
                                <li>审核通过后，缴纳保证金（可选）；</li>
                                <li>开通商家账号，配置商家信息；</li>
                                <li>上架API服务，开始运营。</li>
                            </ol>
                            
                            <h4>三、商家权益</h4>
                            <ol>
                                <li>获得平台流量支持，提高API服务曝光度；</li>
                                <li>使用平台提供的API管理工具，简化运营流程；</li>
                                <li>享受平台提供的结算服务，保障资金安全；</li>
                                <li>参与平台举办的各类活动，提升品牌影响力；</li>
                                <li>获得平台技术支持，解决运营过程中的问题。</li>
                            </ol>
                            
                            <h4>四、商家义务</h4>
                            <ol>
                                <li>提供真实、准确、完整的入驻信息；</li>
                                <li>确保API服务的稳定性和可靠性；</li>
                                <li>及时响应用户的问题和反馈；</li>
                                <li>遵守平台的各项规则和协议；</li>
                                <li>保护用户的隐私和数据安全。</li>
                            </ol>
                            
                            <h4>五、违规处罚</h4>
                            <p>如商家违反平台规则或协议，平台将视情节轻重采取以下处罚措施：</p>
                            <ol>
                                <li>警告；</li>
                                <li>暂停部分API服务；</li>
                                <li>暂停商家账号；</li>
                                <li>永久封禁商家账号；</li>
                                <li>扣除保证金；</li>
                                <li>追究法律责任。</li>
                            </ol>
                            
                            <div class="layui-form" style="margin-top: 30px;">
                                <div class="layui-form-item">
                                    <input type="checkbox" name="agree" lay-skin="primary" title="我已阅读并同意以上入驻须知" lay-verify="required">
                                </div>
                                <div class="layui-form-item">
                                    <button class="layui-btn" id="nextStep">下一步</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 申请表单 -->
                    <div class="layui-tab-item">
                        <form class="layui-form" id="applyForm" lay-filter="applyForm">
                            <div class="layui-form-item">
                                <label class="layui-form-label">商家名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="name" required lay-verify="required" placeholder="请输入商家名称" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系人</label>
                                <div class="layui-input-block">
                                    <input type="text" name="contact" required lay-verify="required" placeholder="请输入联系人姓名" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系电话</label>
                                <div class="layui-input-block">
                                    <input type="text" name="phone" required lay-verify="required|phone" placeholder="请输入联系电话" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系邮箱</label>
                                <div class="layui-input-block">
                                    <input type="text" name="email" required lay-verify="required|email" placeholder="请输入联系邮箱" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系地址</label>
                                <div class="layui-input-block">
                                    <input type="text" name="address" placeholder="请输入联系地址" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">营业执照</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn" id="uploadBusinessLicense">上传营业执照</button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="businessLicenseImg" style="max-width: 300px;">
                                            <p id="businessLicenseText"></p>
                                        </div>
                                        <input type="hidden" name="business_license" required lay-verify="required">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">身份证正面</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn" id="uploadIdCardFront">上传身份证正面</button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="idCardFrontImg" style="max-width: 300px;">
                                            <p id="idCardFrontText"></p>
                                        </div>
                                        <input type="hidden" name="id_card_front">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">身份证背面</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn" id="uploadIdCardBack">上传身份证背面</button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="idCardBackImg" style="max-width: 300px;">
                                            <p id="idCardBackText"></p>
                                        </div>
                                        <input type="hidden" name="id_card_back">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">商家介绍</label>
                                <div class="layui-input-block">
                                    <textarea name="description" placeholder="请输入商家介绍" class="layui-textarea"></textarea>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="applySubmit">提交申请</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 申请状态 -->
                    <div class="layui-tab-item">
                        <div class="layui-card" id="statusCard">
                            <div class="layui-card-header">申请状态</div>
                            <div class="layui-card-body">
                                <div class="layui-text" id="statusContent">
                                    <p>正在加载申请状态...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入网站底部 -->
<div id="footer"></div>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script>
layui.use(['layer', 'form', 'element', 'upload', 'jquery'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var element = layui.element;
    var upload = layui.upload;
    
    // 加载头部和底部
    $('#header').load('/common/header.html');
    $('#footer').load('/common/footer.html');
    
    // 检查登录状态
    $.get('/user/check_login', function(res) {
        if (res.code !== 0) {
            layer.msg('请先登录', {icon: 2}, function() {
                window.location.href = '/user/login?redirect=' + encodeURIComponent(window.location.href);
            });
        } else {
            // 获取申请状态
            getApplyStatus();
        }
    }, 'json');
    
    // 下一步按钮点击事件
    $('#nextStep').click(function() {
        var agree = $('input[name="agree"]:checked').val();
        if (!agree) {
            layer.msg('请先阅读并同意入驻须知', {icon: 2});
            return;
        }
        
        element.tabChange('tab', 1);
    });
    
    // 上传营业执照
    upload.render({
        elem: '#uploadBusinessLicense',
        url: '/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        before: function(obj) {
            obj.preview(function(index, file, result) {
                $('#businessLicenseImg').attr('src', result);
            });
            layer.load();
        },
        done: function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                $('#businessLicenseText').html('上传成功');
                $('input[name="business_license"]').val(res.data.url);
            } else {
                $('#businessLicenseText').html('上传失败：' + res.msg);
            }
        },
        error: function() {
            layer.closeAll('loading');
            $('#businessLicenseText').html('上传失败，请重试');
        }
    });
    
    // 上传身份证正面
    upload.render({
        elem: '#uploadIdCardFront',
        url: '/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        before: function(obj) {
            obj.preview(function(index, file, result) {
                $('#idCardFrontImg').attr('src', result);
            });
            layer.load();
        },
        done: function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                $('#idCardFrontText').html('上传成功');
                $('input[name="id_card_front"]').val(res.data.url);
            } else {
                $('#idCardFrontText').html('上传失败：' + res.msg);
            }
        },
        error: function() {
            layer.closeAll('loading');
            $('#idCardFrontText').html('上传失败，请重试');
        }
    });
    
    // 上传身份证背面
    upload.render({
        elem: '#uploadIdCardBack',
        url: '/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        before: function(obj) {
            obj.preview(function(index, file, result) {
                $('#idCardBackImg').attr('src', result);
            });
            layer.load();
        },
        done: function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                $('#idCardBackText').html('上传成功');
                $('input[name="id_card_back"]').val(res.data.url);
            } else {
                $('#idCardBackText').html('上传失败：' + res.msg);
            }
        },
        error: function() {
            layer.closeAll('loading');
            $('#idCardBackText').html('上传失败，请重试');
        }
    });
    
    // 提交申请
    form.on('submit(applySubmit)', function(data) {
        layer.load();
        $.post('/merchant/apply', data.field, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1}, function() {
                    element.tabChange('tab', 2);
                    getApplyStatus();
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 获取申请状态
    function getApplyStatus() {
        $.get('/merchant/get_apply_status', function(res) {
            if (res.code === 0) {
                var html = '';
                
                if (res.data.is_merchant) {
                    // 已经是商家
                    var statusText = res.data.status === 1 ? '<span class="layui-badge layui-bg-green">正常</span>' : '<span class="layui-badge">已禁用</span>';
                    
                    html += '<div class="layui-bg-green" style="padding: 20px; border-radius: 4px; margin-bottom: 20px;">';
                    html += '<h3><i class="layui-icon layui-icon-ok-circle"></i> 您已成为商家</h3>';
                    html += '<p>当前状态：' + statusText + '</p>';
                    html += '<p>商家等级：' + res.data.level_name + '</p>';
                    html += '</div>';
                    
                    html += '<div class="layui-form-item">';
                    html += '<a href="/merchant/center" class="layui-btn">进入商家中心</a>';
                    html += '</div>';
                } else if (res.data.has_application) {
                    // 有申请记录
                    var application = res.data.application;
                    var statusText = '';
                    
                    switch (application.status) {
                        case 0:
                            statusText = '<span class="layui-badge layui-bg-orange">审核中</span>';
                            break;
                        case 1:
                            statusText = '<span class="layui-badge layui-bg-green">已通过</span>';
                            break;
                        case 2:
                            statusText = '<span class="layui-badge">已拒绝</span>';
                            break;
                    }
                    
                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">申请状态</label>';
                    html += '<div class="layui-input-block">' + statusText + '</div>';
                    html += '</div>';
                    
                    html += '<div class="layui-form-item">';
                    html += '<label class="layui-form-label">申请时间</label>';
                    html += '<div class="layui-input-block">' + formatDate(application.create_time * 1000) + '</div>';
                    html += '</div>';
                    
                    if (application.audit_time) {
                        html += '<div class="layui-form-item">';
                        html += '<label class="layui-form-label">审核时间</label>';
                        html += '<div class="layui-input-block">' + formatDate(application.audit_time * 1000) + '</div>';
                        html += '</div>';
                    }
                    
                    if (application.audit_remark) {
                        html += '<div class="layui-form-item">';
                        html += '<label class="layui-form-label">审核备注</label>';
                        html += '<div class="layui-input-block">' + application.audit_remark + '</div>';
                        html += '</div>';
                    }
                    
                    if (application.status === 2) {
                        html += '<div class="layui-form-item">';
                        html += '<div class="layui-input-block">';
                        html += '<button class="layui-btn" onclick="element.tabChange(\'tab\', 1)">重新申请</button>';
                        html += '</div>';
                        html += '</div>';
                    } else if (application.status === 0) {
                        html += '<div class="layui-bg-orange" style="padding: 15px; border-radius: 4px;">';
                        html += '<i class="layui-icon layui-icon-about"></i> 您的申请正在审核中，请耐心等待。';
                        html += '</div>';
                    } else if (application.status === 1) {
                        html += '<div class="layui-bg-green" style="padding: 15px; border-radius: 4px;">';
                        html += '<i class="layui-icon layui-icon-ok-circle"></i> 您的申请已通过，请刷新页面查看最新状态。';
                        html += '</div>';
                    }
                } else {
                    // 没有申请记录
                    html += '<div class="layui-bg-gray" style="padding: 20px; border-radius: 4px;">';
                    html += '<h3><i class="layui-icon layui-icon-about"></i> 您还没有提交商家入驻申请</h3>';
                    html += '<p>请点击下方按钮开始申请流程。</p>';
                    html += '</div>';
                    
                    html += '<div class="layui-form-item" style="margin-top: 20px;">';
                    html += '<button class="layui-btn" onclick="element.tabChange(\'tab\', 0)">开始申请</button>';
                    html += '</div>';
                }
                
                $('#statusContent').html(html);
            } else {
                $('#statusContent').html('<p class="layui-text">获取申请状态失败：' + res.msg + '</p>');
            }
        }, 'json');
    }
    
    // 格式化日期
    function formatDate(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        var seconds = ('0' + date.getSeconds()).slice(-2);
        
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }
});
</script>
</body>
</html>