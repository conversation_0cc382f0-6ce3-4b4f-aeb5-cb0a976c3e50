<?php
/**
 * API管理控制器
 */
class ApiController {
    private $db;
    private $user;
    
    public function __construct() {
        // 引入数据库连接
        require_once '../../core/Database.php';
        require_once '../../core/Auth.php';
        require_once '../../core/ApiManager.php';
        
        $this->db = new Database();
        $this->auth = new Auth();
        $this->apiManager = new ApiManager();
        
        // 验证管理员登录
        if (!isset($_GET['action']) || $_GET['action'] != 'getApiList' && $_GET['action'] != 'getApiDetail') {
            $this->auth->checkAdminLogin();
        }
        
        // 处理请求
        $action = isset($_GET['action']) ? $_GET['action'] : '';
        switch ($action) {
            case 'getApiList':
                $this->getApiList();
                break;
            case 'getApiDetail':
                $this->getApiDetail();
                break;
            case 'add':
                $this->addApi();
                break;
            case 'edit':
                $this->editApi();
                break;
            case 'delete':
                $this->deleteApi();
                break;
            case 'changeStatus':
                $this->changeStatus();
                break;
            case 'getCategories':
                $this->getCategories();
                break;
            case 'addCategory':
                $this->addCategory();
                break;
            case 'editCategory':
                $this->editCategory();
                break;
            case 'deleteCategory':
                $this->deleteCategory();
                break;
            case 'getLogs':
                $this->getLogs();
                break;
            case 'generateDoc':
                $this->generateDoc();
                break;
            default:
                $this->response(1, '未知操作');
        }
    }
    
    /**
     * 获取API列表
     */
    private function getApiList() {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $keyword = isset($_GET['keyword']) ? $_GET['keyword'] : '';
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        
        $where = "1=1";
        $params = [];
        
        if (!empty($keyword)) {
            $where .= " AND (name LIKE ? OR description LIKE ?)";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
        }
        
        if ($category_id > 0) {
            $where .= " AND category_id = ?";
            $params[] = $category_id;
        }
        
        // 如果是AJAX请求，返回分页数据
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $count = $this->db->count("SELECT COUNT(*) FROM api_apis WHERE {$where}", $params);
            
            $offset = ($page - 1) * $limit;
            $sql = "SELECT a.*, c.name as category_name 
                    FROM api_apis a 
                    LEFT JOIN api_categories c ON a.category_id = c.id 
                    WHERE {$where} 
                    ORDER BY a.sort_order ASC, a.id DESC 
                    LIMIT {$offset}, {$limit}";
            
            $list = $this->db->query($sql, $params);
            
            $this->response(0, 'success', [
                'count' => $count,
                'list' => $list
            ]);
        } else {
            // 如果是普通请求，返回所有数据用于API调试页面
            $sql = "SELECT a.*, c.name as category_name 
                    FROM api_apis a 
                    LEFT JOIN api_categories c ON a.category_id = c.id 
                    WHERE {$where} 
                    ORDER BY a.sort_order ASC, a.id DESC";
            
            $list = $this->db->query($sql, $params);
            
            $this->response(0, 'success', $list);
        }
    }
    
    /**
     * 获取API详情
     */
    private function getApiDetail() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            $this->response(1, 'ID不能为空');
        }
        
        $api = $this->db->get("SELECT * FROM api_apis WHERE id = ?", [$id]);
        if (!$api) {
            $this->response(1, 'API不存在');
        }
        
        // 获取API参数
        $params = $this->db->query("SELECT * FROM api_params WHERE api_id = ? ORDER BY sort_order ASC", [$id]);
        $api['params'] = $params;
        
        // 获取API响应示例
        $responses = $this->db->query("SELECT * FROM api_responses WHERE api_id = ?", [$id]);
        $api['responses'] = $responses;
        
        $this->response(0, 'success', $api);
    }
    
    /**
     * 添加API
     */
    private function addApi() {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->response(1, '请求方法错误');
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) {
            $this->response(1, '无效的请求数据');
        }
        
        // 验证必填字段
        if (empty($data['name']) || empty($data['url']) || empty($data['method'])) {
            $this->response(1, '名称、URL和请求方法不能为空');
        }
        
        // 准备API数据
        $apiData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'url' => $data['url'],
            'method' => strtoupper($data['method']),
            'category_id' => intval($data['category_id'] ?? 0),
            'status' => intval($data['status'] ?? 1),
            'is_free' => intval($data['is_free'] ?? 0),
            'price' => floatval($data['price'] ?? 0),
            'merchant_price' => floatval($data['merchant_price'] ?? 0),
            'sort_order' => intval($data['sort_order'] ?? 0),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 开始事务
        $this->db->beginTransaction();
        
        try {
            // 插入API
            $apiId = $this->db->insert('api_apis', $apiData);
            
            // 插入参数
            if (!empty($data['params'])) {
                foreach ($data['params'] as $param) {
                    $paramData = [
                        'api_id' => $apiId,
                        'name' => $param['name'],
                        'type' => $param['type'],
                        'description' => $param['description'] ?? '',
                        'required' => intval($param['required'] ?? 0),
                        'default_value' => $param['default_value'] ?? '',
                        'sort_order' => intval($param['sort_order'] ?? 0)
                    ];
                    $this->db->insert('api_params', $paramData);
                }
            }
            
            // 插入响应示例
            if (!empty($data['responses'])) {
                foreach ($data['responses'] as $response) {
                    $responseData = [
                        'api_id' => $apiId,
                        'name' => $response['name'],
                        'content' => $response['content'],
                        'type' => $response['type'] ?? 'success'
                    ];
                    $this->db->insert('api_responses', $responseData);
                }
            }
            
            // 提交事务
            $this->db->commit();
            
            // 生成文档
            $this->apiManager->generateDoc($apiId);
            
            $this->response(0, '添加成功', ['id' => $apiId]);
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->response(1, '添加失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 编辑API
     */
    private function editApi() {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->response(1, '请求方法错误');
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data || empty($data['id'])) {
            $this->response(1, '无效的请求数据');
        }
        
        $id = intval($data['id']);
        
        // 验证API是否存在
        $api = $this->db->get("SELECT * FROM api_apis WHERE id = ?", [$id]);
        if (!$api) {
            $this->response(1, 'API不存在');
        }
        
        // 准备API数据
        $apiData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'url' => $data['url'],
            'method' => strtoupper($data['method']),
            'category_id' => intval($data['category_id'] ?? 0),
            'status' => intval($data['status'] ?? 1),
            'is_free' => intval($data['is_free'] ?? 0),
            'price' => floatval($data['price'] ?? 0),
            'merchant_price' => floatval($data['merchant_price'] ?? 0),
            'sort_order' => intval($data['sort_order'] ?? 0),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 开始事务
        $this->db->beginTransaction();
        
        try {
            // 更新API
            $this->db->update('api_apis', $apiData, ['id' => $id]);
            
            // 删除旧参数
            $this->db->delete('api_params', ['api_id' => $id]);
            
            // 插入新参数
            if (!empty($data['params'])) {
                foreach ($data['params'] as $param) {
                    $paramData = [
                        'api_id' => $id,
                        'name' => $param['name'],
                        'type' => $param['type'],
                        'description' => $param['description'] ?? '',
                        'required' => intval($param['required'] ?? 0),
                        'default_value' => $param['default_value'] ?? '',
                        'sort_order' => intval($param['sort_order'] ?? 0)
                    ];
                    $this->db->insert('api_params', $paramData);
                }
            }
            
            // 删除旧响应示例
            $this->db->delete('api_responses', ['api_id' => $id]);
            
            // 插入新响应示例
            if (!empty($data['responses'])) {
                foreach ($data['responses'] as $response) {
                    $responseData = [
                        'api_id' => $id,
                        'name' => $response['name'],
                        'content' => $response['content'],
                        'type' => $response['type'] ?? 'success'
                    ];
                    $this->db->insert('api_responses', $responseData);
                }
            }
            
            // 提交事务
            $this->db->commit();
            
            // 重新生成文档
            $this->apiManager->generateDoc($id);
            
            $this->response(0, '更新成功');
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->response(1, '更新失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 删除API
     */
    private function deleteApi() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            $this->response(1, 'ID不能为空');
        }
        
        // 开始事务
        $this->db->beginTransaction();
        
        try {
            // 删除API
            $this->db->delete('api_apis', ['id' => $id]);
            
            // 删除参数
            $this->db->delete('api_params', ['api_id' => $id]);
            
            // 删除响应示例
            $this->db->delete('api_responses', ['api_id' => $id]);
            
            // 提交事务
            $this->db->commit();
            
            $this->response(0, '删除成功');
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            $this->response(1, '删除失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 修改API状态
     */
    private function changeStatus() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $status = isset($_GET['status']) ? intval($_GET['status']) : 0;
        
        if ($id <= 0) {
            $this->response(1, 'ID不能为空');
        }
        
        $this->db->update('api_apis', ['status' => $status], ['id' => $id]);
        $this->response(0, '状态修改成功');
    }
    
    /**
     * 获取API分类列表
     */
    private function getCategories() {
        $list = $this->db->query("SELECT * FROM api_categories ORDER BY sort_order ASC, id ASC");
        $this->response(0, 'success', $list);
    }
    
    /**
     * 添加API分类
     */
    private function addCategory() {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->response(1, '请求方法错误');
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data || empty($data['name'])) {
            $this->response(1, '分类名称不能为空');
        }
        
        $categoryData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'icon' => $data['icon'] ?? '',
            'sort_order' => intval($data['sort_order'] ?? 0),
            'status' => intval($data['status'] ?? 1),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $id = $this->db->insert('api_categories', $categoryData);
        $this->response(0, '添加成功', ['id' => $id]);
    }
    
    /**
     * 编辑API分类
     */
    private function editCategory() {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->response(1, '请求方法错误');
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data || empty($data['id']) || empty($data['name'])) {
            $this->response(1, '分类ID和名称不能为空');
        }
        
        $id = intval($data['id']);
        
        $categoryData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'icon' => $data['icon'] ?? '',
            'sort_order' => intval($data['sort_order'] ?? 0),
            'status' => intval($data['status'] ?? 1),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->update('api_categories', $categoryData, ['id' => $id]);
        $this->response(0, '更新成功');
    }
    
    /**
     * 删除API分类
     */
    private function deleteCategory() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            $this->response(1, 'ID不能为空');
        }
        
        // 检查分类下是否有API
        $count = $this->db->count("SELECT COUNT(*) FROM api_apis WHERE category_id = ?", [$id]);
        if ($count > 0) {
            $this->response(1, '该分类下有API，无法删除');
        }
        
        $this->db->delete('api_categories', ['id' => $id]);
        $this->response(0, '删除成功');
    }
    
    /**
     * 获取API调用日志
     */
    private function getLogs() {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $api_id = isset($_GET['api_id']) ? intval($_GET['api_id']) : 0;
        $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
        $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
        $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
        
        $where = "1=1";
        $params = [];
        
        if ($api_id > 0) {
            $where .= " AND api_id = ?";
            $params[] = $api_id;
        }
        
        if ($user_id > 0) {
            $where .= " AND user_id = ?";
            $params[] = $user_id;
        }
        
        if (!empty($start_date)) {
            $where .= " AND created_at >= ?";
            $params[] = $start_date . ' 00:00:00';
        }
        
        if (!empty($end_date)) {
            $where .= " AND created_at <= ?";
            $params[] = $end_date . ' 23:59:59';
        }
        
        $count = $this->db->count("SELECT COUNT(*) FROM api_logs WHERE {$where}", $params);
        
        $offset = ($page - 1) * $limit;
        $sql = "SELECT l.*, a.name as api_name, u.username as username 
                FROM api_logs l 
                LEFT JOIN api_apis a ON l.api_id = a.id 
                LEFT JOIN api_users u ON l.user_id = u.id 
                WHERE {$where} 
                ORDER BY l.id DESC 
                LIMIT {$offset}, {$limit}";
        
        $list = $this->db->query($sql, $params);
        
        $this->response(0, 'success', [
            'count' => $count,
            'list' => $list
        ]);
    }
    
    /**
     * 生成API文档
     */
    private function generateDoc() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id > 0) {
            // 生成单个API文档
            $result = $this->apiManager->generateDoc($id);
        } else {
            // 生成所有API文档
            $result = $this->apiManager->generateAllDocs();
        }
        
        if ($result) {
            $this->response(0, '文档生成成功');
        } else {
            $this->response(1, '文档生成失败');
        }
    }
    
    /**
     * 返回JSON响应
     */
    private function response($code, $msg, $data = null) {
        $result = [
            'code' => $code,
            'msg' => $msg
        ];
        
        if ($data !== null) {
            $result['data'] = $data;
        }
        
        header('Content-Type: application/json');
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 实例化控制器
new ApiController();