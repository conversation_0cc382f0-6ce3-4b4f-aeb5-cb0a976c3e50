<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="../../../assets/images/favicon.ico" rel="icon">
    <title>重置密码-EasyWeb</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        body {
            background-image: url("../../../assets/images/bg-login.jpg");
            background-repeat: no-repeat;
            background-size: cover;
            min-height: 100vh;
        }

        body:before {
            content: "";
            background-color: rgba(0, 0, 0, .2);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .login-wrapper {
            max-width: 420px;
            padding: 20px;
            margin: 0 auto;
            position: relative;
            box-sizing: border-box;
            z-index: 2;
        }

        .login-wrapper > .layui-form {
            padding: 25px 30px;
            background-color: #fff;
            box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.19);
            box-sizing: border-box;
            border-radius: 4px;
        }

        .login-wrapper > .layui-form > h2 {
            color: #333;
            font-size: 18px;
            text-align: center;
            margin-bottom: 25px;
        }

        .login-wrapper > .layui-form > .layui-form-item {
            margin-bottom: 25px;
            position: relative;
        }

        .login-wrapper > .layui-form > .layui-form-item:last-child {
            margin-bottom: 0;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-input {
            height: 46px;
            line-height: 1;
            border-radius: 2px !important;
        }

        .login-wrapper .layui-input-icon-group > .layui-input {
            padding-left: 46px;
        }

        .login-wrapper .layui-input-icon-group > .layui-icon {
            width: 46px;
            height: 46px;
            line-height: 46px;
            font-size: 20px;
            color: #909399;
            position: absolute;
            left: 0;
            top: 0;
            text-align: center;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-captcha-group {
            padding-right: 135px;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-captcha-group > .login-captcha {
            height: 46px;
            width: 120px;
            cursor: pointer;
            box-sizing: border-box;
            border: 1px solid #e6e6e6;
            border-radius: 2px !important;
            position: absolute;
            right: 0;
            top: 0;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-form-checkbox {
            margin: 0 !important;
            padding-left: 25px;
        }

        .login-wrapper > .layui-form > .layui-form-item > .layui-form-checkbox > .layui-icon {
            width: 15px !important;
            height: 15px !important;
        }

        .login-wrapper > .layui-form .layui-btn-fluid {
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            border-radius: 2px !important;
        }

        .login-wrapper > .layui-form > .layui-form-item.login-oauth-group > a > .layui-icon {
            font-size: 26px;
        }

        .login-copyright {
            color: #eee;
            padding-bottom: 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        @media screen and (min-height: 550px) {
            .login-wrapper {
                margin: -250px auto 0;
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                width: 100%;
            }

            .login-copyright {
                position: absolute;
                bottom: 0;
                right: 0;
                left: 0;
            }
        }

        .layui-btn {
            background-color: #5FB878;
            border-color: #5FB878;
        }

        .layui-link {
            color: #5FB878 !important;
        }

        .login-captcha-btn {
            line-height: 44px;
            text-align: center;
            background-color: transparent;
            outline: none;
            color: #666;
            padding: 0 !important;
        }

        /** 获取图形验证码弹窗 */
        .layer-get-code {
            padding: 25px 25px;
        }

        .layer-get-code > p {
            color: #666;
            font-size: 16px;
        }

        .layer-get-code > .lay-code-group {
            position: relative;
            padding-right: 135px;
            margin: 15px 0;
        }

        .layer-get-code > .lay-code-group > .layui-input {
            border-radius: 0;
            height: 46px;
            line-height: 1;
            background-color: transparent;
            border-color: rgba(111, 121, 122, 0.3);
        }

        .layer-get-code > .lay-code-group > img {
            position: absolute;
            right: 0;
            top: 0;
            height: 46px;
            width: 120px;
            box-sizing: border-box;
            cursor: pointer;
            border: 1px solid #e6e6e6;
        }

        .layer-get-code .layui-btn-fluid {
            line-height: 50px;
            height: 50px;
        }

        /** //获取图形验证码弹窗 */

        .layui-btn-disabled {
            cursor: not-allowed !important;
            color: #999 !important;
        }
    </style>
</head>
<body>
<div class="login-wrapper layui-anim layui-anim-scale layui-hide">
    <form class="layui-form">
        <h2>重置密码</h2>
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-email"></i>
            <input class="layui-input" name="email" placeholder="请输入邮箱账号" autocomplete="off"
                   lay-verType="tips" lay-verify="email" required/>
        </div>
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-password"></i>
            <input class="layui-input" name="password" placeholder="请输入新的登录密码" type="password"
                   lay-verType="tips" lay-verify="required" required/>
        </div>
        <div class="layui-form-item layui-input-icon-group">
            <i class="layui-icon layui-icon-key"></i>
            <input class="layui-input" name="password2" placeholder="请再次输入登录密码" type="password"
                   lay-verType="tips" lay-verify="equalTo" lay-equalTo="[name=password]" required/>
        </div>
        <div class="layui-form-item layui-input-icon-group login-captcha-group">
            <i class="layui-icon layui-icon-auz"></i>
            <input class="layui-input" name="code" placeholder="请输入验证码" autocomplete="off"
                   lay-verType="tips" lay-verify="required" required/>
            <button type="button" id="btnGetCode" class="login-captcha login-captcha-btn layui-input">获取验证码</button>
        </div>
        <div class="layui-form-item">
            <a href="login.html" class="layui-link pull-right">返回登录</a>
        </div>
        <div class="layui-form-item" style="margin-bottom: 20px;">
            <button class="layui-btn layui-btn-fluid" lay-filter="loginSubmit" lay-submit>修改密码</button>
        </div>
    </form>
</div>
<div class="login-copyright">copyright © 2020 easyweb.vip all rights reserved.</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form', 'formX'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        $('.login-wrapper').removeClass('layui-hide');

        /* 表单提交 */
        form.on('submit(loginSubmit)', function (obj) {
            // console.log(obj.field);
            layer.msg('注册成功', {icon: 1, time: 1500}, function () {
                location.href = 'login.html';
            });
            /*var loadIndex = layer.load(2);
            $.post('reg', obj.field, function (res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    layer.msg('注册成功', {icon: 1, time: 1500}, function () {
                        location.href = 'login.html';
                    });
                } else {
                    layer.msg(res.msg, {icon: 2, anim: 6});
                }
            }, 'json');*/
            return false;
        });

        /* 获取验证码 */
        var captchaUrl = 'http://shiro.easyweb.vip/assets/captcha';
        $('#btnGetCode').click(function () {
            var $btn = $(this);
            var $inputEmail = $('input[name="email"]');
            var email = $inputEmail.val();
            /*if (!email) {
                layer.tips('请输入邮箱账号', $inputEmail, {tips: [1, '#ff4c4c']});
                return;
            }
            var emailReg = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
            if (!emailReg.test(email)) {
                layer.tips('邮箱格式不正确', $inputEmail, {tips: [1, '#ff4c4c']});
                return;
            }*/
            var layIndex = layer.open({
                type: 1,
                title: false,
                shade: .1,
                content: [
                    '<div class="layer-get-code">',
                    '   <p>验证码将发送到您的邮箱，输入下方图形验证码点击按钮即可发送：</p>',
                    '   <div class="lay-code-group">',
                    '      <input placeholder="请输入图形验证码" class="layui-input"/>',
                    '      <img class="login-captcha" />',
                    '   </div>',
                    '   <div><button class="layui-btn layui-btn-fluid">立即发送</button></div>',
                    '</div>'
                ].join(''),
                success: function () {
                    // 图形验证码
                    $('.layer-get-code>.lay-code-group>img').click(function () {
                        this.src = captchaUrl + '?t=' + (new Date).getTime();
                    }).trigger('click');
                    // 立即发送
                    $('.layer-get-code .layui-btn-fluid').click(function () {
                        var $input = $('.layer-get-code>.lay-code-group>input');
                        var code = $input.val();
                        if (!code) {
                            $input.addClass('layui-form-danger').focus();
                            layer.tips('请输入图形验证码', $input, {tips: [3, '#ff4c4c']});
                            return;
                        }
                        layer.close(layIndex);
                        layui.formX.startTimer($btn, 30);
                        /*var loadIndex = layer.msg('请求中...', {icon: 16, shade: 0.01, time: false});
                        $.post('/sendEmailVer', {
                            code: code,
                            type: 0,
                            email: email
                        }, function (res) {
                            layer.close(loadIndex);
                            if (res.code == 200) {
                                layer.msg(res.msg, {icon: 1});
                                layer.close(layIndex);
                                layui.formX.startTimer($btn, 30);
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');*/
                    });
                }, end: function () {
                    layer.closeAll('tips');
                }
            })
        });

    });
</script>
</body>
</html>