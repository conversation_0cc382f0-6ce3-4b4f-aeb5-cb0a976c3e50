<?php
/**
 * 后台文章管理控制器
 */
class AdminArticleController {
    private $db;
    
    public function __construct() {
        global $db;
        $this->db = $db;
        
        // 检查管理员登录状态
        checkAdminLogin();
    }
    
    /**
     * 获取文章列表
     */
    public function getList() {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        $status = isset($_GET['status']) && $_GET['status'] !== '' ? intval($_GET['status']) : -1;
        
        $where = "WHERE 1=1 ";
        $params = [];
        
        if (!empty($keyword)) {
            $where .= "AND (a.title LIKE ? OR a.author LIKE ?) ";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
        }
        
        if ($category_id > 0) {
            $where .= "AND a.category_id = ? ";
            $params[] = $category_id;
        }
        
        if ($status >= 0) {
            $where .= "AND a.status = ? ";
            $params[] = $status;
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM articles a $where");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT a.*, c.name as category_name 
                                   FROM articles a 
                                   LEFT JOIN article_categories c ON a.category_id = c.id 
                                   $where 
                                   ORDER BY a.sort ASC, a.create_time DESC 
                                   LIMIT $offset, $limit");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 获取文章详情
     */
    public function getDetail() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '文章ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("SELECT a.*, c.name as category_name 
                                   FROM articles a 
                                   LEFT JOIN article_categories c ON a.category_id = c.id 
                                   WHERE a.id = ?");
        $stmt->execute([$id]);
        $article = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$article) {
            return json([
                'code' => 1,
                'msg' => '文章不存在'
            ]);
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $article
        ]);
    }
    
    /**
     * 保存文章（添加/编辑）
     */
    public function saveArticle() {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $title = isset($_POST['title']) ? trim($_POST['title']) : '';
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
        $author = isset($_POST['author']) ? trim($_POST['author']) : '';
        $cover = isset($_POST['cover']) ? trim($_POST['cover']) : '';
        $summary = isset($_POST['summary']) ? trim($_POST['summary']) : '';
        $content = isset($_POST['content']) ? $_POST['content'] : '';
        $keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : '';
        $sort = isset($_POST['sort']) ? intval($_POST['sort']) : 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : 0;
        
        if (empty($title)) {
            return json([
                'code' => 1,
                'msg' => '文章标题不能为空'
            ]);
        }
        
        if ($category_id <= 0) {
            return json([
                'code' => 1,
                'msg' => '请选择文章分类'
            ]);
        }
        
        $data = [
            'title' => $title,
            'category_id' => $category_id,
            'author' => $author,
            'cover' => $cover,
            'summary' => $summary,
            'content' => $content,
            'keywords' => $keywords,
            'sort' => $sort,
            'status' => $status,
            'update_time' => time()
        ];
        
        if ($id > 0) {
            // 更新
            $stmt = $this->db->prepare("UPDATE articles SET title = ?, category_id = ?, author = ?, 
                                       cover = ?, summary = ?, content = ?, keywords = ?, 
                                       sort = ?, status = ?, update_time = ? 
                                       WHERE id = ?");
            $stmt->execute([
                $data['title'], $data['category_id'], $data['author'],
                $data['cover'], $data['summary'], $data['content'], $data['keywords'],
                $data['sort'], $data['status'], $data['update_time'],
                $id
            ]);
            
            $msg = '更新成功';
        } else {
            // 添加
            $data['create_time'] = time();
            $data['view_count'] = 0;
            
            $stmt = $this->db->prepare("INSERT INTO articles (title, category_id, author, cover, summary, 
                                       content, keywords, sort, status, create_time, update_time, view_count) 
                                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $data['title'], $data['category_id'], $data['author'], $data['cover'], $data['summary'],
                $data['content'], $data['keywords'], $data['sort'], $data['status'], 
                $data['create_time'], $data['update_time'], $data['view_count']
            ]);
            
            $id = $this->db->lastInsertId();
            $msg = '添加成功';
        }
        
        return json([
            'code' => 0,
            'msg' => $msg,
            'data' => [
                'id' => $id
            ]
        ]);
    }
    
    /**
     * 删除文章
     */
    public function deleteArticle() {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '文章ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("DELETE FROM articles WHERE id = ?");
        $stmt->execute([$id]);
        
        return json([
            'code' => 0,
            'msg' => '删除成功'
        ]);
    }
    
    /**
     * 更新文章状态
     */
    public function updateStatus() {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '文章ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("UPDATE articles SET status = ? WHERE id = ?");
        $stmt->execute([$status, $id]);
        
        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }
    
    /**
     * 获取所有文章分类
     */
    public function getAllCategories() {
        $stmt = $this->db->prepare("SELECT id, name FROM article_categories ORDER BY sort ASC");
        $stmt->execute();
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $list
        ]);
    }
    
    /**
     * 获取文章分类列表（分页）
     */
    public function getCategoryList() {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        
        $where = "";
        $params = [];
        
        if (!empty($keyword)) {
            $where = "WHERE name LIKE ? ";
            $params[] = "%{$keyword}%";
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM article_categories $where");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT c.*, 
                                   (SELECT COUNT(*) FROM articles WHERE category_id = c.id) as article_count 
                                   FROM article_categories c 
                                   $where 
                                   ORDER BY sort ASC 
                                   LIMIT $offset, $limit");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 保存文章分类（添加/编辑）
     */
    public function saveCategory() {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $sort = isset($_POST['sort']) ? intval($_POST['sort']) : 0;
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        
        if (empty($name)) {
            return json([
                'code' => 1,
                'msg' => '分类名称不能为空'
            ]);
        }
        
        $data = [
            'name' => $name,
            'sort' => $sort,
            'description' => $description
        ];
        
        if ($id > 0) {
            // 更新
            $stmt = $this->db->prepare("UPDATE article_categories SET name = ?, sort = ?, description = ? WHERE id = ?");
            $stmt->execute([$data['name'], $data['sort'], $data['description'], $id]);
            
            $msg = '更新成功';
        } else {
            // 添加
            $data['create_time'] = time();
            
            $stmt = $this->db->prepare("INSERT INTO article_categories (name, sort, description, create_time) VALUES (?, ?, ?, ?)");
            $stmt->execute([$data['name'], $data['sort'], $data['description'], $data['create_time']]);
            
            $id = $this->db->lastInsertId();
            $msg = '添加成功';
        }
        
        return json([
            'code' => 0,
            'msg' => $msg,
            'data' => [
                'id' => $id
            ]
        ]);
    }
    
    /**
     * 删除文章分类
     */
    public function deleteCategory() {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '分类ID不能为空'
            ]);
        }
        
        // 检查分类下是否有文章
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM articles WHERE category_id = ?");
        $stmt->execute([$id]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        if ($count > 0) {
            return json([
                'code' => 1,
                'msg' => '该分类下有'.$count.'篇文章，请先删除或转移这些文章'
            ]);
        }
        
        $stmt = $this->db->prepare("DELETE FROM article_categories WHERE id = ?");
        $stmt->execute([$id]);
        
        return json([
            'code' => 0,
            'msg' => '删除成功'
        ]);
    }
}