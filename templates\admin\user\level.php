<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>会员等级管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .level-card {
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            position: relative;
        }
        .level-card .level-icon {
            width: 60px;
            height: 60px;
            object-fit: contain;
            margin-bottom: 10px;
        }
        .level-card .level-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .level-card .level-desc {
            color: #666;
            margin-bottom: 15px;
        }
        .level-card .level-price {
            font-size: 16px;
            color: #FF5722;
            margin-bottom: 10px;
        }
        .level-card .level-discount {
            font-size: 14px;
            color: #1E9FFF;
            margin-bottom: 10px;
        }
        .level-card .level-features {
            margin-bottom: 15px;
        }
        .level-card .level-features .feature-item {
            margin-bottom: 5px;
        }
        .level-card .level-features .feature-item i {
            color: #5FB878;
            margin-right: 5px;
        }
        .level-card .level-actions {
            margin-top: 15px;
        }
        .level-card .level-status {
            position: absolute;
            top: 20px;
            right: 20px;
        }
        .level-default-badge {
            display: inline-block;
            padding: 2px 5px;
            background-color: #5FB878;
            color: #fff;
            font-size: 12px;
            border-radius: 2px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">会员等级管理</div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-btn-group">
                        <button id="addLevelBtn" class="layui-btn"><i class="layui-icon">&#xe654;</i>添加等级</button>
                        <button id="sortLevelBtn" class="layui-btn layui-btn-normal"><i class="layui-icon">&#xe658;</i>排序</button>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space20" id="levelContainer">
                <!-- 会员等级卡片将通过JS动态生成 -->
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑会员等级弹窗 -->
<script type="text/html" id="levelFormDialog">
    <form id="levelForm" lay-filter="levelForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id || ''}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">等级名称</label>
            <div class="layui-input-block">
                <input name="name" placeholder="请输入等级名称" type="text" class="layui-input" value="{{d.name || ''}}" maxlength="20" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">等级图标</label>
            <div class="layui-input-block">
                <input name="icon" placeholder="请上传等级图标" type="text" class="layui-input" value="{{d.icon || ''}}" id="iconInput"/>
                <button type="button" class="layui-btn layui-btn-primary" id="iconUpload">
                    <i class="layui-icon">&#xe67c;</i>上传图标
                </button>
                <div class="layui-form-mid layui-word-aux">建议尺寸：60px * 60px</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">等级描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入等级描述" class="layui-textarea">{{d.description || ''}}</textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">价格</label>
            <div class="layui-input-block">
                <input name="price" placeholder="请输入价格" type="number" class="layui-input" value="{{d.price || '0'}}" lay-verify="required|number" required/>
                <div class="layui-form-mid layui-word-aux">单位：元/年，0表示免费</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">API折扣</label>
            <div class="layui-input-block">
                <input name="api_discount" placeholder="请输入API折扣" type="number" class="layui-input" value="{{d.api_discount || '100'}}" lay-verify="required|number" required/>
                <div class="layui-form-mid layui-word-aux">取值范围：1-100，100表示不打折</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">每日调用次数</label>
            <div class="layui-input-block">
                <input name="daily_calls" placeholder="请输入每日调用次数" type="number" class="layui-input" value="{{d.daily_calls || '1000'}}" lay-verify="required|number" required/>
                <div class="layui-form-mid layui-word-aux">-1表示不限制</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">并发请求数</label>
            <div class="layui-input-block">
                <input name="concurrent_calls" placeholder="请输入并发请求数" type="number" class="layui-input" value="{{d.concurrent_calls || '10'}}" lay-verify="required|number" required/>
                <div class="layui-form-mid layui-word-aux">-1表示不限制</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">特权功能</label>
            <div class="layui-input-block">
                <input type="checkbox" name="features[custom_domain]" title="自定义域名" lay-skin="primary" {{d.features && d.features.custom_domain ? 'checked' : ''}}>
                <input type="checkbox" name="features[api_doc]" title="API文档下载" lay-skin="primary" {{d.features && d.features.api_doc ? 'checked' : ''}}>
                <input type="checkbox" name="features[priority_support]" title="优先技术支持" lay-skin="primary" {{d.features && d.features.priority_support ? 'checked' : ''}}>
                <input type="checkbox" name="features[data_export]" title="数据导出" lay-skin="primary" {{d.features && d.features.data_export ? 'checked' : ''}}>
                <input type="checkbox" name="features[white_label]" title="白标服务" lay-skin="primary" {{d.features && d.features.white_label ? 'checked' : ''}}>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否启用</label>
            <div class="layui-input-block">
                <input type="checkbox" name="status" lay-skin="switch" lay-text="启用|禁用" {{d.status == undefined || d.status == 1 ? 'checked' : ''}}>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否默认</label>
            <div class="layui-input-block">
                <input type="checkbox" name="is_default" lay-skin="switch" lay-text="是|否" {{d.is_default == 1 ? 'checked' : ''}}>
                <div class="layui-form-mid layui-word-aux">设为默认后，新用户注册将自动获得此等级</div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="levelSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- 排序弹窗 -->
<script type="text/html" id="sortDialog">
    <div class="layui-form model-form" style="padding-top: 20px;">
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 0;">
                <div id="sortableList" class="layui-form">
                    {{# layui.each(d, function(index, item){ }}
                    <div class="layui-form-item" data-id="{{item.id}}">
                        <div class="layui-input-inline" style="width: 40px; margin-right: 0;">
                            <input type="text" class="layui-input" value="{{index + 1}}" disabled>
                        </div>
                        <div class="layui-form-mid" style="margin-left: 10px;">{{item.name}}</div>
                        <div class="layui-input-inline" style="float: right;">
                            <i class="layui-icon layui-icon-up sort-btn" style="cursor: pointer; margin-right: 5px;"></i>
                            <i class="layui-icon layui-icon-down sort-btn" style="cursor: pointer;"></i>
                        </div>
                    </div>
                    {{# }); }}
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" id="sortSubmitBtn">保存</button>
        </div>
    </div>
</script>

<!-- 会员等级卡片模板 -->
<script type="text/html" id="levelCardTpl">
    <div class="layui-col-md4">
        <div class="level-card">
            <div class="level-status">
                <input type="checkbox" lay-filter="levelSwitch" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status == 1 ? 'checked' : ''}}>
            </div>
            {{# if(d.icon){ }}
            <img src="{{d.icon}}" class="level-icon" alt="{{d.name}}">
            {{# } }}
            <div class="level-title">
                {{d.name}}
                {{# if(d.is_default == 1){ }}
                <span class="level-default-badge">默认</span>
                {{# } }}
            </div>
            <div class="level-desc">{{d.description || '暂无描述'}}</div>
            <div class="level-price">¥ {{d.price}} / 年</div>
            <div class="level-discount">API折扣：{{d.api_discount}}%</div>
            <div class="level-features">
                <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 每日调用次数：{{d.daily_calls == -1 ? '不限' : d.daily_calls}}</div>
                <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 并发请求数：{{d.concurrent_calls == -1 ? '不限' : d.concurrent_calls}}</div>
                {{# if(d.features){ }}
                    {{# if(d.features.custom_domain){ }}
                    <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 自定义域名</div>
                    {{# } }}
                    {{# if(d.features.api_doc){ }}
                    <div class="feature-item"><i class="layui-icon">&#xe63c;</i> API文档下载</div>
                    {{# } }}
                    {{# if(d.features.priority_support){ }}
                    <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 优先技术支持</div>
                    {{# } }}
                    {{# if(d.features.data_export){ }}
                    <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 数据导出</div>
                    {{# } }}
                    {{# if(d.features.white_label){ }}
                    <div class="feature-item"><i class="layui-icon">&#xe63c;</i> 白标服务</div>
                    {{# } }}
                {{# } }}
            </div>
            <div class="level-actions">
                <button class="layui-btn layui-btn-sm" data-id="{{d.id}}" lay-event="edit">编辑</button>
                <button class="layui-btn layui-btn-danger layui-btn-sm" data-id="{{d.id}}" lay-event="del">删除</button>
                {{# if(d.is_default != 1){ }}
                <button class="layui-btn layui-btn-normal layui-btn-sm" data-id="{{d.id}}" lay-event="setDefault">设为默认</button>
                {{# } }}
            </div>
        </div>
    </div>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'laytpl', 'upload'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var util = layui.util;
    var admin = layui.admin;
    var laytpl = layui.laytpl;
    var upload = layui.upload;
    
    // 渲染会员等级列表
    function renderLevelList() {
        $.get('/admin/user/level/list', function(res) {
            if (res.code === 0) {
                var html = '';
                for (var i = 0; i < res.data.length; i++) {
                    laytpl($('#levelCardTpl').html()).render(res.data[i], function(cardHtml) {
                        html += cardHtml;
                    });
                }
                $('#levelContainer').html(html || '<div class="layui-col-md12"><div class="layui-form-mid layui-word-aux">暂无会员等级，请点击"添加等级"按钮创建</div></div>');
                
                // 绑定事件
                bindEvents();
                
                // 渲染表单元素
                form.render();
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 绑定事件
    function bindEvents() {
        // 编辑按钮点击事件
        $('#levelContainer').on('click', '[lay-event="edit"]', function() {
            var id = $(this).data('id');
            showEditDialog(id);
        });
        
        // 删除按钮点击事件
        $('#levelContainer').on('click', '[lay-event="del"]', function() {
            var id = $(this).data('id');
            layer.confirm('确定要删除该会员等级吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/user/level/delete', {
                    id: id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        renderLevelList();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        });
        
        // 设为默认按钮点击事件
        $('#levelContainer').on('click', '[lay-event="setDefault"]', function() {
            var id = $(this).data('id');
            layer.confirm('确定要将该等级设为默认等级吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/user/level/set_default', {
                    id: id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        renderLevelList();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        });
        
        // 启用/禁用开关事件
        form.on('switch(levelSwitch)', function(obj) {
            var id = obj.value;
            var status = obj.elem.checked ? 1 : 0;
            
            $.post('/admin/user/level/update_status', {
                id: id,
                status: status
            }, function (res) {
                if (res.code !== 0) {
                    layer.msg(res.msg, {icon: 2});
                    $(obj.elem).prop('checked', !obj.elem.checked);
                    form.render('checkbox');
                }
            }, 'json');
        });
    }
    
    // 显示添加/编辑弹窗
    function showEditDialog(id) {
        var title = id ? '编辑会员等级' : '添加会员等级';
        
        if (id) {
            // 编辑
            $.get('/admin/user/level/detail', {
                id: id
            }, function(res) {
                if (res.code === 0) {
                    showFormDialog(title, res.data);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }, 'json');
        } else {
            // 添加
            showFormDialog(title, {});
        }
    }
    
    // 显示表单弹窗
    function showFormDialog(title, data) {
        laytpl($('#levelFormDialog').html()).render(data, function(html) {
            admin.open({
                type: 1,
                title: title,
                content: html,
                area: '600px',
                success: function (layero, dIndex) {
                    // 渲染表单
                    form.render();
                    
                    // 图标上传
                    upload.render({
                        elem: '#iconUpload',
                        url: '/admin/upload/image',
                        accept: 'images',
                        acceptMime: 'image/*',
                        done: function(res) {
                            if (res.code === 0) {
                                $('#iconInput').val(res.data.url);
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    
                    // 表单提交事件
                    form.on('submit(levelSubmit)', function (data) {
                        // 处理特权功能
                        var features = {};
                        for (var key in data.field) {
                            if (key.indexOf('features[') === 0) {
                                var featureName = key.substring(9, key.length - 1);
                                features[featureName] = true;
                                delete data.field[key];
                            }
                        }
                        data.field.features = features;
                        
                        // 处理开关
                        data.field.status = data.field.status === 'on' ? 1 : 0;
                        data.field.is_default = data.field.is_default === 'on' ? 1 : 0;
                        
                        layer.load(2);
                        $.post('/admin/user/level/save', data.field, function (res) {
                            layer.closeAll('loading');
                            if (res.code === 0) {
                                layer.close(dIndex);
                                layer.msg(res.msg, {icon: 1});
                                renderLevelList();
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');
                        return false;
                    });
                }
            });
        });
    }
    
    // 显示排序弹窗
    function showSortDialog() {
        $.get('/admin/user/level/list', function(res) {
            if (res.code === 0) {
                laytpl($('#sortDialog').html()).render(res.data, function(html) {
                    admin.open({
                        type: 1,
                        title: '会员等级排序',
                        content: html,
                        area: '500px',
                        success: function (layero, dIndex) {
                            // 上移按钮点击事件
                            $(layero).find('.layui-icon-up').click(function() {
                                var item = $(this).closest('.layui-form-item');
                                var prev = item.prev();
                                if (prev.length > 0) {
                                    item.insertBefore(prev);
                                    updateSortIndex(layero);
                                }
                            });
                            
                            // 下移按钮点击事件
                            $(layero).find('.layui-icon-down').click(function() {
                                var item = $(this).closest('.layui-form-item');
                                var next = item.next();
                                if (next.length > 0) {
                                    item.insertAfter(next);
                                    updateSortIndex(layero);
                                }
                            });
                            
                            // 保存排序按钮点击事件
                            $(layero).find('#sortSubmitBtn').click(function() {
                                var ids = [];
                                $(layero).find('.layui-form-item').each(function() {
                                    ids.push($(this).data('id'));
                                });
                                
                                layer.load(2);
                                $.post('/admin/user/level/sort', {
                                    ids: ids.join(',')
                                }, function (res) {
                                    layer.closeAll('loading');
                                    if (res.code === 0) {
                                        layer.close(dIndex);
                                        layer.msg(res.msg, {icon: 1});
                                        renderLevelList();
                                    } else {
                                        layer.msg(res.msg, {icon: 2});
                                    }
                                }, 'json');
                            });
                        }
                    });
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
    }
    
    // 更新排序序号
    function updateSortIndex(layero) {
        $(layero).find('.layui-form-item').each(function(index) {
            $(this).find('.layui-input').val(index + 1);
        });
    }
    
    // 添加等级按钮点击事件
    $('#addLevelBtn').click(function() {
        showEditDialog();
    });
    
    // 排序按钮点击事件
    $('#sortLevelBtn').click(function() {
        showSortDialog();
    });
    
    // 初始化
    renderLevelList();
});
</script>
</body>
</html>