<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>邮箱发信配置 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">邮箱发信配置</div>
        <div class="layui-card-body">
            <div class="layui-tab" lay-filter="emailConfigTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">基本配置</li>
                    <li>邮件模板</li>
                    <li>发送测试</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 基本配置 -->
                    <div class="layui-tab-item layui-show">
                        <form id="emailConfigForm" lay-filter="emailConfigForm" class="layui-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">发信方式</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="mail_driver" value="smtp" title="SMTP" checked>
                                    <input type="radio" name="mail_driver" value="sendmail" title="Sendmail">
                                    <input type="radio" name="mail_driver" value="mailgun" title="Mailgun">
                                    <input type="radio" name="mail_driver" value="sendcloud" title="SendCloud">
                                </div>
                            </div>
                            
                            <!-- SMTP配置 -->
                            <div id="smtpConfig">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">SMTP服务器</label>
                                    <div class="layui-input-block">
                                        <input name="smtp_host" placeholder="请输入SMTP服务器地址" type="text" class="layui-input" value=""/>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">SMTP端口</label>
                                    <div class="layui-input-block">
                                        <input name="smtp_port" placeholder="请输入SMTP端口" type="number" class="layui-input" value="25"/>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">加密方式</label>
                                    <div class="layui-input-block">
                                        <input type="radio" name="smtp_encryption" value="" title="无" checked>
                                        <input type="radio" name="smtp_encryption" value="ssl" title="SSL">
                                        <input type="radio" name="smtp_encryption" value="tls" title="TLS">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">邮箱账号</label>
                                    <div class="layui-input-block">
                                        <input name="smtp_username" placeholder="请输入邮箱账号" type="text" class="layui-input" value=""/>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">邮箱密码</label>
                                    <div class="layui-input-block">
                                        <input name="smtp_password" placeholder="请输入邮箱密码或授权码" type="password" class="layui-input" value=""/>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Mailgun配置 -->
                            <div id="mailgunConfig" style="display: none;">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">域名</label>
                                    <div class="layui-input-block">
                                        <input name="mailgun_domain" placeholder="请输入Mailgun域名" type="text" class="layui-input" value=""/>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">API密钥</label>
                                    <div class="layui-input-block">
                                        <input name="mailgun_secret" placeholder="请输入Mailgun API密钥" type="text" class="layui-input" value=""/>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- SendCloud配置 -->
                            <div id="sendcloudConfig" style="display: none;">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">API User</label>
                                    <div class="layui-input-block">
                                        <input name="sendcloud_api_user" placeholder="请输入SendCloud API User" type="text" class="layui-input" value=""/>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">API Key</label>
                                    <div class="layui-input-block">
                                        <input name="sendcloud_api_key" placeholder="请输入SendCloud API Key" type="text" class="layui-input" value=""/>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">发件人名称</label>
                                <div class="layui-input-block">
                                    <input name="mail_from_name" placeholder="请输入发件人名称" type="text" class="layui-input" value=""/>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">发件人邮箱</label>
                                <div class="layui-input-block">
                                    <input name="mail_from_address" placeholder="请输入发件人邮箱" type="text" class="layui-input" value=""/>
                                </div>
                            </div>
                            
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-filter="emailConfigSubmit" lay-submit>保存配置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 邮件模板 -->
                    <div class="layui-tab-item">
                        <div class="layui-form toolbar">
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <div class="layui-input-inline">
                                        <input id="searchKeyword" class="layui-input" type="text" placeholder="模板名称/标题"/>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <button id="searchBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                    <button id="addTemplateBtn" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加模板</button>
                                </div>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="templateTable" lay-filter="templateTable"></table>
                    </div>
                    
                    <!-- 发送测试 -->
                    <div class="layui-tab-item">
                        <form id="testEmailForm" lay-filter="testEmailForm" class="layui-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">收件人邮箱</label>
                                <div class="layui-input-block">
                                    <input name="to_email" placeholder="请输入收件人邮箱" type="text" class="layui-input" value="" lay-verify="required|email" required/>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邮件主题</label>
                                <div class="layui-input-block">
                                    <input name="subject" placeholder="请输入邮件主题" type="text" class="layui-input" value="测试邮件" lay-verify="required" required/>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邮件内容</label>
                                <div class="layui-input-block">
                                    <textarea name="content" placeholder="请输入邮件内容" class="layui-textarea" style="min-height: 200px;" lay-verify="required" required>这是一封测试邮件，用于验证邮箱发信功能是否正常。</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-filter="testEmailSubmit" lay-submit>发送测试邮件</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 添加/编辑邮件模板弹窗 -->
<script type="text/html" id="templateFormDialog">
    <form id="templateForm" lay-filter="templateForm" class="layui-form model-form">
        <input name="id" type="hidden" value="{{d.id || ''}}"/>
        <div class="layui-form-item">
            <label class="layui-form-label">模板名称</label>
            <div class="layui-input-block">
                <input name="name" placeholder="请输入模板名称" type="text" class="layui-input" value="{{d.name || ''}}" maxlength="50" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">模板代码</label>
            <div class="layui-input-block">
                <input name="code" placeholder="请输入模板代码" type="text" class="layui-input" value="{{d.code || ''}}" maxlength="50" lay-verify="required" required/>
                <div class="layui-form-mid layui-word-aux">用于系统调用，只能包含字母、数字和下划线</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">邮件主题</label>
            <div class="layui-input-block">
                <input name="subject" placeholder="请输入邮件主题" type="text" class="layui-input" value="{{d.subject || ''}}" maxlength="100" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">邮件内容</label>
            <div class="layui-input-block">
                <textarea id="content" name="content" style="display: none;">{{d.content || ''}}</textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">变量说明</label>
            <div class="layui-input-block">
                <textarea name="variables" placeholder="请输入变量说明" class="layui-textarea">{{d.variables || ''}}</textarea>
                <div class="layui-form-mid layui-word-aux">格式：变量名|说明，每行一个，如：username|用户名</div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closePageDialog">取消</button>
            <button class="layui-btn" lay-filter="templateSubmit" lay-submit>保存</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'element', 'layedit'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var element = layui.element;
    var layedit = layui.layedit;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#templateTable',
        url: '/admin/config/email/template/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'name', title: '模板名称'},
            {field: 'code', title: '模板代码'},
            {field: 'subject', title: '邮件主题'},
            {field: 'create_time', title: '创建时间', templet: function (d) {
                return util.toDateString(d.create_time * 1000);
            }},
            {title: '操作', toolbar: '#tableBar', width: 120}
        ]]
    });
    
    // 加载邮箱配置
    $.get('/admin/config/email/get', function(res) {
        if (res.code === 0) {
            form.val('emailConfigForm', res.data);
            toggleMailConfig(res.data.mail_driver);
        }
    }, 'json');
    
    // 监听发信方式切换
    form.on('radio(mail_driver)', function(data) {
        toggleMailConfig(data.value);
    });
    
    // 切换不同发信方式的配置表单
    function toggleMailConfig(driver) {
        $('#smtpConfig, #mailgunConfig, #sendcloudConfig').hide();
        
        if (driver === 'smtp') {
            $('#smtpConfig').show();
        } else if (driver === 'mailgun') {
            $('#mailgunConfig').show();
        } else if (driver === 'sendcloud') {
            $('#sendcloudConfig').show();
        }
    }
    
    // 邮箱配置表单提交
    form.on('submit(emailConfigSubmit)', function(data) {
        layer.load(2);
        $.post('/admin/config/email/save', data.field, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 测试邮件表单提交
    form.on('submit(testEmailSubmit)', function(data) {
        layer.load(2);
        $.post('/admin/config/email/test', data.field, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        return false;
    });
    
    // 搜索按钮点击事件
    $('#searchBtn').click(function () {
        insTb.reload({
            where: {
                keyword: $('#searchKeyword').val()
            },
            page: {curr: 1}
        });
    });
    
    // 添加模板按钮点击事件
    $('#addTemplateBtn').click(function() {
        showEditDialog();
    });
    
    // 工具条点击事件
    table.on('tool(templateTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showEditDialog(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该邮件模板吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                layer.load(2);
                $.post('/admin/config/email/template/delete', {
                    id: data.id
                }, function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 显示编辑弹窗
    function showEditDialog(data) {
        var title = data ? '编辑邮件模板' : '添加邮件模板';
        
        admin.open({
            type: 1,
            title: title,
            content: laytpl($('#templateFormDialog').html()).render(data || {}),
            area: ['800px', '600px'],
            success: function (layero, dIndex) {
                // 创建富文本编辑器
                var editIndex = layedit.build('content', {
                    height: 300,
                    tool: [
                        'strong', 'italic', 'underline', 'del', '|', 
                        'left', 'center', 'right', '|',
                        'link', 'unlink', 'face', 'image', 'code', 'preview'
                    ]
                });
                
                // 表单提交事件
                form.on('submit(templateSubmit)', function (data) {
                    // 获取富文本内容
                    data.field.content = layedit.getContent(editIndex);
                    
                    layer.load(2);
                    $.post('/admin/config/email/template/save', data.field, function (res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            insTb.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    return false;
                });
            }
        });
    }
});
</script>
</body>
</html>