<header class="header">
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <?php if (!empty($siteConfig['site_logo'])): ?>
                <img src="<?php echo $siteConfig['site_logo']; ?>" alt="<?php echo $siteConfig['site_name']; ?>" height="40">
                <?php else: ?>
                <?php echo $siteConfig['site_name']; ?>
                <?php endif; ?>
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="切换导航">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav mr-auto">
                    <li class="nav-item <?php echo ($path == '/' || $path == '') ? 'active' : ''; ?>">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item <?php echo (strpos($path, '/api/market') === 0) ? 'active' : ''; ?>">
                        <a class="nav-link" href="/api/market">API市场</a>
                    </li>
                    <li class="nav-item <?php echo (strpos($path, '/docs') === 0) ? 'active' : ''; ?>">
                        <a class="nav-link" href="/docs">开发文档</a>
                    </li>
                    <li class="nav-item <?php echo (strpos($path, '/article') === 0) ? 'active' : ''; ?>">
                        <a class="nav-link" href="/article/list">文章中心</a>
                    </li>
                    <li class="nav-item <?php echo (strpos($path, '/merchant') === 0) ? 'active' : ''; ?>">
                        <a class="nav-link" href="/merchant/list">商家中心</a>
                    </li>
                    
                    <?php
                    // 获取自定义导航
                    $stmt = $pdo->query("SELECT * FROM api_navigations WHERE status = 1 ORDER BY sort_order ASC");
                    while ($nav = $stmt->fetch()) {
                        echo '<li class="nav-item">';
                        echo '<a class="nav-link" href="'.$nav['url'].'" '.($nav['target'] ? 'target="_blank"' : '').'>'.$nav['name'].'</a>';
                        echo '</li>';
                    }
                    ?>
                </ul>
                
                <div class="navbar-nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <img src="<?php echo $_SESSION['user_avatar'] ?: 'public/images/default-avatar.png'; ?>" class="rounded-circle mr-1" width="24" height="24">
                            <?php echo $_SESSION['username']; ?>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown">
                            <a class="dropdown-item" href="/user/profile"><i class="fa fa-user mr-2"></i>个人中心</a>
                            <a class="dropdown-item" href="/user/apikey"><i class="fa fa-key mr-2"></i>API密钥</a>
                            <a class="dropdown-item" href="/user/balance"><i class="fa fa-credit-card mr-2"></i>余额充值</a>
                            <?php if ($_SESSION['user_role'] == 'merchant'): ?>
                            <a class="dropdown-item" href="/merchant/dashboard"><i class="fa fa-dashboard mr-2"></i>商家后台</a>
                            <?php endif; ?>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="/user/logout"><i class="fa fa-sign-out mr-2"></i>退出登录</a>
                        </div>
                    </div>
                    <?php else: ?>
                    <a class="nav-link" href="/user/login">登录</a>
                    <a class="btn btn-outline-primary ml-2" href="/user/register">注册</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
</header>