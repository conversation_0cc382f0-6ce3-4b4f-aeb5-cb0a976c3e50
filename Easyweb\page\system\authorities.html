<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>权限管理</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">权限名称:</label>
                        <div class="layui-input-inline">
                            <input name="authorityName" class="layui-input" placeholder="输入权限名称"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">菜单url:</label>
                        <div class="layui-input-inline">
                            <input name="menuUrl" class="layui-input" placeholder="输入路由地址"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">权限标识:</label>
                        <div class="layui-input-inline">
                            <input name="authority" class="layui-input" placeholder="输入权限标识"/>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="authoritiesTbSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>&nbsp;
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="authoritiesTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="authoritiesTbBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<!-- 表单弹窗 -->
<script type="text/html" id="authoritiesEditDialog">
    <form id="authoritiesEditForm" lay-filter="authoritiesEditForm" class="layui-form model-form"
          style="padding-right: 20px;">
        <input name="authorityId" type="hidden"/>
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">上级菜单</label>
                    <div class="layui-input-block">
                        <div id="authoritiesEditParentSel" class="ew-xmselect-tree"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">权限名称:</label>
                    <div class="layui-input-block">
                        <input name="authorityName" placeholder="请输入权限名称" class="layui-input"
                               lay-verType="tips" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">权限类型:</label>
                    <div class="layui-input-block">
                        <input name="isMenu" type="radio" value="0" title="菜单" checked/>
                        <input name="isMenu" type="radio" value="1" title="按钮"/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">菜单url:</label>
                    <div class="layui-input-block">
                        <input name="menuUrl" placeholder="请输入菜单url" class="layui-input"/>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">权限标识:</label>
                    <div class="layui-input-block">
                        <input name="authority" placeholder="请输入权限标识" class="layui-input"/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">菜单图标:</label>
                    <div class="layui-input-block">
                        <input name="menuIcon" placeholder="请输入菜单图标" class="layui-input"/>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label layui-form-required">排序号:</label>
                    <div class="layui-input-block">
                        <input name="orderNumber" placeholder="请输入排序号" type="number" class="layui-input"
                               lay-verType="tips" lay-verify="required|number" required/>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="authoritiesEditSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>
<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form', 'admin', 'treeTable', 'util', 'xmSelect'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var admin = layui.admin;
        var treeTable = layui.treeTable;
        var util = layui.util;
        var xmSelect = layui.xmSelect;
        var tbDataList = [];

        // 渲染表格
        var insTb = treeTable.render({
            elem: '#authoritiesTable',
            url: '../../json/authorities.json',
            toolbar: ['<p>',
                '<button lay-event="add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="del" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            tree: {
                iconIndex: 2,
                idName: 'authorityId',
                pidName: 'parentId',
                isPidData: true
            },
            cols: [[
                {type: 'checkbox'},
                {type: 'numbers'},
                {field: 'authorityName', title: '权限名称', minWidth: 150},
                {title: '菜单图标', templet: '<p><i class="{{d.menuIcon}}"></i></p>', align: 'center', width: 100},
                {field: 'menuUrl', title: '菜单url'},
                {field: 'authority', title: '权限标识'},
                {field: 'orderNumber', title: '排序号', align: 'center', width: 80},
                {
                    title: '类型', templet: function (d) {
                        return [
                            '<span class="layui-badge layui-badge-green">菜单</span>',
                            '<span class="layui-badge layui-badge-gray">按钮</span>'
                        ][d.isMenu];
                    }, align: 'center', width: 80
                },
                {title: '创建时间', templet: '<p>{{layui.util.toDateString(d.createTime)}}</p>', align: 'center'},
                {title: '操作', toolbar: '#authoritiesTbBar', align: 'center', width: 120}
            ]],
            done: function (data) {
                tbDataList = data;
            }
        });

        /* 表格操作列点击事件 */
        treeTable.on('tool(authoritiesTable)', function (obj) {
            if (obj.event === 'edit') { // 修改
                showEditModel(obj.data);
            } else if (obj.event === 'del') { // 删除
                doDel(obj);
            }
        });

        /* 表格头工具栏点击事件 */
        treeTable.on('toolbar(authoritiesTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showEditModel();
            } else if (obj.event === 'del') { // 删除
                var checkRows = insTb.checkStatus();
                if (checkRows.length === 0) {
                    layer.msg('请选择要删除的数据', {icon: 2});
                    return;
                }
                var ids = checkRows.map(function (d) {
                    return d.authorityId;
                });
                doDel({ids: ids});
            }
        });

        /* 表格搜索 */
        form.on('submit(authoritiesTbSearch)', function (data) {
            doTbSearch(data.field, 'authorityId');
            return false;
        });

        /* 添加 */
        $('#authoritiesAddBtn').click(function () {
            showEditModel();
        });

        /* 显示表单弹窗 */
        function showEditModel(mData) {
            admin.open({
                type: 1,
                area: '600px',
                title: (mData ? '修改' : '添加') + '权限',
                content: $('#authoritiesEditDialog').html(),
                success: function (layero, dIndex) {
                    // 回显表单数据
                    form.val('authoritiesEditForm', mData);
                    // 表单提交事件
                    form.on('submit(authoritiesEditSubmit)', function (data) {
                        data.field.parentId = insXmSel.getValue('valueStr');
                        var loadIndex = layer.load(2);
                        $.get(mData ? '../../json/ok.json' : '../../json/ok.json', data.field, function (res) {
                            layer.close(loadIndex);
                            if (res.code === 200) {
                                layer.close(dIndex);
                                layer.msg(res.msg, {icon: 1});
                                insTb.refresh();
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');
                        return false;
                    });
                    // 渲染下拉树
                    var insXmSel = xmSelect.render({
                        el: '#authoritiesEditParentSel',
                        height: '250px',
                        data: insTb.options.data,
                        initValue: mData ? [mData.parentId] : [],
                        model: {label: {type: 'text'}},
                        prop: {
                            name: 'authorityName',
                            value: 'authorityId'
                        },
                        radio: true,
                        clickClose: true,
                        tree: {
                            show: true,
                            indent: 15,
                            strict: false,
                            expandedKeys: true
                        }
                    });
                    // 弹窗不出现滚动条
                    $(layero).children('.layui-layer-content').css('overflow', 'visible');
                }
            });
        }

        /* 删除 */
        function doDel(obj) {
            layer.confirm('确定要删除选中数据吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                var loadIndex = layer.load(2);
                $.get('../../json/ok.json', {id: obj.data ? obj.data.authorityId : obj.ids}, function (res) {
                    layer.close(loadIndex);
                    if (res.code === 200) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.refresh();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }

        /* 搜索表格数据 */
        function doTbSearch(field, idName) {
            var ids = [], isClear = true;
            for (var i = 0; i < tbDataList.length; i++) {
                var item = tbDataList[i], flag = true;
                for (var f in field) {
                    if (!field.hasOwnProperty(f)) continue;
                    if (!field[f]) continue;
                    isClear = false;
                    if (!item[f] || item[f].indexOf(field[f]) === -1) {
                        flag = false;
                        break;
                    }
                }
                if (flag) ids.push(item[idName]);
            }
            if (isClear) {
                insTb.clearFilter();
            } else {
                insTb.filterData(ids);
            }
        }

    });
</script>
</body>
</html>