-- 上传文件记录表
CREATE TABLE IF NOT EXISTS `uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `user_type` enum('admin','user','merchant') NOT NULL DEFAULT 'user' COMMENT '用户类型',
  `file_url` varchar(255) NOT NULL COMMENT '文件URL',
  `file_size` int(11) NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) NOT NULL COMMENT '文件类型',
  `upload_time` int(11) NOT NULL COMMENT '上传时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`,`user_type`),
  KEY `idx_time` (`upload_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上传文件记录表';