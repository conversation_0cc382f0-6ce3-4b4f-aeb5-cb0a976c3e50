<?php
/**
 * API文章控制器
 */
class ApiArticleController {
    private $db;
    
    public function __construct() {
        global $db;
        $this->db = $db;
        
        // 验证API访问权限
        $this->checkApiAccess();
    }
    
    /**
     * 验证API访问权限
     */
    private function checkApiAccess() {
        // 获取API密钥
        $apiKey = isset($_GET['api_key']) ? trim($_GET['api_key']) : '';
        
        if (empty($apiKey)) {
            $this->outputError('API密钥不能为空', 401);
        }
        
        // 验证API密钥
        $stmt = $this->db->prepare("SELECT u.id, u.username, u.status, a.id as api_key_id, a.key_name, a.key_value, 
                                   a.ip_whitelist, a.request_limit, a.request_count, a.expire_time 
                                   FROM api_keys a 
                                   LEFT JOIN users u ON a.user_id = u.id 
                                   WHERE a.key_value = ? AND a.status = 1");
        $stmt->execute([$apiKey]);
        $keyInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$keyInfo) {
            $this->outputError('无效的API密钥', 401);
        }
        
        // 检查用户状态
        if ($keyInfo['status'] != 1) {
            $this->outputError('用户已被禁用', 403);
        }
        
        // 检查密钥是否过期
        if ($keyInfo['expire_time'] > 0 && $keyInfo['expire_time'] < time()) {
            $this->outputError('API密钥已过期', 403);
        }
        
        // 检查IP白名单
        if (!empty($keyInfo['ip_whitelist'])) {
            $clientIp = $this->getClientIp();
            $whiteList = explode(',', $keyInfo['ip_whitelist']);
            
            if (!in_array($clientIp, $whiteList)) {
                $this->outputError('IP地址不在白名单中', 403);
            }
        }
        
        // 检查请求限制
        if ($keyInfo['request_limit'] > 0 && $keyInfo['request_count'] >= $keyInfo['request_limit']) {
            $this->outputError('已达到API请求限制', 429);
        }
        
        // 更新请求计数
        $stmt = $this->db->prepare("UPDATE api_keys SET request_count = request_count + 1, last_used_time = ? WHERE id = ?");
        $stmt->execute([time(), $keyInfo['api_key_id']]);
        
        // 记录API请求日志
        $this->logApiRequest($keyInfo['api_key_id'], $keyInfo['id']);
    }
    
    /**
     * 记录API请求日志
     */
    private function logApiRequest($keyId, $userId) {
        $requestUri = $_SERVER['REQUEST_URI'];
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestParams = json_encode($_REQUEST);
        $clientIp = $this->getClientIp();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $stmt = $this->db->prepare("INSERT INTO api_request_logs (api_key_id, user_id, request_uri, request_method, 
                                   request_params, client_ip, user_agent, request_time) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$keyId, $userId, $requestUri, $requestMethod, $requestParams, $clientIp, $userAgent, time()]);
    }
    
    /**
     * 获取客户端IP地址
     */
    private function getClientIp() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
    
    /**
     * 输出错误信息
     */
    private function outputError($message, $code = 400) {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code($code);
        
        echo json_encode([
            'code' => $code,
            'msg' => $message,
            'data' => null
        ]);
        
        exit;
    }
    
    /**
     * 获取文章列表
     */
    public function getList() {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        
        $where = "WHERE status = 1 ";
        $params = [];
        
        if (!empty($keyword)) {
            $where .= "AND (title LIKE ? OR summary LIKE ? OR content LIKE ?) ";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
            $params[] = "%{$keyword}%";
        }
        
        if ($category_id > 0) {
            $where .= "AND category_id = ? ";
            $params[] = $category_id;
        }
        
        // 获取总数
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM articles $where");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取列表
        $stmt = $this->db->prepare("SELECT a.id, a.title, a.category_id, c.name as category_name, 
                                   a.author, a.cover, a.summary, a.keywords, a.view_count, 
                                   a.create_time, a.update_time 
                                   FROM articles a 
                                   LEFT JOIN article_categories c ON a.category_id = c.id 
                                   $where 
                                   ORDER BY a.sort ASC, a.create_time DESC 
                                   LIMIT $offset, $limit");
        if (!empty($params)) {
            $stmt->execute($params);
        } else {
            $stmt->execute();
        }
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'count' => $count,
                'list' => $list,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }
    
    /**
     * 获取文章详情
     */
    public function getDetail() {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '文章ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("SELECT a.*, c.name as category_name 
                                   FROM articles a 
                                   LEFT JOIN article_categories c ON a.category_id = c.id 
                                   WHERE a.id = ? AND a.status = 1");
        $stmt->execute([$id]);
        $article = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$article) {
            return json([
                'code' => 1,
                'msg' => '文章不存在或已被删除'
            ]);
        }
        
        // 更新浏览量
        $stmt = $this->db->prepare("UPDATE articles SET view_count = view_count + 1 WHERE id = ?");
        $stmt->execute([$id]);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $article
        ]);
    }
    
    /**
     * 获取文章分类列表
     */
    public function getCategoryList() {
        $stmt = $this->db->prepare("SELECT id, name, description 
                                   FROM article_categories 
                                   ORDER BY sort ASC");
        $stmt->execute();
        $list = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $list
        ]);
    }
}