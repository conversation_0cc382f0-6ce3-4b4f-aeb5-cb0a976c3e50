<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title></title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=315"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        .demo-cp-item {
            display: inline-block;
            text-align: center;
            margin: 8px 25px 0 25px;
            color: #666;
        }

        .demo-cp-item > div:nth-child(2) {
            margin-top: 6px;
        }

        /** 自定义样式1 */
        #demoProgress9 .circle-progress-value {
            stroke-width: 11px;
            stroke: #00CC00;
            stroke-dasharray: 0 23.3;
            stroke-linecap: round;
        }

        #demoProgress9 .circle-progress-circle {
            stroke: transparent;
        }

        #demoProgress9 .circle-progress-text {
            font-family: "Gotham rounded";
            font-size: 16px;
            fill: #00CC00;
        }

        /** 自定义样式2 */
        #demoProgress10 .circle-progress-circle {
            stroke-width: 6px;
            stroke: #ccc;
            stroke-dasharray: 1 5;
            stroke-dashoffset: 1.6;
        }

        #demoProgress10 .circle-progress-value {
            stroke: #43A3FB;
        }

        #demoProgress10.circle-progress-success .circle-progress-value {
            stroke: #5CB85C;
        }

        #demoProgress10.circle-progress-success .circle-progress-text {
            fill: #5CB85C;
        }

        /** 自定义样式3 */
        #demoProgress11 .circle-progress-value {
            stroke-width: 8px;
            stroke: #3FDABA;
        }

        #demoProgress11 .circle-progress-circle {
            stroke-width: 6px;
            stroke: #E0FAF1;
        }

        /** 自定义样式4 */
        #demoProgress12 .circle-progress-value {
            stroke-width: 22px;
            stroke: #3FDABA;
            stroke-dasharray: 60 2;
            stroke-linecap: butt;
        }

        #demoProgress12 .circle-progress-circle {
            stroke: #E0FAF1;
            stroke-width: 22px;
            fill: #E0FAF1;
        }

        #demoProgress12 .circle-progress-text {
            font-family: "Gotham";
            font-size: 16px;
            fill: #3FDABA;
        }

        /** 自定义样式5 */
        #demoProgress13 > svg {
            width: 40px;
            height: 40px;
        }

        #demoProgress13 .circle-progress-value {
            stroke-width: 8px;
        }

        #demoProgress13 .circle-progress-circle {
            stroke-width: 8px;
        }

        #demoProgress13 .circle-progress-text {
            font-size: 30px;
        }
    </style>
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">基本用法</div>
        <div class="layui-card-body">
            <div class="demo-cp-item">
                <div id="demoProgress1"></div>
                <div>默认样式</div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress2"></div>
                <div>逆时针方向</div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress3"></div>
                <div>自定义起始位置</div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">文字样式</div>
        <div class="layui-card-body">
            <div class="demo-cp-item">
                <div id="demoProgress4"></div>
                <div>vertical</div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress5"></div>
                <div>percent</div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress6"></div>
                <div>value</div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress7"></div>
                <div>valueOnCircle</div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress8"></div>
                <div>none</div>
            </div>
        </div>
    </div>
    <div class="layui-card">
        <div class="layui-card-header">自定义样式</div>
        <div class="layui-card-body">
            <div class="demo-cp-item">
                <div id="demoProgress9"></div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress10"></div>
            </div>
            <div class="demo-cp-item">
                <div style="position: relative;">
                    <div id="demoProgress11"></div>
                    <div style="position: absolute;left: 50%;top: 50%;transform: translate(-50%,-50%);">
                        <div style="font-size: 22px;color: #3DDABB;">60<span style="font-size: 14px;">%</span></div>
                        <div style="color: #515a6e;font-size: 14px;">通过率</div>
                    </div>
                </div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress12"></div>
            </div>
            <div class="demo-cp-item">
                <div id="demoProgress13"></div>
            </div>
        </div>
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=315"></script>
<script>
    layui.use(['layer', 'CircleProgress'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var CircleProgress = layui.CircleProgress;

        // 快速使用
        new CircleProgress('#demoProgress1', {
            max: 100,
            value: 20,
        });

        // 逆时针方向
        new CircleProgress('#demoProgress2', {
            max: 100,
            value: 20,
            clockwise: false
        });

        // 自定义起始位置
        new CircleProgress('#demoProgress3', {
            max: 100,
            value: 20,
            startAngle: 225
        });

        // 文字垂直
        new CircleProgress('#demoProgress4', {
            max: 100,
            value: 20,
            textFormat: 'vertical'
        });

        // 文字百分比
        new CircleProgress('#demoProgress5', {
            max: 100,
            value: 20,
            textFormat: 'percent'
        });

        // 文字只显示值
        new CircleProgress('#demoProgress6', {
            max: 100,
            value: 20,
            textFormat: 'value'
        });

        // 文字值显示在进度条上
        new CircleProgress('#demoProgress7', {
            max: 100,
            value: 20,
            textFormat: 'valueOnCircle'
        });

        // 文字不显示
        new CircleProgress('#demoProgress8', {
            max: 100,
            value: 20,
            textFormat: 'none'
        });

        // 自定义样式1
        new CircleProgress('#demoProgress9', {
            max: 12,
            value: 9,
            textFormat: function (value, max) {
                return value + ' dots';
            }
        });
        // 自定义样式2
        var ins10 = new CircleProgress('#demoProgress10', {
            max: 100,
            value: 0,
            textFormat: function (value, max) {
                if (value == max) {
                    return 'OK';
                } else {
                    return value + '%';
                }
            }
        });

        var timer10 = setInterval(function () {
            ins10.value += 1;
            if (ins10.value == ins10.max) {
                $('#demoProgress10').addClass('circle-progress-success');
                clearInterval(timer10);
            }
        }, 100);

        // 自定义样式3
        new CircleProgress('#demoProgress11', {
            max: 100,
            value: 60,
            textFormat: 'none'
        });
        // 自定义样式4
        new CircleProgress('#demoProgress12', {
            max: 4,
            value: 3,
            textFormat: 'vertical',
            clockwise: false
        });
        // 自定义样式5
        new CircleProgress('#demoProgress13', {
            max: 100,
            value: 20,
            textFormat: 'percent'
        });

    });
</script>
</body>
</html>