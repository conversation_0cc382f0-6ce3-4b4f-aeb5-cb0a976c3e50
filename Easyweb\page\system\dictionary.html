<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>字典管理</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        #dictTable + .layui-table-view .layui-table-tool-temp {
            padding-right: 0;
        }

        #dictTable + .layui-table-view .layui-table-body tbody > tr td {
            cursor: pointer;
        }

        #dictTable + .layui-table-view .layui-table-body tbody > tr.layui-table-click {
            background-color: #fff3e0;
        }

        #dictTable + .layui-table-view .layui-table-body tbody > tr.layui-table-click td:last-child > div:before {
            position: absolute;
            right: 6px;
            content: "\e602";
            font-size: 12px;
            font-style: normal;
            font-family: layui-icon !important;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid" style="padding-bottom: 0;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body" style="padding: 10px;">
                    <!-- 表格工具栏1 -->
                    <form class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline" style="max-width: 140px;">
                                <input name="dictName" class="layui-input" placeholder="输入字典名称"/>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn icon-btn" lay-filter="dictTbSearch" lay-submit>
                                    <i class="layui-icon">&#xe615;</i>搜索
                                </button>
                            </div>
                        </div>
                    </form>
                    <!-- 数据表格1 -->
                    <table id="dictTable" lay-filter="dictTable"></table>
                </div>
            </div>
        </div>
        <div class="layui-col-md9">
            <div class="layui-card">
                <div class="layui-card-body" style="padding: 10px;">
                    <!-- 表格工具栏2 -->
                    <form class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">字典名称:</label>
                                <div class="layui-input-inline">
                                    <input name="dictDataName" class="layui-input" placeholder="输入字典名称"/>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">字典键值:</label>
                                <div class="layui-input-inline">
                                    <input name="dictDataCode" class="layui-input" placeholder="输入字典键值"/>
                                </div>
                            </div>
                            <div class="layui-inline">&emsp;
                                <button class="layui-btn icon-btn" lay-filter="dictDataTbSearch" lay-submit>
                                    <i class="layui-icon">&#xe615;</i>搜索
                                </button>
                            </div>
                        </div>
                    </form>
                    <!-- 数据表格2 -->
                    <table id="dictDataTable" lay-filter="dictDataTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表单弹窗1 -->
<script type="text/html" id="dictEditDialog">
    <form id="dictEditForm" lay-filter="dictEditForm" class="layui-form model-form">
        <input name="dictId" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">字典名称:</label>
            <div class="layui-input-block">
                <input name="dictName" placeholder="请输入字典名称" class="layui-input"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">字典代码:</label>
            <div class="layui-input-block">
                <input name="dictCode" placeholder="请输入字典键值" class="layui-input"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">排序号:</label>
            <div class="layui-input-block">
                <input name="sortNumber" placeholder="请输入排序号" class="layui-input" type="number"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注:</label>
            <div class="layui-input-block">
                <textarea name="comments" placeholder="请输入备注" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="dictEditSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>
<!-- 表格操作列 -->
<script type="text/html" id="dictDataTbBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<!-- 表单弹窗 -->
<script type="text/html" id="dictDataEditDialog">
    <form id="dictDataEditForm" lay-filter="dictDataEditForm" class="layui-form model-form" style="padding-left: 10px;">
        <input name="dictDataId" type="hidden"/>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required" style="padding-left: 0;width: 95px;">字典项名称:</label>
            <div class="layui-input-block">
                <input name="dictDataName" placeholder="请输入字典项名称" class="layui-input"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required" style="padding-left: 0;width: 95px;">字典项代码:</label>
            <div class="layui-input-block">
                <input name="dictDataCode" placeholder="请输入字典键值" class="layui-input"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label layui-form-required">排序号:</label>
            <div class="layui-input-block">
                <input name="sortNumber" placeholder="请输入排序号" class="layui-input" type="number"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注:</label>
            <div class="layui-input-block">
                <textarea name="comments" placeholder="请输入备注" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="dictDataEditSubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>
<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var admin = layui.admin;
        var selObj;  // 左表选中数据

        /* 渲染表格 */
        var insTb = table.render({
            elem: '#dictTable',
            url: '../../json/dict.json',
            height: 'full-100',
            toolbar: ['<p>',
                '<button lay-event="add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="edit" class="layui-btn layui-btn-sm layui-btn-warm icon-btn"><i class="layui-icon">&#xe642;</i>修改</button>&nbsp;',
                '<button lay-event="del" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>',
                '</p>'].join(''),
            defaultToolbar: [],
            cols: [[
                {type: 'numbers'},
                {field: 'dictName', title: '字典名称'}
            ]],
            done: function (res, curr, count) {
                $('#dictTable+.layui-table-view .layui-table-body tbody>tr:first').trigger('click');
            }
        });

        /* 表格搜索 */
        form.on('submit(dictTbSearch)', function (data) {
            insTb.reload({where: data.field});
            return false;
        });

        /* 表格头工具栏点击事件 */
        table.on('toolbar(dictTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showEditModel();
            } else if (obj.event === 'edit') { // 修改
                showEditModel(selObj.data, selObj);
            } else if (obj.event === 'del') { // 删除
                doDel(selObj);
            }
        });

        /* 监听行单击事件 */
        table.on('row(dictTable)', function (obj) {
            selObj = obj;
            obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
            insTb2.reload({where: {dictId: obj.data.dictId}, page: {curr: 1}, url: '../../json/dict-data.json'});
        });

        /* 显示表单弹窗 */
        function showEditModel(mData, obj) {
            admin.open({
                type: 1,
                title: (mData ? '修改' : '添加') + '字典',
                content: $('#dictEditDialog').html(),
                success: function (layero, dIndex) {
                    // 回显表单数据
                    form.val('dictEditForm', mData);
                    // 表单提交事件
                    form.on('submit(dictEditSubmit)', function (data) {
                        var loadIndex = layer.load(2);
                        $.get(mData ? '../../json/ok.json' : '../../json/ok.json', data.field, function (res) {
                            layer.close(loadIndex);
                            if (200 === res.code) {
                                layer.close(dIndex);
                                layer.msg(res.msg, {icon: 1});
                                if (obj) {
                                    obj.update(data.field);
                                } else {
                                    insTb.reload();
                                }
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');
                        return false;
                    });
                }
            });
        }

        /* 删除 */
        function doDel(obj) {
            layer.confirm('确定要删除此字典吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                var loadIndex = layer.load(2);
                $.get('../../json/ok.json', {
                    id: obj.data.dictId,
                }, function (res) {
                    layer.close(loadIndex);
                    if (200 === res.code) {
                        layer.msg(res.msg, {icon: 1});
                        obj.del();
                        $('#dictTable+.layui-table-view .layui-table-body tbody>tr:first').trigger('click');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }

        /* 渲染表格2 */
        var insTb2 = table.render({
            elem: '#dictDataTable',
            data: [],
            height: 'full-100',
            page: true,
            toolbar: ['<p>',
                '<button lay-event="add" class="layui-btn layui-btn-sm icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>&nbsp;',
                '<button lay-event="del" class="layui-btn layui-btn-sm layui-btn-danger icon-btn"><i class="layui-icon">&#xe640;</i>删除</button>&nbsp;',
                '</p>'].join(''),
            cellMinWidth: 100,
            cols: [[
                {type: 'checkbox'},
                {type: 'numbers'},
                {field: 'dictDataName', title: '字典项名称', sort: true},
                {field: 'dictDataCode', title: '字典项代码', sort: true},
                {field: 'sortNumber', title: '排序号', sort: true, align: 'center', width: 90, minWidth: 90},
                {field: 'comments', title: '备注', sort: true},
                {
                    field: 'createTime', title: '创建时间', templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, sort: true
                },
                {title: '操作', toolbar: '#dictDataTbBar', align: 'center', width: 120, minWidth: 120}
            ]]
        });

        /* 表格2搜索 */
        form.on('submit(dictDataTbSearch)', function (data) {
            insTb2.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 表格2工具条点击事件 */
        table.on('tool(dictDataTable)', function (obj) {
            if (obj.event === 'edit') { // 修改
                showEditModel2(obj.data);
            } else if (obj.event === 'del') { // 删除
                doDel2(obj);
            }
        });

        /* 表格2头工具栏点击事件 */
        table.on('toolbar(dictDataTable)', function (obj) {
            if (obj.event === 'add') { // 添加
                showEditModel2();
            } else if (obj.event === 'del') { // 删除
                var checkRows = table.checkStatus('dictDataTable');
                if (checkRows.data.length === 0) {
                    layer.msg('请选择要删除的数据', {icon: 2});
                    return;
                }
                var ids = checkRows.data.map(function (d) {
                    return d.dictDataId;
                });
                doDel2({ids: ids});
            }
        });

        /* 显示表单弹窗2 */
        function showEditModel2(mData) {
            admin.open({
                type: 1,
                title: (mData ? '修改' : '添加') + '数据字典项',
                content: $('#dictDataEditDialog').html(),
                success: function (layero, dIndex) {
                    // 回显表单数据
                    form.val('dictDataEditForm', mData);
                    // 表单提交事件
                    form.on('submit(dictDataEditSubmit)', function (data) {
                        data.field.dictId = selObj.data.dictId;
                        var loadIndex = layer.load(2);
                        $.get(mData ? '../../json/ok.json' : '../../json/ok.json', data.field, function (res) {
                            layer.close(loadIndex);
                            if (200 === res.code) {
                                layer.close(dIndex);
                                layer.msg(res.msg, {icon: 1});
                                insTb2.reload({page: {curr: 1}});
                            } else {
                                layer.msg(res.msg, {icon: 2});
                            }
                        }, 'json');
                        return false;
                    });
                }
            });
        }

        /* 删除2 */
        function doDel2(obj) {
            layer.confirm('确定要删除选中数据吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function (i) {
                layer.close(i);
                var loadIndex = layer.load(2);
                $.get('../../json/ok.json', {
                    id: obj.data ? obj.data.dictDataId : '',
                    ids: obj.ids ? obj.ids.join(',') : ''
                }, function (res) {
                    layer.close(loadIndex);
                    if (200 === res.code) {
                        layer.msg(res.msg, {icon: 1});
                        insTb2.reload({page: {curr: 1}});
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }

    });
</script>
</body>
</html>