<?php
/**
 * 商家管理控制器
 */
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

class MerchantController {
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new Auth();
        
        // 检查管理员权限
        $this->checkAdminAuth();
        
        // 处理请求
        $action = isset($_GET['action']) ? $_GET['action'] : '';
        switch ($action) {
            case 'getList':
                $this->getList();
                break;
            case 'add':
                $this->add();
                break;
            case 'update':
                $this->update();
                break;
            case 'delete':
                $this->delete();
                break;
            case 'batchDelete':
                $this->batchDelete();
                break;
            case 'updateStatus':
                $this->updateStatus();
                break;
            case 'getApiList':
                $this->getApiList();
                break;
            case 'getSettlementList':
                $this->getSettlementList();
                break;
            case 'getIncomeData':
                $this->getIncomeData();
                break;
            default:
                $this->jsonResponse(['code' => 400, 'msg' => '未知操作']);
        }
    }
    
    /**
     * 检查管理员权限
     */
    private function checkAdminAuth() {
        $token = $this->auth->getTokenFromHeader();
        if (!$token) {
            $this->jsonResponse(['code' => 401, 'msg' => '未授权访问']);
        }
        
        $user = $this->auth->validateToken($token);
        if (!$user || $user['role'] !== 'admin') {
            $this->jsonResponse(['code' => 403, 'msg' => '没有权限执行此操作']);
        }
    }
    
    /**
     * 获取商家列表
     */
    public function getList() {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $offset = ($page - 1) * $limit;
        
        // 构建查询条件
        $where = "1=1";
        $params = [];
        
        if (isset($_GET['name']) && $_GET['name'] !== '') {
            $where .= " AND name LIKE ?";
            $params[] = '%' . $_GET['name'] . '%';
        }
        
        if (isset($_GET['level']) && $_GET['level'] !== '') {
            $where .= " AND level = ?";
            $params[] = $_GET['level'];
        }
        
        if (isset($_GET['status']) && $_GET['status'] !== '') {
            $where .= " AND status = ?";
            $params[] = $_GET['status'];
        }
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as count FROM merchants WHERE $where";
        $count = $this->db->fetchOne($countSql, $params)['count'];
        
        // 查询数据
        $sql = "SELECT * FROM merchants WHERE $where ORDER BY id DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        $list = $this->db->fetchAll($sql, $params);
        
        $this->jsonResponse([
            'code' => 0,
            'msg' => '',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 添加商家
     */
    public function add() {
        $data = [
            'name' => $_POST['name'] ?? '',
            'contact_name' => $_POST['contact_name'] ?? '',
            'contact_phone' => $_POST['contact_phone'] ?? '',
            'contact_email' => $_POST['contact_email'] ?? '',
            'level' => $_POST['level'] ?? 1,
            'commission_rate' => $_POST['commission_rate'] ?? 0,
            'settlement_cycle' => $_POST['settlement_cycle'] ?? 'monthly',
            'status' => $_POST['status'] ?? 1,
            'remark' => $_POST['remark'] ?? '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 验证数据
        if (empty($data['name'])) {
            $this->jsonResponse(['code' => 400, 'msg' => '商家名称不能为空']);
        }
        
        // 检查商家名称是否已存在
        $exists = $this->db->fetchOne(
            "SELECT id FROM merchants WHERE name = ?",
            [$data['name']]
        );
        
        if ($exists) {
            $this->jsonResponse(['code' => 400, 'msg' => '商家名称已存在']);
        }
        
        // 插入数据
        $result = $this->db->insert('merchants', $data);
        
        if ($result) {
            $this->jsonResponse(['code' => 200, 'msg' => '添加成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '添加失败']);
        }
    }
    
    /**
     * 更新商家
     */
    public function update() {
        $id = $_POST['id'] ?? 0;
        
        if (!$id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        $data = [
            'name' => $_POST['name'] ?? '',
            'contact_name' => $_POST['contact_name'] ?? '',
            'contact_phone' => $_POST['contact_phone'] ?? '',
            'contact_email' => $_POST['contact_email'] ?? '',
            'level' => $_POST['level'] ?? 1,
            'commission_rate' => $_POST['commission_rate'] ?? 0,
            'settlement_cycle' => $_POST['settlement_cycle'] ?? 'monthly',
            'status' => $_POST['status'] ?? 1,
            'remark' => $_POST['remark'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 验证数据
        if (empty($data['name'])) {
            $this->jsonResponse(['code' => 400, 'msg' => '商家名称不能为空']);
        }
        
        // 检查商家名称是否已存在（排除自身）
        $exists = $this->db->fetchOne(
            "SELECT id FROM merchants WHERE name = ? AND id != ?",
            [$data['name'], $id]
        );
        
        if ($exists) {
            $this->jsonResponse(['code' => 400, 'msg' => '商家名称已存在']);
        }
        
        // 更新数据
        $result = $this->db->update('merchants', $data, 'id = ?', [$id]);
        
        if ($result !== false) {
            $this->jsonResponse(['code' => 200, 'msg' => '更新成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '更新失败']);
        }
    }
    
    /**
     * 删除商家
     */
    public function delete() {
        $id = $_POST['id'] ?? 0;
        
        if (!$id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 检查商家是否存在
        $merchant = $this->db->fetchOne(
            "SELECT id FROM merchants WHERE id = ?",
            [$id]
        );
        
        if (!$merchant) {
            $this->jsonResponse(['code' => 400, 'msg' => '商家不存在']);
        }
        
        // 删除商家
        $result = $this->db->delete('merchants', 'id = ?', [$id]);
        
        if ($result) {
            $this->jsonResponse(['code' => 200, 'msg' => '删除成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '删除失败']);
        }
    }
    
    /**
     * 批量删除商家
     */
    public function batchDelete() {
        $ids = $_POST['ids'] ?? '';
        
        if (empty($ids)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        $idArr = explode(',', $ids);
        $placeholders = implode(',', array_fill(0, count($idArr), '?'));
        
        // 删除商家
        $result = $this->db->delete('merchants', "id IN ($placeholders)", $idArr);
        
        if ($result !== false) {
            $this->jsonResponse(['code' => 200, 'msg' => '删除成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '删除失败']);
        }
    }
    
    /**
     * 更新商家状态
     */
    public function updateStatus() {
        $id = $_POST['id'] ?? 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : -1;
        
        if (!$id || $status < 0) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 更新状态
        $result = $this->db->update(
            'merchants',
            ['status' => $status, 'updated_at' => date('Y-m-d H:i:s')],
            'id = ?',
            [$id]
        );
        
        if ($result !== false) {
            $this->jsonResponse(['code' => 200, 'msg' => '更新成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '更新失败']);
        }
    }
    
    /**
     * 获取商家API列表
     */
    public function getApiList() {
        $merchantId = $_GET['merchant_id'] ?? 0;
        
        if (!$merchantId) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 查询商家的API
        $sql = "SELECT a.*, c.name as category FROM apis a 
                LEFT JOIN api_categories c ON a.category_id = c.id
                WHERE a.merchant_id = ?";
        $list = $this->db->fetchAll($sql, [$merchantId]);
        
        $this->jsonResponse([
            'code' => 0,
            'msg' => '',
            'count' => count($list),
            'data' => $list
        ]);
    }
    
    /**
     * 获取商家结算记录
     */
    public function getSettlementList() {
        $merchantId = $_GET['merchant_id'] ?? 0;
        
        if (!$merchantId) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 查询商家的结算记录
        $sql = "SELECT * FROM merchant_settlements WHERE merchant_id = ? ORDER BY id DESC";
        $list = $this->db->fetchAll($sql, [$merchantId]);
        
        $this->jsonResponse([
            'code' => 0,
            'msg' => '',
            'count' => count($list),
            'data' => $list
        ]);
    }
    
    /**
     * 获取商家收入数据
     */
    public function getIncomeData() {
        $merchantId = $_GET['merchant_id'] ?? 0;
        
        if (!$merchantId) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 获取最近30天的数据
        $dates = [];
        $amounts = [];
        $calls = [];
        
        for ($i = 29; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $dates[] = $date;
            
            // 查询当天收入
            $income = $this->db->fetchOne(
                "SELECT SUM(amount) as total FROM merchant_incomes 
                WHERE merchant_id = ? AND DATE(created_at) = ?",
                [$merchantId, $date]
            );
            $amounts[] = floatval($income['total'] ?? 0);
            
            // 查询当天API调用次数
            $callCount = $this->db->fetchOne(
                "SELECT COUNT(*) as count FROM api_request_logs 
                WHERE merchant_id = ? AND DATE(created_at) = ?",
                [$merchantId, $date]
            );
            $calls[] = intval($callCount['count'] ?? 0);
        }
        
        $this->jsonResponse([
            'code' => 200,
            'msg' => '',
            'data' => [
                'dates' => $dates,
                'amounts' => $amounts,
                'calls' => $calls
            ]
        ]);
    }
    
    /**
     * 输出JSON响应
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// 实例化控制器
new MerchantController();