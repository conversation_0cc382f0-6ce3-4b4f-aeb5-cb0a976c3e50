<?php
/**
 * 系统配置管理模型
 * API管理系统 - 系统配置管理
 */

require_once __DIR__ . '/../core/Model.php';

class SystemConfig extends Model {
    protected $table = 'system_configs';
    protected $fillable = ['config_key', 'config_value', 'config_type', 'description', 'group_name'];
    
    // 配置类型常量
    const TYPE_STRING = 'string';
    const TYPE_INT = 'int';
    const TYPE_FLOAT = 'float';
    const TYPE_BOOL = 'bool';
    const TYPE_JSON = 'json';
    const TYPE_TEXT = 'text';
    
    /**
     * 获取配置值
     */
    public function getConfig($key, $default = null) {
        $config = $this->findByKey($key);
        if (!$config) {
            return $default;
        }
        
        return $this->parseConfigValue($config['config_value'], $config['config_type']);
    }
    
    /**
     * 设置配置值
     */
    public function setConfig($key, $value, $type = self::TYPE_STRING, $description = '', $group = 'system') {
        $configValue = $this->formatConfigValue($value, $type);
        
        $existing = $this->findByKey($key);
        if ($existing) {
            return $this->update($existing['id'], [
                'config_value' => $configValue,
                'config_type' => $type,
                'description' => $description,
                'group_name' => $group
            ]);
        } else {
            return $this->create([
                'config_key' => $key,
                'config_value' => $configValue,
                'config_type' => $type,
                'description' => $description,
                'group_name' => $group
            ]);
        }
    }
    
    /**
     * 根据键名查找配置
     */
    private function findByKey($key) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE config_key = :key";
        return $this->db->fetchOne($sql, ['key' => $key]);
    }
    
    /**
     * 解析配置值
     */
    private function parseConfigValue($value, $type) {
        switch ($type) {
            case self::TYPE_INT:
                return (int)$value;
            case self::TYPE_FLOAT:
                return (float)$value;
            case self::TYPE_BOOL:
                return (bool)$value;
            case self::TYPE_JSON:
                return json_decode($value, true);
            case self::TYPE_TEXT:
            case self::TYPE_STRING:
            default:
                return $value;
        }
    }
    
    /**
     * 格式化配置值
     */
    private function formatConfigValue($value, $type) {
        switch ($type) {
            case self::TYPE_JSON:
                return json_encode($value, JSON_UNESCAPED_UNICODE);
            case self::TYPE_BOOL:
                return $value ? '1' : '0';
            default:
                return (string)$value;
        }
    }
    
    /**
     * 获取配置组列表
     */
    public function getConfigGroups() {
        $sql = "SELECT group_name, COUNT(*) as count FROM {$this->getTableName()} GROUP BY group_name ORDER BY group_name";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * 获取指定组的配置
     */
    public function getConfigsByGroup($group) {
        $sql = "SELECT * FROM {$this->getTableName()} WHERE group_name = :group ORDER BY config_key";
        $configs = $this->db->fetchAll($sql, ['group' => $group]);
        
        $result = [];
        foreach ($configs as $config) {
            $result[$config['config_key']] = [
                'value' => $this->parseConfigValue($config['config_value'], $config['config_type']),
                'type' => $config['config_type'],
                'description' => $config['description']
            ];
        }
        
        return $result;
    }
    
    /**
     * 批量设置配置
     */
    public function setConfigs($configs) {
        try {
            $this->db->getPdo()->beginTransaction();
            
            foreach ($configs as $key => $data) {
                $value = $data['value'] ?? $data;
                $type = $data['type'] ?? self::TYPE_STRING;
                $description = $data['description'] ?? '';
                $group = $data['group'] ?? 'system';
                
                $this->setConfig($key, $value, $type, $description, $group);
            }
            
            $this->db->getPdo()->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->getPdo()->rollBack();
            throw $e;
        }
    }
    
    /**
     * 初始化默认配置
     */
    public function initDefaultConfigs() {
        $defaultConfigs = [
            // 网站基本配置
            'site_name' => [
                'value' => 'API管理系统',
                'type' => self::TYPE_STRING,
                'description' => '网站名称',
                'group' => 'site'
            ],
            'site_title' => [
                'value' => 'API管理系统 - 专业的API接口管理平台',
                'type' => self::TYPE_STRING,
                'description' => '网站标题',
                'group' => 'site'
            ],
            'site_keywords' => [
                'value' => 'API,接口,管理,平台',
                'type' => self::TYPE_STRING,
                'description' => '网站关键词',
                'group' => 'site'
            ],
            'site_description' => [
                'value' => '专业的API接口管理平台，提供API发布、管理、调用等服务',
                'type' => self::TYPE_STRING,
                'description' => '网站描述',
                'group' => 'site'
            ],
            'site_logo' => [
                'value' => '/assets/images/logo.png',
                'type' => self::TYPE_STRING,
                'description' => '网站Logo',
                'group' => 'site'
            ],
            'site_favicon' => [
                'value' => '/assets/images/favicon.ico',
                'type' => self::TYPE_STRING,
                'description' => '网站图标',
                'group' => 'site'
            ],
            
            // 邮件配置
            'mail_host' => [
                'value' => 'smtp.qq.com',
                'type' => self::TYPE_STRING,
                'description' => 'SMTP服务器',
                'group' => 'mail'
            ],
            'mail_port' => [
                'value' => 587,
                'type' => self::TYPE_INT,
                'description' => 'SMTP端口',
                'group' => 'mail'
            ],
            'mail_username' => [
                'value' => '',
                'type' => self::TYPE_STRING,
                'description' => '邮箱账号',
                'group' => 'mail'
            ],
            'mail_password' => [
                'value' => '',
                'type' => self::TYPE_STRING,
                'description' => '邮箱密码',
                'group' => 'mail'
            ],
            'mail_from_name' => [
                'value' => 'API管理系统',
                'type' => self::TYPE_STRING,
                'description' => '发件人名称',
                'group' => 'mail'
            ],
            
            // 支付配置
            'alipay_app_id' => [
                'value' => '',
                'type' => self::TYPE_STRING,
                'description' => '支付宝应用ID',
                'group' => 'payment'
            ],
            'alipay_private_key' => [
                'value' => '',
                'type' => self::TYPE_TEXT,
                'description' => '支付宝私钥',
                'group' => 'payment'
            ],
            'alipay_public_key' => [
                'value' => '',
                'type' => self::TYPE_TEXT,
                'description' => '支付宝公钥',
                'group' => 'payment'
            ],
            'wechat_app_id' => [
                'value' => '',
                'type' => self::TYPE_STRING,
                'description' => '微信应用ID',
                'group' => 'payment'
            ],
            'wechat_mch_id' => [
                'value' => '',
                'type' => self::TYPE_STRING,
                'description' => '微信商户号',
                'group' => 'payment'
            ],
            'wechat_key' => [
                'value' => '',
                'type' => self::TYPE_STRING,
                'description' => '微信支付密钥',
                'group' => 'payment'
            ],
            
            // 系统配置
            'system_timezone' => [
                'value' => 'Asia/Shanghai',
                'type' => self::TYPE_STRING,
                'description' => '系统时区',
                'group' => 'system'
            ],
            'system_language' => [
                'value' => 'zh-CN',
                'type' => self::TYPE_STRING,
                'description' => '系统语言',
                'group' => 'system'
            ],
            'api_rate_limit' => [
                'value' => 1000,
                'type' => self::TYPE_INT,
                'description' => 'API调用频率限制（次/小时）',
                'group' => 'system'
            ],
            'upload_max_size' => [
                'value' => 10,
                'type' => self::TYPE_INT,
                'description' => '文件上传大小限制（MB）',
                'group' => 'system'
            ],
            'allowed_file_types' => [
                'value' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
                'type' => self::TYPE_JSON,
                'description' => '允许上传的文件类型',
                'group' => 'system'
            ],
            
            // 安全配置
            'login_max_attempts' => [
                'value' => 5,
                'type' => self::TYPE_INT,
                'description' => '登录最大尝试次数',
                'group' => 'security'
            ],
            'login_lockout_time' => [
                'value' => 30,
                'type' => self::TYPE_INT,
                'description' => '登录锁定时间（分钟）',
                'group' => 'security'
            ],
            'password_min_length' => [
                'value' => 6,
                'type' => self::TYPE_INT,
                'description' => '密码最小长度',
                'group' => 'security'
            ],
            'enable_captcha' => [
                'value' => true,
                'type' => self::TYPE_BOOL,
                'description' => '启用验证码',
                'group' => 'security'
            ],
            'enable_ip_whitelist' => [
                'value' => false,
                'type' => self::TYPE_BOOL,
                'description' => '启用IP白名单',
                'group' => 'security'
            ]
        ];
        
        foreach ($defaultConfigs as $key => $config) {
            $existing = $this->findByKey($key);
            if (!$existing) {
                $this->setConfig($key, $config['value'], $config['type'], $config['description'], $config['group']);
            }
        }
    }
    
    /**
     * 获取所有配置（按组分类）
     */
    public function getAllConfigs() {
        $sql = "SELECT * FROM {$this->getTableName()} ORDER BY group_name, config_key";
        $configs = $this->db->fetchAll($sql);
        
        $result = [];
        foreach ($configs as $config) {
            $group = $config['group_name'];
            if (!isset($result[$group])) {
                $result[$group] = [];
            }
            
            $result[$group][$config['config_key']] = [
                'value' => $this->parseConfigValue($config['config_value'], $config['config_type']),
                'type' => $config['config_type'],
                'description' => $config['description']
            ];
        }
        
        return $result;
    }
    
    /**
     * 删除配置
     */
    public function deleteConfig($key) {
        $config = $this->findByKey($key);
        if ($config) {
            return $this->delete($config['id']);
        }
        return false;
    }
    
    /**
     * 清空配置缓存
     */
    public function clearCache() {
        // 如果使用了缓存系统，在这里清空缓存
        // 例如：Redis::del('system_configs');
        return true;
    }
}