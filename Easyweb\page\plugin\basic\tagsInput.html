<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>标签输入框</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        .layui-card-body {
            padding: 45px 15px;
        }
    </style>
</head>
<body>

<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">输入框样式</div>
                <div class="layui-card-body">
                    <div class="layui-form-item" style="margin-bottom: 14px;">
                        <label class="layui-form-label">请输入标签</label>
                        <div class="layui-input-block" style="max-width: 280px;">
                            <input id="demoTagsInput1" value="辣妹子,大长腿" class="layui-hide"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">无边框样式</div>
                <div class="layui-card-body">
                    <div class="layui-form-item">
                        <label class="layui-form-label">请输入标签</label>
                        <div class="layui-input-block">
                            <input id="demoTagsInput2" value="辣妹子,大长腿" class="layui-hide"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="layui-row layui-col-space15">

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">BackSpace键可删除标签</div>
                <div class="layui-card-body">
                    <div class="layui-form-item" style="margin-bottom: 14px;">
                        <label class="layui-form-label">请输入标签</label>
                        <div class="layui-input-block" style="max-width: 280px;">
                            <input id="demoTagsInput3" value="辣妹子,大长腿" class="layui-hide"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-col-xs12 layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">输入自动提示</div>
                <div class="layui-card-body">
                    <div class="layui-form-item">
                        <label class="layui-form-label">请输入标签</label>
                        <div class="layui-input-block">
                            <input id="demoTagsInput4" value="辣妹子,大长腿" class="layui-hide"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['jquery', 'tagsInput'], function () {
        var $ = layui.jquery;
        var tagsInput = layui.tagsInput;

        // 输入框样式
        $('#demoTagsInput1').tagsInput();

        // 无边框样式
        $('#demoTagsInput2').tagsInput({skin: 'tagsinput-default'});

        // BackSpace键可删除标签
        $('#demoTagsInput3').tagsInput({removeWithBackspace: true});

        // 输入列表提示
        $('#demoTagsInput4').tagsInput({
            skin: 'tagsinput-default',
            autocomplete_url: '../../../json/tagsInput.json'
        });

    });
</script>
</body>
</html>