<?php
/**
 * 工单管理控制器
 */
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

class TicketController {
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->auth = new Auth();
        
        // 检查管理员权限
        $this->checkAdminAuth();
    }
    
    /**
     * 获取工单列表
     */
    public function getList() {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $title = $_GET['title'] ?? '';
        $status = $_GET['status'] ?? '';
        $priority = $_GET['priority'] ?? '';
        
        $offset = ($page - 1) * $limit;
        $where = "1=1";
        $params = [];
        
        if (!empty($title)) {
            $where .= " AND t.title LIKE ?";
            $params[] = '%' . $title . '%';
        }
        
        if (!empty($status)) {
            $where .= " AND t.status = ?";
            $params[] = $status;
        }
        
        if (!empty($priority)) {
            $where .= " AND t.priority = ?";
            $params[] = $priority;
        }
        
        // 获取总数
        $total = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM tickets t WHERE {$where}",
            $params
        )['count'];
        
        // 获取数据
        $data = $this->db->fetchAll(
            "SELECT t.*, u.username, a.username as admin_name 
             FROM tickets t 
             LEFT JOIN users u ON t.user_id = u.id 
             LEFT JOIN users a ON t.admin_id = a.id 
             WHERE {$where} 
             ORDER BY t.id DESC 
             LIMIT {$limit} OFFSET {$offset}",
            $params
        );
        
        $this->jsonResponse([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $data
        ]);
    }
    
    /**
     * 获取工单详情
     */
    public function getDetail() {
        $id = $_GET['id'] ?? 0;
        
        if (empty($id)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 获取工单信息
        $ticket = $this->db->fetchOne(
            "SELECT t.*, u.username, a.username as admin_name 
             FROM tickets t 
             LEFT JOIN users u ON t.user_id = u.id 
             LEFT JOIN users a ON t.admin_id = a.id 
             WHERE t.id = ?",
            [$id]
        );
        
        if (!$ticket) {
            $this->jsonResponse(['code' => 404, 'msg' => '工单不存在']);
        }
        
        // 获取回复列表
        $replies = $this->db->fetchAll(
            "SELECT r.*, u.username, r.is_admin 
             FROM ticket_replies r 
             LEFT JOIN users u ON r.user_id = u.id 
             WHERE r.ticket_id = ? 
             ORDER BY r.id ASC",
            [$id]
        );
        
        $this->jsonResponse([
            'code' => 200,
            'msg' => '',
            'data' => [
                'ticket' => $ticket,
                'replies' => $replies
            ]
        ]);
    }
    
    /**
     * 更新工单信息
     */
    public function updateTicket() {
        $id = $_POST['id'] ?? 0;
        $status = $_POST['status'] ?? '';
        $priority = $_POST['priority'] ?? '';
        $admin_id = $_POST['admin_id'] ?? null;
        
        if (empty($id) || empty($status) || empty($priority)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数不完整']);
        }
        
        try {
            $this->db->update('tickets', [
                'status' => $status,
                'priority' => $priority,
                'admin_id' => $admin_id ?: null,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$id]);
            
            // 如果状态变更，记录状态变更历史
            $this->logTicketStatusChange($id, $status);
            
            $this->jsonResponse(['code' => 200, 'msg' => '更新成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 添加回复
     */
    public function addReply() {
        $ticket_id = $_POST['ticket_id'] ?? 0;
        $content = $_POST['content'] ?? '';
        
        if (empty($ticket_id) || empty($content)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数不完整']);
        }
        
        // 获取当前管理员ID
        $admin_id = $this->auth->getUserId();
        
        try {
            // 添加回复
            $this->db->insert('ticket_replies', [
                'ticket_id' => $ticket_id,
                'user_id' => $admin_id,
                'content' => $content,
                'is_admin' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // 更新工单状态为处理中
            $this->db->update('tickets', [
                'status' => 'processing',
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ? AND status = ?', [$ticket_id, 'pending']);
            
            // 发送邮件通知用户
            $this->sendReplyNotification($ticket_id);
            
            $this->jsonResponse(['code' => 200, 'msg' => '回复成功']);
        } catch (Exception $e) {
            $this->jsonResponse(['code' => 500, 'msg' => '回复失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 删除工单
     */
    public function delete() {
        $id = $_POST['id'] ?? 0;
        
        if (empty($id)) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        try {
            $this->db->getConnection()->beginTransaction();
            
            // 删除工单回复
            $this->db->delete('ticket_replies', 'ticket_id = ?', [$id]);
            
            // 删除工单
            $this->db->delete('tickets', 'id = ?', [$id]);
            
            $this->db->getConnection()->commit();
            $this->jsonResponse(['code' => 200, 'msg' => '删除成功']);
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            $this->jsonResponse(['code' => 500, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取管理员列表
     */
    public function getAdmins() {
        $admins = $this->db->fetchAll(
            "SELECT id, username FROM users WHERE role = 'admin' ORDER BY id ASC"
        );
        
        $this->jsonResponse(['code' => 200, 'data' => $admins]);
    }
    
    /**
     * 记录工单状态变更历史
     */
    private function logTicketStatusChange($ticket_id, $status) {
        $admin_id = $this->auth->getUserId();
        
        $this->db->insert('ticket_logs', [
            'ticket_id' => $ticket_id,
            'user_id' => $admin_id,
            'action' => 'status_change',
            'content' => json_encode(['status' => $status]),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 发送回复通知邮件
     */
    private function sendReplyNotification($ticket_id) {
        // 获取工单信息和用户邮箱
        $ticket = $this->db->fetchOne(
            "SELECT t.*, u.email, u.username 
             FROM tickets t 
             LEFT JOIN users u ON t.user_id = u.id 
             WHERE t.id = ?",
            [$ticket_id]
        );
        
        if (!$ticket || empty($ticket['email'])) {
            return;
        }
        
        // 获取系统配置
        $configs = $this->db->fetchAll(
            "SELECT `key`, `value` FROM system_configs WHERE `key` LIKE 'smtp_%' OR `key` LIKE 'from_%' OR `key` = 'site_name'"
        );
        
        $config = [];
        foreach ($configs as $item) {
            $config[$item['key']] = $item['value'];
        }
        
        // 邮件内容
        $subject = '您的工单有新回复 - ' . ($config['site_name'] ?? 'API商业系统');
        $body = "尊敬的 {$ticket['username']}，<br><br>";
        $body .= "您的工单 <strong>{$ticket['title']}</strong> (编号: {$ticket['ticket_no']}) 有新的回复。<br><br>";
        $body .= "请登录系统查看详情：<a href='http://{$_SERVER['HTTP_HOST']}/user/ticket/detail.html?id={$ticket_id}'>点击查看</a><br><br>";
        $body .= "如有任何问题，请随时与我们联系。<br><br>";
        $body .= "此致，<br>";
        $body .= ($config['site_name'] ?? 'API商业系统') . " 团队";
        
        // 发送邮件
        // 实际项目中应该使用PHPMailer或其他邮件库
        $headers = [
            'From: ' . ($config['from_name'] ?? 'API商业系统') . ' <' . ($config['from_email'] ?? '<EMAIL>') . '>',
            'Reply-To: ' . ($config['from_email'] ?? '<EMAIL>'),
            'Content-Type: text/html; charset=UTF-8'
        ];
        
        @mail($ticket['email'], $subject, $body, implode("\r\n", $headers));
    }
    
    /**
     * 检查管理员权限
     */
    private function checkAdminAuth() {
        if (!$this->auth->checkAuth() || !$this->auth->isAdmin()) {
            $this->jsonResponse(['code' => 401, 'msg' => '无权限访问']);
        }
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

// 处理请求
$action = $_GET['action'] ?? '';
$controller = new TicketController();

switch ($action) {
    case 'getList':
        $controller->getList();
        break;
    case 'getDetail':
        $controller->getDetail();
        break;
    case 'updateTicket':
        $controller->updateTicket();
        break;
    case 'addReply':
        $controller->addReply();
        break;
    case 'delete':
        $controller->delete();
        break;
    case 'getAdmins':
        $controller->getAdmins();
        break;
    default:
        header('HTTP/1.1 404 Not Found');
        echo json_encode(['code' => 404, 'msg' => '接口不存在']);
        break;
}