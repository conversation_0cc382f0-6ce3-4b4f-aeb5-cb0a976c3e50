<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <h2 class="header-title">用户管理</h2>
            <div class="layui-btn-group">
                <button class="layui-btn" id="btnAdd"><i class="layui-icon layui-icon-add-1"></i>添加用户</button>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 搜索表单 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">用户名:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="username" placeholder="用户名" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">邮箱:</label>
                        <div class="layui-input-inline">
                            <input type="text" name="email" placeholder="邮箱" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label w-auto">角色:</label>
                        <div class="layui-input-inline">
                            <select name="role">
                                <option value="">全部角色</option>
                                <option value="admin">管理员</option>
                                <option value="merchant">商家</option>
                                <option value="user">普通用户</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="searchBtn" lay-submit>
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary icon-btn">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                </div>
            </form>
            
            <!-- 数据表格 -->
            <table id="userTable" lay-filter="userTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs" lay-event="recharge">充值</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="statusSwitch" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status==1?'checked':''}} />
</script>

<!-- 表格角色列 -->
<script type="text/html" id="roleTpl">
    {{#  if(d.role == 'admin'){ }}
    <span class="layui-badge layui-bg-red">管理员</span>
    {{#  } else if(d.role == 'merchant'){ }}
    <span class="layui-badge layui-bg-blue">商家</span>
    {{#  } else { }}
    <span class="layui-badge layui-bg-green">普通用户</span>
    {{#  } }}
</script>

<!-- 添加/编辑用户弹窗 -->
<script type="text/html" id="userFormTpl">
    <form class="layui-form" lay-filter="userForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <input type="text" name="username" placeholder="请输入用户名" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-block">
                <input type="text" name="email" placeholder="请输入邮箱" class="layui-input" lay-verify="required|email">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
                <input type="text" name="mobile" placeholder="请输入手机号" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <input type="password" name="password" placeholder="请输入密码，不修改请留空" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">角色</label>
            <div class="layui-input-block">
                <select name="role" lay-verify="required">
                    <option value="user">普通用户</option>
                    <option value="merchant">商家</option>
                    <option value="admin">管理员</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" id="merchantLevelItem" style="display: none;">
            <label class="layui-form-label">商家等级</label>
            <div class="layui-input-block">
                <select name="merchant_level" id="merchantLevelSelect">
                    <option value="0">请选择商家等级</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" id="vipLevelItem">
            <label class="layui-form-label">会员等级</label>
            <div class="layui-input-block">
                <select name="vip_level" id="vipLevelSelect">
                    <option value="0">普通会员</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" id="vipExpireItem" style="display: none;">
            <label class="layui-form-label">会员到期</label>
            <div class="layui-input-block">
                <input type="text" name="vip_expire" id="vipExpire" placeholder="请选择会员到期时间" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-filter="userSubmit" lay-submit>保存</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<!-- 充值弹窗 -->
<script type="text/html" id="rechargeFormTpl">
    <form class="layui-form" lay-filter="rechargeForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id" value="{{d.id}}">
        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <div class="layui-form-mid">{{d.username}}</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">当前余额</label>
            <div class="layui-input-block">
                <div class="layui-form-mid">{{d.balance}} 元</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">充值金额</label>
            <div class="layui-input-block">
                <input type="number" name="amount" placeholder="请输入充值金额" class="layui-input" lay-verify="required|number">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-filter="rechargeSubmit" lay-submit>确认充值</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../../Easyweb/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script>
layui.use(['table', 'form', 'layer', 'laytpl', 'laydate'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var laydate = layui.laydate;
    var $ = layui.jquery;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#userTable',
        url: '../controllers/UserController.php?action=getUserList',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'username', title: '用户名', sort: true},
            {field: 'email', title: '邮箱', sort: true},
            {field: 'mobile', title: '手机号'},
            {field: 'role', title: '角色', templet: '#roleTpl', width: 100, sort: true},
            {field: 'balance', title: '余额', width: 100, sort: true},
            {field: 'api_key', title: 'API密钥'},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100, sort: true},
            {field: 'created_at', title: '注册时间', sort: true},
            {field: 'last_login', title: '最后登录', sort: true},
            {title: '操作', toolbar: '#tableBar', width: 180, fixed: 'right'}
        ]]
    });
    
    // 表格工具条点击事件
    table.on('tool(userTable)', function(obj){
        var data = obj.data;
        var layEvent = obj.event;
        
        if(layEvent === 'edit'){ // 修改
            showEditForm(data);
        } else if(layEvent === 'del'){ // 删除
            layer.confirm('确定要删除该用户吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(i){
                layer.close(i);
                
                $.get('../controllers/UserController.php?action=deleteUser&id=' + data.id, function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        } else if(layEvent === 'recharge'){ // 充值
            showRechargeForm(data);
        }
    });
    
    // 表单提交事件
    form.on('submit(userSubmit)', function(data){
        var loadIndex = layer.load(2);
        
        $.ajax({
            url: '../controllers/UserController.php?action=' + (data.field.id ? 'editUser' : 'addUser'),
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function(res){
                layer.close(loadIndex);
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                    layer.closeAll('page');
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(){
                layer.close(loadIndex);
                layer.msg('服务器错误', {icon: 2});
            }
        });
        
        return false;
    });
    
    // 充值表单提交事件
    form.on('submit(rechargeSubmit)', function(data){
        var loadIndex = layer.load(2);
        
        $.ajax({
            url: '../controllers/UserController.php?action=rechargeUser',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data.field),
            success: function(res){
                layer.close(loadIndex);
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                    layer.closeAll('page');
                    insTb.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function(){
                layer.close(loadIndex);
                layer.msg('服务器错误', {icon: 2});
            }
        });
        
        return false;
    });
    
    // 添加按钮点击事件
    $('#btnAdd').click(function(){
        showEditForm();
    });
    
    // 状态开关点击事件
    form.on('switch(statusSwitch)', function(obj){
        var loadIndex = layer.load(2);
        
        $.get('../controllers/UserController.php?action=changeUserStatus&id=' + obj.value + '&status=' + (obj.elem.checked ? 1 : 0), function(res){
            layer.close(loadIndex);
            if(res.code !== 0){
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });
    
    // 搜索按钮点击事件
    form.on('submit(searchBtn)', function(data){
        insTb.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 角色选择事件
    form.on('select(role)', function(data){
        if(data.value === 'merchant'){
            $('#merchantLevelItem').show();
            loadMerchantLevels();
        } else {
            $('#merchantLevelItem').hide();
        }
    });
    
    // 会员等级选择事件
    form.on('select(vipLevel)', function(data){
        if(data.value > 0){
            $('#vipExpireItem').show();
        } else {
            $('#vipExpireItem').hide();
        }
    });
    
    // 加载商家等级
    function loadMerchantLevels(){
        $.get('../controllers/MerchantController.php?action=getLevels', function(res){
            if(res.code === 0){
                var options = '<option value="0">请选择商家等级</option>';
                
                $.each(res.data, function(i, item){
                    options += '<option value="' + item.id + '">' + item.name + '</option>';
                });
                
                $('#merchantLevelSelect').html(options);
                form.render('select');
            }
        }, 'json');
    }
    
    // 加载会员等级
    function loadVipLevels(){
        $.get('../controllers/UserController.php?action=getVipLevels', function(res){
            if(res.code === 0){
                var options = '<option value="0">普通会员</option>';
                
                $.each(res.data, function(i, item){
                    options += '<option value="' + item.id + '">' + item.name + '</option>';
                });
                
                $('#vipLevelSelect').html(options);
                form.render('select');
            }
        }, 'json');
    }
    
    // 显示编辑表单
    function showEditForm(data){
        var title = data ? '编辑用户' : '添加用户';
        
        layer.open({
            type: 1,
            title: title,
            area: ['500px', '600px'],
            content: laytpl($('#userFormTpl').html()).render({}),
            success: function(layero, index){
                // 加载会员等级
                loadVipLevels();
                
                // 渲染日期选择器
                laydate.render({
                    elem: '#vipExpire',
                    type: 'datetime'
                });
                
                // 监听角色选择
                form.on('select(role)', function(data){
                    if(data.value === 'merchant'){
                        $('#merchantLevelItem').show();
                        loadMerchantLevels();
                    } else {
                        $('#merchantLevelItem').hide();
                    }
                });
                
                // 监听会员等级选择
                form.on('select(vipLevel)', function(data){
                    if(data.value > 0){
                        $('#vipExpireItem').show();
                    } else {
                        $('#vipExpireItem').hide();
                    }
                });
                
                form.render();
                
                // 如果是编辑，加载数据
                if(data){
                    form.val('userForm', data);
                    
                    if(data.role === 'merchant'){
                        $('#merchantLevelItem').show();
                        loadMerchantLevels();
                    }
                    
                    if(data.vip_level > 0){
                        $('#vipExpireItem').show();
                    }
                }
            }
        });
    }
    
    // 显示充值表单
    function showRechargeForm(data){
        layer.open({
            type: 1,
            title: '用户充值',
            area: ['500px', '400px'],
            content: laytpl($('#rechargeFormTpl').html()).render(data),
            success: function(){
                form.render();
            }
        });
    }
    
    // 初始化加载
    loadVipLevels();
});
</script>
</body>
</html>