<?php
/**
 * 安装处理脚本
 */
session_start();

// 检查是否已安装
if (file_exists('../config/installed.lock')) {
    die('系统已安装');
}

$step = $_POST['step'] ?? 0;

if ($step == 2) {
    // 处理数据库配置
    $dbConfig = [
        'host' => $_POST['db_host'],
        'port' => $_POST['db_port'] ?? 3306,
        'dbname' => $_POST['db_name'],
        'username' => $_POST['db_username'],
        'password' => $_POST['db_password'],
        'charset' => 'utf8mb4'
    ];
    
    // 测试数据库连接
    try {
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 创建数据库（如果不存在）
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbConfig['dbname']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // 保存数据库配置到session
        $_SESSION['db_config'] = $dbConfig;
        
        // 跳转到下一步
        header('Location: install.php?step=3');
        exit;
        
    } catch (PDOException $e) {
        $error = '数据库连接失败: ' . $e->getMessage();
        header('Location: install.php?step=2&error=' . urlencode($error));
        exit;
    }
    
} elseif ($step == 3) {
    // 处理管理员设置和系统安装
    $adminUsername = $_POST['admin_username'];
    $adminEmail = $_POST['admin_email'];
    $adminPassword = $_POST['admin_password'];
    $adminPasswordConfirm = $_POST['admin_password_confirm'];
    
    // 验证密码
    if ($adminPassword !== $adminPasswordConfirm) {
        header('Location: install.php?step=3&error=' . urlencode('两次密码输入不一致'));
        exit;
    }
    
    if (strlen($adminPassword) < 6) {
        header('Location: install.php?step=3&error=' . urlencode('密码长度不能少于6位'));
        exit;
    }
    
    // 获取数据库配置
    $dbConfig = $_SESSION['db_config'] ?? null;
    if (!$dbConfig) {
        header('Location: install.php?step=2&error=' . urlencode('数据库配置丢失，请重新配置'));
        exit;
    }
    
    try {
        // 连接数据库
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 执行数据库结构文件
        $sqlFile = __DIR__ . '/database.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('数据库结构文件不存在');
        }
        
        $sql = file_get_contents($sqlFile);
        $pdo->exec($sql);
        
        // 更新管理员账户
        $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
        $apiKey = 'ak_' . bin2hex(random_bytes(16));
        
        $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ?, api_key = ? WHERE role = 'admin'");
        $stmt->execute([$adminUsername, $adminEmail, $hashedPassword, $apiKey]);
        
        // 生成配置文件
        generateConfigFiles($dbConfig);
        
        // 创建安装锁定文件
        file_put_contents('../config/installed.lock', date('Y-m-d H:i:s'));
        
        // 保存管理员信息到session
        $_SESSION['admin_username'] = $adminUsername;
        
        // 跳转到完成页面
        header('Location: install.php?step=4');
        exit;
        
    } catch (Exception $e) {
        header('Location: install.php?step=3&error=' . urlencode('安装失败: ' . $e->getMessage()));
        exit;
    }
}

/**
 * 生成配置文件
 */
function generateConfigFiles($dbConfig) {
    // 生成数据库配置文件
    $databaseConfigContent = "<?php\n/**\n * 数据库配置文件\n */\nreturn [\n";
    $databaseConfigContent .= "    'host' => '{$dbConfig['host']}',\n";
    $databaseConfigContent .= "    'dbname' => '{$dbConfig['dbname']}',\n";
    $databaseConfigContent .= "    'username' => '{$dbConfig['username']}',\n";
    $databaseConfigContent .= "    'password' => '{$dbConfig['password']}',\n";
    $databaseConfigContent .= "    'charset' => '{$dbConfig['charset']}',\n";
    $databaseConfigContent .= "    'options' => [\n";
    $databaseConfigContent .= "        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
    $databaseConfigContent .= "        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
    $databaseConfigContent .= "        PDO::ATTR_EMULATE_PREPARES => false,\n";
    $databaseConfigContent .= "    ]\n";
    $databaseConfigContent .= "];";
    
    file_put_contents('../config/database.php', $databaseConfigContent);
    
    // 生成应用配置文件
    $jwtSecret = bin2hex(random_bytes(32));
    $passwordSalt = bin2hex(random_bytes(16));
    
    $appConfigContent = "<?php\n/**\n * 应用配置文件\n */\nreturn [\n";
    $appConfigContent .= "    'app_name' => 'API商业系统',\n";
    $appConfigContent .= "    'app_version' => '1.0.0',\n";
    $appConfigContent .= "    'debug' => false,\n";
    $appConfigContent .= "    'timezone' => 'Asia/Shanghai',\n";
    $appConfigContent .= "    'default_language' => 'zh-cn',\n";
    $appConfigContent .= "    \n";
    $appConfigContent .= "    // 安全配置\n";
    $appConfigContent .= "    'security' => [\n";
    $appConfigContent .= "        'jwt_secret' => '{$jwtSecret}',\n";
    $appConfigContent .= "        'password_salt' => '{$passwordSalt}',\n";
    $appConfigContent .= "        'session_lifetime' => 7200, // 2小时\n";
    $appConfigContent .= "    ],\n";
    $appConfigContent .= "    \n";
    $appConfigContent .= "    // 文件上传配置\n";
    $appConfigContent .= "    'upload' => [\n";
    $appConfigContent .= "        'max_size' => 10 * 1024 * 1024, // 10MB\n";
    $appConfigContent .= "        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],\n";
    $appConfigContent .= "        'upload_path' => 'uploads/',\n";
    $appConfigContent .= "    ],\n";
    $appConfigContent .= "    \n";
    $appConfigContent .= "    // API配置\n";
    $appConfigContent .= "    'api' => [\n";
    $appConfigContent .= "        'rate_limit' => 1000, // 每小时请求限制\n";
    $appConfigContent .= "        'qps_limit' => 10, // 每秒请求限制\n";
    $appConfigContent .= "        'default_timeout' => 30,\n";
    $appConfigContent .= "    ],\n";
    $appConfigContent .= "];";
    
    file_put_contents('../config/app.php', $appConfigContent);
    
    // 创建uploads目录
    if (!is_dir('../uploads')) {
        mkdir('../uploads', 0755, true);
    }
    
    // 创建.htaccess文件
    $htaccessContent = "RewriteEngine On\n";
    $htaccessContent .= "RewriteCond %{REQUEST_FILENAME} !-f\n";
    $htaccessContent .= "RewriteCond %{REQUEST_FILENAME} !-d\n";
    $htaccessContent .= "RewriteRule ^api/(.*)$ api/index.php [QSA,L]\n";
    $htaccessContent .= "RewriteRule ^admin/(.*)$ admin/index.php [QSA,L]\n";
    
    file_put_contents('../.htaccess', $htaccessContent);
}