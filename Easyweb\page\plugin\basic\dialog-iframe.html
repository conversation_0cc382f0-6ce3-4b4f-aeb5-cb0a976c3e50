<!DOCTYPE html>
<html lang="en" class="bg-white">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>admin风格的弹窗</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<div style="padding: 25px 25px 15px 25px;">
    <div class="layui-text">
        这是一个iframe类型的弹窗，可以通过admin.getLayerData()方法接收上个页面传递的数据，
        下面的这个关闭按钮不需要写js，只用增加ew-event="closeDialog"属性。
    </div>
    <div class="text-center" style="padding-top: 15px;">
        <button class="layui-btn" ew-event="closeDialog">关闭我</button>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var admin = layui.admin;
        admin.iframeAuto();

        var layerData = admin.getLayerData();
        parent.layui.layer.msg('我收到了的数据：' + JSON.stringify(layerData), {icon: 2});

    });
</script>
</body>
</html>