<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>富文本编辑器</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=314"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /**  */
        .ew-datagrid-loadmore, .ew-datagrid-page {
            margin-top: 10px;
            text-align: center;
        }

        .ew-datagrid-loadmore {
            color: #666;
            cursor: pointer;
        }

        .ew-datagrid-loadmore > div {
            padding: 12px;
        }

        .ew-datagrid-loadmore > div:hover {
            background-color: rgba(0, 0, 0, .03);
        }

        .ew-datagrid-loadmore .ew-icon-loading {
            margin-right: 6px;
            display: none;
        }

        .ew-datagrid-loadmore.ew-loading .ew-icon-loading {
            display: inline
        }

        /**  */

        .demo-grid-item {
            background-color: #f8f8f8;
            border-radius: 3px;
            padding: 8px 10px;
            color: #333;
            position: relative;
        }

        .demo-grid-item .layui-badge {
            width: 18px;
            height: 18px;
            line-height: 18px;
            border-radius: 50%;
            padding: 0;
            margin-right: 10px;
        }

        .demo-grid-item img {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: 2px solid #fff;
        }

        .demo-grid-item .dropdown-menu {
            position: absolute;
            right: 0;
            top: 50%;
            margin-top: -12px;
        }

        .demo-grid-item .dropdown-menu .layui-badge {
            cursor: pointer;
        }

        .demo-grid-item .dropdown-menu .layui-badge > .layui-icon {
        }

        .demo-grid-item .dropdown-menu .dropdown-menu-nav {
            min-width: 85px;
        }

        .demo-data-grid {
            overflow-y: auto;
        }

        .demo-grid-tab {
            margin: 0;
        }

        .demo-grid-tab > .layui-tab-title {
            background-color: #fff;
            border-radius: 2px;
            border: none;
        }

        .demo-grid-tab > .layui-tab-title .layui-this:after {
            height: 40px;
        }

        .demo-grid-tab > .layui-tab-content {
            padding-left: 0;
            padding-right: 0;
            padding-bottom: 0;
        }
    </style>
</head>
<body>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<!-- 正文开始 -->
<div class="layui-fluid" style="padding-bottom: 0;">
    <div class="layui-tab layui-tab-brief demo-grid-tab">
        <ul class="layui-tab-title">
            <li class="layui-this">基础用法</li>
            <li>进阶用法</li>
            <li>自动渲染</li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">数据方式(前端分页)</div>
                            <div class="layui-card-body demo-data-grid">
                                <div class="layui-row layui-col-space15" id="demoGrid1"></div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">url方式(后端分页)</div>
                            <div class="layui-card-body demo-data-grid">
                                <div class="layui-row layui-col-space15" id="demoGrid2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-tab-item ">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">加载更多模式</div>
                            <div class="layui-card-body demo-data-grid">
                                <div class="layui-row layui-col-space15" id="demoGrid3"></div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">自定义异步方式</div>
                            <div class="layui-card-body demo-data-grid">
                                <div class="layui-row layui-col-space15" id="demoGrid4"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-tab-item">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">无js方式</div>
                            <div class="layui-card-body demo-data-grid">
                                <div class="layui-row layui-col-space15" lay-data="{data:[{},{},{}],page:true}"
                                     data-grid>
                                    <script type="text/html" data-grid-tpl>
                                        <div class="layui-col-md4">
                                            <div class="demo-grid-item">
                                                <span class="layui-badge layui-bg-blue">{{d.LAY_NUMBER}}</span>
                                                <img src="../../../assets/images/head.png"/>
                                                <span>{{d.nickName||'用户'}}</span>
                                                <div class="dropdown-menu dropdown-hover">
                                                    <span class="layui-badge layui-bg-gray">
                                                        <i class="layui-icon layui-icon-more-vertical"></i>
                                                    </span>
                                                    <ul class="dropdown-menu-nav dropdown-bottom-right">
                                                        <div class="dropdown-anchor"></div>
                                                        <li><a><i class="layui-icon layui-icon-tips"></i>详情</a></li>
                                                        <li><a><i class="layui-icon layui-icon-edit"></i>修改</a></li>
                                                        <li><a><i class="layui-icon layui-icon-delete"></i>删除</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header">无js方式(加载更多模式)</div>
                            <div class="layui-card-body demo-data-grid">
                                <div class="layui-row layui-col-space15" lay-data="{data:[{},{},{}],loadMore:true}"
                                     data-grid>
                                    <script type="text/html" data-grid-tpl>
                                        <div class="layui-col-md4">
                                            <div class="demo-grid-item">
                                                <span class="layui-badge layui-bg-blue">{{d.LAY_NUMBER}}</span>
                                                <img src="../../../assets/images/head.png"/>
                                                <span>{{d.nickName}}</span>
                                                <div class="dropdown-menu dropdown-hover">
                                                    <span class="layui-badge layui-bg-gray">
                                                        <i class="layui-icon layui-icon-more-vertical"></i>
                                                    </span>
                                                    <ul class="dropdown-menu-nav dropdown-bottom-right">
                                                        <div class="dropdown-anchor"></div>
                                                        <li><a><i class="layui-icon layui-icon-tips"></i>详情</a></li>
                                                        <li><a><i class="layui-icon layui-icon-edit"></i>修改</a></li>
                                                        <li><a><i class="layui-icon layui-icon-delete"></i>删除</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- 模板1 -->
<script type="text/html" id="demoGridItem1">
    <div class="layui-col-md4">
        <div class="demo-grid-item">
            <span class="layui-badge layui-bg-blue">{{d.LAY_NUMBER}}</span>
            <img src="../../../assets/images/head.png"/>
            <span>{{d.nickName}}</span>
            <div class="dropdown-menu dropdown-hover">
                <span class="layui-badge layui-bg-gray">
                    <i class="layui-icon layui-icon-more-vertical"></i>
                </span>
                <ul class="dropdown-menu-nav dropdown-bottom-right">
                    <div class="dropdown-anchor"></div>
                    <li><a lay-event="info"><i class="layui-icon layui-icon-tips"></i>详情</a></li>
                    <li><a lay-event="edit"><i class="layui-icon layui-icon-edit"></i>修改</a></li>
                    <li><a lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a></li>
                </ul>
            </div>
        </div>
    </div>
</script>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=314"></script>
<script>
    layui.use(['layer', 'dataGrid', 'admin', 'element', 'dropdown'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var dataGrid = layui.dataGrid;
        var admin = layui.admin;
        var element = layui.element;
        var dataList = [
            {
                nickName: '用户01'
            }, {
                nickName: '用户02'
            }, {
                nickName: '用户03'
            }, {
                nickName: '用户04'
            }, {
                nickName: '用户05'
            }, {
                nickName: '用户06'
            }, {
                nickName: '用户07'
            }, {
                nickName: '用户08'
            }, {
                nickName: '用户09'
            }, {
                nickName: '用户10'
            }, {
                nickName: '用户11'
            }, {
                nickName: '用户12'
            }, {
                nickName: '用户13'
            }, {
                nickName: '用户14'
            }, {
                nickName: '用户15'
            }, {
                nickName: '用户16'
            }, {
                nickName: '用户17'
            }, {
                nickName: '用户18'
            }, {
                nickName: '用户19'
            }, {
                nickName: '用户20'
            }, {
                nickName: '用户21'
            }, {
                nickName: '用户22'
            }, {
                nickName: '用户23'
            }, {
                nickName: '用户24'
            }, {
                nickName: '用户25'
            }, {
                nickName: '用户26'
            }, {
                nickName: '用户27'
            }, {
                nickName: '用户28'
            }, {
                nickName: '用户29'
            }, {
                nickName: '用户30'
            }, {
                nickName: '用户31'
            }, {
                nickName: '用户32'
            }, {
                nickName: '用户33'
            }
        ];

        // 数据方式
        var ins1 = dataGrid.render({
            elem: '#demoGrid1',  // 容器
            templet: '#demoGridItem1',  // 模板
            data: dataList,  // 数据
            page: true,  // 开启分页
            onItemClick: function (obj) {
                layer.msg('点击了' + obj.data.nickName, {icon: 1});
            },
            onToolBarClick: function (obj) {
                var event = obj.event;
                if (event == 'info') {
                    layer.msg('点击了详情', {icon: 1});
                } else if (event == 'edit') {
                    layer.msg('点击了修改', {icon: 1});
                    obj.update({nickName: '新用户'});
                } else if (event == 'del') {
                    obj.del();
                    layer.msg('点击了删除', {icon: 1});
                }
            }
        });

        setTimeout(function () {
            ins1.reload({page: {curr: 2}})
        }, 5000);

        // url方式
        dataGrid.render({
            elem: '#demoGrid2',  // 容器
            templet: '#demoGridItem1',  // 模板
            data: '../../../json/user.json',  // url
            page: true  // 开启分页
        });

        // 加载更多模式
        dataGrid.render({
            elem: '#demoGrid3',  // 容器
            templet: '#demoGridItem1',  // 模板
            data: '../../../json/user.json',  // url
            loadMore: true  // 开启加载更多
        });

        // 自定义异步方式
        dataGrid.render({
            elem: '#demoGrid4',  // 容器
            templet: '#demoGridItem1',  // 模板
            page: true,  // 开启分页
            reqData: function (param, callback) {
                admin.showLoading($('#demoGrid4').parent());
                // 模拟耗时操作
                setTimeout(function () {
                    admin.removeLoading($('#demoGrid4').parent());
                    var start = (param.page - 1) * param.limit;
                    var end = start + param.limit;
                    (end > dataList.length) && (end = dataList.length);
                    var res = {code: 0, count: dataList.length, data: []};
                    for (var i = start; i < end; i++) {
                        res.data.push(dataList[i]);
                    }
                    callback(res);
                }, 1000);
            }
        });

        // 设置高度全屏
        $('.demo-data-grid').css('height', (admin.getPageHeight() - 140) + 'px');
        window.onresize = function () {
            $('.demo-data-grid').css('height', (admin.getPageHeight() - 140) + 'px');
        }

    });
</script>
</body>
</html>