<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API商业系统</title>
    <link rel="stylesheet" href="Easyweb/assets/libs/layui/css/layui.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        .hero-section h1 {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .hero-section p {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        .features {
            padding: 80px 0;
            background: #f8f9fa;
        }
        .feature-card {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 3rem;
            color: #1E9FFF;
            margin-bottom: 20px;
        }
        .stats {
            padding: 60px 0;
            background: #1E9FFF;
            color: white;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .footer {
            background: #2F4056;
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        .navbar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 15px 0;
        }
        .navbar .layui-nav {
            background: transparent;
        }
        .navbar .layui-nav .layui-nav-item a {
            color: #333;
        }
        body {
            padding-top: 70px;
        }
        .pricing {
            padding: 80px 0;
        }
        .price-card {
            text-align: center;
            padding: 40px 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            position: relative;
        }
        .price-card.featured {
            border: 3px solid #1E9FFF;
            transform: scale(1.05);
        }
        .price-card .price {
            font-size: 3rem;
            color: #1E9FFF;
            font-weight: bold;
            margin: 20px 0;
        }
        .price-card .features-list {
            text-align: left;
            margin: 30px 0;
        }
        .price-card .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .price-card .features-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="navbar">
        <div class="layui-container">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <h2 style="margin: 0; color: #1E9FFF;">API商业系统</h2>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <ul class="layui-nav" lay-filter="">
                        <li class="layui-nav-item"><a href="#features">功能特色</a></li>
                        <li class="layui-nav-item"><a href="#pricing">价格方案</a></li>
                        <li class="layui-nav-item"><a href="api/" target="_blank">API文档</a></li>
                        <li class="layui-nav-item"><a href="admin/" target="_blank">管理后台</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="layui-container">
            <h1>专业的API接口管理平台</h1>
            <p>提供完整的API商业化解决方案，支持多种接口类型，内置支付系统，助力您的API业务快速发展</p>
            <div>
                <a href="admin/" class="layui-btn layui-btn-lg" target="_blank">立即开始</a>
                <a href="api/docs" class="layui-btn layui-btn-lg layui-btn-primary" target="_blank">查看文档</a>
            </div>
        </div>
    </section>

    <!-- 功能特色 -->
    <section class="features" id="features">
        <div class="layui-container">
            <div class="layui-row">
                <div class="layui-col-md12" style="text-align: center; margin-bottom: 50px;">
                    <h2>功能特色</h2>
                    <p>全面的API管理功能，满足您的各种需求</p>
                </div>
            </div>
            <div class="layui-row layui-col-space20">
                <div class="layui-col-md4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-set"></i>
                        </div>
                        <h3>API管理</h3>
                        <p>支持本地和远程API接口管理，自动生成文档，在线调试功能</p>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-user"></i>
                        </div>
                        <h3>用户管理</h3>
                        <p>多级用户权限管理，支持商家入驻，灵活的会员等级设置</p>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-rmb"></i>
                        </div>
                        <h3>支付系统</h3>
                        <p>内置支付宝、微信支付，支持多种扣费模式，自动余额管理</p>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-chart"></i>
                        </div>
                        <h3>数据统计</h3>
                        <p>详细的请求日志分析，收入统计，用户行为分析</p>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-engine"></i>
                        </div>
                        <h3>安全防护</h3>
                        <p>IP黑白名单，QPS限制，异常监控，多重安全保障</p>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="layui-icon layui-icon-template"></i>
                        </div>
                        <h3>多模板支持</h3>
                        <p>支持多套前端模板，自定义样式，打造专属品牌形象</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 数据统计 -->
    <section class="stats">
        <div class="layui-container">
            <div class="layui-row layui-col-space30">
                <div class="layui-col-md3">
                    <div class="stat-item">
                        <div class="stat-number" id="api-count">1000+</div>
                        <div>API接口</div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-item">
                        <div class="stat-number" id="user-count">5000+</div>
                        <div>注册用户</div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-item">
                        <div class="stat-number" id="request-count">100万+</div>
                        <div>API调用次数</div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-item">
                        <div class="stat-number" id="uptime">99.9%</div>
                        <div>系统稳定性</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格方案 -->
    <section class="pricing" id="pricing">
        <div class="layui-container">
            <div class="layui-row">
                <div class="layui-col-md12" style="text-align: center; margin-bottom: 50px;">
                    <h2>价格方案</h2>
                    <p>选择适合您的套餐，开启API商业化之旅</p>
                </div>
            </div>
            <div class="layui-row layui-col-space20">
                <div class="layui-col-md4">
                    <div class="price-card">
                        <h3>基础版</h3>
                        <div class="price">免费</div>
                        <ul class="features-list">
                            <li>✓ 最多10个API接口</li>
                            <li>✓ 每日1000次调用</li>
                            <li>✓ 基础统计功能</li>
                            <li>✓ 邮件支持</li>
                            <li>✗ 自定义域名</li>
                            <li>✗ 高级安全功能</li>
                        </ul>
                        <button class="layui-btn layui-btn-fluid">立即使用</button>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="price-card featured">
                        <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); background: #1E9FFF; color: white; padding: 5px 20px; border-radius: 15px; font-size: 12px;">推荐</div>
                        <h3>专业版</h3>
                        <div class="price">¥299<small>/月</small></div>
                        <ul class="features-list">
                            <li>✓ 无限API接口</li>
                            <li>✓ 每日10万次调用</li>
                            <li>✓ 高级统计分析</li>
                            <li>✓ 优先技术支持</li>
                            <li>✓ 自定义域名</li>
                            <li>✓ 高级安全功能</li>
                        </ul>
                        <button class="layui-btn layui-btn-fluid">立即购买</button>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <div class="price-card">
                        <h3>企业版</h3>
                        <div class="price">¥999<small>/月</small></div>
                        <ul class="features-list">
                            <li>✓ 无限API接口</li>
                            <li>✓ 无限次调用</li>
                            <li>✓ 完整统计分析</li>
                            <li>✓ 专属客服支持</li>
                            <li>✓ 多域名支持</li>
                            <li>✓ 企业级安全</li>
                        </ul>
                        <button class="layui-btn layui-btn-fluid">联系销售</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 底部 -->
    <footer class="footer">
        <div class="layui-container">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <h3>API商业系统</h3>
                    <p>专业的API接口管理平台，助力您的API业务快速发展</p>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <div>
                        <a href="admin/" target="_blank" style="color: white; margin-right: 20px;">管理后台</a>
                        <a href="api/" target="_blank" style="color: white; margin-right: 20px;">API文档</a>
                        <a href="#" style="color: white;">技术支持</a>
                    </div>
                    <div style="margin-top: 20px;">
                        <p>&copy; 2024 API商业系统. All rights reserved.</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['element'], function(){
            var element = layui.element;
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 数字动画效果
            function animateNumber(element, target, duration = 2000) {
                const start = 0;
                const increment = target / (duration / 16);
                let current = start;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current).toLocaleString();
                }, 16);
            }

            // 当滚动到统计区域时触发动画
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 这里可以添加实际的统计数据获取逻辑
                        setTimeout(() => {
                            document.getElementById('api-count').textContent = '1000+';
                            document.getElementById('user-count').textContent = '5000+';
                            document.getElementById('request-count').textContent = '100万+';
                            document.getElementById('uptime').textContent = '99.9%';
                        }, 500);
                        observer.unobserve(entry.target);
                    }
                });
            });

            const statsSection = document.querySelector('.stats');
            if (statsSection) {
                observer.observe(statsSection);
            }
        });
    </script>
</body>
</html>