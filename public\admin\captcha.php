<?php
/**
 * 验证码生成器
 * 生成简单的数字+字母验证码
 */

session_start();

// 验证码配置
$width = 120;
$height = 45;
$length = 4;
$font_size = 18;

// 生成随机验证码
$chars = 'ABCDEFGHIJKLMNPQRSTUVWXYZ123456789';
$captcha = '';
for ($i = 0; $i < $length; $i++) {
    $captcha .= $chars[rand(0, strlen($chars) - 1)];
}

// 将验证码存储到session
$_SESSION['captcha'] = strtolower($captcha);

// 创建画布
$image = imagecreate($width, $height);

// 定义颜色
$bg_color = imagecolorallocate($image, 240, 240, 240);
$text_color = imagecolorallocate($image, 50, 50, 50);
$line_color = imagecolorallocate($image, 200, 200, 200);
$noise_color = imagecolorallocate($image, 150, 150, 150);

// 填充背景
imagefill($image, 0, 0, $bg_color);

// 添加干扰线
for ($i = 0; $i < 5; $i++) {
    imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $line_color);
}

// 添加噪点
for ($i = 0; $i < 50; $i++) {
    imagesetpixel($image, rand(0, $width), rand(0, $height), $noise_color);
}

// 添加验证码文字
$x = 15;
for ($i = 0; $i < $length; $i++) {
    $char = $captcha[$i];
    $angle = rand(-15, 15);
    $y = rand(25, 35);
    
    // 使用内置字体
    imagestring($image, 5, $x, $y - 15, $char, $text_color);
    $x += 25;
}

// 输出图片
header('Content-Type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

imagepng($image);
imagedestroy($image);
?>