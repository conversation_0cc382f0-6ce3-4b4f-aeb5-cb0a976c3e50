<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>控制台</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
</head>
<body>
<div class="layui-fluid">
    <!-- 统计卡片 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    API总数
                    <span class="layui-badge layui-bg-blue layuiadmin-badge">总计</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="api-count">0</p>
                    <p>
                        API总数量
                        <span class="layuiadmin-span-color">
                            <i class="layui-icon layui-icon-flag"></i>
                        </span>
                    </p>
                </div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    用户总数
                    <span class="layui-badge layui-bg-green layuiadmin-badge">总计</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="user-count">0</p>
                    <p>
                        注册用户数
                        <span class="layuiadmin-span-color">
                            <i class="layui-icon layui-icon-user"></i>
                        </span>
                    </p>
                </div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    订单总数
                    <span class="layui-badge layui-bg-orange layuiadmin-badge">总计</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="order-count">0</p>
                    <p>
                        支付订单数
                        <span class="layuiadmin-span-color">
                            <i class="layui-icon layui-icon-cart"></i>
                        </span>
                    </p>
                </div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">
                    待处理工单
                    <span class="layui-badge layui-bg-red layuiadmin-badge">待处理</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font" id="ticket-count">0</p>
                    <p>
                        工单数量
                        <span class="layuiadmin-span-color">
                            <i class="layui-icon layui-icon-dialogue"></i>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 收入统计图表 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">收入统计</div>
                <div class="layui-card-body">
                    <div id="income-chart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近API调用和订单 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">最近API调用</div>
                <div class="layui-card-body">
                    <table class="layui-table" lay-skin="line">
                        <colgroup>
                            <col width="50%">
                            <col width="30%">
                            <col width="20%">
                        </colgroup>
                        <thead>
                            <tr>
                                <th>API名称</th>
                                <th>用户</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="recent-api-calls">
                            <tr>
                                <td colspan="3">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">最近订单</div>
                <div class="layui-card-body">
                    <table class="layui-table" lay-skin="line">
                        <colgroup>
                            <col width="40%">
                            <col width="20%">
                            <col width="20%">
                            <col width="20%">
                        </colgroup>
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>用户</th>
                                <th>金额</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="recent-orders">
                            <tr>
                                <td colspan="4">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">系统信息</div>
                <div class="layui-card-body">
                    <table class="layui-table" lay-skin="line">
                        <colgroup>
                            <col width="25%">
                            <col width="75%">
                        </colgroup>
                        <tbody id="system-info">
                            <tr>
                                <td>服务器软件</td>
                                <td id="server-info">加载中...</td>
                            </tr>
                            <tr>
                                <td>PHP版本</td>
                                <td id="php-version">加载中...</td>
                            </tr>
                            <tr>
                                <td>MySQL版本</td>
                                <td id="mysql-version">加载中...</td>
                            </tr>
                            <tr>
                                <td>上传限制</td>
                                <td id="upload-limit">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../Easyweb/assets/libs/layui/layui.js"></script>
<script src="../../Easyweb/assets/libs/echarts/echarts.min.js"></script>
<script>
layui.use(['jquery', 'layer'], function(){
    var $ = layui.jquery;
    var layer = layui.layer;
    
    // 加载统计数据
    loadStats();
    
    // 加载统计数据
    function loadStats() {
        $.ajax({
            url: '../controllers/DashboardController.php?action=getStats',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if (res.code === 200) {
                    var data = res.data;
                    
                    // 更新统计卡片
                    $('#api-count').text(data.api_count);
                    $('#user-count').text(data.user_count);
                    $('#order-count').text(data.order_count);
                    $('#ticket-count').text(data.ticket_count);
                    
                    // 渲染收入图表
                    renderIncomeChart(data.income_data);
                    
                    // 更新最近API调用
                    renderRecentApiCalls(data.recent_api_calls);
                    
                    // 更新最近订单
                    renderRecentOrders(data.recent_orders);
                    
                    // 更新系统信息
                    renderSystemInfo(data.system_info);
                } else {
                    layer.msg(res.msg || '加载统计数据失败');
                }
            },
            error: function() {
                layer.msg('服务器错误，请稍后再试');
            }
        });
    }
    
    // 渲染收入图表
    function renderIncomeChart(data) {
        var myChart = echarts.init(document.getElementById('income-chart'));
        
        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['收入金额', '订单数量']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    data: data.dates
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '金额',
                    axisLabel: {
                        formatter: '{value} 元'
                    }
                },
                {
                    type: 'value',
                    name: '订单数',
                    axisLabel: {
                        formatter: '{value} 个'
                    }
                }
            ],
            series: [
                {
                    name: '收入金额',
                    type: 'bar',
                    data: data.amounts
                },
                {
                    name: '订单数量',
                    type: 'line',
                    yAxisIndex: 1,
                    data: data.counts
                }
            ]
        };
        
        myChart.setOption(option);
        
        // 窗口大小变化时，重新调整图表大小
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    }
    
    // 渲染最近API调用
    function renderRecentApiCalls(data) {
        var html = '';
        
        if (data && data.length > 0) {
            data.forEach(function(item) {
                var statusClass = item.status === 'success' ? 'layui-bg-green' : 'layui-bg-red';
                var statusText = item.status === 'success' ? '成功' : '失败';
                
                html += '<tr>';
                html += '<td>' + item.api_name + '</td>';
                html += '<td>' + item.username + '</td>';
                html += '<td><span class="layui-badge ' + statusClass + '">' + statusText + '</span></td>';
                html += '</tr>';
            });
        } else {
            html = '<tr><td colspan="3">暂无数据</td></tr>';
        }
        
        $('#recent-api-calls').html(html);
    }
    
    // 渲染最近订单
    function renderRecentOrders(data) {
        var html = '';
        
        if (data && data.length > 0) {
            data.forEach(function(item) {
                var statusClass = '';
                var statusText = '';
                
                switch (item.status) {
                    case 'completed':
                        statusClass = 'layui-bg-green';
                        statusText = '已完成';
                        break;
                    case 'pending':
                        statusClass = 'layui-bg-orange';
                        statusText = '待支付';
                        break;
                    case 'failed':
                        statusClass = 'layui-bg-red';
                        statusText = '已失败';
                        break;
                    default:
                        statusClass = 'layui-bg-gray';
                        statusText = '未知';
                }
                
                html += '<tr>';
                html += '<td>' + item.order_no + '</td>';
                html += '<td>' + item.username + '</td>';
                html += '<td>￥' + item.amount + '</td>';
                html += '<td><span class="layui-badge ' + statusClass + '">' + statusText + '</span></td>';
                html += '</tr>';
            });
        } else {
            html = '<tr><td colspan="4">暂无数据</td></tr>';
        }
        
        $('#recent-orders').html(html);
    }
    
    // 渲染系统信息
    function renderSystemInfo(data) {
        $('#server-info').text(data.server);
        $('#php-version').text(data.php_version);
        $('#mysql-version').text(data.mysql_version);
        $('#upload-limit').text(data.upload_limit);
    }
});
</script>
</body>
</html>