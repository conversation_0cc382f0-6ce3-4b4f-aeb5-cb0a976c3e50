<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>登录日志</title>
    <link rel="stylesheet" href="../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 表格工具栏 -->
            <form class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">用户账号:</label>
                        <div class="layui-input-inline">
                            <input name="account" class="layui-input" placeholder="输入账号"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">登录日期:</label>
                        <div class="layui-input-inline">
                            <input name="loginRecordDateSel" class="layui-input icon-date" placeholder="选择日期范围"
                                   autocomplete="off"/>
                        </div>
                    </div>
                    <div class="layui-inline">&emsp;
                        <button class="layui-btn icon-btn" lay-filter="loginRecordTbSearch" lay-submit>
                            <i class="layui-icon">&#xe615;</i>搜索
                        </button>&nbsp;
                        <button id="loginRecordExportBtn" class="layui-btn icon-btn" type="button">
                            <i class="layui-icon">&#xe67d;</i>导出
                        </button>
                    </div>
                </div>
            </form>
            <!-- 数据表格 -->
            <table id="loginRecordTable" lay-filter="loginRecordTable"></table>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'form', 'table', 'util', 'laydate', 'tableX'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var table = layui.table;
        var util = layui.util;
        var laydate = layui.laydate;
        var tableX = layui.tableX;

        /* 渲染表格 */
        var insTb = table.render({
            elem: '#loginRecordTable',
            url: '../../json/login-record.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'checkbox'},
                {field: 'username', title: '账号', sort: true},
                {field: 'nickName', title: '用户名', sort: true},
                {field: 'ipAddress', title: 'IP', sort: true},
                {field: 'device', title: '设备', sort: true},
                {field: 'osName', title: '设备类型', sort: true},
                {field: 'browserType', title: '浏览器', sort: true},
                {
                    field: 'createTime', title: '登录时间', templet: function (d) {
                        return util.toDateString(d.createTime);
                    }, sort: true
                }
            ]]
        });

        /* 渲染时间选择 */
        laydate.render({
            elem: 'input[name="loginRecordDateSel"]',
            type: 'date',
            range: true,
            trigger: 'click'
        });

        /* 表格搜索 */
        form.on('submit(loginRecordTbSearch)', function (data) {
            if (data.field.loginRecordDateSel) {
                var searchDate = data.field.loginRecordDateSel.split(' - ');
                data.field.startDate = searchDate[0];
                data.field.endDate = searchDate[1];
            } else {
                data.field.startDate = null;
                data.field.endDate = null;
            }
            data.field.loginRecordDateSel = undefined;
            insTb.reload({where: data.field, page: {curr: 1}});
            return false;
        });

        /* 导出excel */
        $('#loginRecordExportBtn').click(function () {
            var checkRows = table.checkStatus('loginRecordTable');
            if (checkRows.data.length === 0) {
                layer.msg('请选择要导出的数据', {icon: 2});
            } else {
                tableX.exportDataX({
                    cols: insTb.config.cols,
                    data: checkRows.data,
                    fileName: '登录日志'
                });
            }
        });

    });
</script>
</body>
</html>