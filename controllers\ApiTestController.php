<?php
/**
 * API测试控制器
 */
class ApiTestController {
    private $db;
    
    public function __construct() {
        global $db;
        $this->db = $db;
        
        // 检查用户登录状态
        checkLogin();
    }
    
    /**
     * 测试API接口
     */
    public function testApi() {
        $method = isset($_POST['method']) ? strtoupper($_POST['method']) : 'GET';
        $url = isset($_POST['url']) ? trim($_POST['url']) : '';
        $params = isset($_POST['params']) ? json_decode($_POST['params'], true) : [];
        $headers = isset($_POST['headers']) ? json_decode($_POST['headers'], true) : [];
        $body = isset($_POST['body']) ? $_POST['body'] : '';
        
        if (empty($url)) {
            return json([
                'code' => 1,
                'msg' => 'URL不能为空'
            ]);
        }
        
        // 构建请求URL
        $requestUrl = $url;
        if ($method === 'GET' && !empty($params)) {
            $queryString = http_build_query($params);
            $requestUrl .= (strpos($url, '?') !== false ? '&' : '?') . $queryString;
        }
        
        // 初始化cURL
        $ch = curl_init();
        
        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $requestUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        // 设置请求头
        $headerArray = [];
        foreach ($headers as $name => $value) {
            $headerArray[] = "$name: $value";
        }
        
        // 添加默认请求头
        $headerArray[] = "User-Agent: API-Test-Tool";
        $headerArray[] = "Accept: */*";
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
        
        // 设置请求体
        if ($method !== 'GET' && !empty($params)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        } elseif (!empty($body)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        }
        
        // 执行请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        
        // 分离响应头和响应体
        $responseHeaders = substr($response, 0, $headerSize);
        $responseBody = substr($response, $headerSize);
        
        // 解析响应头
        $headers = $this->parseHeaders($responseHeaders);
        
        // 关闭cURL
        curl_close($ch);
        
        // 记录测试历史
        $this->saveTestHistory($method, $url, $params, $headers, $body, $httpCode, $responseBody);
        
        return json([
            'code' => 0,
            'msg' => '请求成功',
            'data' => [
                'status_code' => $httpCode,
                'headers' => $headers,
                'body' => $this->formatResponseBody($responseBody, $headers)
            ]
        ]);
    }
    
    /**
     * 获取API列表
     */
    public function getApiList() {
        // 查询API分类
        $stmt = $this->db->prepare("SELECT id, name, description FROM api_categories ORDER BY sort ASC");
        $stmt->execute();
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [
            'categories' => []
        ];
        
        // 查询每个分类下的API
        foreach ($categories as $category) {
            $stmt = $this->db->prepare("SELECT id, name, method, url, description FROM apis WHERE category_id = ? AND status = 1 ORDER BY sort ASC");
            $stmt->execute([$category['id']]);
            $apis = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $result['categories'][] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'description' => $category['description'],
                'apis' => $apis
            ];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $result
        ]);
    }
    
    /**
     * 获取用户API密钥列表
     */
    public function getApiKeys() {
        $userId = $_SESSION['user_id'];
        
        $stmt = $this->db->prepare("SELECT id, key_name, key_value, request_limit, request_count, expire_time, status 
                                   FROM api_keys 
                                   WHERE user_id = ? 
                                   ORDER BY create_time DESC");
        $stmt->execute([$userId]);
        $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $keys
        ]);
    }
    
    /**
     * 解析响应头
     */
    private function parseHeaders($headerText) {
        $headers = [];
        $headerLines = explode("\r\n", $headerText);
        
        foreach ($headerLines as $line) {
            if (strpos($line, ':') !== false) {
                list($name, $value) = explode(':', $line, 2);
                $headers[trim($name)] = trim($value);
            }
        }
        
        return $headers;
    }
    
    /**
     * 格式化响应体
     */
    private function formatResponseBody($body, $headers) {
        // 检查Content-Type
        $contentType = isset($headers['Content-Type']) ? $headers['Content-Type'] : '';
        
        // 如果是JSON格式，尝试解析
        if (strpos($contentType, 'application/json') !== false) {
            $json = json_decode($body);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $json;
            }
        }
        
        return $body;
    }
    
    /**
     * 保存测试历史
     */
    private function saveTestHistory($method, $url, $params, $headers, $body, $statusCode, $responseBody) {
        $userId = $_SESSION['user_id'];
        
        $stmt = $this->db->prepare("INSERT INTO api_test_history (user_id, method, url, params, headers, body, 
                                   status_code, response, test_time) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $userId,
            $method,
            $url,
            json_encode($params),
            json_encode($headers),
            $body,
            $statusCode,
            $responseBody,
            time()
        ]);
    }
    
    /**
     * 获取测试历史
     */
    public function getTestHistory() {
        $userId = $_SESSION['user_id'];
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        
        $stmt = $this->db->prepare("SELECT id, method, url, params, status_code, test_time 
                                   FROM api_test_history 
                                   WHERE user_id = ? 
                                   ORDER BY test_time DESC 
                                   LIMIT ?");
        $stmt->execute([$userId, $limit]);
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $history
        ]);
    }
    
    /**
     * 获取测试历史详情
     */
    public function getTestHistoryDetail() {
        $userId = $_SESSION['user_id'];
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => 'ID不能为空'
            ]);
        }
        
        $stmt = $this->db->prepare("SELECT * FROM api_test_history WHERE id = ? AND user_id = ?");
        $stmt->execute([$id, $userId]);
        $detail = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$detail) {
            return json([
                'code' => 1,
                'msg' => '记录不存在'
            ]);
        }
        
        // 解析JSON字段
        $detail['params'] = json_decode($detail['params'], true);
        $detail['headers'] = json_decode($detail['headers'], true);
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $detail
        ]);
    }
}