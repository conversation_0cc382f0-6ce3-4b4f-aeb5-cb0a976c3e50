<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - API管理系统</title>
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="icon" href="../Easyweb/assets/images/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 900px;
            max-width: 90vw;
            min-height: 600px;
            display: flex;
        }
        
        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }
        
        .login-left-content {
            position: relative;
            z-index: 1;
        }
        
        .login-left h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .login-left p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        .login-features {
            list-style: none;
        }
        
        .login-features li {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .login-features li i {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-form-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .login-form-header h2 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #333;
        }
        
        .login-form-header p {
            color: #666;
            font-size: 1rem;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
            width: 100%;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
            background: #f8f9fa;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-input.error {
            border-color: #dc3545;
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .remember-me input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }
        
        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .forgot-password:hover {
            text-decoration: underline;
        }
        
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
            color: #999;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 20px;
        }
        
        .social-login {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .social-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .social-btn:hover {
            border-color: #667eea;
            background: #f8f9fa;
        }
        
        .register-link {
            text-align: center;
            color: #666;
        }
        
        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                width: 95vw;
                min-height: auto;
            }
            
            .login-left {
                padding: 40px 30px;
                text-align: center;
            }
            
            .login-left h1 {
                font-size: 2rem;
            }
            
            .login-right {
                padding: 40px 30px;
            }
            
            .social-login {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 左侧介绍 -->
        <div class="login-left">
            <div class="login-left-content">
                <h1>欢迎回来</h1>
                <p>登录您的账户，继续使用我们强大的API管理平台</p>
                
                <ul class="login-features">
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        完整的API生命周期管理
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        实时数据统计与分析
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        安全可靠的支付系统
                    </li>
                    <li>
                        <i class="layui-icon layui-icon-ok-circle"></i>
                        24/7 技术支持服务
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 右侧登录表单 -->
        <div class="login-right">
            <div class="login-form-header">
                <h2>用户登录</h2>
                <p>请输入您的账户信息</p>
            </div>
            
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="username">用户名/邮箱</label>
                    <input type="text" id="username" name="username" class="form-input" placeholder="请输入用户名或邮箱" required>
                    <div class="error-message" id="usernameError"></div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码" required>
                    <div class="error-message" id="passwordError"></div>
                </div>
                
                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <span>记住我</span>
                    </label>
                    <a href="forgot-password.html" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <span id="loginBtnText">登录</span>
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" id="loginLoading" style="display: none;"></i>
                </button>
                
                <div class="divider">
                    <span>或者</span>
                </div>
                
                <div class="social-login">
                    <button type="button" class="social-btn" onclick="loginWithQQ()">
                        <i class="layui-icon layui-icon-login-qq"></i>
                        QQ登录
                    </button>
                    <button type="button" class="social-btn" onclick="loginWithWechat()">
                        <i class="layui-icon layui-icon-login-wechat"></i>
                        微信登录
                    </button>
                </div>
                
                <div class="register-link">
                    还没有账户？<a href="register.html">立即注册</a>
                </div>
            </form>
        </div>
    </div>

    <script src="../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        // 表单验证和提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            // 清除之前的错误信息
            clearErrors();
            
            // 验证表单
            let hasError = false;
            
            if (!username) {
                showError('usernameError', '请输入用户名或邮箱');
                hasError = true;
            }
            
            if (!password) {
                showError('passwordError', '请输入密码');
                hasError = true;
            } else if (password.length < 6) {
                showError('passwordError', '密码长度不能少于6位');
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            // 显示加载状态
            showLoading(true);
            
            // 模拟登录请求
            setTimeout(() => {
                // 模拟登录成功
                if (username === 'admin' && password === '123456') {
                    // 登录成功，跳转到仪表板
                    window.location.href = 'admin/dashboard.html';
                } else {
                    // 登录失败
                    showError('passwordError', '用户名或密码错误');
                    showLoading(false);
                }
            }, 2000);
        });
        
        // 显示错误信息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            const inputElement = errorElement.previousElementSibling;
            
            errorElement.textContent = message;
            inputElement.classList.add('error');
        }
        
        // 清除错误信息
        function clearErrors() {
            const errorElements = document.querySelectorAll('.error-message');
            const inputElements = document.querySelectorAll('.form-input');
            
            errorElements.forEach(element => {
                element.textContent = '';
            });
            
            inputElements.forEach(element => {
                element.classList.remove('error');
            });
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginLoading = document.getElementById('loginLoading');
            
            if (show) {
                loginBtn.disabled = true;
                loginBtnText.textContent = '登录中...';
                loginLoading.style.display = 'inline-block';
            } else {
                loginBtn.disabled = false;
                loginBtnText.textContent = '登录';
                loginLoading.style.display = 'none';
            }
        }
        
        // QQ登录
        function loginWithQQ() {
            alert('QQ登录功能开发中...');
        }
        
        // 微信登录
        function loginWithWechat() {
            alert('微信登录功能开发中...');
        }
        
        // 输入框焦点事件
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.classList.remove('error');
                const errorElement = this.nextElementSibling;
                if (errorElement && errorElement.classList.contains('error-message')) {
                    errorElement.textContent = '';
                }
            });
        });
    </script>
</body>
</html>
