<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>右键菜单</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid ">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-header">DIV内鼠标右键菜单</div>
                <div class="layui-card-body text-center" id="ctxMenuDiv" style="padding: 100px 0;">
                    请在此区域内点击鼠标右键
                </div>
            </div>
            <div class="layui-card">
                <div class="layui-card-header">点击方式触发</div>
                <div class="layui-card-body text-center" style="padding: 43px 0;">
                    <button id="ctxMenuBtn" class="layui-btn">请点击我</button>
                </div>
            </div>
        </div>
        <div class="layui-col-md9">
            <div class="layui-card">
                <div class="layui-card-header">表格行鼠标右键菜单</div>
                <div class="layui-card-body">
                    <table id="ctxMenuTable" lay-filter="ctxMenuTable"></table>
                </div>
            </div>
        </div>
        <div class="layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body">
                    全局鼠标右键菜单，请在页面任意位置点击鼠标右键。
                </div>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['layer', 'contextMenu', 'table', 'tableX'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var contextMenu = layui.contextMenu;
        var table = layui.table;
        var tableX = layui.tableX;
        var items = [
            {
                icon: 'layui-icon layui-icon-heart-fill',
                name: '加入收藏',
                click: function () {
                    layer.msg('点击了' + this.name);
                }
            },
            {
                icon: 'layui-icon layui-icon-rss',
                name: '加入订阅',
                subs: [
                    {
                        icon: 'layui-icon layui-icon-email',
                        name: '邮件订阅',
                        click: function () {
                            layer.msg('点击了' + this.name);
                        }
                    },
                    {
                        icon: 'layui-icon layui-icon-cellphone-fine',
                        name: '短信订阅',
                        click: function () {
                            layer.msg('点击了' + this.name);
                        }
                    }
                ]
            },
            {
                icon: 'layui-icon layui-icon-gift',
                name: '赠送礼物',
                click: function () {
                    layer.msg('点击了' + this.name);
                }
            },
            {
                icon: 'layui-icon layui-icon-share',
                name: '分享链接',
                subs: [
                    {
                        icon: 'layui-icon layui-icon-login-qq',
                        name: '分享到QQ',
                        click: function () {
                            layer.msg('点击了' + this.name);
                        }
                    },
                    {
                        icon: 'layui-icon layui-icon-login-wechat',
                        name: '分享到微信',
                        click: function () {
                            layer.msg('点击了' + this.name);
                        }
                    },
                    {
                        icon: 'layui-icon layui-icon-login-weibo',
                        name: '分享到微博',
                        click: function () {
                            layer.msg('点击了' + this.name);
                        }
                    }
                ]
            }
        ];

        // DIV内鼠标右键菜单
        contextMenu.bind('#ctxMenuDiv', items);

        // 点击方式触发
        $('#ctxMenuBtn').click(function (e) {
            var x = $(this).offset().left;
            var y = $(this).offset().top + $(this).outerHeight() - $('html,body').scrollTop();
            contextMenu.show(items, x, y);
            e.stopPropagation();
        });

        // 全局鼠标右键菜单
        contextMenu.bind('html', [
            {
                icon: 'layui-icon layui-icon-refresh',
                name: '刷新子页面',
                click: function () {
                    location.reload();
                }
            },
            {
                icon: 'layui-icon layui-icon-layouts',
                name: '刷新主框架',
                click: function () {
                    parent.location.reload();
                }
            },
            {
                icon: 'layui-icon layui-icon-print',
                name: '打印此页面',
                click: function () {
                    setTimeout(function () {
                        window.print();
                    }, 0);
                },
                hr: true
            }
        ].concat(items));

        // 表格行右键
        table.render({
            elem: '#ctxMenuTable',
            url: '../../../json/user.json',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers'},
                {field: 'nickName', title: '用户名', sort: true},
                {field: 'sex', title: '性别', sort: true},
                {field: 'phone', title: '手机号', sort: true},
                {field: 'createTime', title: '创建时间', sort: true}
            ]],
            done: function () {
                // 绑定鼠标右键
                tableX.bindCtxMenu('ctxMenuTable', function (d) {
                    if (d.sex === '女') return items;
                    return [
                        {
                            icon: 'layui-icon layui-icon-password',
                            name: '锁定用户',
                            click: function (d) {
                                layer.msg('点击了锁定，userId：' + d.userId);
                            }
                        },
                        {
                            icon: 'layui-icon layui-icon-key',
                            name: '重置密码',
                            click: function (d) {
                                layer.msg('点击了重置，userId：' + d.userId);
                            }
                        },
                        {
                            icon: 'layui-icon layui-icon-delete text-danger',
                            name: '<span class="text-danger">删除用户</span>',
                            click: function (d) {
                                layer.msg('点击了删除，userId：' + d.userId);
                            }
                        },
                        {
                            icon: 'layui-icon layui-icon-upload-drag',
                            name: '上传资料',
                            subs: [
                                {
                                    icon: 'layui-icon layui-icon-camera',
                                    name: '上传头像',
                                    click: function (d) {
                                        layer.msg('点击了上传头像，userId：' + d.userId);
                                    }
                                },
                                {
                                    icon: 'layui-icon layui-icon-picture-fine',
                                    name: '上传照片',
                                    click: function (d) {
                                        layer.msg('点击了上传照片，userId：' + d.userId);
                                    }
                                },
                                {
                                    icon: 'layui-icon layui-icon-note',
                                    name: '上传附件',
                                    subs: [
                                        {
                                            icon: 'layui-icon layui-icon-mike',
                                            name: '上传音频',
                                            click: function (d) {
                                                layer.msg('点击了上传音频，userId：' + d.userId);
                                            }
                                        },
                                        {
                                            icon: 'layui-icon layui-icon-video',
                                            name: '上传视频',
                                            click: function (d) {
                                                layer.msg('点击了上传视频，userId：' + d.userId);
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ];
                });
            }
        });

    });
</script>
</body>
</html>