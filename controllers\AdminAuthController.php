<?php
/**
 * 管理员认证控制器
 */
class AdminAuthController extends BaseController
{
    /**
     * 登录页面
     */
    public function loginPage()
    {
        // 如果已登录，跳转到后台首页
        if ($this->isAdminLoggedIn()) {
            $this->redirect('/admin');
        }
        
        // 渲染登录页面
        $this->render('admin/login');
    }
    
    /**
     * 处理登录请求
     */
    public function login()
    {
        // 获取参数
        $username = $this->getParam('username', '');
        $password = $this->getParam('password', '');
        $captcha = $this->getParam('captcha', '');
        
        // 验证参数
        if (empty($username) || empty($password)) {
            $this->error('用户名和密码不能为空');
        }
        
        // 验证验证码
        if (empty($captcha) || !isset($_SESSION['captcha']) || strtolower($captcha) !== strtolower($_SESSION['captcha'])) {
            $this->error('验证码错误');
        }
        
        // 查询管理员
        $admin = $this->db->query(
            "SELECT * FROM admins WHERE username = ? AND status = 1 LIMIT 1",
            [$username],
            true
        );
        
        // 验证管理员
        if (empty($admin) || !password_verify($password, $admin['password'])) {
            $this->error('用户名或密码错误');
        }
        
        // 更新登录信息
        $this->db->execute(
            "UPDATE admins SET last_login_time = ?, last_login_ip = ? WHERE id = ?",
            [time(), $_SERVER['REMOTE_ADDR'], $admin['id']]
        );
        
        // 设置会话
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['admin_login_time'] = time();
        
        // 记录登录日志
        try {
            $this->db->execute(
                "INSERT INTO admin_login_logs (admin_id, username, ip, user_agent, create_time) VALUES (?, ?, ?, ?, ?)",
                [$admin['id'], $admin['username'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], time()]
            );
        } catch (Exception $e) {
            // 如果表不存在，忽略错误
        }
        
        $this->success('登录成功', ['url' => '/admin/dashboard']);
    }
    
    /**
     * 退出登录
     */
    public function logout()
    {
        // 清除会话
        unset($_SESSION['admin_id']);
        unset($_SESSION['admin_username']);
        unset($_SESSION['admin_role']);
        unset($_SESSION['admin_login_time']);
        
        // 销毁会话
        session_destroy();
        
        // 跳转到登录页面
        $this->redirect('/admin/login');
    }
    
    /**
     * 生成验证码
     */
    public function captcha()
    {
        // 创建验证码图片
        $width = 130;
        $height = 46;
        $image = imagecreatetruecolor($width, $height);
        
        // 设置背景色
        $bgColor = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $bgColor);
        
        // 生成随机验证码
        $chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
        $length = 4;
        $code = '';
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        
        // 保存验证码到会话
        $_SESSION['captcha'] = strtolower($code);
        
        // 添加干扰线
        for ($i = 0; $i < 6; $i++) {
            $lineColor = imagecolorallocate($image, mt_rand(100, 200), mt_rand(100, 200), mt_rand(100, 200));
            imageline($image, mt_rand(0, $width), mt_rand(0, $height), mt_rand(0, $width), mt_rand(0, $height), $lineColor);
        }
        
        // 添加干扰点
        for ($i = 0; $i < 100; $i++) {
            $pointColor = imagecolorallocate($image, mt_rand(100, 200), mt_rand(100, 200), mt_rand(100, 200));
            imagesetpixel($image, mt_rand(0, $width), mt_rand(0, $height), $pointColor);
        }
        
        // 绘制验证码
        for ($i = 0; $i < $length; $i++) {
            $fontSize = mt_rand(20, 24);
            $angle = mt_rand(-15, 15);
            $textColor = imagecolorallocate($image, mt_rand(50, 100), mt_rand(50, 100), mt_rand(50, 100));
            $fontFile = __DIR__ . '/../assets/fonts/arial.ttf';
            
            if (!file_exists($fontFile)) {
                // 如果字体文件不存在，使用默认字体
                $x = 10 + $i * 25;
                $y = mt_rand(25, 35);
                imagestring($image, 5, $x, $y, $code[$i], $textColor);
            } else {
                $x = 10 + $i * 25;
                $y = $height - mt_rand(10, 15);
                imagettftext($image, $fontSize, $angle, $x, $y, $textColor, $fontFile, $code[$i]);
            }
        }
        
        // 输出图片
        header('Content-Type: image/png');
        imagepng($image);
        imagedestroy($image);
        exit;
    }
}
