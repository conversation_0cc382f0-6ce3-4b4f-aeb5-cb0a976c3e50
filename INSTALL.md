# API管理系统 - 安装指南

本文档详细介绍了API管理系统的安装和配置过程。

## 📋 安装前准备

### 系统要求
- **操作系统**：Linux (推荐 Ubuntu 18.04+, CentOS 7+) 或 Windows Server
- **PHP版本**：7.4 或更高版本
- **MySQL版本**：5.7 或更高版本
- **Web服务器**：Apache 2.4+ 或 Nginx 1.16+
- **内存要求**：最少 512MB，推荐 2GB+
- **磁盘空间**：最少 1GB，推荐 10GB+

### PHP扩展检查
确保以下PHP扩展已安装：
```bash
# 检查PHP扩展
php -m | grep -E "(pdo|mysql|json|curl|openssl|mbstring|gd|zip)"
```

如果缺少扩展，请安装：
```bash
# Ubuntu/Debian
sudo apt-get install php-pdo php-mysql php-json php-curl php-openssl php-mbstring php-gd php-zip

# CentOS/RHEL
sudo yum install php-pdo php-mysql php-json php-curl php-openssl php-mbstring php-gd php-zip
```

## 🚀 快速安装

### 方法一：自动安装脚本

1. **下载安装脚本**
```bash
wget https://raw.githubusercontent.com/your-repo/api-system/main/install.sh
chmod +x install.sh
```

2. **运行安装脚本**
```bash
sudo ./install.sh
```

3. **按照提示完成配置**
- 输入数据库信息
- 设置管理员账号
- 配置域名和路径

### 方法二：手动安装

#### 1. 下载源码
```bash
# 使用Git克隆
git clone https://github.com/your-repo/api-system.git
cd api-system

# 或下载压缩包
wget https://github.com/your-repo/api-system/archive/main.zip
unzip main.zip
cd api-system-main
```

#### 2. 设置目录权限
```bash
# 设置基本权限
chmod -R 755 .

# 设置可写目录权限
chmod -R 777 logs/
chmod -R 777 uploads/
chmod -R 777 cache/
chmod -R 777 temp/
```

#### 3. 配置数据库

**创建数据库：**
```sql
CREATE DATABASE api_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'api_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON api_system.* TO 'api_user'@'localhost';
FLUSH PRIVILEGES;
```

**配置数据库连接：**
```bash
cp config/database.php.example config/database.php
```

编辑 `config/database.php`：
```php
<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'api_system');
define('DB_USER', 'api_user');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');
?>
```

#### 4. 配置系统参数
```bash
cp config/config.php.example config/config.php
```

编辑 `config/config.php`：
```php
<?php
// 基本配置
define('APP_NAME', 'API管理系统');
define('APP_VERSION', '1.0.0');
define('APP_DEBUG', false);
define('APP_URL', 'https://your-domain.com');

// 安全配置
define('SECURITY_KEY', 'your-32-character-secret-key-here');
define('SESSION_LIFETIME', 7200);

// 文件上传配置
define('UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_ALLOWED_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// 邮件配置
define('MAIL_HOST', 'smtp.example.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-email-password');
define('MAIL_FROM_NAME', 'API管理系统');

// 支付配置
define('ALIPAY_APP_ID', 'your-alipay-app-id');
define('WECHAT_APP_ID', 'your-wechat-app-id');
define('WECHAT_MCH_ID', 'your-wechat-mch-id');
?>
```

#### 5. 初始化数据库
```bash
# 方法1：使用安装脚本
php config/install.php

# 方法2：手动导入SQL文件
mysql -u api_user -p api_system < config/database.sql
```

#### 6. 配置Web服务器

**Apache配置 (.htaccess)：**
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# 安全配置
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

<FilesMatch "^(config|logs|cache|temp)/">
    Order deny,allow
    Deny from all
</FilesMatch>
```

**Nginx配置：**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/api-system/public;
    index index.php index.html;

    # 重写规则
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 安全配置
    location ~ ^/(config|logs|cache|temp)/ {
        deny all;
    }

    location ~ /\. {
        deny all;
    }
}
```

#### 7. 运行部署脚本
```bash
php deploy/deploy.php
```

## 🔧 详细配置

### SSL证书配置

**使用Let's Encrypt：**
```bash
# 安装Certbot
sudo apt-get install certbot python3-certbot-apache

# 获取证书
sudo certbot --apache -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

**手动配置SSL：**
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}
```

### 缓存配置

**Redis缓存：**
```bash
# 安装Redis
sudo apt-get install redis-server

# 配置Redis
sudo nano /etc/redis/redis.conf
# 设置密码：requirepass your_redis_password
```

在 `config/config.php` 中添加：
```php
// Redis配置
define('REDIS_HOST', '127.0.0.1');
define('REDIS_PORT', 6379);
define('REDIS_PASSWORD', 'your_redis_password');
define('REDIS_DATABASE', 0);
```

### 队列配置

**配置消息队列：**
```bash
# 创建队列处理脚本
sudo nano /etc/systemd/system/api-queue.service
```

```ini
[Unit]
Description=API System Queue Worker
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/api-system
ExecStart=/usr/bin/php queue/worker.php
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# 启动队列服务
sudo systemctl enable api-queue
sudo systemctl start api-queue
```

## 🧪 安装验证

### 1. 系统测试
```bash
# 运行系统测试
php tests/SystemTest.php

# 检查测试结果
cat logs/test_report_*.json
```

### 2. 功能验证

**访问管理后台：**
- URL: `https://your-domain.com/admin/`
- 默认账号: `admin`
- 默认密码: `admin123`

**访问用户前台：**
- URL: `https://your-domain.com/`

**API接口测试：**
```bash
# 测试API接口
curl -X GET "https://your-domain.com/api/v1/test" \
     -H "Authorization: Bearer your-api-key"
```

### 3. 性能测试
```bash
# 使用Apache Bench测试
ab -n 1000 -c 10 https://your-domain.com/

# 使用wrk测试
wrk -t12 -c400 -d30s https://your-domain.com/
```

## 🔒 安全加固

### 1. 文件权限
```bash
# 设置严格的文件权限
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;

# 可写目录
chmod 777 logs/ uploads/ cache/ temp/

# 敏感文件
chmod 600 config/database.php
chmod 600 config/config.php
```

### 2. 防火墙配置
```bash
# UFW防火墙
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# iptables防火墙
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
```

### 3. 数据库安全
```bash
# MySQL安全配置
sudo mysql_secure_installation

# 创建专用数据库用户
mysql -u root -p
```

```sql
-- 创建只读用户（用于备份）
CREATE USER 'backup_user'@'localhost' IDENTIFIED BY 'backup_password';
GRANT SELECT, LOCK TABLES ON api_system.* TO 'backup_user'@'localhost';

-- 限制root用户访问
UPDATE mysql.user SET Host='localhost' WHERE User='root';
FLUSH PRIVILEGES;
```

## 📊 监控配置

### 1. 日志监控
```bash
# 配置logrotate
sudo nano /etc/logrotate.d/api-system
```

```
/path/to/api-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. 系统监控
```bash
# 安装监控工具
sudo apt-get install htop iotop nethogs

# 配置系统监控脚本
crontab -e
# 添加：*/5 * * * * /path/to/api-system/scripts/monitor.sh
```

## 🔄 备份配置

### 1. 数据库备份
```bash
# 创建备份脚本
nano backup.sh
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/api-system"
DB_NAME="api_system"
DB_USER="backup_user"
DB_PASS="backup_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database_$DATE.sql

# 备份文件
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /path/to/api-system/uploads/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

```bash
# 设置定时备份
chmod +x backup.sh
crontab -e
# 添加：0 2 * * * /path/to/backup.sh
```

## 🚨 故障排除

### 常见问题

**1. 数据库连接失败**
```bash
# 检查MySQL服务
sudo systemctl status mysql

# 检查数据库配置
php -r "
require 'config/database.php';
try {
    \$pdo = new PDO('mysql:host='.DB_HOST.';dbname='.DB_NAME, DB_USER, DB_PASS);
    echo 'Database connection successful\n';
} catch (Exception \$e) {
    echo 'Database connection failed: ' . \$e->getMessage() . '\n';
}
"
```

**2. 权限问题**
```bash
# 检查文件权限
ls -la logs/ uploads/ cache/ temp/

# 修复权限
sudo chown -R www-data:www-data .
chmod -R 755 .
chmod -R 777 logs/ uploads/ cache/ temp/
```

**3. PHP错误**
```bash
# 检查PHP错误日志
tail -f /var/log/php_errors.log

# 检查Apache错误日志
tail -f /var/log/apache2/error.log

# 检查Nginx错误日志
tail -f /var/log/nginx/error.log
```

**4. 性能问题**
```bash
# 检查系统资源
htop
iotop
df -h

# 检查MySQL性能
mysql -u root -p -e "SHOW PROCESSLIST;"
mysql -u root -p -e "SHOW STATUS LIKE 'Slow_queries';"
```

### 日志分析
```bash
# 查看访问日志
tail -f logs/access.log

# 查看错误日志
tail -f logs/error.log

# 查看API调用日志
tail -f logs/api_*.log

# 分析日志统计
grep "ERROR" logs/*.log | wc -l
grep "$(date +%Y-%m-%d)" logs/access.log | wc -l
```

## 📞 技术支持

如果在安装过程中遇到问题，请：

1. **查看日志文件**：检查 `logs/` 目录下的错误日志
2. **运行测试**：执行 `php tests/SystemTest.php` 进行系统测试
3. **检查配置**：确认所有配置文件设置正确
4. **联系支持**：发送邮件至 <EMAIL>

---

安装完成后，请及时修改默认密码并进行安全配置！