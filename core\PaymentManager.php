<?php
/**
 * 支付管理类
 */
class PaymentManager {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = require_once 'config/app.php';
    }
    
    /**
     * 创建支付订单
     */
    public function createOrder($userId, $amount, $paymentMethod, $description = '') {
        $orderNo = $this->generateOrderNo();
        
        $orderData = [
            'order_no' => $orderNo,
            'user_id' => $userId,
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'description' => $description,
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->db->insert('payment_orders', $orderData)) {
            return [
                'success' => true,
                'order_no' => $orderNo,
                'payment_url' => $this->generatePaymentUrl($orderNo, $amount, $paymentMethod)
            ];
        }
        
        return ['success' => false, 'message' => '订单创建失败'];
    }
    
    /**
     * 处理支付回调
     */
    public function handleCallback($paymentMethod, $data) {
        switch ($paymentMethod) {
            case 'alipay':
                return $this->handleAlipayCallback($data);
            case 'wechat':
                return $this->handleWechatCallback($data);
            default:
                return false;
        }
    }
    
    /**
     * 处理支付宝回调
     */
    private function handleAlipayCallback($data) {
        // 验证签名
        if (!$this->verifyAlipaySign($data)) {
            return false;
        }
        
        $orderNo = $data['out_trade_no'];
        $tradeStatus = $data['trade_status'];
        
        if ($tradeStatus == 'TRADE_SUCCESS' || $tradeStatus == 'TRADE_FINISHED') {
            return $this->completePayment($orderNo, $data['trade_no']);
        }
        
        return false;
    }
    
    /**
     * 处理微信支付回调
     */
    private function handleWechatCallback($data) {
        // 验证签名
        if (!$this->verifyWechatSign($data)) {
            return false;
        }
        
        $orderNo = $data['out_trade_no'];
        $resultCode = $data['result_code'];
        
        if ($resultCode == 'SUCCESS') {
            return $this->completePayment($orderNo, $data['transaction_id']);
        }
        
        return false;
    }
    
    /**
     * 完成支付
     */
    private function completePayment($orderNo, $transactionId) {
        // 获取订单信息
        $order = $this->db->fetchOne(
            "SELECT * FROM payment_orders WHERE order_no = ? AND status = 'pending'",
            [$orderNo]
        );
        
        if (!$order) {
            return false;
        }
        
        // 更新订单状态
        $this->db->update('payment_orders', [
            'status' => 'completed',
            'transaction_id' => $transactionId,
            'paid_at' => date('Y-m-d H:i:s')
        ], 'order_no = ?', [$orderNo]);
        
        // 增加用户余额
        $this->db->update('users', [
            'balance' => new PDO_Expression('balance + ' . $order['amount'])
        ], 'id = ?', [$order['user_id']]);
        
        // 记录余额变动
        $this->db->insert('balance_logs', [
            'user_id' => $order['user_id'],
            'type' => 'recharge',
            'amount' => $order['amount'],
            'description' => '在线充值 - ' . $order['description'],
            'order_no' => $orderNo,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        return true;
    }
    
    /**
     * 生成订单号
     */
    private function generateOrderNo() {
        return date('YmdHis') . rand(1000, 9999);
    }
    
    /**
     * 生成支付链接
     */
    private function generatePaymentUrl($orderNo, $amount, $paymentMethod) {
        switch ($paymentMethod) {
            case 'alipay':
                return $this->generateAlipayUrl($orderNo, $amount);
            case 'wechat':
                return $this->generateWechatUrl($orderNo, $amount);
            default:
                return '';
        }
    }
    
    /**
     * 生成支付宝支付链接
     */
    private function generateAlipayUrl($orderNo, $amount) {
        $config = $this->config['payment']['alipay'];
        
        $params = [
            'app_id' => $config['app_id'],
            'method' => 'alipay.trade.page.pay',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'notify_url' => 'http://your-domain.com/api/payment/alipay/notify',
            'return_url' => 'http://your-domain.com/payment/success',
            'biz_content' => json_encode([
                'out_trade_no' => $orderNo,
                'total_amount' => $amount,
                'subject' => 'API系统充值',
                'product_code' => 'FAST_INSTANT_TRADE_PAY'
            ])
        ];
        
        // 生成签名
        $params['sign'] = $this->generateAlipaySign($params);
        
        return 'https://openapi.alipay.com/gateway.do?' . http_build_query($params);
    }
    
    /**
     * 生成微信支付链接
     */
    private function generateWechatUrl($orderNo, $amount) {
        // 微信支付实现
        // 这里需要根据微信支付API文档实现
        return '';
    }
    
    /**
     * 验证支付宝签名
     */
    private function verifyAlipaySign($data) {
        // 支付宝签名验证实现
        return true; // 简化实现
    }
    
    /**
     * 验证微信签名
     */
    private function verifyWechatSign($data) {
        // 微信签名验证实现
        return true; // 简化实现
    }
    
    /**
     * 生成支付宝签名
     */
    private function generateAlipaySign($params) {
        // 支付宝签名生成实现
        return 'signature'; // 简化实现
    }
    
    /**
     * 获取支付订单列表
     */
    public function getOrderList($page = 1, $limit = 20, $filters = []) {
        $offset = ($page - 1) * $limit;
        $where = "1=1";
        $params = [];
        
        if (!empty($filters['user_id'])) {
            $where .= " AND user_id = ?";
            $params[] = $filters['user_id'];
        }
        
        if (!empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['payment_method'])) {
            $where .= " AND payment_method = ?";
            $params[] = $filters['payment_method'];
        }
        
        // 获取总数
        $total = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM payment_orders WHERE {$where}",
            $params
        )['count'];
        
        // 获取订单列表
        $orders = $this->db->fetchAll(
            "SELECT po.*, u.username 
             FROM payment_orders po 
             LEFT JOIN users u ON po.user_id = u.id 
             WHERE {$where} 
             ORDER BY po.created_at DESC 
             LIMIT {$limit} OFFSET {$offset}",
            $params
        );
        
        return [
            'total' => $total,
            'data' => $orders,
            'page' => $page,
            'limit' => $limit
        ];
    }
}