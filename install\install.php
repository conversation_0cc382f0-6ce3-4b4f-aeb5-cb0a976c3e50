<?php
/**
 * 系统安装向导
 */
session_start();

// 检查是否已安装
if (file_exists('../config/installed.lock')) {
    die('系统已安装，如需重新安装请删除 config/installed.lock 文件');
}

$step = isset($_GET['step']) ? intval($_GET['step']) : 1;

// 环境检测结果
$envCheckResult = [];
$extCheckResult = [];
$dirCheckResult = [];

// 如果是第一步，检查环境
if ($step == 1) {
    // 检查PHP版本
    $phpVersion = PHP_VERSION;
    $envCheckResult['php_version'] = [
        'name' => 'PHP版本',
        'current' => $phpVersion,
        'require' => '7.0.0',
        'result' => version_compare($phpVersion, '7.0.0', '>=')
    ];
    
    // 检查MySQL版本 - 使用更可靠的方法
    $mysqlVersion = '';
    
    // 尝试使用命令行获取MySQL版本
    if (function_exists('exec')) {
        @exec('mysql -V', $output);
        if (!empty($output[0])) {
            // 从输出中提取版本号
            if (preg_match('/Distrib ([0-9.]+)/', $output[0], $matches)) {
                $mysqlVersion = $matches[1];
            }
        }
    }
    
    // 如果命令行方法失败，尝试使用mysqli
    if (empty($mysqlVersion) && extension_loaded('mysqli')) {
        try {
            // 尝试连接本地MySQL
            $mysqli = @new mysqli('localhost', 'root', '');
            if (!$mysqli->connect_error) {
                $mysqlVersion = $mysqli->server_info;
                $mysqli->close();
            }
        } catch (Exception $e) {
            // 忽略连接错误
        }
    }
    
    // 如果mysqli方法失败，尝试使用PDO
    if (empty($mysqlVersion) && extension_loaded('pdo_mysql')) {
        try {
            $pdo = @new PDO('mysql:host=localhost', 'root', '');
            $mysqlVersion = $pdo->getAttribute(PDO::ATTR_SERVER_VERSION);
        } catch (PDOException $e) {
            // 忽略连接错误
        }
    }
    
    // 如果仍然无法获取版本，尝试使用phpinfo
    if (empty($mysqlVersion)) {
        ob_start();
        @phpinfo(INFO_MODULES);
        $info = ob_get_clean();
        
        // 从phpinfo输出中查找MySQL版本
        if (preg_match('/Client API version[^>]*>([0-9.]+)/i', $info, $matches)) {
            $mysqlVersion = $matches[1];
        } elseif (preg_match('/MySQL client API version[^>]*>([0-9.]+)/i', $info, $matches)) {
            $mysqlVersion = $matches[1];
        }
    }
    
    // 如果所有方法都失败，设置为未知且不允许继续安装
    if (empty($mysqlVersion)) {
        $mysqlVersion = '未知 (无法检测，请确保MySQL已正确安装并可访问)';
        $mysqlVersionCompare = '0.0.0'; // 设置一个低版本，确保检测不通过
    } else {
        $mysqlVersionCompare = $mysqlVersion; // 如果能获取到版本，直接用于比较
    }
    
    $envCheckResult['mysql_version'] = [
        'name' => 'MySQL版本',
        'current' => $mysqlVersion,
        'require' => '5.7.0',
        'result' => version_compare($mysqlVersionCompare, '5.7.0', '>=')
    ];
    
    // 检查PHP扩展
    $requiredExt = ['pdo', 'pdo_mysql', 'curl', 'json', 'openssl'];
    foreach ($requiredExt as $ext) {
        $extCheckResult[$ext] = [
            'name' => $ext,
            'result' => extension_loaded($ext)
        ];
    }
    
    // 检查目录权限
    $checkDirs = [
        '../config',
        '../uploads',
        '../temp'
    ];
    foreach ($checkDirs as $dir) {
        if (!file_exists($dir)) {
            @mkdir($dir, 0777, true);
        }
        
        $dirCheckResult[$dir] = [
            'name' => $dir,
            'result' => is_writable($dir)
        ];
    }
}

// 保存数据库配置
if ($step == 2 && isset($_POST['db_host'])) {
    $_SESSION['db_config'] = [
        'host' => $_POST['db_host'],
        'dbname' => $_POST['db_name'],
        'username' => $_POST['db_user'],
        'password' => $_POST['db_pass'],
        'prefix' => $_POST['db_prefix']
    ];
    
    // 测试数据库连接
    try {
        $dsn = "mysql:host={$_POST['db_host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $_POST['db_user'], $_POST['db_pass']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 检查数据库是否存在，不存在则创建
        $dbname = $_POST['db_name'];
        $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{$dbname}'");
        if (!$stmt->fetch()) {
            $pdo->exec("CREATE DATABASE `{$dbname}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        }
        
        // 跳转到下一步
        header("Location: install.php?step=3");
        exit;
    } catch (PDOException $e) {
        $dbError = $e->getMessage();
    }
}

// 保存管理员信息
if ($step == 3 && isset($_POST['admin_user'])) {
    $_SESSION['admin_config'] = [
        'username' => $_POST['admin_user'],
        'password' => $_POST['admin_pass'],
        'email' => $_POST['admin_email']
    ];
    
    // 跳转到下一步
    header("Location: install.php?step=4");
    exit;
}

// 保存网站配置
if ($step == 4 && isset($_POST['site_name'])) {
    $_SESSION['site_config'] = [
        'site_name' => $_POST['site_name'],
        'site_url' => $_POST['site_url'],
        'site_description' => $_POST['site_description'],
        'site_keywords' => $_POST['site_keywords']
    ];
    
    // 跳转到安装页面
    header("Location: install.php?step=5");
    exit;
}

// 执行安装
if ($step == 5) {
    $installResult = [
        'status' => true,
        'message' => '安装成功！'
    ];
    
    try {
        // 创建数据库配置文件
        $dbConfig = $_SESSION['db_config'];
        $dbConfigContent = "<?php\n/**\n * 数据库配置文件\n */\nreturn [\n";
        $dbConfigContent .= "    'host' => '{$dbConfig['host']}',\n";
        $dbConfigContent .= "    'dbname' => '{$dbConfig['dbname']}',\n";
        $dbConfigContent .= "    'username' => '{$dbConfig['username']}',\n";
        $dbConfigContent .= "    'password' => '{$dbConfig['password']}',\n";
        $dbConfigContent .= "    'prefix' => '{$dbConfig['prefix']}',\n";
        $dbConfigContent .= "    'charset' => 'utf8mb4',\n";
        $dbConfigContent .= "    'options' => [\n";
        $dbConfigContent .= "        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
        $dbConfigContent .= "        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
        $dbConfigContent .= "        PDO::ATTR_EMULATE_PREPARES => false,\n";
        $dbConfigContent .= "    ]\n";
        $dbConfigContent .= "];\n";
        
        file_put_contents('../config/database.php', $dbConfigContent);
        
        // 创建应用配置文件
        $siteConfig = $_SESSION['site_config'];
        $appConfigContent = "<?php\n/**\n * 应用配置文件\n */\nreturn [\n";
        $appConfigContent .= "    'app_name' => '{$siteConfig['site_name']}',\n";
        $appConfigContent .= "    'app_version' => '1.0.0',\n";
        $appConfigContent .= "    'debug' => true,\n";
        $appConfigContent .= "    'timezone' => 'Asia/Shanghai',\n";
        $appConfigContent .= "    'default_language' => 'zh-cn',\n";
        $appConfigContent .= "    'site_url' => '{$siteConfig['site_url']}',\n";
        $appConfigContent .= "    'site_description' => '{$siteConfig['site_description']}',\n";
        $appConfigContent .= "    'site_keywords' => '{$siteConfig['site_keywords']}',\n";
        $appConfigContent .= "    'security' => [\n";
        $appConfigContent .= "        'jwt_secret' => '" . bin2hex(random_bytes(32)) . "',\n";
        $appConfigContent .= "        'password_salt' => '" . bin2hex(random_bytes(16)) . "',\n";
        $appConfigContent .= "        'session_lifetime' => 7200,\n";
        $appConfigContent .= "    ],\n";
        $appConfigContent .= "    'upload' => [\n";
        $appConfigContent .= "        'max_size' => 10 * 1024 * 1024,\n";
        $appConfigContent .= "        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],\n";
        $appConfigContent .= "        'upload_path' => 'uploads/',\n";
        $appConfigContent .= "    ],\n";
        $appConfigContent .= "    'mail' => [\n";
        $appConfigContent .= "        'smtp_host' => '',\n";
        $appConfigContent .= "        'smtp_port' => 587,\n";
        $appConfigContent .= "        'smtp_username' => '',\n";
        $appConfigContent .= "        'smtp_password' => '',\n";
        $appConfigContent .= "        'from_email' => '',\n";
        $appConfigContent .= "        'from_name' => '{$siteConfig['site_name']}',\n";
        $appConfigContent .= "    ],\n";
        $appConfigContent .= "];\n";
        
        file_put_contents('../config/app.php', $appConfigContent);
        
        // 导入数据库
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 读取SQL文件
        $sql = file_get_contents('database.sql');
        
        // 替换表前缀
        if (!empty($dbConfig['prefix'])) {
            $sql = str_replace('api_', $dbConfig['prefix'], $sql);
        }
        
        // 执行SQL
        $pdo->exec($sql);
        
        // 创建管理员账户
        $adminConfig = $_SESSION['admin_config'];
        $password = password_hash($adminConfig['password'], PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO {$dbConfig['prefix']}users (username, password, email, role, status, created_at) VALUES (?, ?, ?, 'admin', 1, NOW())");
        $stmt->execute([$adminConfig['username'], $password, $adminConfig['email']]);
        
        // 创建安装锁定文件
        file_put_contents('../config/installed.lock', date('Y-m-d H:i:s'));
        
        // 清除安装会话
        session_destroy();
    } catch (Exception $e) {
        $installResult = [
            'status' => false,
            'message' => '安装失败：' . $e->getMessage()
        ];
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API商业系统 - 安装向导</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../Easyweb/assets/module/admin.css">
    <link rel="stylesheet" href="../Easyweb/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css">
    <style>
        :root {
            --primary-color: #1890FF;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --danger-color: #f5222d;
            --dark-color: #001529;
            --light-color: #f0f2f5;
        }
        
        body {
            background: var(--light-color);
            font-family: "PingFang SC", "Helvetica Neue", Helvetica, "microsoft yahei", arial, STHeiTi, sans-serif;
        }
        
        .install-header {
            background: var(--dark-color);
            height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 1px 4px rgba(0,21,41,.08);
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        
        .install-header .logo {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .install-header .logo i {
            margin-right: 10px;
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .install-container {
            max-width: 900px;
            margin: 80px auto 30px;
            padding: 0 20px;
        }
        
        .install-card {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            overflow: hidden;
            transition: all .3s;
        }
        
        .step-nav {
            padding: 30px 30px 0;
        }
        
        .step-progress {
            height: 8px;
            border-radius: 100px;
            background: #e9ecef;
            overflow: hidden;
            position: relative;
        }
        
        .step-progress-bar {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: var(--primary-color);
            border-radius: 100px;
            transition: width .5s ease;
        }
        
        .step-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            position: relative;
        }
        
        .step-label {
            text-align: center;
            width: 20%;
            position: relative;
            font-size: 13px;
            color: #999;
            transition: all .3s;
        }
        
        .step-label.active {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .step-label.done {
            color: var(--primary-color);
        }
        
        .step-label .step-point {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            position: relative;
            z-index: 2;
            transition: all .3s;
            border: 2px solid #e9ecef;
            color: #fff;
        }
        
        .step-label.active .step-point {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .step-label.done .step-point {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .install-content {
            padding: 30px;
            min-height: 400px;
        }
        
        .step-title {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .step-title h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: #333;
        }
        
        .step-title p {
            margin: 5px 0 0;
            color: #999;
            font-size: 14px;
        }
        
        .step-title .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
        }
        
        .check-item {
            padding: 15px;
            border-radius: 4px;
            background: #fafafa;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all .3s;
        }
        
        .check-item:hover {
            background: #f5f5f5;
        }
        
        .check-item .check-name {
            font-weight: 500;
            color: #333;
        }
        
        .check-item .check-detail {
            color: #666;
            font-size: 13px;
            margin-top: 3px;
        }
        
        .check-result {
            display: flex;
            align-items: center;
        }
        
        .check-result .badge {
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .check-success {
            color: var(--success-color);
        }
        
        .check-error {
            color: var(--danger-color);
        }
        
        .badge-success {
            background: rgba(82, 196, 26, 0.1);
            color: var(--success-color);
        }
        
        .badge-danger {
            background: rgba(245, 34, 45, 0.1);
            color: var(--danger-color);
        }
        
        .badge-warning {
            background: rgba(250, 173, 20, 0.1);
            color: var(--warning-color);
        }
        
        .badge-info {
            background: rgba(24, 144, 255, 0.1);
            color: var(--primary-color);
        }
        
        .footer {
            padding: 20px 30px;
            border-top: 1px solid #f0f0f0;
            text-align: right;
            background: #fafafa;
        }
        
        .btn {
            height: 40px;
            line-height: 40px;
            padding: 0 20px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all .3s;
            display: inline-block;
            text-align: center;
            border: none;
            outline: none;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: #fff;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        
        .btn-default {
            background: #fff;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-success {
            background: var(--success-color);
            color: #fff;
        }
        
        .btn-success:hover {
            background: #73d13d;
            color: #fff;
        }
        
        .btn-disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
            border: 1px solid #d9d9d9;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            height: 40px;
            line-height: 40px;
            padding: 0 15px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            transition: all .3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-control:hover {
            border-color: #40a9ff;
        }
        
        .form-textarea {
            min-height: 80px;
            padding: 10px 15px;
            line-height: 1.5;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .alert-success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        
        .alert-danger {
            background: #fff2f0;
            border: 1px solid #ffccc7;
        }
        
        .alert-warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
        }
        
        .alert-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
        }
        
        .log-window {
            background: #001529;
            color: #fff;
            padding: 15px;
            border-radius: 4px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            height: 200px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .log-window .log-line {
            margin-bottom: 5px;
            line-height: 1.5;
        }
        
        .log-window .log-success {
            color: #52c41a;
        }
        
        .log-window .log-error {
            color: #f5222d;
        }
        
        .log-window .log-warning {
            color: #faad14;
        }
        
        .log-window .log-info {
            color: #1890ff;
        }
        
        .tab-nav {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 20px;
        }
        
        .tab-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: all .3s;
            font-weight: 500;
            position: relative;
            color: #666;
        }
        
        .tab-item.active {
            color: var(--primary-color);
        }
        
        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-color);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .install-success-icon {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-success-icon i {
            font-size: 80px;
            color: var(--success-color);
        }
        
        .install-error-icon {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-error-icon i {
            font-size: 80px;
            color: var(--danger-color);
        }
        
        .install-result-title {
            text-align: center;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .install-result-message {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }
        
        .install-result-info {
            background: #fafafa;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .install-result-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #f0f0f0;
        }
        
        .install-result-info-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .install-result-info-label {
            font-weight: 500;
            color: #333;
        }
        
        .install-result-info-value {
            color: #666;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .install-container {
                margin-top: 70px;
                padding: 0 10px;
            }
            
            .step-nav, .install-content, .footer {
                padding: 15px;
            }
            
            .step-label {
                font-size: 12px;
            }
            
            .step-label .step-point {
                width: 20px;
                height: 20px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="install-header">
        <div class="logo">
            <i class="fa fa-cloud"></i>
            <span>API商业系统安装向导</span>
        </div>
    </div>
    
    <div class="install-container">
        <!-- 安装卡片 -->
        <div class="install-card animate-fade-in">
            <!-- 步骤导航 -->
            <div class="step-nav">
                <div class="step-progress">
                    <div class="step-progress-bar" style="width: <?php echo ($step / 5) * 100; ?>%;"></div>
                </div>
                <div class="step-labels">
                    <div class="step-label <?php echo $step >= 1 ? 'done' : ''; ?> <?php echo $step == 1 ? 'active' : ''; ?>">
                        <div class="step-point">
                            <?php if ($step > 1): ?>
                                <i class="fa fa-check"></i>
                            <?php else: ?>
                                1
                            <?php endif; ?>
                        </div>
                        <div>环境检测</div>
                    </div>
                    <div class="step-label <?php echo $step >= 2 ? 'done' : ''; ?> <?php echo $step == 2 ? 'active' : ''; ?>">
                        <div class="step-point">
                            <?php if ($step > 2): ?>
                                <i class="fa fa-check"></i>
                            <?php else: ?>
                                2
                            <?php endif; ?>
                        </div>
                        <div>数据库配置</div>
                    </div>
                    <div class="step-label <?php echo $step >= 3 ? 'done' : ''; ?> <?php echo $step == 3 ? 'active' : ''; ?>">
                        <div class="step-point">
                            <?php if ($step > 3): ?>
                                <i class="fa fa-check"></i>
                            <?php else: ?>
                                3
                            <?php endif; ?>
                        </div>
                        <div>管理员设置</div>
                    </div>
                    <div class="step-label <?php echo $step >= 4 ? 'done' : ''; ?> <?php echo $step == 4 ? 'active' : ''; ?>">
                        <div class="step-point">
                            <?php if ($step > 4): ?>
                                <i class="fa fa-check"></i>
                            <?php else: ?>
                                4
                            <?php endif; ?>
                        </div>
                        <div>网站配置</div>
                    </div>
                    <div class="step-label <?php echo $step >= 5 ? 'done' : ''; ?> <?php echo $step == 5 ? 'active' : ''; ?>">
                        <div class="step-point">
                            <?php if ($step > 5): ?>
                                <i class="fa fa-check"></i>
                            <?php else: ?>
                                5
                            <?php endif; ?>
                        </div>
                        <div>安装完成</div>
                    </div>
                </div>
            </div>
            
            <!-- 安装内容 -->
            <div class="install-content">
                <?php if ($step == 1): ?>
                    <!-- 环境检测 -->
                    <div class="step-title">
                        <div class="step-icon">
                            <i class="fa fa-server"></i>
                        </div>
                        <div>
                            <h2>环境检测</h2>
                            <p>安装前，请确保您的服务器环境满足以下要求</p>
                        </div>
                    </div>
                    
                    <div class="tab-nav">
                        <div class="tab-item active" data-tab="env-tab">环境要求</div>
                        <div class="tab-item" data-tab="ext-tab">PHP扩展</div>
                        <div class="tab-item" data-tab="dir-tab">目录权限</div>
                    </div>
                    
                    <div class="tab-content active" id="env-tab">
                        <?php foreach ($envCheckResult as $item): ?>
                            <div class="check-item">
                                <div>
                                    <div class="check-name"><?php echo $item['name']; ?></div>
                                    <div class="check-detail">要求: <?php echo $item['require']; ?> / 当前: <?php echo $item['current']; ?></div>
                                </div>
                                <div class="check-result">
                                    <?php if ($item['result']): ?>
                                        <span class="badge badge-success">通过</span>
                                        <i class="fa fa-check-circle check-success"></i>
                                    <?php else: ?>
                                        <span class="badge badge-danger">不通过</span>
                                        <i class="fa fa-times-circle check-error"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="tab-content" id="ext-tab">
                        <?php foreach ($extCheckResult as $item): ?>
                            <div class="check-item">
                                <div>
                                    <div class="check-name"><?php echo $item['name']; ?> 扩展</div>
                                    <div class="check-detail">PHP <?php echo $item['name']; ?> 扩展是系统正常运行所必需的</div>
                                </div>
                                <div class="check-result">
                                    <?php if ($item['result']): ?>
                                        <span class="badge badge-success">已安装</span>
                                        <i class="fa fa-check-circle check-success"></i>
                                    <?php else: ?>
                                        <span class="badge badge-danger">未安装</span>
                                        <i class="fa fa-times-circle check-error"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="tab-content" id="dir-tab">
                        <?php foreach ($dirCheckResult as $item): ?>
                            <div class="check-item">
                                <div>
                                    <div class="check-name"><?php echo $item['name']; ?></div>
                                    <div class="check-detail">该目录需要有写入权限</div>
                                </div>
                                <div class="check-result">
                                    <?php if ($item['result']): ?>
                                        <span class="badge badge-success">可写</span>
                                        <i class="fa fa-check-circle check-success"></i>
                                    <?php else: ?>
                                        <span class="badge badge-danger">不可写</span>
                                        <i class="fa fa-times-circle check-error"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- 实时日志输出窗口 -->
                    <div class="log-window">
                        <div class="log-line log-info">[INFO] 开始检测系统环境...</div>
                        <div class="log-line log-info">[INFO] 检测PHP版本: <?php echo $envCheckResult['php_version']['current']; ?></div>
                        <?php if ($envCheckResult['php_version']['result']): ?>
                        <div class="log-line log-success">[SUCCESS] PHP版本符合要求</div>
                        <?php else: ?>
                        <div class="log-line log-error">[ERROR] PHP版本不符合要求，需要 <?php echo $envCheckResult['php_version']['require']; ?> 或更高版本</div>
                        <?php endif; ?>
                        <div class="log-line log-info">[INFO] 检测MySQL版本: <?php echo $envCheckResult['mysql_version']['current']; ?></div>
                        <?php if ($envCheckResult['mysql_version']['result']): ?>
                        <div class="log-line log-success">[SUCCESS] MySQL版本符合要求</div>
                        <?php else: ?>
                        <div class="log-line log-error">[ERROR] MySQL版本不符合要求，需要 <?php echo $envCheckResult['mysql_version']['require']; ?> 或更高版本</div>
                        <?php endif; ?>
                        <div class="log-line log-info">[INFO] 检测PHP扩展...</div>
                        <?php foreach ($extCheckResult as $ext => $item): ?>
                        <?php if ($item['result']): ?>
                        <div class="log-line log-success">[SUCCESS] <?php echo $ext; ?> 扩展已安装</div>
                        <?php else: ?>
                        <div class="log-line log-error">[ERROR] <?php echo $ext; ?> 扩展未安装</div>
                        <?php endif; ?>
                        <?php endforeach; ?>
                        <div class="log-line log-info">[INFO] 检测目录权限...</div>
                        <?php foreach ($dirCheckResult as $dir => $item): ?>
                        <?php if ($item['result']): ?>
                        <div class="log-line log-success">[SUCCESS] <?php echo $dir; ?> 目录可写</div>
                        <?php else: ?>
                        <div class="log-line log-error">[ERROR] <?php echo $dir; ?> 目录不可写</div>
                        <?php endif; ?>
                        <?php endforeach; ?>
                        <div class="log-line log-info">[INFO] 环境检测完成</div>
                    </div>
                    
                    <div class="footer">
                        <?php
                        $canContinue = true;
                        foreach ($envCheckResult as $item) {
                            if (!$item['result']) {
                                $canContinue = false;
                                break;
                            }
                        }
                        foreach ($extCheckResult as $item) {
                            if (!$item['result']) {
                                $canContinue = false;
                                break;
                            }
                        }
                        foreach ($dirCheckResult as $item) {
                            if (!$item['result']) {
                                $canContinue = false;
                                break;
                            }
                        }
                        ?>
                        <?php if ($canContinue): ?>
                            <a href="install.php?step=2" class="btn btn-primary">下一步</a>
                        <?php else: ?>
                            <button class="btn btn-disabled">请解决所有环境问题后继续</button>
                            <div class="alert alert-danger" style="margin-top: 15px;">
                                <i class="fa fa-exclamation-circle"></i> 检测到环境问题，请确保所有检测项都通过后再继续安装。
                                <?php if (!$envCheckResult['mysql_version']['result']): ?>
                                <br><strong>MySQL版本检测问题：</strong> 请确保MySQL服务已启动且配置正确。如果您确认MySQL版本符合要求但仍无法检测，请检查数据库连接设置。
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php elseif ($step == 2): ?>
                    <!-- 数据库配置 -->
                    <div class="step-title">
                        <div class="step-icon">
                            <i class="fa fa-database"></i>
                        </div>
                        <div>
                            <h2>数据库配置</h2>
                            <p>请填写您的数据库连接信息</p>
                        </div>
                    </div>
                    
                    <?php if (isset($dbError)): ?>
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> 数据库连接错误：<?php echo $dbError; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form action="install.php?step=2" method="post">
                        <div class="form-group">
                            <label class="form-label">数据库主机</label>
                            <input type="text" name="db_host" value="localhost" required class="form-control" placeholder="请输入数据库主机地址">
                        </div>
                        <div class="form-group">
                            <label class="form-label">数据库名</label>
                            <input type="text" name="db_name" value="api_system" required class="form-control" placeholder="请输入数据库名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">用户名</label>
                            <input type="text" name="db_user" value="root" required class="form-control" placeholder="请输入数据库用户名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">密码</label>
                            <input type="password" name="db_pass" class="form-control" placeholder="请输入数据库密码">
                        </div>
                        <div class="form-group">
                            <label class="form-label">表前缀</label>
                            <input type="text" name="db_prefix" value="api_" class="form-control" placeholder="请输入数据表前缀">
                        </div>
                        
                        <!-- 实时日志输出窗口 -->
                        <div class="log-window">
                            <div class="log-line log-info">[INFO] 准备配置数据库连接...</div>
                            <div class="log-line log-info">[INFO] 数据库主机: localhost</div>
                            <div class="log-line log-info">[INFO] 数据库名: api_system</div>
                            <div class="log-line log-info">[INFO] 表前缀: api_</div>
                            <div class="log-line log-info">[INFO] 请填写正确的数据库信息并点击"下一步"按钮</div>
                        </div>
                        
                        <div class="footer">
                            <a href="install.php?step=1" class="btn btn-default">上一步</a>
                            <button type="submit" class="btn btn-primary">下一步</button>
                        </div>
                    </form>
                <?php elseif ($step == 3): ?>
                    <!-- 管理员设置 -->
                    <div class="step-title">
                        <div class="step-icon">
                            <i class="fa fa-user-circle"></i>
                        </div>
                        <div>
                            <h2>管理员设置</h2>
                            <p>请设置系统管理员账户信息</p>
                        </div>
                    </div>
                    
                    <form action="install.php?step=3" method="post">
                        <div class="form-group">
                            <label class="form-label">用户名</label>
                            <input type="text" name="admin_user" value="admin" required class="form-control" placeholder="请输入管理员用户名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">密码</label>
                            <input type="password" name="admin_pass" required class="form-control" placeholder="请输入管理员密码">
                        </div>
                        <div class="form-group">
                            <label class="form-label">确认密码</label>
                            <input type="password" name="admin_pass_confirm" required class="form-control" placeholder="请再次输入密码">
                        </div>
                        <div class="form-group">
                            <label class="form-label">邮箱</label>
                            <input type="email" name="admin_email" required class="form-control" placeholder="请输入管理员邮箱">
                        </div>
                        
                        <!-- 实时日志输出窗口 -->
                        <div class="log-window">
                            <div class="log-line log-info">[INFO] 准备设置管理员账户...</div>
                            <div class="log-line log-info">[INFO] 默认用户名: admin</div>
                            <div class="log-line log-warning">[WARNING] 请使用安全的密码，建议包含字母、数字和特殊字符</div>
                            <div class="log-line log-info">[INFO] 管理员账户将拥有系统的所有权限</div>
                        </div>
                        
                        <div class="footer">
                            <a href="install.php?step=2" class="btn btn-default">上一步</a>
                            <button type="submit" class="btn btn-primary">下一步</button>
                        </div>
                    </form>
                <?php elseif ($step == 4): ?>
                    <!-- 网站配置 -->
                    <div class="step-title">
                        <div class="step-icon">
                            <i class="fa fa-cog"></i>
                        </div>
                        <div>
                            <h2>网站配置</h2>
                            <p>请设置您的网站基本信息</p>
                        </div>
                    </div>
                    
                    <form action="install.php?step=4" method="post">
                        <div class="form-group">
                            <label class="form-label">网站名称</label>
                            <input type="text" name="site_name" value="API商业系统" required class="form-control" placeholder="请输入网站名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">网站URL</label>
                            <input type="text" name="site_url" value="<?php echo 'http://' . $_SERVER['HTTP_HOST']; ?>" required class="form-control" placeholder="请输入网站URL">
                        </div>
                        <div class="form-group">
                            <label class="form-label">网站描述</label>
                            <textarea name="site_description" class="form-control form-textarea" placeholder="请输入网站描述">专业的API接口管理平台，提供完整的API商业化解决方案</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">网站关键词</label>
                            <input type="text" name="site_keywords" value="API,接口,商业化,管理系统" class="form-control" placeholder="请输入网站关键词">
                        </div>
                        
                        <!-- 实时日志输出窗口 -->
                        <div class="log-window">
                            <div class="log-line log-info">[INFO] 准备配置网站基本信息...</div>
                            <div class="log-line log-info">[INFO] 默认网站名称: API商业系统</div>
                            <div class="log-line log-info">[INFO] 自动检测到网站URL: <?php echo 'http://' . $_SERVER['HTTP_HOST']; ?></div>
                            <div class="log-line log-info">[INFO] 这些信息将用于网站SEO和系统配置</div>
                        </div>
                        
                        <div class="footer">
                            <a href="install.php?step=3" class="btn btn-default">上一步</a>
                            <button type="submit" class="btn btn-primary">下一步</button>
                        </div>
                    </form>
                <?php elseif ($step == 5): ?>
                    <!-- 安装完成 -->
                    <?php if ($installResult['status']): ?>
                        <div class="install-success-icon">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <div class="install-result-title">安装成功</div>
                        <div class="install-result-message">恭喜您，API商业系统已成功安装！</div>
                        
                        <div class="install-result-info">
                            <div class="install-result-info-item">
                                <div class="install-result-info-label">管理员账号</div>
                                <div class="install-result-info-value"><?php echo $_SESSION['admin_config']['username'] ?? 'admin'; ?></div>
                            </div>
                            <div class="install-result-info-item">
                                <div class="install-result-info-label">管理员密码</div>
                                <div class="install-result-info-value">已设置，请牢记您的密码</div>
                            </div>
                            <div class="install-result-info-item">
                                <div class="install-result-info-label">网站名称</div>
                                <div class="install-result-info-value"><?php echo $_SESSION['site_config']['site_name'] ?? 'API商业系统'; ?></div>
                            </div>
                            <div class="install-result-info-item">
                                <div class="install-result-info-label">网站URL</div>
                                <div class="install-result-info-value"><?php echo $_SESSION['site_config']['site_url'] ?? 'http://' . $_SERVER['HTTP_HOST']; ?></div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i> 为了安全起见，请删除 install 目录
                        </div>
                        
                        <!-- 实时日志输出窗口 -->
                        <div class="log-window">
                            <div class="log-line log-info">[INFO] 开始执行安装过程...</div>
                            <div class="log-line log-info">[INFO] 创建数据库配置文件...</div>
                            <div class="log-line log-success">[SUCCESS] 数据库配置文件创建成功</div>
                            <div class="log-line log-info">[INFO] 创建应用配置文件...</div>
                            <div class="log-line log-success">[SUCCESS] 应用配置文件创建成功</div>
                            <div class="log-line log-info">[INFO] 导入数据库结构...</div>
                            <div class="log-line log-success">[SUCCESS] 数据库结构导入成功</div>
                            <div class="log-line log-info">[INFO] 创建管理员账户...</div>
                            <div class="log-line log-success">[SUCCESS] 管理员账户创建成功</div>
                            <div class="log-line log-info">[INFO] 创建安装锁定文件...</div>
                            <div class="log-line log-success">[SUCCESS] 安装锁定文件创建成功</div>
                            <div class="log-line log-success">[SUCCESS] 安装完成！</div>
                        </div>
                    <?php else: ?>
                        <div class="install-error-icon">
                            <i class="fa fa-times-circle"></i>
                        </div>
                        <div class="install-result-title">安装失败</div>
                        <div class="install-result-message"><?php echo $installResult['message']; ?></div>
                        
                        <!-- 实时日志输出窗口 -->
                        <div class="log-window">
                            <div class="log-line log-info">[INFO] 开始执行安装过程...</div>
                            <div class="log-line log-error">[ERROR] <?php echo $installResult['message']; ?></div>
                            <div class="log-line log-info">[INFO] 请检查错误信息并重试</div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="footer">
                        <?php if ($installResult['status']): ?>
                            <a href="../admin/" class="btn btn-success">进入后台管理</a>
                            <a href="../" class="btn btn-primary">进入前台首页</a>
                        <?php else: ?>
                            <a href="install.php?step=1" class="btn btn-default">重新安装</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
    layui.use(['element', 'form'], function(){
        var element = layui.element;
        var form = layui.form;
        
        // 标签页切换
        document.querySelectorAll('.tab-item').forEach(function(tab) {
            tab.addEventListener('click', function() {
                // 移除所有标签页的active类
                document.querySelectorAll('.tab-item').forEach(function(t) {
                    t.classList.remove('active');
                });
                
                // 移除所有内容区的active类
                document.querySelectorAll('.tab-content').forEach(function(c) {
                    c.classList.remove('active');
                });
                
                // 给当前标签页添加active类
                this.classList.add('active');
                
                // 显示对应的内容区
                var tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        <?php if ($step == 3): ?>
        // 验证两次密码是否一致
        form.verify({
            confirmPass: function(value) {
                if(value !== document.querySelector('input[name=admin_pass]').value) {
                    return '两次输入的密码不一致';
                }
            }
        });
        <?php endif; ?>
    });
    </script>
</body>
</html>
