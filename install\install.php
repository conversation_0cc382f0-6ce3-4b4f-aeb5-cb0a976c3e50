<?php
/**
 * API商业系统安装脚本
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('BASE_PATH', dirname(__DIR__));
define('INSTALL_PATH', __DIR__);

// 安装步骤
$step = isset($_GET['step']) ? intval($_GET['step']) : 1;

// 安装状态
$installLock = BASE_PATH . '/install/install.lock';
if (file_exists($installLock) && $step != 5) {
    die('系统已经安装，如需重新安装，请删除 install/install.lock 文件');
}

// 检查PHP版本
$phpVersion = phpversion();
$phpVersionCheck = version_compare($phpVersion, '7.2.0', '>=');

// 检查必要的PHP扩展
$extensions = [
    'pdo' => extension_loaded('pdo'),
    'pdo_mysql' => extension_loaded('pdo_mysql'),
    'curl' => extension_loaded('curl'),
    'gd' => extension_loaded('gd'),
    'mbstring' => extension_loaded('mbstring'),
    'json' => extension_loaded('json'),
    'openssl' => extension_loaded('openssl'),
    'fileinfo' => extension_loaded('fileinfo')
];

// 检查目录权限
$directories = [
    '/config',
    '/uploads',
    '/logs',
    '/cache',
    '/templates/cache'
];

$directoryPermissions = [];
foreach ($directories as $dir) {
    $path = BASE_PATH . $dir;
    if (!file_exists($path)) {
        @mkdir($path, 0755, true);
    }
    $directoryPermissions[$dir] = is_writable($path);
}

// 处理表单提交
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // 环境检查通过，进入下一步
            header('Location: install.php?step=3');
            exit;
            
        case 3:
            // 数据库配置
            $dbHost = $_POST['db_host'] ?? '';
            $dbName = $_POST['db_name'] ?? '';
            $dbUser = $_POST['db_user'] ?? '';
            $dbPass = $_POST['db_pass'] ?? '';
            $dbPrefix = $_POST['db_prefix'] ?? '';
            
            if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
                $error = '请填写完整的数据库信息';
            } else {
                try {
                    // 测试数据库连接
                    $dsn = "mysql:host=$dbHost;charset=utf8mb4";
                    $pdo = new PDO($dsn, $dbUser, $dbPass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // 检查数据库是否存在，不存在则创建
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    
                    // 保存数据库配置
                    $dbConfig = <<<EOT
<?php
/**
 * 数据库配置文件
 */
return [
    'host' => '$dbHost',
    'dbname' => '$dbName',
    'username' => '$dbUser',
    'password' => '$dbPass',
    'prefix' => '$dbPrefix',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
EOT;
                    
                    file_put_contents(BASE_PATH . '/config/database.php', $dbConfig);
                    
                    // 进入下一步
                    header('Location: install.php?step=4');
                    exit;
                } catch (PDOException $e) {
                    $error = '数据库连接失败：' . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // 网站配置
            $siteName = $_POST['site_name'] ?? '';
            $siteUrl = $_POST['site_url'] ?? '';
            $adminEmail = $_POST['admin_email'] ?? '';
            $adminUser = $_POST['admin_user'] ?? '';
            $adminPass = $_POST['admin_pass'] ?? '';
            $adminPassConfirm = $_POST['admin_pass_confirm'] ?? '';
            
            if (empty($siteName) || empty($siteUrl) || empty($adminEmail) || empty($adminUser) || empty($adminPass)) {
                $error = '请填写完整的网站信息';
            } elseif ($adminPass !== $adminPassConfirm) {
                $error = '两次输入的密码不一致';
            } else {
                try {
                    // 加载数据库配置
                    $dbConfig = require_once BASE_PATH . '/config/database.php';
                    
                    // 连接数据库
                    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
                    
                    // 导入数据库结构
                    $sql = file_get_contents(INSTALL_PATH . '/database.sql');
                    $pdo->exec($sql);
                    
                    // 导入额外的数据库结构
                    if (file_exists(INSTALL_PATH . '/database_part2.sql')) {
                        $sql = file_get_contents(INSTALL_PATH . '/database_part2.sql');
                        $pdo->exec($sql);
                    }
                    
                    // 更新网站配置
                    $stmt = $pdo->prepare("UPDATE site_config SET site_name = ?, site_url = ?, api_base_url = ?, admin_email = ? WHERE id = 1");
                    $apiBaseUrl = rtrim($siteUrl, '/') . '/api.php';
                    $stmt->execute([$siteName, $siteUrl, $apiBaseUrl, $adminEmail]);
                    
                    // 更新管理员信息
                    $adminPassHash = password_hash($adminPass, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE admins SET username = ?, password = ?, email = ? WHERE id = 1");
                    $stmt->execute([$adminUser, $adminPassHash, $adminEmail]);
                    
                    // 创建安装锁定文件
                    file_put_contents($installLock, date('Y-m-d H:i:s'));
                    
                    // 安装完成
                    header('Location: install.php?step=5');
                    exit;
                } catch (Exception $e) {
                    $error = '安装失败：' . $e->getMessage();
                }
            }
            break;
    }
}

// 页面头部
function outputHeader($title) {
    echo <<<EOT
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>$title - API商业系统安装</title>
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <style>
        body {
            background-color: #f2f2f2;
            padding: 20px;
        }
        .install-box {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .install-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .install-header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .install-body {
            padding: 20px 0;
        }
        .install-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .step-box {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        .step-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            position: relative;
        }
        .step-item:not(:last-child):after {
            content: "";
            position: absolute;
            top: 50%;
            right: 0;
            width: 100%;
            height: 1px;
            background-color: #e6e6e6;
            z-index: 1;
        }
        .step-item .step-num {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            background-color: #e6e6e6;
            color: #fff;
            position: relative;
            z-index: 2;
        }
        .step-item.active .step-num {
            background-color: #009688;
        }
        .step-item.done .step-num {
            background-color: #5FB878;
        }
        .check-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f2f2f2;
        }
        .check-item:last-child {
            border-bottom: none;
        }
        .check-item .check-name {
            flex: 1;
        }
        .check-item .check-status {
            width: 100px;
            text-align: center;
        }
        .check-status.success {
            color: #5FB878;
        }
        .check-status.error {
            color: #FF5722;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #3c763d;
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
    </style>
</head>
<body>
    <div class="install-box">
        <div class="install-header">
            <h1>API商业系统安装向导</h1>
            <p>欢迎使用API商业系统，本向导将帮助您完成系统的安装</p>
        </div>
        <div class="step-box">
            <div class="step-item <?php echo $step >= 1 ? 'active' : ''; ?> <?php echo $step > 1 ? 'done' : ''; ?>">
                <div class="step-num">1</div>
                <div class="step-text">许可协议</div>
            </div>
            <div class="step-item <?php echo $step >= 2 ? 'active' : ''; ?> <?php echo $step > 2 ? 'done' : ''; ?>">
                <div class="step-num">2</div>
                <div class="step-text">环境检查</div>
            </div>
            <div class="step-item <?php echo $step >= 3 ? 'active' : ''; ?> <?php echo $step > 3 ? 'done' : ''; ?>">
                <div class="step-num">3</div>
                <div class="step-text">数据库配置</div>
            </div>
            <div class="step-item <?php echo $step >= 4 ? 'active' : ''; ?> <?php echo $step > 4 ? 'done' : ''; ?>">
                <div class="step-num">4</div>
                <div class="step-text">网站配置</div>
            </div>
            <div class="step-item <?php echo $step >= 5 ? 'active' : ''; ?>">
                <div class="step-num">5</div>
                <div class="step-text">安装完成</div>
            </div>
        </div>
        <div class="install-body">
EOT;
}

// 页面底部
function outputFooter() {
    echo <<<EOT
        </div>
        <div class="install-footer">
            <p>Copyright © 2023 API商业系统 All Rights Reserved</p>
        </div>
    </div>
    <script src="../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function() {
            var form = layui.form;
            var layer = layui.layer;
            
            // 表单验证
            form.verify({
                pass: [/^[\S]{6,16}$/, '密码必须6到16位，且不能出现空格'],
                repass: function(value) {
                    var password = document.getElementById('adminPass').value;
                    if (value !== password) {
                        return '两次输入的密码不一致';
                    }
                }
            });
        });
    </script>
</body>
</html>
EOT;
}

// 显示错误信息
function showError($message) {
    if (!empty($message)) {
        echo '<div class="alert alert-danger">' . $message . '</div>';
    }
}

// 显示成功信息
function showSuccess($message) {
    if (!empty($message)) {
        echo '<div class="alert alert-success">' . $message . '</div>';
    }
}

// 根据步骤显示不同的内容
switch ($step) {
    case 1:
        // 许可协议
        outputHeader('许可协议');
        ?>
        <div class="layui-card">
            <div class="layui-card-header">使用许可协议</div>
            <div class="layui-card-body">
                <div style="height: 300px; overflow-y: auto; padding: 10px; border: 1px solid #e6e6e6; margin-bottom: 20px;">
                    <h3>API商业系统使用许可协议</h3>
                    <p>版权所有 (c) 2023，API商业系统保留所有权利。</p>
                    <p>感谢您选择API商业系统。本协议是您与API商业系统之间关于您使用API商业系统产品及服务的法律协议。无论您是个人或组织、盈利与否、用途如何，均需仔细阅读本协议。</p>
                    
                    <h4>一、协议许可的权利</h4>
                    <p>1. 您可以在完全遵守本许可协议的基础上，将本软件应用于商业用途。</p>
                    <p>2. 您可以在协议规定的约束和限制范围内修改源代码或界面风格以适应您的需求。</p>
                    <p>3. 您拥有使用本软件构建的网站中全部会员资料、文章及相关信息的所有权，并独立承担与之相关的法律责任。</p>
                    
                    <h4>二、协议规定的约束和限制</h4>
                    <p>1. 未获商业授权之前，不得将本软件用于商业用途（包括但不限于企业网站、经营性网站、以营利为目的或实现盈利的网站）。</p>
                    <p>2. 不得对本软件或与之关联的商业授权进行出租、出售、抵押或发放子许可证。</p>
                    <p>3. 无论如何，即无论用途如何、是否经过修改或美化、修改程度如何，只要使用本软件的整体或任何部分，未经书面许可，必须保留本软件的版权信息。</p>
                    <p>4. 禁止在本软件的整体或任何部分基础上以发展任何派生版本、修改版本或第三方版本用于重新分发。</p>
                    
                    <h4>三、免责声明</h4>
                    <p>1. 本软件及所附带的文件是作为不提供任何明确的或隐含的赔偿或担保的形式提供的。</p>
                    <p>2. 用户出于自愿而使用本软件，您必须了解使用本软件的风险，在尚未购买产品技术服务之前，我们不承诺提供任何形式的技术支持、使用担保，也不承担任何因使用本软件而产生问题的相关责任。</p>
                    <p>3. API商业系统不对使用本软件构建的网站中的文章或信息承担责任。</p>
                </div>
                <form class="layui-form" action="?step=2" method="post">
                    <div class="layui-form-item">
                        <input type="checkbox" name="agree" lay-skin="primary" title="我已阅读并同意上述协议" lay-verify="required" required>
                    </div>
                    <div class="layui-form-item">
                        <button class="layui-btn" lay-submit lay-filter="formStep1">下一步</button>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;
        
    case 2:
        // 环境检查
        outputHeader('环境检查');
        showError($error);
        ?>
        <div class="layui-card">
            <div class="layui-card-header">系统环境检查</div>
            <div class="layui-card-body">
                <div class="check-item">
                    <div class="check-name">PHP版本（>= 7.2.0）</div>
                    <div class="check-status <?php echo $phpVersionCheck ? 'success' : 'error'; ?>">
                        <?php echo $phpVersion; ?> <?php echo $phpVersionCheck ? '✓' : '✗'; ?>
                    </div>
                </div>
                <?php foreach ($extensions as $name => $loaded): ?>
                <div class="check-item">
                    <div class="check-name">PHP扩展：<?php echo $name; ?></div>
                    <div class="check-status <?php echo $loaded ? 'success' : 'error'; ?>">
                        <?php echo $loaded ? '已安装 ✓' : '未安装 ✗'; ?>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <div class="layui-card-header" style="margin-top: 20px;">目录权限检查</div>
                <?php foreach ($directoryPermissions as $dir => $writable): ?>
                <div class="check-item">
                    <div class="check-name">目录：<?php echo $dir; ?></div>
                    <div class="check-status <?php echo $writable ? 'success' : 'error'; ?>">
                        <?php echo $writable ? '可写 ✓' : '不可写 ✗'; ?>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <?php
                // 检查是否所有条件都满足
                $allPassed = $phpVersionCheck && !in_array(false, $extensions) && !in_array(false, $directoryPermissions);
                ?>
                
                <form class="layui-form" action="?step=2" method="post" style="margin-top: 20px;">
                    <div class="layui-form-item">
                        <?php if ($allPassed): ?>
                        <button class="layui-btn" lay-submit>下一步</button>
                        <?php else: ?>
                        <button class="layui-btn layui-btn-disabled" disabled>请解决上述问题后继续</button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;
        
    case 3:
        // 数据库配置
        outputHeader('数据库配置');
        showError($error);
        ?>
        <div class="layui-card">
            <div class="layui-card-header">数据库配置</div>
            <div class="layui-card-body">
                <form class="layui-form" action="?step=3" method="post">
                    <div class="layui-form-item">
                        <label class="layui-form-label">数据库主机</label>
                        <div class="layui-input-block">
                            <input type="text" name="db_host" value="localhost" required lay-verify="required" placeholder="数据库主机地址" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">数据库名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="db_name" value="api_system" required lay-verify="required" placeholder="数据库名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-block">
                            <input type="text" name="db_user" value="root" required lay-verify="required" placeholder="数据库用户名" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="db_pass" placeholder="数据库密码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">表前缀</label>
                        <div class="layui-input-block">
                            <input type="text" name="db_prefix" value="" placeholder="数据库表前缀（可选）" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="formStep3">下一步</button>
                            <a href="?step=2" class="layui-btn layui-btn-primary">上一步</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;
        
    case 4:
        // 网站配置
        outputHeader('网站配置');
        showError($error);
        ?>
        <div class="layui-card">
            <div class="layui-card-header">网站配置</div>
            <div class="layui-card-body">
                <form class="layui-form" action="?step=4" method="post">
                    <div class="layui-form-item">
                        <label class="layui-form-label">网站名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="site_name" value="API商业系统" required lay-verify="required" placeholder="网站名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">网站URL</label>
                        <div class="layui-input-block">
                            <input type="text" name="site_url" value="<?php echo 'http://' . $_SERVER['HTTP_HOST']; ?>" required lay-verify="required" placeholder="网站URL" autocomplete="off" class="layui-input">
                            <div class="layui-form-mid layui-word-aux">请以http://或https://开头，结尾不要带斜杠</div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">管理员邮箱</label>
                        <div class="layui-input-block">
                            <input type="email" name="admin_email" required lay-verify="required|email" placeholder="管理员邮箱" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">管理员账号</label>
                        <div class="layui-input-block">
                            <input type="text" name="admin_user" value="admin" required lay-verify="required" placeholder="管理员账号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">管理员密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="admin_pass" id="adminPass" required lay-verify="required|pass" placeholder="管理员密码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">确认密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="admin_pass_confirm" required lay-verify="required|repass" placeholder="确认密码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="formStep4">立即安装</button>
                            <a href="?step=3" class="layui-btn layui-btn-primary">上一步</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;
        
    case 5:
        // 安装完成
        outputHeader('安装完成');
        ?>
        <div class="layui-card">
            <div class="layui-card-header">安装完成</div>
            <div class="layui-card-body" style="text-align: center;">
                <i class="layui-icon layui-icon-ok" style="font-size: 100px; color: #5FB878;"></i>
                <h2 style="margin: 20px 0;">恭喜您，API商业系统安装成功！</h2>
                <p>为了系统安全，请删除install目录</p>
                <div style="margin-top: 30px;">
                    <a href="../admin/login.php" class="layui-btn">进入后台管理</a>
                    <a href="../index.php" class="layui-btn layui-btn-normal">进入前台首页</a>
                </div>
            </div>
        </div>
        <?php
        break;
}

outputFooter();
