<?php
/**
 * 管理员认证类
 */
class AdminAuth
{
    private $db;
    private $jwtSecret;
    
    public function __construct()
    {
        require_once __DIR__ . '/../classes/Database.php';
        $this->db = Database::getInstance();
        
        // 获取JWT密钥
        if (file_exists(__DIR__ . '/../config/app.php')) {
            $config = require __DIR__ . '/../config/app.php';
            $this->jwtSecret = $config['security']['jwt_secret'] ?? 'default_secret_key';
        } else {
            $this->jwtSecret = 'default_secret_key';
        }
    }
    
    /**
     * 生成JWT令牌
     */
    public function generateToken($payload)
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->jwtSecret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * 验证JWT令牌
     */
    public function verifyToken($token)
    {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        // 验证签名
        $validSignature = hash_hmac('sha256', $header . "." . $payload, $this->jwtSecret, true);
        $validBase64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));
        
        if ($signature !== $validBase64Signature) {
            return false;
        }
        
        // 解码payload
        $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $payload));
        $data = json_decode($payload, true);
        
        // 检查过期时间
        if (isset($data['exp']) && $data['exp'] < time()) {
            return false;
        }
        
        return $data;
    }
    
    /**
     * 验证密码
     */
    public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * 哈希密码
     */
    public function hashPassword($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
}
?>
