<?php
// 检查是否已登录
if (isset($_SESSION['user_id'])) {
    header('Location: /user/profile');
    exit;
}

// 处理注册请求
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // 验证输入
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
        $error = '所有字段都是必填的';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = '请输入有效的电子邮件地址';
    } elseif ($password !== $confirm_password) {
        $error = '两次输入的密码不一致';
    } elseif (strlen($password) < 6) {
        $error = '密码长度不能少于6个字符';
    } else {
        // 检查用户名和邮箱是否已存在
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM api_users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            $error = '用户名或邮箱已被注册';
        } else {
            // 创建新用户
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $api_key = bin2hex(random_bytes(32)); // 生成API密钥
            
            $stmt = $pdo->prepare("INSERT INTO api_users (username, email, password, api_key, role, status, created_at) VALUES (?, ?, ?, ?, 'user', 1, NOW())");
            $result = $stmt->execute([$username, $email, $hashed_password, $api_key]);
            
            if ($result) {
                $success = '注册成功！请登录您的账号';
                
                // 发送欢迎邮件
                if (!empty($siteConfig['enable_email_notification'])) {
                    $subject = '欢迎注册 ' . $siteConfig['site_name'];
                    $message = "亲爱的 {$username}，\n\n";
                    $message .= "感谢您注册 {$siteConfig['site_name']}！\n\n";
                    $message .= "您的账号信息：\n";
                    $message .= "用户名: {$username}\n";
                    $message .= "邮箱: {$email}\n";
                    $message .= "API密钥: {$api_key}\n\n";
                    $message .= "请妥善保管您的API密钥，不要泄露给他人。\n\n";
                    $message .= "祝您使用愉快！\n";
                    $message .= "{$siteConfig['site_name']} 团队";
                    
                    // 发送邮件
                    mail($email, $subject, $message, "From: {$siteConfig['contact_email']}");
                }
            } else {
                $error = '注册失败，请稍后再试';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - <?php echo $siteConfig['site_name']; ?></title>
    <link rel="stylesheet" href="/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="/public/css/font-awesome.min.css">
    <link rel="stylesheet" href="/public/css/style.css">
</head>
<body class="bg-light">
    <!-- 头部导航 -->
    <?php include '../templates/common/header.php'; ?>
    
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">用户注册</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                        <div class="alert alert-success">
                            <?php echo $success; ?>
                            <div class="mt-3">
                                <a href="/user/login" class="btn btn-primary">立即登录</a>
                            </div>
                        </div>
                        <?php else: ?>
                        <form method="post" action="">
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-user"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                                </div>
                                <small class="form-text text-muted">用户名只能包含字母、数字和下划线</small>
                            </div>
                            <div class="form-group">
                                <label for="email">电子邮箱</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-envelope"></i></span>
                                    </div>
                                    <input type="email" class="form-control" id="email" name="email" placeholder="请输入电子邮箱" required value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="password">密码</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-lock"></i></span>
                                    </div>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                                </div>
                                <small class="form-text text-muted">密码长度不能少于6个字符</small>
                            </div>
                            <div class="form-group">
                                <label for="confirm_password">确认密码</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-lock"></i></span>
                                    </div>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="请再次输入密码" required>
                                </div>
                            </div>
                            <div class="form-group form-check">
                                <input type="checkbox" class="form-check-input" id="agree" name="agree" required>
                                <label class="form-check-label" for="agree">我已阅读并同意 <a href="/page/terms" target="_blank">服务条款</a> 和 <a href="/page/privacy" target="_blank">隐私政策</a></label>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block">注册</button>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <p>已有账号? <a href="/user/login">立即登录</a></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <?php include '../templates/common/footer.php'; ?>
    
    <script src="/public/js/jquery-3.5.1.min.js"></script>
    <script src="/public/js/bootstrap.bundle.min.js"></script>
    <script src="/public/js/main.js"></script>
</body>
</html>