/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.6.2 (2020-12-08)
 */
!function(){"use strict";var e,t,n,r,d=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},o=tinymce.util.Tools.resolve("tinymce.PluginManager"),h=function(){return(h=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},i=function(e){return function(){return e}},a=i(!1),m=i(!0),u=i("[!-#%-*,-\\/:;?@\\[-\\]_{}\xa1\xab\xb7\xbb\xbf;\xb7\u055a-\u055f\u0589\u058a\u05be\u05c0\u05c3\u05c6\u05f3\u05f4\u0609\u060a\u060c\u060d\u061b\u061e\u061f\u066a-\u066d\u06d4\u0700-\u070d\u07f7-\u07f9\u0830-\u083e\u085e\u0964\u0965\u0970\u0df4\u0e4f\u0e5a\u0e5b\u0f04-\u0f12\u0f3a-\u0f3d\u0f85\u0fd0-\u0fd4\u0fd9\u0fda\u104a-\u104f\u10fb\u1361-\u1368\u1400\u166d\u166e\u169b\u169c\u16eb-\u16ed\u1735\u1736\u17d4-\u17d6\u17d8-\u17da\u1800-\u180a\u1944\u1945\u1a1e\u1a1f\u1aa0-\u1aa6\u1aa8-\u1aad\u1b5a-\u1b60\u1bfc-\u1bff\u1c3b-\u1c3f\u1c7e\u1c7f\u1cd3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205e\u207d\u207e\u208d\u208e\u3008\u3009\u2768-\u2775\u27c5\u27c6\u27e6-\u27ef\u2983-\u2998\u29d8-\u29db\u29fc\u29fd\u2cf9-\u2cfc\u2cfe\u2cff\u2d70\u2e00-\u2e2e\u2e30\u2e31\u3001-\u3003\u3008-\u3011\u3014-\u301f\u3030\u303d\u30a0\u30fb\ua4fe\ua4ff\ua60d-\ua60f\ua673\ua67e\ua6f2-\ua6f7\ua874-\ua877\ua8ce\ua8cf\ua8f8-\ua8fa\ua92e\ua92f\ua95f\ua9c1-\ua9cd\ua9de\ua9df\uaa5c-\uaa5f\uaade\uaadf\uabeb\ufd3e\ufd3f\ufe10-\ufe19\ufe30-\ufe52\ufe54-\ufe61\ufe63\ufe68\ufe6a\ufe6b\uff01-\uff03\uff05-\uff0a\uff0c-\uff0f\uff1a\uff1b\uff1f\uff20\uff3b-\uff3d\uff3f\uff5b\uff5d\uff5f-\uff65]"),c=function(){return l},l=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:a,isSome:a,isNone:m,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:i(null),getOrUndefined:i(undefined),or:n,orThunk:t,map:c,each:function(){},bind:c,exists:a,forall:m,filter:c,equals:e,equals_:e,toArray:function(){return[]},toString:i("none()")}),s=function(n){var e=i(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:m,isNone:a,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return s(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(a,function(e){return t(n,e)})}};return o},g={some:s,none:c,from:function(e){return null===e||e===undefined?l:s(e)}},p=u,v=tinymce.util.Tools.resolve("tinymce.util.Tools"),f=function(r){return function(e){return n=typeof(t=e),(null===t?"null":"object"==n&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":n)===r;var t,n}},y=function(t){return function(e){return typeof e===t}},x=f("string"),b=f("array"),w=y("boolean"),O=y("number"),C=Array.prototype.slice,T=Array.prototype.push,N=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o)}return r},E=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},k=function(e,t){for(var n=e.length-1;0<=n;n--){t(e[n],n)}},S=function(e,t){return function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!b(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);T.apply(t,e[n])}return t}(N(e,t))},A=Object.hasOwnProperty,D=function(e,t){return A.call(e,t)},M=("undefined"!=typeof window||Function("return this;")(),r=3,function(e){return e.dom.nodeType===r}),B=function(e,t,n){!function(e,t,n){if(!(x(n)||w(n)||O(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")}(e.dom,t,n)},F=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:e}},I={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return F(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return F(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return F(n)},fromDom:F,fromPoint:function(e,t,n){return g.from(e.dom.elementFromPoint(t,n)).map(F)}},P=function(e,t){return{element:e,offset:t}},R=function(e,t){var n=N(e.dom.childNodes,I.fromDom);return 0<n.length&&t<n.length?P(n[t],0):P(e,t)},W=function(t,n){var e;(e=t,g.from(e.dom.parentNode).map(I.fromDom)).each(function(e){e.dom.insertBefore(n.dom,t.dom)})},j=function(e,t){var n;W(e,t),n=e,t.dom.appendChild(n.dom)};var q,V,_,H=(q=M,V="text",{get:function(e){if(!q(e))throw new Error("Can only get "+V+" value of a "+V+" node");return _(e).getOr("")},getOption:_=function(e){return q(e)?g.from(e.dom.nodeValue):g.none()},set:function(e,t){if(!q(e))throw new Error("Can only set raw "+V+" value of a "+V+" node");e.dom.nodeValue=t}}),L=function(e){return H.get(e)},U=function(e,t){return n=t,i=(r=e)===undefined?document:r.dom,1!==(o=i).nodeType&&9!==o.nodeType&&11!==o.nodeType||0===o.childElementCount?[]:N(i.querySelectorAll(n),I.fromDom);var n,r,o,i},$=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),z=function(e,t){return e.isBlock(t)||D(e.schema.getShortEndedElements(),t.nodeName)},G=function(e,t){return"false"===e.getContentEditable(t)},K=function(e,t){return!e.isBlock(t)&&D(e.schema.getWhiteSpaceElements(),t.nodeName)},J=function(){return{sOffset:0,fOffset:0,elements:[]}},Q=function(e,t){return R(I.fromDom(e),t)},X=function(e,t,n,r,o,i){void 0===i&&(i=!0);for(var a=i?t(!1):n;a;){var u=G(e,a);if(u||K(e,a)){if(u?r.cef(a):r.boundary(a))break;a=t(!0)}else{if(z(e,a)){if(r.boundary(a))break}else 3===a.nodeType&&r.text(a);if(a===o)break;a=t(!1)}}},Y=function(e,t,n,r,o){var i,a,u,c,l,s,f;z(i=e,a=n)||G(i,a)||K(i,a)||(c=a,"true"===(u=i).getContentEditable(c)&&"false"===u.getContentEditableParent(c.parentNode))||(l=e.getParent(r,e.isBlock),s=new $(n,l),f=o?s.next:s.prev,X(e,f,n,{boundary:m,cef:m,text:function(e){o?t.fOffset+=e.length:t.sOffset+=e.length,t.elements.push(I.fromDom(e))}}))},Z=function(e,t,n,r,o,i){void 0===i&&(i=!0);var a=new $(n,t),u=[],c=J();Y(e,c,n,t,!1);var l=function(){return 0<c.elements.length&&(u.push(c),c=J()),!1};return X(e,a.next,n,{boundary:l,cef:function(e){return l(),o&&u.push.apply(u,o.cef(e)),!1},text:function(e){c.elements.push(I.fromDom(e)),o&&o.text(e,c)}},r,i),r&&Y(e,c,r,t,!0),l(),u},ee=function(i,e){var n=Q(e.startContainer,e.startOffset),r=n.element.dom,o=Q(e.endContainer,e.endOffset),a=o.element.dom;return Z(i,e.commonAncestorContainer,r,a,{text:function(e,t){e===a?t.fOffset+=e.length-o.offset:e===r&&(t.sOffset+=n.offset)},cef:function(e){var t,n,r,o=S(U(I.fromDom(e),"*[contenteditable=true]"),function(e){var t=e.dom;return Z(i,t,t)});return t=o,n=function(e,t){return n=e.elements[0].dom,r=t.elements[0].dom,o=n,i=r,a=Node.DOCUMENT_POSITION_PRECEDING,0!=(o.compareDocumentPosition(i)&a)?1:-1;var n,r,o,i,a},(r=C.call(t,0)).sort(n),r}},!1)},te=function(e,t){return t.collapsed?[]:ee(e,t)},ne=function(e,t){var n=e.createRng();return n.selectNode(t),te(e,n)},re=function(e,a){var t,n;return function(e,t){if(0===e.length)return[];for(var n=t(e[0]),r=[],o=[],i=0,a=e.length;i<a;i++){var u=e[i],c=t(u);c!==n&&(r.push(o),o=[]),n=c,o.push(u)}return 0!==o.length&&r.push(o),r}((t=function(e,n){var t=L(n),r=e.last,o=r+t.length,i=S(a,function(e,t){return e.start<o&&e.finish>r?[{element:n,start:Math.max(r,e.start)-r,finish:Math.min(o,e.finish)-r,matchId:t}]:[]});return{results:e.results.concat(i),last:o}},n={results:[],last:0},E(e,function(e){n=t(n,e)}),n.results),function(e){return e.matchId})},oe=function(o,e){return S(e,function(e){var t=e.elements,n=N(t,L).join(""),r=function(e,t,n,r){void 0===n&&(n=0),void 0===r&&(r=e.length);var o=t.regex;o.lastIndex=n;for(var i,a=[];i=o.exec(e);){var u=i[t.matchIndex],c=i.index+i[0].indexOf(u),l=c+u.length;if(r<l)break;a.push({start:c,finish:l}),o.lastIndex=l}return a}(n,o,e.sOffset,n.length-e.fOffset);return re(t,r)})},ie=function(e,i){k(e,function(e,o){k(e,function(e){var t=I.fromDom(i.cloneNode(!1));B(t,"data-mce-index",o);var n,r=e.element.dom;r.length===e.finish&&0===e.start?j(e.element,t):(r.length!==e.finish&&r.splitText(e.finish),n=r.splitText(e.start),j(I.fromDom(n),t))})})},ae=function(e,t,n,r){var o,i=n.getBookmark(),a=e.select("td[data-mce-selected],th[data-mce-selected]"),u=0<a.length?(o=e,S(a,function(e){return ne(o,e)})):te(e,n.getRng()),c=oe(t,u);return ie(c,r),n.moveToBookmark(i),c.length},ue=function(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t},ce=function(e,t,n,r){var o=e.dom.create("span",{"data-mce-bogus":1});o.className="mce-match-marker";var i,a,u,c,l,s=e.getBody();return ye(e,t,!1),r?ae(e.dom,n,e.selection,o):(i=e.dom,a=n,u=o,c=ne(i,s),l=oe(a,c),ie(l,u),l.length)},le=function(e){var t=e.parentNode;e.firstChild&&t.insertBefore(e.firstChild,e),e.parentNode.removeChild(e)},se=function(e,t){var n=[],r=v.toArray(e.getBody().getElementsByTagName("span"));if(r.length)for(var o=0;o<r.length;o++){var i=ue(r[o]);null!==i&&i.length&&i===t.toString()&&n.push(r[o])}return n},fe=function(e,t,n){var r=t.get(),o=r.index,i=e.dom;(n=!1!==n)?o+1===r.count?o=0:o++:o-1==-1?o=r.count-1:o--,i.removeClass(se(e,r.index),"mce-match-marker-selected");var a=se(e,o);return a.length?(i.addClass(se(e,o),"mce-match-marker-selected"),e.selection.scrollIntoView(a[0]),o):-1},de=function(e,t){var n=t.parentNode;e.remove(t),e.isEmpty(n)&&e.remove(n)},me=function(e,t,n,r,o,i){var a,u,c,l=(a=o,u="("+n.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&").replace(/\s/g,"[^\\S\\r\\n\\uFEFF]")+")",a?"(?:^|\\s|"+p()+")"+u+"(?=$|\\s|"+p()+")":u),s={regex:new RegExp(l,r?"g":"gi"),matchIndex:1},f=ce(e,t,s,i);return f&&(c=fe(e,t,!0),t.set({index:c,count:f,text:n,matchCase:r,wholeWord:o,inSelection:i})),f},he=function(e,t){var n=fe(e,t,!0);t.set(h(h({},t.get()),{index:n}))},ge=function(e,t){var n=fe(e,t,!1);t.set(h(h({},t.get()),{index:n}))},pe=function(e){var t=ue(e);return null!==t&&0<t.length},ve=function(e,t,n,r,o){var i,a=t.get(),u=a.index,c=u;r=!1!==r;for(var l=e.getBody(),s=v.grep(v.toArray(l.getElementsByTagName("span")),pe),f=0;f<s.length;f++){var d=ue(s[f]),m=i=parseInt(d,10);if(o||m===a.index){for(n.length?(s[f].firstChild.nodeValue=n,le(s[f])):de(e.dom,s[f]);s[++f];){if((m=parseInt(ue(s[f]),10))!==i){f--;break}de(e.dom,s[f])}r&&c--}else u<i&&s[f].setAttribute("data-mce-index",String(i-1))}return t.set(h(h({},a),{count:o?0:a.count-1,index:c})),(r?he:ge)(e,t),!o&&0<t.get().count},ye=function(e,t,n){for(var r,o,i=t.get(),a=v.toArray(e.getBody().getElementsByTagName("span")),u=0;u<a.length;u++){var c=ue(a[u]);null!==c&&c.length&&(c===i.index.toString()&&(r=r||a[u].firstChild,o=a[u].firstChild),le(a[u]))}if(t.set(h(h({},i),{index:-1,count:0,text:""})),r&&o){var l=e.dom.createRng();return l.setStart(r,0),l.setEnd(o,o.data.length),!1!==n&&e.selection.setRng(l),l}},xe=tinymce.util.Tools.resolve("tinymce.Env"),be=function(i,a){var t,e=(t=d(g.none()),{clear:function(){return t.set(g.none())},set:function(e){return t.set(g.some(e))},isSet:function(){return t.get().isSome()},on:function(e){return t.get().each(e)}});i.undoManager.add();var n=v.trim(i.selection.getContent({format:"text"}));function u(e){(1<a.get().count?e.enable:e.disable)("next"),(1<a.get().count?e.enable:e.disable)("prev")}var c=function(e,t){var n=t?e.disable:e.enable;E(["replace","replaceall","prev","next"],n)};var l=function(e,t){xe.browser.isSafari()&&xe.deviceType.isTouch()&&("find"===t||"replace"===t||"replaceall"===t)&&e.focus(t)},s=function(e){ye(i,a,!1),c(e,!0),u(e)},f=function(e){var t,n,r=e.getData(),o=a.get();r.findtext.length?(o.text===r.findtext&&o.matchCase===r.matchcase&&o.wholeWord===r.wholewords?he(i,a):((t=me(i,a,r.findtext,r.matchcase,r.wholewords,r.inselection))<=0&&(n=e,i.windowManager.alert("Could not find the specified string.",function(){n.focus("findtext")})),c(e,0===t)),u(e)):s(e)},r=a.get(),o={title:"Find and Replace",size:"normal",body:{type:"panel",items:[{type:"bar",items:[{type:"input",name:"findtext",placeholder:"Find",maximized:!0,inputMode:"search"},{type:"button",name:"prev",text:"Previous",icon:"action-prev",disabled:!0,borderless:!0},{type:"button",name:"next",text:"Next",icon:"action-next",disabled:!0,borderless:!0}]},{type:"input",name:"replacetext",placeholder:"Replace with",inputMode:"search"}]},buttons:[{type:"menu",name:"options",icon:"preferences",tooltip:"Preferences",align:"start",items:[{type:"togglemenuitem",name:"matchcase",text:"Match case"},{type:"togglemenuitem",name:"wholewords",text:"Find whole words only"},{type:"togglemenuitem",name:"inselection",text:"Find in selection"}]},{type:"custom",name:"find",text:"Find",primary:!0},{type:"custom",name:"replace",text:"Replace",disabled:!0},{type:"custom",name:"replaceall",text:"Replace All",disabled:!0}],initialData:{findtext:n,replacetext:"",wholewords:r.wholeWord,matchcase:r.matchCase,inselection:r.inSelection},onChange:function(e,t){"findtext"===t.name&&0<a.get().count&&s(e)},onAction:function(e,t){var n,r,o=e.getData();switch(t.name){case"find":f(e);break;case"replace":(ve(i,a,o.replacetext)?u:s)(e);break;case"replaceall":ve(i,a,o.replacetext,!0,!0),s(e);break;case"prev":ge(i,a),u(e);break;case"next":he(i,a),u(e);break;case"matchcase":case"wholewords":case"inselection":n=e.getData(),r=a.get(),a.set(h(h({},r),{matchCase:n.matchcase,wholeWord:n.wholewords,inSelection:n.inselection})),s(e)}l(e,t.name)},onSubmit:function(e){f(e),l(e,"find")},onClose:function(){i.focus(),ye(i,a),i.undoManager.add()}};e.set(i.windowManager.open(o,{inline:"toolbar"}))},we=function(e,t){return function(){be(e,t)}};o.add("searchreplace",function(e){var t,n,r,o,i,a,u=d({index:-1,count:0,text:"",matchCase:!1,wholeWord:!1,inSelection:!1});return n=u,(t=e).addCommand("SearchReplace",function(){be(t,n)}),o=u,(r=e).ui.registry.addMenuItem("searchreplace",{text:"Find and replace...",shortcut:"Meta+F",onAction:we(r,o),icon:"search"}),r.ui.registry.addButton("searchreplace",{tooltip:"Find and replace",onAction:we(r,o),icon:"search"}),r.shortcuts.add("Meta+F","",we(r,o)),i=e,a=u,{done:function(e){return ye(i,a,e)},find:function(e,t,n,r){return void 0===r&&(r=!1),me(i,a,e,t,n,r)},next:function(){return he(i,a)},prev:function(){return ge(i,a)},replace:function(e,t,n){return ve(i,a,e,t,n)}}})}();