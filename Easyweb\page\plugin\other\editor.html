<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>富文本编辑器</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=318"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        body .tox-tinymce-aux {
            z-index: 19892000;
        }
    </style>
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-btn-container">
                <button id="btnDemoEdtGetContent" class="layui-btn layui-btn-sm">获取内容</button>
                <button id="btnDemoEdtGetText" class="layui-btn layui-btn-sm">获取纯文本</button>
                <button id="btnDemoEdtSetContent" class="layui-btn layui-btn-sm">修改内容</button>
                <button id="btnDemoEdtInsertContent" class="layui-btn layui-btn-sm">插入内容</button>
                <button id="btnDemoEdtShowLayer" class="layui-btn layui-btn-sm">弹窗中使用</button>
            </div>
            <textarea id="demoEditor"></textarea>
        </div>
    </div>
</div>
<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script type="text/javascript" src="../../../assets/libs/tinymce/tinymce.min.js"></script>
<script>
    layui.use(['layer', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var admin = layui.admin;
        var content = [
            '<div style="text-align: center;color: #fff;background-image: linear-gradient(to right,#009688,#5fb878);padding: 20px;">',
            '   <div style="font-size: 28px;margin-bottom: 10px;">EASY WEB 后台管理模板</div>',
            '   <div style="font-size: 18px">基于Layui的一套通用型后台管理模板，拥有众多原创组件及模板页面</div>',
            '</div><br/>'
        ].join('');

        // 渲染富文本编辑器
        tinymce.init({
            selector: '#demoEditor',
            height: 525,
            branding: false,
            language: 'zh_CN',
            plugins: 'code print preview fullscreen paste searchreplace save autosave link autolink image imagetools media table codesample lists advlist hr charmap emoticons anchor directionality pagebreak quickbars nonbreaking visualblocks visualchars wordcount',
            toolbar: 'fullscreen preview code | undo redo | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | formatselect fontselect fontsizeselect | link image media emoticons charmap anchor pagebreak codesample | ltr rtl',
            toolbar_drawer: 'sliding',
            images_upload_url: '../../../json/tinymce-upload-ok.json',
            file_picker_types: 'media',
            file_picker_callback: function (callback, value, meta) {
                layer.msg('演示环境不允许上传', {anim: 6});
            },
            init_instance_callback: function (editor) {
                console.log(editor);
            }
        });

        // 获取内容
        $('#btnDemoEdtGetContent').click(function () {
            var content = tinymce.get('demoEditor').getContent();
            layer.alert(content, {skin: 'layui-layer-admin', shade: .1});
        });

        // 获取文本
        $('#btnDemoEdtGetText').click(function () {
            var content = tinymce.get('demoEditor').getContent({format: 'text'});
            layer.alert(content, {skin: 'layui-layer-admin', shade: .1});
        });

        // 修改文本
        $('#btnDemoEdtSetContent').click(function () {
            tinymce.get('demoEditor').setContent(content);
        });

        // 插入文本
        $('#btnDemoEdtInsertContent').click(function () {
            tinymce.get('demoEditor').insertContent('👍赞~', {});
        });

        // 弹窗中使用
        $('#btnDemoEdtShowLayer').click(function () {
            admin.open({
                title: '富文本编辑器',
                type: 1,
                area: '820px',
                offset: '45px',
                content: '<textarea id="demoEditor2"></textarea>',
                success: function () {
                    tinymce.init({
                        selector: '#demoEditor2',
                        height: 480,
                        branding: false,
                        language: 'zh_CN',
                        plugins: 'code print preview fullscreen paste searchreplace save autosave link autolink image imagetools media table codesample lists advlist hr charmap emoticons anchor directionality pagebreak quickbars nonbreaking visualblocks visualchars wordcount',
                        toolbar: 'fullscreen preview code | undo redo | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | formatselect fontselect fontsizeselect | link image media emoticons charmap anchor pagebreak codesample | ltr rtl',
                        toolbar_drawer: 'sliding',
                        images_upload_url: '../../../json/tinymce-upload-ok.json',
                        file_picker_types: 'media',
                        file_picker_callback: function (callback, value, meta) {
                            layer.msg('演示环境不允许上传', {anim: 6});
                        },
                        init_instance_callback: function (editor) {
                            tinymce.get('demoEditor2').setContent(content);
                        }
                    });
                },
                end: function () {
                    tinymce.get('demoEditor2').destroy(false);
                }
            });
        });

    });
</script>
</body>
</html>