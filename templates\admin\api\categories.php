<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API分类管理 - 管理后台</title>
    <link rel="stylesheet" href="/Easyweb/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/Easyweb/assets/module/admin.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <style>
        .layui-card-body {
            padding: 15px;
        }
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-table-tool-temp {
            padding-right: 0;
        }
        .layui-table-cell {
            height: auto;
            line-height: 28px;
            padding: 6px 15px;
            position: relative;
            box-sizing: border-box;
        }
        .category-icon {
            font-size: 18px;
            margin-right: 5px;
        }
        .category-status {
            cursor: pointer;
        }
        .category-sort {
            width: 60px;
            height: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">API分类管理</div>
        <div class="layui-card-body">
            <div class="layui-form toolbar">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <input type="text" name="keyword" placeholder="分类名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn icon-btn" lay-filter="searchBtn" lay-submit>
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button class="layui-btn icon-btn" id="addBtn">
                            <i class="layui-icon layui-icon-add-1"></i>添加
                        </button>
                    </div>
                </div>
            </div>
            
            <table id="categoryTable" lay-filter="categoryTable"></table>
        </div>
    </div>
</div>

<!-- 表格操作列 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 表格状态列 -->
<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="statusSwitch" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" {{d.status==1?'checked':''}} />
</script>

<!-- 表格图标列 -->
<script type="text/html" id="iconTpl">
    <i class="layui-icon {{d.icon}} category-icon"></i> {{d.icon}}
</script>

<!-- 添加/编辑分类弹窗 -->
<script type="text/html" id="categoryForm">
    <form class="layui-form" lay-filter="categoryForm" style="padding: 20px 30px 0 0;">
        <input type="hidden" name="id">
        <div class="layui-form-item">
            <label class="layui-form-label">分类名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" placeholder="请输入分类名称" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">分类图标</label>
            <div class="layui-input-block">
                <input type="text" name="icon" id="iconPicker" placeholder="请选择图标" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort" placeholder="数字越小越靠前" value="100" lay-verify="required|number" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入分类描述" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item text-right">
            <button class="layui-btn" lay-filter="categorySubmit" lay-submit>保存</button>
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
        </div>
    </form>
</script>

<!-- js部分 -->
<script src="/Easyweb/assets/libs/layui/layui.js"></script>
<script src="/Easyweb/assets/js/common.js"></script>
<script>
layui.use(['layer', 'form', 'table', 'util', 'admin', 'iconPicker'], function() {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var iconPicker = layui.iconPicker;
    
    // 渲染表格
    var insTb = table.render({
        elem: '#categoryTable',
        url: '/admin/api/categories/list',
        page: true,
        toolbar: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'name', title: '分类名称', sort: true},
            {field: 'icon', title: '图标', templet: '#iconTpl'},
            {field: 'api_count', title: '接口数量', sort: true},
            {field: 'sort', title: '排序', sort: true, width: 100, templet: function(d) {
                return '<input type="number" class="category-sort" data-id="' + d.id + '" value="' + d.sort + '">';
            }},
            {field: 'description', title: '描述'},
            {field: 'status', title: '状态', templet: '#statusTpl', width: 100},
            {field: 'created_at', title: '创建时间', sort: true, templet: function(d) {
                return util.toDateString(d.created_at * 1000);
            }},
            {title: '操作', toolbar: '#tableBar', width: 120, align: 'center'}
        ]]
    });
    
    // 搜索按钮点击事件
    form.on('submit(searchBtn)', function(data) {
        insTb.reload({where: data.field, page: {curr: 1}});
        return false;
    });
    
    // 添加按钮点击事件
    $('#addBtn').click(function() {
        showEditForm();
    });
    
    // 工具条点击事件
    table.on('tool(categoryTable)', function(obj) {
        var data = obj.data;
        var layEvent = obj.event;
        
        if (layEvent === 'edit') { // 修改
            showEditForm(data);
        } else if (layEvent === 'del') { // 删除
            layer.confirm('确定要删除该分类吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function(index) {
                layer.close(index);
                var loadIndex = layer.load(2);
                
                $.post('/admin/api/categories/delete', {
                    id: data.id
                }, function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg(res.msg, {icon: 1});
                        insTb.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
            });
        }
    });
    
    // 修改状态
    form.on('switch(statusSwitch)', function(obj) {
        var loadIndex = layer.load(2);
        
        $.post('/admin/api/categories/status', {
            id: obj.value,
            status: obj.elem.checked ? 1 : 0
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                $(obj.elem).prop('checked', !obj.elem.checked);
                form.render('checkbox');
            }
        }, 'json');
    });
    
    // 监听排序修改
    $(document).on('change', '.category-sort', function() {
        var id = $(this).data('id');
        var sort = $(this).val();
        
        var loadIndex = layer.load(2);
        $.post('/admin/api/categories/sort', {
            id: id,
            sort: sort
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 0) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
                insTb.reload();
            }
        }, 'json');
    });
    
    // 显示表单弹窗
    function showEditForm(data) {
        admin.open({
            type: 1,
            title: (data ? '修改' : '添加') + '分类',
            content: $('#categoryForm').html(),
            area: ['500px', '480px'],
            success: function(layero, dIndex) {
                // 回显表单数据
                if (data) {
                    form.val('categoryForm', data);
                }
                
                // 表单提交事件
                form.on('submit(categorySubmit)', function(data) {
                    var loadIndex = layer.load(2);
                    
                    $.post('/admin/api/categories/' + (data.field.id ? 'update' : 'add'), data.field, function(res) {
                        layer.close(loadIndex);
                        if (res.code === 0) {
                            layer.close(dIndex);
                            layer.msg(res.msg, {icon: 1});
                            insTb.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }, 'json');
                    
                    return false;
                });
                
                // 渲染图标选择器
                iconPicker.render({
                    elem: '#iconPicker',
                    type: 'fontClass',
                    search: true,
                    page: true,
                    limit: 12,
                    click: function(data) {
                        $('#iconPicker').val('layui-icon ' + data.icon);
                    }
                });
                
                // 禁止弹窗出现滚动条
                $(layero).children('.layui-layer-content').css('overflow', 'visible');
            }
        });
    }
});
</script>
</body>
</html>