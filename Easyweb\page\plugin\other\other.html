<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>鼠标滚轮监听</title>
    <link rel="stylesheet" href="../../../assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="../../../assets/module/admin.css?v=32"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<!-- 正文开始 -->
<div class="layui-fluid" id="LAY_page_other">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">省市区选择city-picker</div>
                <div class="layui-card-body"
                     style="padding: 20px 20px 30px 20px;height: 119px;box-sizing: border-box;">
                    <p class="layui-text" style="margin-bottom: 10px;">
                        此插件来自于GitHub，<a href="https://github.com/tshi0912/city-picker" target="_blank">前往查看</a>。
                    </p>
                    <div style="position: relative;max-width: 400px;">
                        <input id="otherCitySel" class="layui-input" placeholder="请选择城市" data-toggle="city-picker"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">多选下拉xmSelect</div>
                <div class="layui-card-body"
                     style="padding: 20px 20px 30px 20px;height: 119px;box-sizing: border-box;">
                    <p class="layui-text" style="margin-bottom: 10px;">
                        此插件来自于社区，<a href="https://fly.layui.com/extend/xmSelect" target="_blank">前往查看</a>。
                    </p>
                    <div id="otherXmSel" style="max-width: 400px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header" data-step="1" data-intro="你好! 这是引导插件intro.js" data-position="bottom">
                    引导插件intro.js
                </div>
                <div class="layui-card-body text-center"
                     style="padding: 26px 0;height: 126px;box-sizing: border-box;">
                    <p class="layui-text" style="font-size: 16px;margin-bottom: 14px;"
                       data-step="2" data-intro="它是一个jquery插件，在EasyWeb中已经将它封装成Layui扩展模块了，可以很方便的使用它。">
                        Step-by-step guide and feature introduction</p>
                    <button id="otherIntroBtn1" class="layui-btn">开始指引</button>
                    <button id="otherIntroBtn2" class="layui-btn">开启进度条</button>
                </div>
            </div>
        </div>
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">二维码
                    <button id="otherQRCodeBtn" class="layui-btn layui-btn-xs"
                            style="margin-top: 10px;float: right;">更换内容
                    </button>
                </div>
                <div class="layui-card-body text-center" style="height: 126px;box-sizing: border-box;">
                    <div id="otherQRCode" class="layui-inline"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">视频播放器</div>
                <div class="layui-card-body" style="padding: 5px;min-height: 354px;box-sizing: border-box;">
                    <div id="otherVideo1"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm6">
            <div class="layui-card">
                <div class="layui-card-header">视频播放器开启弹幕</div>
                <div class="layui-card-body" style="padding: 5px;min-height: 354px;box-sizing: border-box;">
                    <div id="otherVideo2"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- js部分 -->
<script type="text/javascript" src="../../../assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="../../../assets/js/common.js?v=318"></script>
<script>
    layui.use(['citypicker', 'xmSelect', 'introJs', 'QRCode', 'Player'], function () {
        var $ = layui.jquery;
        var citypicker = layui.citypicker;
        var xmSelect = layui.xmSelect;
        var introJs = layui.introJs;
        var QRCode = layui.QRCode;
        var Player = layui.Player;

        // 渲染多选下拉
        xmSelect.render({
            el: '#otherXmSel',
            toolbar: {show: true},
            data: [
                {name: '张三', value: 1},
                {name: '李四', value: 2},
                {name: '王五', value: 3},
            ]
        });

        // 开始指引
        $('#otherIntroBtn1').click(function () {
            introJs().start();
        });

        // 带进度条
        $('#otherIntroBtn2').click(function () {
            introJs().setOption('showProgress', true).start();
        });

        // 二维码
        var qcIns = new QRCode(document.getElementById("otherQRCode"), {
            text: 'http://easyweb.vip',
            width: 106,
            height: 106,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
        });

        // 更换二维码内容
        $('#otherQRCodeBtn').click(function () {
            qcIns.makeCode("http://easyweb.vip?v=" + Math.floor(Math.random() * 10));
        });

        // 视频播放器
        new Player({
            id: 'otherVideo1',
            url: '//s1.pstatp.com/cdn/expire-1-M/byted-player-videos/1.0.0/xgplayer-demo.mp4',  // 视频地址
            poster: 'https://imgcache.qq.com/open_proj/proj_qcloud_v2/gateway/solution/general-video/css/img/scene/1.png',  // 封面
            fluid: true,  // 宽度100%
            playbackRate: [0.5, 1, 1.5, 2],  // 开启倍速播放
            pip: true,  // 开启画中画
            lang: 'zh-cn'
        });

        // 开启弹幕
        var dmStyle = {
            color: '#ffcd08', fontSize: '20px'
        };
        new Player({
            id: 'otherVideo2',
            url: 'http://demo.htmleaf.com/1704/************/video/2.mp4',  // 视频地址
            autoplay: false,
            fluid: true,  // 宽度100%
            lang: 'zh-cn',
            danmu: {
                comments: [
                    {id: '1', start: 0, txt: '空降', color: true, style: dmStyle, duration: 15000},
                    {id: '2', start: 1500, txt: '前方高能', color: true, style: dmStyle, duration: 15000},
                    {id: '3', start: 3500, txt: '弹幕护体', color: true, style: dmStyle, duration: 15000},
                    {id: '4', start: 4500, txt: '弹幕护体', color: true, style: dmStyle, duration: 15000},
                    {id: '5', start: 6000, txt: '前方高能', color: true, style: dmStyle, duration: 15000},
                    {id: '6', start: 8500, txt: '弹幕护体', color: true, style: dmStyle, duration: 15000},
                    {id: '7', start: 10000, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '8', start: 12500, txt: '前方高能', color: true, style: dmStyle, duration: 15000},
                    {id: '9', start: 15500, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '10', start: 16500, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '11', start: 18000, txt: '关弹幕，保智商', color: true, style: dmStyle, duration: 15000},
                    {id: '12', start: 20500, txt: '关弹幕，保智商', color: true, style: dmStyle, duration: 15000},
                    {id: '13', start: 22000, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '14', start: 25500, txt: '666666666', color: true, style: dmStyle, duration: 15000},
                    {id: '15', start: 26000, txt: '前方高能', color: true, style: dmStyle, duration: 15000}
                ]
            }
        });

    });
</script>
</body>
</html>