<?php
/**
 * API管理系统安装向导
 * 使用Easyweb组件开发的现代化安装界面
 */

// 处理安装请求
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $dbHost = $_POST["db_host"] ?? "localhost";
    $dbPort = $_POST["db_port"] ?? "3306";
    $dbName = $_POST["db_name"] ?? "api_system";
    $dbUser = $_POST["db_user"] ?? "root";
    $dbPass = $_POST["db_pass"] ?? "";
    $adminUser = $_POST["admin_user"] ?? "admin";
    $adminPass = $_POST["admin_pass"] ?? "123456";
    $adminEmail = $_POST["admin_email"] ?? "<EMAIL>";
    $siteName = $_POST["site_name"] ?? "API管理系统";
    $siteUrl = $_POST["site_url"] ?? "http://localhost";
    
    try {
        // 测试数据库连接
        $dsn = "mysql:host=$dbHost;port=$dbPort;charset=utf8mb4";
        $pdo = new PDO($dsn, $dbUser, $dbPass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 创建数据库
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbName`");
        
        // 执行数据库结构文件
        $sqlFile = __DIR__ . "/../config/database.sql";
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            // 分割SQL语句并执行
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
        }
        
        // 创建数据库配置文件
        $configContent = "<?php
return [
    'host' => '$dbHost',
    'port' => '$dbPort',
    'database' => '$dbName',
    'username' => '$dbUser',
    'password' => '$dbPass',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];";
        
        file_put_contents(__DIR__ . "/../config/database.php", $configContent);
        
        // 创建系统配置文件
        $systemConfigContent = "<?php
return [
    'app' => [
        'name' => '$siteName',
        'url' => '$siteUrl',
        'version' => '1.0.0',
        'debug' => false,
        'timezone' => 'Asia/Shanghai',
    ],
    'security' => [
        'jwt_secret' => '" . bin2hex(random_bytes(32)) . "',
        'password_salt' => '" . bin2hex(random_bytes(16)) . "',
        'csrf_token' => '" . bin2hex(random_bytes(32)) . "',
    ],
    'cache' => [
        'driver' => 'file',
        'path' => __DIR__ . '/../cache/',
        'expire' => 3600,
    ],
    'upload' => [
        'path' => __DIR__ . '/../uploads/',
        'max_size' => 10485760,
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    ]
];";
        
        file_put_contents(__DIR__ . "/../config/config.php", $systemConfigContent);
        
        // 创建默认管理员账号
        $hashedPassword = password_hash($adminPass, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO api_admins (username, password, email, role_id, status, created_at) VALUES (?, ?, ?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE password = VALUES(password), email = VALUES(email)");
        $stmt->execute([$adminUser, $hashedPassword, $adminEmail, 1, 1]);
        
        // 插入系统基础配置
        $configs = [
            ['key' => 'site_name', 'value' => $siteName, 'group' => 'website'],
            ['key' => 'site_url', 'value' => $siteUrl, 'group' => 'website'],
            ['key' => 'site_description', 'value' => '专业的API接口管理平台', 'group' => 'website'],
            ['key' => 'site_keywords', 'value' => 'API,接口,管理,平台', 'group' => 'website'],
            ['key' => 'admin_email', 'value' => $adminEmail, 'group' => 'system'],
            ['key' => 'system_timezone', 'value' => 'Asia/Shanghai', 'group' => 'system'],
            ['key' => 'default_language', 'value' => 'zh-CN', 'group' => 'system'],
        ];
        
        $configStmt = $pdo->prepare("INSERT INTO api_configs (`key`, `value`, `group`, created_at) VALUES (?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)");
        foreach ($configs as $config) {
            $configStmt->execute([$config['key'], $config['value'], $config['group']]);
        }
        
        // 创建安装锁定文件
        $lockContent = [
            'installed_at' => date('Y-m-d H:i:s'),
            'version' => '1.0.0',
            'admin_user' => $adminUser,
            'database' => $dbName,
            'site_name' => $siteName
        ];
        file_put_contents(__DIR__ . "/../config/install.lock", json_encode($lockContent, JSON_PRETTY_PRINT));
        
        $success = true;
        $message = "系统安装成功！";
        
    } catch (Exception $e) {
        $success = false;
        $message = "安装失败：" . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API管理系统 - 安装向导</title>
    <link rel="stylesheet" href="../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../Easyweb/assets/module/admin.css">
    <link rel="icon" href="../Easyweb/assets/images/favicon.ico">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .install-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 800px;
            max-width: 95vw;
            position: relative;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .install-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }
        
        .install-header-content {
            position: relative;
            z-index: 1;
        }
        
        .install-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .install-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .install-body {
            padding: 40px;
        }
        
        .install-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            margin: 0 20px;
            color: #999;
        }
        
        .step-item.active {
            color: #667eea;
        }
        
        .step-item.completed {
            color: #52c41a;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .step-item.active .step-number {
            background: #667eea;
            color: white;
        }
        
        .step-item.completed .step-number {
            background: #52c41a;
            color: white;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
            font-size: 1.3rem;
        }
        
        .form-section .layui-icon {
            margin-right: 8px;
            color: #667eea;
        }
        
        .success-container {
            text-align: center;
            padding: 40px;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #52c41a;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
        }
        
        .success-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 10px;
        }
        
        .success-desc {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .success-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .install-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .install-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .install-btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .install-btn-secondary:hover {
            background: #667eea;
            color: white;
        }
        
        .requirement-check {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .requirement-item:last-child {
            margin-bottom: 0;
        }
        
        .requirement-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .requirement-status.ok {
            background: #52c41a;
        }
        
        .requirement-status.error {
            background: #f5222d;
        }
        
        .requirement-status.warning {
            background: #fa8c16;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s;
        }
        
        @media (max-width: 768px) {
            .install-container {
                width: 95vw;
                margin: 20px;
            }
            
            .install-header {
                padding: 30px 20px;
            }
            
            .install-header h1 {
                font-size: 2rem;
            }
            
            .install-body {
                padding: 30px 20px;
            }
            
            .install-steps {
                flex-direction: column;
                align-items: center;
            }
            
            .step-item {
                margin: 10px 0;
            }
            
            .success-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="install-container">
        <!-- 安装头部 -->
        <div class="install-header">
            <div class="install-header-content">
                <h1>
                    <i class="layui-icon layui-icon-set-sm"></i>
                    API管理系统
                </h1>
                <p>欢迎使用API管理系统安装向导</p>
            </div>
        </div>
        
        <div class="install-body">
            <?php if (isset($success) && $success): ?>
                <!-- 安装成功页面 -->
                <div class="success-container">
                    <div class="success-icon">
                        <i class="layui-icon layui-icon-ok"></i>
                    </div>
                    <div class="success-title">安装成功！</div>
                    <div class="success-desc">
                        恭喜您！API管理系统已经成功安装完成。<br>
                        您现在可以开始使用系统的所有功能了。
                    </div>
                    <div class="success-actions">
                        <a href="index.php" class="install-btn">
                            <i class="layui-icon layui-icon-home"></i>
                            访问首页
                        </a>
                        <a href="admin/" class="install-btn install-btn-secondary">
                            <i class="layui-icon layui-icon-set"></i>
                            管理后台
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- 安装表单 -->
                
                <!-- 进度指示器 -->
                <div class="install-steps">
                    <div class="step-item active">
                        <div class="step-number">1</div>
                        <span>环境检测</span>
                    </div>
                    <div class="step-item active">
                        <div class="step-number">2</div>
                        <span>系统配置</span>
                    </div>
                    <div class="step-item">
                        <div class="step-number">3</div>
                        <span>安装完成</span>
                    </div>
                </div>
                
                <!-- 环境检测 -->
                <div class="requirement-check">
                    <h3 style="margin-bottom: 15px;">
                        <i class="layui-icon layui-icon-survey"></i>
                        环境检测
                    </h3>
                    <div class="requirement-item">
                        <div class="requirement-status <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'ok' : 'error'; ?>">
                            <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? '✓' : '✗'; ?>
                        </div>
                        <span>PHP版本 >= 7.4.0 (当前: <?php echo PHP_VERSION; ?>)</span>
                    </div>
                    <div class="requirement-item">
                        <div class="requirement-status <?php echo extension_loaded('pdo') ? 'ok' : 'error'; ?>">
                            <?php echo extension_loaded('pdo') ? '✓' : '✗'; ?>
                        </div>
                        <span>PDO扩展</span>
                    </div>
                    <div class="requirement-item">
                        <div class="requirement-status <?php echo extension_loaded('pdo_mysql') ? 'ok' : 'error'; ?>">
                            <?php echo extension_loaded('pdo_mysql') ? '✓' : '✗'; ?>
                        </div>
                        <span>PDO MySQL扩展</span>
                    </div>
                    <div class="requirement-item">
                        <div class="requirement-status <?php echo extension_loaded('json') ? 'ok' : 'error'; ?>">
                            <?php echo extension_loaded('json') ? '✓' : '✗'; ?>
                        </div>
                        <span>JSON扩展</span>
                    </div>
                    <div class="requirement-item">
                        <div class="requirement-status <?php echo is_writable(__DIR__ . '/../config/') ? 'ok' : 'error'; ?>">
                            <?php echo is_writable(__DIR__ . '/../config/') ? '✓' : '✗'; ?>
                        </div>
                        <span>config目录可写</span>
                    </div>
                </div>
                
                <?php if (isset($success) && !$success): ?>
                    <div class="layui-elem-quote layui-quote-nm" style="border-left: 5px solid #f5222d; background: #fff2f0; margin-bottom: 20px;">
                        <i class="layui-icon layui-icon-close" style="color: #f5222d;"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <!-- 配置表单 -->
                <form class="layui-form" method="POST" lay-filter="installForm">
                    <!-- 数据库配置 -->
                    <div class="form-section">
                        <h3>
                            <i class="layui-icon layui-icon-template-1"></i>
                            数据库配置
                        </h3>
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md8">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">数据库主机</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="db_host" value="localhost" placeholder="请输入数据库主机地址" class="layui-input" required lay-verify="required">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">端口</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="db_port" value="3306" placeholder="端口" class="layui-input" required lay-verify="required|number">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">数据库名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="db_name" value="api_system" placeholder="请输入数据库名称" class="layui-input" required lay-verify="required">
                            </div>
                        </div>
                        
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">用户名</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="db_user" value="root" placeholder="数据库用户名" class="layui-input" required lay-verify="required">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">密码</label>
                                    <div class="layui-input-block">
                                        <input type="password" name="db_pass" placeholder="数据库密码" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 网站配置 -->
                    <div class="form-section">
                        <h3>
                            <i class="layui-icon layui-icon-website"></i>
                            网站配置
                        </h3>
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="site_name" value="API管理系统" placeholder="请输入网站名称" class="layui-input" required lay-verify="required">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">网站地址</label>
                            <div class="layui-input-block">
                                <input type="url" name="site_url" value="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>" placeholder="请输入网站完整地址" class="layui-input" required lay-verify="required|url">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 管理员配置 -->
                    <div class="form-section">
                        <h3>
                            <i class="layui-icon layui-icon-username"></i>
                            管理员账号
                        </h3>
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">用户名</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="admin_user" value="admin" placeholder="管理员用户名" class="layui-input" required lay-verify="required">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">密码</label>
                                    <div class="layui-input-block">
                                        <input type="password" name="admin_pass" value="admin123" placeholder="管理员密码" class="layui-input" required lay-verify="required">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">邮箱地址</label>
                            <div class="layui-input-block">
                                <input type="email" name="admin_email" value="<EMAIL>" placeholder="管理员邮箱" class="layui-input" required lay-verify="required|email">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 安装按钮 -->
                    <div class="layui-form-item" style="text-align: center; margin-top: 40px;">
                        <button type="submit" class="install-btn" lay-submit lay-filter="install">
                            <i class="layui-icon layui-icon-ok"></i>
                            开始安装
                        </button>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <script src="../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            // 表单提交
            form.on('submit(install)', function(data){
                var loadIndex = layer.load(2, {
                    content: '正在安装系统，请稍候...',
                    success: function(layero){
                        layero.find('.layui-layer-content').css({
                            'padding-top': '40px',
                            'width': '200px'
                        });
                    }
                });
                
                return true;
            });
        });
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            var container = document.querySelector('.install-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(50px)';
            
            setTimeout(function() {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>