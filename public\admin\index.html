<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>API管理系统 - 后台登录</title>
    <link rel="stylesheet" href="../../Easyweb/assets/libs/layui/css/layui.css">
    <link rel="stylesheet" href="../../Easyweb/assets/module/admin.css">
    <link rel="stylesheet" href="../../Easyweb/assets/css/login.css">
    <link rel="icon" href="../../Easyweb/assets/images/favicon.ico">
</head>
<body class="login-bg">
    <!-- 登录表单 -->
    <form class="layui-form login-form">
        <div class="login-form-header">
            <h1>API管理系统</h1>
            <p>企业级API接口管理平台</p>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-icon layui-icon-username login-icon"></label>
            <input type="text" name="username" lay-verify="required" placeholder="用户名" class="layui-input">
        </div>
        
        <div class="layui-form-item">
            <label class="layui-icon layui-icon-password login-icon"></label>
            <input type="password" name="password" lay-verify="required" placeholder="密码" class="layui-input">
        </div>
        
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-xs7">
                    <label class="layui-icon layui-icon-vercode login-icon"></label>
                    <input type="text" name="captcha" lay-verify="required" placeholder="验证码" class="layui-input">
                </div>
                <div class="layui-col-xs5">
                    <div class="login-captcha">
                        <img src="captcha.php" class="login-captcha-img" id="captcha">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <input type="checkbox" name="remember" lay-skin="primary" title="记住密码">
        </div>
        
        <div class="layui-form-item">
            <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="login">立即登录</button>
        </div>
        
        <div class="layui-form-item login-form-other">
            <label>还没有账号？</label>
            <a href="javascript:;">立即注册</a>
        </div>
    </form>

    <!-- 版权信息 -->
    <div class="login-footer">
        <p>&copy; 2025 API管理系统. All rights reserved.</p>
    </div>

    <script src="../../Easyweb/assets/libs/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function () {
            var form = layui.form;
            var layer = layui.layer;

            // 验证码点击刷新
            $('#captcha').click(function () {
                this.src = 'captcha.php?' + Math.random();
            });

            // 表单提交
            form.on('submit(login)', function (data) {
                var loadIndex = layer.load(2, {shade: [0.3, '#000']});
                
                // 发送登录请求
                $.ajax({
                    url: 'login.php',
                    type: 'POST',
                    data: data.field,
                    dataType: 'json',
                    success: function (res) {
                        layer.close(loadIndex);
                        if (res.code === 200) {
                            layer.msg(res.msg, {icon: 1}, function () {
                                location.href = res.data.redirect;
                            });
                        } else {
                            layer.msg(res.msg, {icon: 2});
                            // 刷新验证码
                            $('#captcha')[0].src = 'captcha.php?' + Math.random();
                        }
                    },
                    error: function () {
                        layer.close(loadIndex);
                        layer.msg('网络请求失败', {icon: 2});
                    }
                });
                
                return false;
            });
        });
    </script>
</body>
</html>