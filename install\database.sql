-- API商业系统数据库结构
-- 创建时间: 2023-06-01

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

-- --------------------------------------------------------

--
-- 表结构 `api_users`
--

CREATE TABLE IF NOT EXISTS `api_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `role` enum('user','merchant','admin') NOT NULL DEFAULT 'user' COMMENT '角色',
  `vip_level` int(11) NOT NULL DEFAULT '0' COMMENT 'VIP等级',
  `vip_expire` datetime DEFAULT NULL COMMENT 'VIP到期时间',
  `merchant_level` int(11) DEFAULT '0' COMMENT '商家等级',
  `merchant_expire` datetime DEFAULT NULL COMMENT '商家到期时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- --------------------------------------------------------

--
-- 表结构 `api_user_tokens`
--

CREATE TABLE IF NOT EXISTS `api_user_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `token` varchar(255) NOT NULL COMMENT '令牌',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户令牌表';

-- --------------------------------------------------------

--
-- 表结构 `api_api_keys`
--

CREATE TABLE IF NOT EXISTS `api_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `name` varchar(100) DEFAULT NULL COMMENT '密钥名称',
  `ip_whitelist` text COMMENT 'IP白名单',
  `ip_blacklist` text COMMENT 'IP黑名单',
  `qps_limit` int(11) DEFAULT '10' COMMENT 'QPS限制',
  `daily_limit` int(11) DEFAULT '1000' COMMENT '每日调用限制',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_key` (`api_key`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API密钥表';

-- --------------------------------------------------------

--
-- 表结构 `api_categories`
--

CREATE TABLE IF NOT EXISTS `api_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API分类表';

-- --------------------------------------------------------

--
-- 表结构 `api_apis`
--

CREATE TABLE IF NOT EXISTS `api_apis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建者ID（商家）',
  `name` varchar(100) NOT NULL COMMENT 'API名称',
  `description` text COMMENT 'API描述',
  `url` varchar(255) NOT NULL COMMENT 'API地址',
  `method` enum('GET','POST','PUT','DELETE') NOT NULL DEFAULT 'GET' COMMENT '请求方法',
  `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费：0付费，1免费',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `merchant_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商家价格',
  `call_count` int(11) NOT NULL DEFAULT '0' COMMENT '调用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API表';

-- --------------------------------------------------------

--
-- 表结构 `api_parameters`
--

CREATE TABLE IF NOT EXISTS `api_parameters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `name` varchar(50) NOT NULL COMMENT '参数名',
  `type` varchar(20) NOT NULL COMMENT '参数类型',
  `description` text COMMENT '参数描述',
  `required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必填：0否，1是',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `api_id` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API参数表';

-- --------------------------------------------------------

--
-- 表结构 `api_responses`
--

CREATE TABLE IF NOT EXISTS `api_responses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `name` varchar(50) DEFAULT NULL COMMENT '响应名称',
  `content` text NOT NULL COMMENT '响应内容',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `api_id` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API响应示例表';

-- --------------------------------------------------------

--
-- 表结构 `api_logs`
--

CREATE TABLE IF NOT EXISTS `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `ip` varchar(50) NOT NULL COMMENT 'IP地址',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `url` varchar(255) NOT NULL COMMENT '请求URL',
  `params` text COMMENT '请求参数',
  `response_code` int(11) NOT NULL COMMENT '响应状态码',
  `response_time` int(11) NOT NULL COMMENT '响应时间(ms)',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `api_id` (`api_id`),
  KEY `user_id` (`user_id`),
  KEY `api_key` (`api_key`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API调用日志表';

-- --------------------------------------------------------

--
-- 表结构 `api_purchases`
--

CREATE TABLE IF NOT EXISTS `api_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0无效，1有效',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `expire_at` datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `api_id` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API购买记录表';

-- --------------------------------------------------------

--
-- 表结构 `api_orders`
--

CREATE TABLE IF NOT EXISTS `api_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '交易号',
  `order_type` varchar(20) NOT NULL COMMENT '订单类型：recharge充值，purchase购买，vip会员',
  `status` varchar(20) NOT NULL COMMENT '状态：pending待支付，paid已支付，cancelled已取消',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- --------------------------------------------------------

--
-- 表结构 `api_merchant_levels`
--

CREATE TABLE IF NOT EXISTS `api_merchant_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `description` text COMMENT '等级描述',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `duration` int(11) NOT NULL DEFAULT '30' COMMENT '有效期(天)',
  `api_limit` int(11) DEFAULT NULL COMMENT 'API数量限制',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '70.00' COMMENT '分成比例(%)',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家等级表';

-- --------------------------------------------------------

--
-- 表结构 `api_vip_levels`
--

CREATE TABLE IF NOT EXISTS `api_vip_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `description` text COMMENT '等级描述',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `duration` int(11) NOT NULL DEFAULT '30' COMMENT '有效期(天)',
  `discount` decimal(5,2) NOT NULL DEFAULT '100.00' COMMENT '折扣比例(%)',
  `daily_free_calls` int(11) NOT NULL DEFAULT '0' COMMENT '每日免费调用次数',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级表';

-- --------------------------------------------------------

--
-- 表结构 `api_tickets`
--

CREATE TABLE IF NOT EXISTS `api_tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `type` varchar(20) NOT NULL COMMENT '类型：question问题，bug错误，suggestion建议',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending待处理，processing处理中，resolved已解决，closed已关闭',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单表';

-- --------------------------------------------------------

--
-- 表结构 `api_ticket_replies`
--

CREATE TABLE IF NOT EXISTS `api_ticket_replies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) NOT NULL COMMENT '工单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '内容',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `ticket_id` (`ticket_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单回复表';

-- --------------------------------------------------------

--
-- 表结构 `api_banners`
--

CREATE TABLE IF NOT EXISTS `api_banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '标题',
  `image` varchar(255) NOT NULL COMMENT '图片',
  `link` varchar(255) DEFAULT NULL COMMENT '链接',
  `target` varchar(10) NOT NULL DEFAULT '_blank' COMMENT '打开方式',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

-- --------------------------------------------------------

--
-- 表结构 `api_articles`
--

CREATE TABLE IF NOT EXISTS `api_articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `views` int(11) NOT NULL DEFAULT '0' COMMENT '浏览量',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- --------------------------------------------------------

--
-- 表结构 `api_article_categories`
--

CREATE TABLE IF NOT EXISTS `api_article_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表';

-- --------------------------------------------------------

--
-- 表结构 `api_configs`
--

CREATE TABLE IF NOT EXISTS `api_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- --------------------------------------------------------

--
-- 表结构 `api_navigations`
--

CREATE TABLE IF NOT EXISTS `api_navigations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级ID',
  `name` varchar(50) NOT NULL COMMENT '名称',
  `url` varchar(255) NOT NULL COMMENT 'URL',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `target` varchar(10) NOT NULL DEFAULT '_self' COMMENT '打开方式',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导航表';

-- --------------------------------------------------------

--
-- 初始数据
--

-- 插入默认配置
INSERT INTO `api_configs` (`key`, `value`, `description`, `created_at`, `updated_at`) VALUES
('site_name', 'API商业系统', '网站名称', NOW(), NULL),
('site_logo', '', '网站Logo', NOW(), NULL),
('site_keywords', 'API,接口,商业化,管理系统', '网站关键词', NOW(), NULL),
('site_description', '专业的API接口管理平台，提供完整的API商业化解决方案', '网站描述', NOW(), NULL),
('icp_number', '', 'ICP备案号', NOW(), NULL),
('copyright', 'Copyright © 2023 API商业系统 All Rights Reserved.', '版权信息', NOW(), NULL),
('register_status', '1', '是否开放注册', NOW(), NULL),
('merchant_status', '1', '是否开放商家入驻', NOW(), NULL),
('default_avatar', '/public/images/default-avatar.png', '默认头像', NOW(), NULL),
('user_agreement', '用户协议内容', '用户协议', NOW(), NULL),
('privacy_policy', '隐私政策内容', '隐私政策', NOW(), NULL),
('merchant_agreement', '商家入驻协议内容', '商家入驻协议', NOW(), NULL),
('global_css', '', '全局CSS', NOW(), NULL),
('global_js', '', '全局JS', NOW(), NULL);

-- 插入默认导航
INSERT INTO `api_navigations` (`parent_id`, `name`, `url`, `icon`, `target`, `sort_order`, `status`) VALUES
(0, '首页', '/', 'fa fa-home', '_self', 1, 1),
(0, 'API市场', '/api/market', 'fa fa-code', '_self', 2, 1),
(0, '文档中心', '/docs', 'fa fa-book', '_self', 3, 1),
(0, '商家入驻', '/merchant/join', 'fa fa-shopping-bag', '_self', 4, 1),
(0, '帮助中心', '/help', 'fa fa-question-circle', '_self', 5, 1);

-- 插入默认商家等级
INSERT INTO `api_merchant_levels` (`name`, `description`, `price`, `duration`, `api_limit`, `commission_rate`, `sort_order`, `status`) VALUES
('普通商家', '基础商家功能，适合个人开发者', 99.00, 30, 5, 70.00, 1, 1),
('高级商家', '更多API数量，更高分成比例', 299.00, 30, 20, 80.00, 2, 1),
('企业商家', '无限API数量，最高分成比例', 999.00, 30, NULL, 90.00, 3, 1);

-- 插入默认VIP等级
INSERT INTO `api_vip_levels` (`name`, `description`, `price`, `duration`, `discount`, `daily_free_calls`, `sort_order`, `status`) VALUES
('VIP1', '基础会员，享受9折优惠', 19.00, 30, 90.00, 100, 1, 1),
('VIP2', '高级会员，享受8折优惠', 49.00, 30, 80.00, 300, 2, 1),
('VIP3', '尊贵会员，享受7折优惠', 99.00, 30, 70.00, 500, 3, 1);

-- 插入默认API分类
INSERT INTO `api_categories` (`name`, `description`, `icon`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('工具类', '实用工具API', 'fa fa-wrench', 1, 1, NOW(), NULL),
('金融类', '金融数据API', 'fa fa-money', 2, 1, NOW(), NULL),
('天气类', '天气预报API', 'fa fa-cloud', 3, 1, NOW(), NULL),
('地图类', '地理位置API', 'fa fa-map-marker', 4, 1, NOW(), NULL),
('翻译类', '多语言翻译API', 'fa fa-language', 5, 1, NOW(), NULL),
('图像类', '图像处理API', 'fa fa-picture-o', 6, 1, NOW(), NULL);