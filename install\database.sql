-- API商业系统数据库结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `api_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `api_system`;

-- 管理员表
CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `role` tinyint(4) NOT NULL DEFAULT '1' COMMENT '角色：1普通管理员，2超级管理员',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 管理员日志表
CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `content` text COMMENT '操作内容',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '浏览器信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员日志表';

-- 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `vip_level` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'VIP等级',
  `vip_expires` datetime DEFAULT NULL COMMENT 'VIP过期时间',
  `api_key` varchar(50) NOT NULL COMMENT 'API密钥',
  `qps_limit` int(11) NOT NULL DEFAULT '10' COMMENT 'QPS限制',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `remember_token` varchar(100) DEFAULT NULL COMMENT '记住登录令牌',
  `reset_token` varchar(100) DEFAULT NULL COMMENT '重置密码令牌',
  `reset_token_expires` datetime DEFAULT NULL COMMENT '重置密码令牌过期时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `api_key` (`api_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户日志表
CREATE TABLE `user_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `content` text COMMENT '操作内容',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '浏览器信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户日志表';

-- 商家表
CREATE TABLE `merchants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `company_name` varchar(100) DEFAULT NULL COMMENT '公司名称',
  `company_license` varchar(255) DEFAULT NULL COMMENT '营业执照',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '商家等级',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '70.00' COMMENT '分成比例（%）',
  `api_key` varchar(50) NOT NULL COMMENT 'API密钥',
  `qps_limit` int(11) NOT NULL DEFAULT '20' COMMENT 'QPS限制',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1启用，2拒绝，3禁用',
  `remember_token` varchar(100) DEFAULT NULL COMMENT '记住登录令牌',
  `reset_token` varchar(100) DEFAULT NULL COMMENT '重置密码令牌',
  `reset_token_expires` datetime DEFAULT NULL COMMENT '重置密码令牌过期时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `api_key` (`api_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家表';

-- 商家日志表
CREATE TABLE `merchant_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL COMMENT '商家ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `content` text COMMENT '操作内容',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '浏览器信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家日志表';

-- API分类表
CREATE TABLE `api_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API分类表';

-- API表
CREATE TABLE `apis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建者ID（商家ID）',
  `name` varchar(100) NOT NULL COMMENT 'API名称',
  `url` varchar(100) NOT NULL COMMENT 'API路径',
  `description` text COMMENT 'API描述',
  `type` varchar(20) NOT NULL DEFAULT 'local' COMMENT '类型：local本地，remote远程',
  `method` varchar(10) NOT NULL DEFAULT 'GET' COMMENT '请求方法：GET, POST, PUT, DELETE',
  `required_params` text COMMENT '必要参数（JSON格式）',
  `optional_params` text COMMENT '可选参数（JSON格式）',
  `return_params` text COMMENT '返回参数（JSON格式）',
  `example_request` text COMMENT '示例请求',
  `example_response` text COMMENT '示例响应',
  `example_php` text COMMENT 'PHP示例代码',
  `example_java` text COMMENT 'Java示例代码',
  `example_python` text COMMENT 'Python示例代码',
  `example_js` text COMMENT 'JavaScript示例代码',
  `example_csharp` text COMMENT 'C#示例代码',
  `handler_file` varchar(100) DEFAULT NULL COMMENT '处理程序文件（本地API）',
  `handler_class` varchar(100) DEFAULT NULL COMMENT '处理程序类（本地API）',
  `remote_url` varchar(255) DEFAULT NULL COMMENT '远程URL（远程API）',
  `remote_headers` text COMMENT '远程请求头（JSON格式）',
  `expected_response` text COMMENT '期望的响应结构（JSON格式）',
  `is_free` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否免费：0否，1是',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格（元/次）',
  `need_purchase` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否需要购买：0否，1是',
  `min_vip_level` tinyint(4) NOT NULL DEFAULT '0' COMMENT '最低VIP等级',
  `call_count` int(11) NOT NULL DEFAULT '0' COMMENT '调用次数',
  `rating` decimal(3,1) NOT NULL DEFAULT '5.0' COMMENT '评分',
  `tags` varchar(255) DEFAULT NULL COMMENT '标签（逗号分隔）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `url` (`url`),
  KEY `category_id` (`category_id`),
  KEY `creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API表';

-- API调用日志表
CREATE TABLE `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '浏览器信息',
  `request_params` text COMMENT '请求参数',
  `response_data` text COMMENT '响应数据',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0失败，1成功',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  PRIMARY KEY (`id`),
  KEY `api_id` (`api_id`),
  KEY `user_id` (`user_id`),
  KEY `merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API调用日志表';

-- API评价表
CREATE TABLE `api_reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `rating` tinyint(4) NOT NULL COMMENT '评分（1-5）',
  `content` text COMMENT '评价内容',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0隐藏，1显示',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `api_id` (`api_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API评价表';

-- 用户购买API记录表
CREATE TABLE `user_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `price` decimal(10,2) NOT NULL COMMENT '购买价格',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `api_id` (`api_id`),
  KEY `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户购买API记录表';

-- 商家购买API记录表
CREATE TABLE `merchant_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL COMMENT '商家ID',
  `api_id` int(11) NOT NULL COMMENT 'API ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `price` decimal(10,2) NOT NULL COMMENT '购买价格',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `api_id` (`api_id`),
  KEY `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家购买API记录表';

-- 订单表
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `type` varchar(20) NOT NULL COMMENT '订单类型：recharge充值，purchase购买，vip会员',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式：alipay支付宝，wechat微信',
  `payment_no` varchar(100) DEFAULT NULL COMMENT '支付单号',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0待支付，1已支付，2已取消，3已退款',
  `api_id` int(11) DEFAULT NULL COMMENT 'API ID（购买API时）',
  `vip_level` tinyint(4) DEFAULT NULL COMMENT 'VIP等级（购买会员时）',
  `vip_duration` int(11) DEFAULT NULL COMMENT 'VIP时长（月，购买会员时）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `expired_at` datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `api_id` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 交易记录表
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `type` varchar(20) NOT NULL COMMENT '交易类型：recharge充值，withdraw提现，api_call调用API，api_income API收入',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `balance` decimal(10,2) NOT NULL COMMENT '交易后余额',
  `description` varchar(255) DEFAULT NULL COMMENT '交易描述',
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `api_id` int(11) DEFAULT NULL COMMENT 'API ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `order_id` (`order_id`),
  KEY `api_id` (`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易记录表';

-- 提现申请表
CREATE TABLE `withdrawals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `bank_name` varchar(50) DEFAULT NULL COMMENT '银行名称',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `bank_holder` varchar(50) DEFAULT NULL COMMENT '开户人姓名',
  `alipay_account` varchar(100) DEFAULT NULL COMMENT '支付宝账号',
  `wechat_account` varchar(100) DEFAULT NULL COMMENT '微信账号',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1已通过，2已拒绝，3已打款',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- VIP等级表
CREATE TABLE `vip_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `level` tinyint(4) NOT NULL COMMENT '等级值',
  `price` decimal(10,2) NOT NULL COMMENT '价格（元/月）',
  `discount` decimal(5,2) NOT NULL DEFAULT '100.00' COMMENT 'API调用折扣（%）',
  `qps_limit` int(11) NOT NULL DEFAULT '10' COMMENT 'QPS限制',
  `description` text COMMENT '等级描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '等级图标',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP等级表';

-- 商家等级表
CREATE TABLE `merchant_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `level` tinyint(4) NOT NULL COMMENT '等级值',
  `price` decimal(10,2) NOT NULL COMMENT '价格（元/月）',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '70.00' COMMENT '分成比例（%）',
  `qps_limit` int(11) NOT NULL DEFAULT '20' COMMENT 'QPS限制',
  `api_limit` int(11) NOT NULL DEFAULT '10' COMMENT 'API数量限制',
  `description` text COMMENT '等级描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '等级图标',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家等级表';

-- 网站配置表
CREATE TABLE `site_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_name` varchar(100) NOT NULL COMMENT '网站名称',
  `site_logo` varchar(255) DEFAULT NULL COMMENT '网站Logo',
  `site_favicon` varchar(255) DEFAULT NULL COMMENT '网站图标',
  `site_keywords` varchar(255) DEFAULT NULL COMMENT '网站关键词',
  `site_description` varchar(255) DEFAULT NULL COMMENT '网站描述',
  `site_url` varchar(255) NOT NULL COMMENT '网站URL',
  `api_base_url` varchar(255) NOT NULL COMMENT 'API基础URL',
  `icp_number` varchar(50) DEFAULT NULL COMMENT 'ICP备案号',
  `copyright` varchar(255) DEFAULT NULL COMMENT '版权信息',
  `admin_email` varchar(100) DEFAULT NULL COMMENT '管理员邮箱',
  `upload_max_size` int(11) NOT NULL DEFAULT '10485760' COMMENT '上传文件最大大小（字节）',
  `upload_allowed_types` varchar(255) DEFAULT NULL COMMENT '允许上传的文件类型',
  `register_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '注册状态：0关闭，1开放',
  `merchant_register_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '商家注册状态：0关闭，1开放',
  `email_verify` tinyint(4) NOT NULL DEFAULT '0' COMMENT '邮箱验证：0关闭，1开启',
  `email_notify` tinyint(4) NOT NULL DEFAULT '1' COMMENT '邮件通知：0关闭，1开启',
  `exception_notify` tinyint(4) NOT NULL DEFAULT '1' COMMENT '异常通知：0关闭，1开启',
  `exception_email` tinyint(4) NOT NULL DEFAULT '1' COMMENT '异常邮件通知：0关闭，1开启',
  `withdraw_min` decimal(10,2) NOT NULL DEFAULT '100.00' COMMENT '最低提现金额',
  `withdraw_fee` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '提现手续费（%）',
  `custom_css` text COMMENT '自定义CSS',
  `custom_js` text COMMENT '自定义JavaScript',
  `template` varchar(50) NOT NULL DEFAULT 'default' COMMENT '当前模板',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站配置表';

-- 导航菜单表
CREATE TABLE `nav_menus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `url` varchar(255) NOT NULL COMMENT '链接URL',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `parent_id` int(11) DEFAULT '0' COMMENT '父级ID',
  `position` varchar(20) NOT NULL DEFAULT 'top' COMMENT '位置：top顶部，bottom底部，footer页脚',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `target` varchar(10) NOT NULL DEFAULT '_self' COMMENT '打开方式：_self当前窗口，_blank新窗口',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导航菜单表';

-- 轮播图表
CREATE TABLE `banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '标题',
  `image` varchar(255) NOT NULL COMMENT '图片',
  `url` varchar(255) DEFAULT NULL COMMENT '链接URL',
  `position` varchar(20) NOT NULL DEFAULT 'home' COMMENT '位置：home首页，api接口页',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

-- 文章分类表
CREATE TABLE `article_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表';

-- 文章表
CREATE TABLE `articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `summary` varchar(255) DEFAULT NULL COMMENT '摘要',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT '缩略图',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `views` int(11) NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 单页表
CREATE TABLE `pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '标题',
  `slug` varchar(100) NOT NULL COMMENT '别名',
  `content` text NOT NULL COMMENT '内容',
  `keywords` varchar(255) DEFAULT NULL COMMENT '关键词',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单页表';

-- 工单表
CREATE TABLE `tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `priority` tinyint(4) NOT NULL DEFAULT '1' COMMENT '优先级：1低，2中，3高',