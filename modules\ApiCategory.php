<?php
/**
 * API分类模型
 * API管理系统 - API分类数据模型
 */

require_once __DIR__ . '/../core/Model.php';

class ApiCategory extends Model {
    protected $table = 'categories';
    protected $fillable = ['name', 'description', 'icon', 'sort_order', 'status'];
    
    /**
     * 获取分类列表（带API数量统计）
     */
    public function getListWithApiCount($page = 1, $perPage = 20, $conditions = []) {
        $offset = ($page - 1) * $perPage;
        
        // 构建查询条件
        $whereClause = [];
        $params = [];
        
        if (!empty($conditions['name'])) {
            $whereClause[] = "c.name LIKE :name";
            $params['name'] = '%' . $conditions['name'] . '%';
        }
        
        if (!empty($conditions['status'])) {
            $whereClause[] = "c.status = :status";
            $params['status'] = $conditions['status'];
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->getTableName()} c {$whereStr}";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT c.*, 
                       COUNT(i.id) as api_count,
                       SUM(CASE WHEN i.status = 1 THEN 1 ELSE 0 END) as active_api_count
                FROM {$this->getTableName()} c 
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON c.id = i.category_id
                {$whereStr}
                GROUP BY c.id
                ORDER BY c.sort_order ASC, c.id DESC 
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 获取所有启用的分类
     */
    public function getActiveCategories() {
        return $this->findAll(['status' => 1], 'sort_order ASC, id ASC');
    }
    
    /**
     * 检查分类名称是否存在
     */
    public function nameExists($name, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->getTableName()} WHERE name = :name";
        $params = ['name' => $name];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * 删除分类（检查是否有API使用）
     */
    public function delete($id) {
        // 检查是否有API使用此分类
        $sql = "SELECT COUNT(*) as count FROM {$this->db->getPrefix()}interfaces WHERE category_id = :category_id";
        $result = $this->db->fetchOne($sql, ['category_id' => $id]);
        
        if ($result['count'] > 0) {
            throw new Exception('该分类下还有API接口，无法删除');
        }
        
        return parent::delete($id);
    }
    
    /**
     * 获取分类统计信息
     */
    public function getCategoryStats() {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
                    (SELECT COUNT(*) FROM {$this->db->getPrefix()}interfaces WHERE category_id IN (SELECT id FROM {$this->getTableName()} WHERE status = 1)) as total_apis
                FROM {$this->getTableName()}";
        
        return $this->db->fetchOne($sql);
    }
    
    /**
     * 更新排序
     */
    public function updateSortOrder($id, $sortOrder) {
        return $this->update($id, ['sort_order' => $sortOrder]);
    }
    
    /**
     * 批量更新排序
     */
    public function batchUpdateSortOrder($sortData) {
        $this->beginTransaction();
        
        try {
            foreach ($sortData as $item) {
                $this->updateSortOrder($item['id'], $item['sort_order']);
            }
            
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * 获取分类树结构
     */
    public function getCategoryTree() {
        $categories = $this->getActiveCategories();
        $tree = [];
        
        foreach ($categories as $category) {
            $tree[] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'icon' => $category['icon'],
                'description' => $category['description']
            ];
        }
        
        return $tree;
    }
    
    /**
     * 获取热门分类
     */
    public function getHotCategories($limit = 5) {
        $sql = "SELECT c.*, COUNT(cl.id) as call_count
                FROM {$this->getTableName()} c
                LEFT JOIN {$this->db->getPrefix()}interfaces i ON c.id = i.category_id
                LEFT JOIN {$this->db->getPrefix()}call_logs cl ON i.id = cl.api_id
                WHERE c.status = 1 AND cl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY c.id
                ORDER BY call_count DESC
                LIMIT {$limit}";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * 批量更新状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids)) {
            return false;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->getTableName()} SET status = ?, updated_at = ? WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status, date('Y-m-d H:i:s')], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * 获取分类下的API列表
     */
    public function getCategoryApis($categoryId, $page = 1, $perPage = 20) {
        $offset = ($page - 1) * $perPage;
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->db->getPrefix()}interfaces WHERE category_id = :category_id";
        $totalResult = $this->db->fetchOne($countSql, ['category_id' => $categoryId]);
        $total = $totalResult['total'];
        
        // 查询数据
        $sql = "SELECT * FROM {$this->db->getPrefix()}interfaces 
                WHERE category_id = :category_id 
                ORDER BY id DESC 
                LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, ['category_id' => $categoryId]);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 移动API到其他分类
     */
    public function moveApisToCategory($fromCategoryId, $toCategoryId) {
        // 检查目标分类是否存在
        if (!$this->find($toCategoryId)) {
            throw new Exception('目标分类不存在');
        }
        
        $sql = "UPDATE {$this->db->getPrefix()}interfaces SET category_id = :to_category_id WHERE category_id = :from_category_id";
        $stmt = $this->db->query($sql, [
            'to_category_id' => $toCategoryId,
            'from_category_id' => $fromCategoryId
        ]);
        
        return $stmt->rowCount();
    }
    
    /**
     * 复制分类
     */
    public function copyCategory($id, $newName) {
        $category = $this->find($id);
        if (!$category) {
            throw new Exception('原分类不存在');
        }
        
        if ($this->nameExists($newName)) {
            throw new Exception('分类名称已存在');
        }
        
        $newCategoryData = [
            'name' => $newName,
            'description' => $category['description'] . '（复制）',
            'icon' => $category['icon'],
            'sort_order' => $category['sort_order'] + 1,
            'status' => 0 // 默认禁用
        ];
        
        return $this->create($newCategoryData);
    }
    
    /**
     * 获取分类使用统计
     */
    public function getCategoryUsageStats($categoryId) {
        $sql = "SELECT 
                    COUNT(i.id) as total_apis,
                    SUM(CASE WHEN i.status = 1 THEN 1 ELSE 0 END) as active_apis,
                    SUM(i.call_count) as total_calls,
                    SUM(i.success_count) as success_calls,
                    SUM(i.error_count) as error_calls,
                    AVG(i.price) as avg_price
                FROM {$this->db->getPrefix()}interfaces i
                WHERE i.category_id = :category_id";
        
        return $this->db->fetchOne($sql, ['category_id' => $categoryId]);
    }
}