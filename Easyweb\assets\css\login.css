/**
 * Easyweb登录页面样式
 * 标准的Easyweb登录模板样式
 */

/* 登录背景 */
.login-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.login-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

/* 登录表单容器 */
.login-form {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 360px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1;
}

/* 登录表单头部 */
.login-form-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-form-header h1 {
    font-size: 28px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.login-form-header p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* 表单项样式 */
.login-form .layui-form-item {
    position: relative;
    margin-bottom: 20px;
}

.login-form .layui-input {
    height: 48px;
    line-height: 48px;
    border-radius: 6px;
    border: 1px solid #e6e6e6;
    padding-left: 45px;
    font-size: 14px;
    transition: all 0.3s;
}

.login-form .layui-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 登录图标 */
.login-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #999;
    z-index: 2;
}

/* 验证码样式 */
.login-captcha {
    height: 48px;
    margin-left: 10px;
}

.login-captcha-img {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    cursor: pointer;
    border: 1px solid #e6e6e6;
    transition: all 0.3s;
}

.login-captcha-img:hover {
    border-color: #667eea;
}

/* 记住密码 */
.login-form .layui-form-checkbox {
    margin: 0;
}

.login-form .layui-form-checkbox span {
    font-size: 13px;
    color: #666;
}

/* 登录按钮 */
.login-form .layui-btn {
    height: 48px;
    line-height: 48px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s;
}

.login-form .layui-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 其他选项 */
.login-form-other {
    text-align: center;
    margin-top: 20px;
}

.login-form-other label {
    font-size: 13px;
    color: #999;
}

.login-form-other a {
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
    margin-left: 5px;
}

.login-form-other a:hover {
    text-decoration: underline;
}

/* 版权信息 */
.login-footer {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 1;
}

.login-footer p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-form {
        width: 90%;
        padding: 30px 20px;
    }
    
    .login-form-header h1 {
        font-size: 24px;
    }
    
    .login-form .layui-input {
        height: 44px;
        line-height: 44px;
        padding-left: 40px;
    }
    
    .login-form .layui-btn {
        height: 44px;
        line-height: 44px;
    }
    
    .login-captcha {
        height: 44px;
    }
}

/* 加载动画 */
.login-form {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translate(-50%, -40%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* 表单验证错误样式 */
.login-form .layui-form-danger {
    border-color: #ff5722 !important;
}

.login-form .layui-form-danger:focus {
    box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2) !important;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .login-form {
        background: rgba(30, 30, 30, 0.95);
        color: #fff;
    }
    
    .login-form-header h1 {
        color: #fff;
    }
    
    .login-form-header p {
        color: #ccc;
    }
    
    .login-form .layui-input {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #fff;
    }
    
    .login-form .layui-input::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }
    
    .login-icon {
        color: rgba(255, 255, 255, 0.6);
    }
}